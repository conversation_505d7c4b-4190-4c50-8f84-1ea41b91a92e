buildscript {
    repositories {
        // 阿里云公共仓库
        maven { url = "https://maven.aliyun.com/repository/public" }
        // 阿里云 Gradle 插件仓库
        maven { url = "https://maven.aliyun.com/repository/gradle-plugin" }
        // 官方仓库作为备用
        mavenCentral()
        // Gradle 插件仓库
        maven { url = "https://plugins.gradle.org/m2/" }
    }
    dependencies {
        // Kotlin Gradle 插件
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.0"
        // Lombok Gradle 插件
        classpath "io.freefair.gradle:lombok-plugin:8.6"
    }
}

rootProject.name = "iot-jfx"
