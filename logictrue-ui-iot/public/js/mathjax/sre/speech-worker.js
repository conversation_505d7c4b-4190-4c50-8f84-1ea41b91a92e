(()=>{var __webpack_modules__={74:(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__)=>{"use strict";__webpack_require__.d(__webpack_exports__,{f:()=>SystemExternal});var _variables_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(714),_lib_external_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(268);const windowSupported=!("undefined"==typeof window),documentSupported=windowSupported&&!(void 0===window.document),webworker=!("undefined"==typeof DedicatedWorkerGlobalScope),nodeRequire=()=>{try{return eval("require")}catch(t){return t=>null}},SystemExternal={extRequire:t=>"undefined"!=typeof process&&"undefined"!=typeof require?nodeRequire()(t):null,windowSupported,documentSupported,webworker,xmldom:_lib_external_js__WEBPACK_IMPORTED_MODULE_1__.Gb,document:_lib_external_js__WEBPACK_IMPORTED_MODULE_1__.Al,fs:documentSupported||webworker?null:nodeRequire()("fs"),url:_variables_js__WEBPACK_IMPORTED_MODULE_0__.u.url,jsonPath:function(){if(documentSupported||webworker)return _variables_js__WEBPACK_IMPORTED_MODULE_0__.u.url;if(process.env.SRE_JSON_PATH||global.SRE_JSON_PATH)return process.env.SRE_JSON_PATH||global.SRE_JSON_PATH;try{return nodeRequire().resolve("speech-rule-engine").replace(/sre\.js$/,"")+"mathmaps"}catch(t){}try{return nodeRequire().resolve(".").replace(/sre\.js$/,"")+"mathmaps"}catch(t){}return"undefined"!=typeof __dirname?__dirname+(__dirname.match(/lib?$/)?"/mathmaps":"/lib/mathmaps"):process.cwd()+"/lib/mathmaps"}(),xpath:_lib_external_js__WEBPACK_IMPORTED_MODULE_1__.Oe}},159:(t,e,n)=>{"use strict";var r=n(870).freeze;e.XML_ENTITIES=r({amp:"&",apos:"'",gt:">",lt:"<",quot:'"'}),e.HTML_ENTITIES=r({Aacute:"\xc1",aacute:"\xe1",Abreve:"\u0102",abreve:"\u0103",ac:"\u223e",acd:"\u223f",acE:"\u223e\u0333",Acirc:"\xc2",acirc:"\xe2",acute:"\xb4",Acy:"\u0410",acy:"\u0430",AElig:"\xc6",aelig:"\xe6",af:"\u2061",Afr:"\u{1d504}",afr:"\u{1d51e}",Agrave:"\xc0",agrave:"\xe0",alefsym:"\u2135",aleph:"\u2135",Alpha:"\u0391",alpha:"\u03b1",Amacr:"\u0100",amacr:"\u0101",amalg:"\u2a3f",AMP:"&",amp:"&",And:"\u2a53",and:"\u2227",andand:"\u2a55",andd:"\u2a5c",andslope:"\u2a58",andv:"\u2a5a",ang:"\u2220",ange:"\u29a4",angle:"\u2220",angmsd:"\u2221",angmsdaa:"\u29a8",angmsdab:"\u29a9",angmsdac:"\u29aa",angmsdad:"\u29ab",angmsdae:"\u29ac",angmsdaf:"\u29ad",angmsdag:"\u29ae",angmsdah:"\u29af",angrt:"\u221f",angrtvb:"\u22be",angrtvbd:"\u299d",angsph:"\u2222",angst:"\xc5",angzarr:"\u237c",Aogon:"\u0104",aogon:"\u0105",Aopf:"\u{1d538}",aopf:"\u{1d552}",ap:"\u2248",apacir:"\u2a6f",apE:"\u2a70",ape:"\u224a",apid:"\u224b",apos:"'",ApplyFunction:"\u2061",approx:"\u2248",approxeq:"\u224a",Aring:"\xc5",aring:"\xe5",Ascr:"\u{1d49c}",ascr:"\u{1d4b6}",Assign:"\u2254",ast:"*",asymp:"\u2248",asympeq:"\u224d",Atilde:"\xc3",atilde:"\xe3",Auml:"\xc4",auml:"\xe4",awconint:"\u2233",awint:"\u2a11",backcong:"\u224c",backepsilon:"\u03f6",backprime:"\u2035",backsim:"\u223d",backsimeq:"\u22cd",Backslash:"\u2216",Barv:"\u2ae7",barvee:"\u22bd",Barwed:"\u2306",barwed:"\u2305",barwedge:"\u2305",bbrk:"\u23b5",bbrktbrk:"\u23b6",bcong:"\u224c",Bcy:"\u0411",bcy:"\u0431",bdquo:"\u201e",becaus:"\u2235",Because:"\u2235",because:"\u2235",bemptyv:"\u29b0",bepsi:"\u03f6",bernou:"\u212c",Bernoullis:"\u212c",Beta:"\u0392",beta:"\u03b2",beth:"\u2136",between:"\u226c",Bfr:"\u{1d505}",bfr:"\u{1d51f}",bigcap:"\u22c2",bigcirc:"\u25ef",bigcup:"\u22c3",bigodot:"\u2a00",bigoplus:"\u2a01",bigotimes:"\u2a02",bigsqcup:"\u2a06",bigstar:"\u2605",bigtriangledown:"\u25bd",bigtriangleup:"\u25b3",biguplus:"\u2a04",bigvee:"\u22c1",bigwedge:"\u22c0",bkarow:"\u290d",blacklozenge:"\u29eb",blacksquare:"\u25aa",blacktriangle:"\u25b4",blacktriangledown:"\u25be",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",blank:"\u2423",blk12:"\u2592",blk14:"\u2591",blk34:"\u2593",block:"\u2588",bne:"=\u20e5",bnequiv:"\u2261\u20e5",bNot:"\u2aed",bnot:"\u2310",Bopf:"\u{1d539}",bopf:"\u{1d553}",bot:"\u22a5",bottom:"\u22a5",bowtie:"\u22c8",boxbox:"\u29c9",boxDL:"\u2557",boxDl:"\u2556",boxdL:"\u2555",boxdl:"\u2510",boxDR:"\u2554",boxDr:"\u2553",boxdR:"\u2552",boxdr:"\u250c",boxH:"\u2550",boxh:"\u2500",boxHD:"\u2566",boxHd:"\u2564",boxhD:"\u2565",boxhd:"\u252c",boxHU:"\u2569",boxHu:"\u2567",boxhU:"\u2568",boxhu:"\u2534",boxminus:"\u229f",boxplus:"\u229e",boxtimes:"\u22a0",boxUL:"\u255d",boxUl:"\u255c",boxuL:"\u255b",boxul:"\u2518",boxUR:"\u255a",boxUr:"\u2559",boxuR:"\u2558",boxur:"\u2514",boxV:"\u2551",boxv:"\u2502",boxVH:"\u256c",boxVh:"\u256b",boxvH:"\u256a",boxvh:"\u253c",boxVL:"\u2563",boxVl:"\u2562",boxvL:"\u2561",boxvl:"\u2524",boxVR:"\u2560",boxVr:"\u255f",boxvR:"\u255e",boxvr:"\u251c",bprime:"\u2035",Breve:"\u02d8",breve:"\u02d8",brvbar:"\xa6",Bscr:"\u212c",bscr:"\u{1d4b7}",bsemi:"\u204f",bsim:"\u223d",bsime:"\u22cd",bsol:"\\",bsolb:"\u29c5",bsolhsub:"\u27c8",bull:"\u2022",bullet:"\u2022",bump:"\u224e",bumpE:"\u2aae",bumpe:"\u224f",Bumpeq:"\u224e",bumpeq:"\u224f",Cacute:"\u0106",cacute:"\u0107",Cap:"\u22d2",cap:"\u2229",capand:"\u2a44",capbrcup:"\u2a49",capcap:"\u2a4b",capcup:"\u2a47",capdot:"\u2a40",CapitalDifferentialD:"\u2145",caps:"\u2229\ufe00",caret:"\u2041",caron:"\u02c7",Cayleys:"\u212d",ccaps:"\u2a4d",Ccaron:"\u010c",ccaron:"\u010d",Ccedil:"\xc7",ccedil:"\xe7",Ccirc:"\u0108",ccirc:"\u0109",Cconint:"\u2230",ccups:"\u2a4c",ccupssm:"\u2a50",Cdot:"\u010a",cdot:"\u010b",cedil:"\xb8",Cedilla:"\xb8",cemptyv:"\u29b2",cent:"\xa2",CenterDot:"\xb7",centerdot:"\xb7",Cfr:"\u212d",cfr:"\u{1d520}",CHcy:"\u0427",chcy:"\u0447",check:"\u2713",checkmark:"\u2713",Chi:"\u03a7",chi:"\u03c7",cir:"\u25cb",circ:"\u02c6",circeq:"\u2257",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",circledast:"\u229b",circledcirc:"\u229a",circleddash:"\u229d",CircleDot:"\u2299",circledR:"\xae",circledS:"\u24c8",CircleMinus:"\u2296",CirclePlus:"\u2295",CircleTimes:"\u2297",cirE:"\u29c3",cire:"\u2257",cirfnint:"\u2a10",cirmid:"\u2aef",cirscir:"\u29c2",ClockwiseContourIntegral:"\u2232",CloseCurlyDoubleQuote:"\u201d",CloseCurlyQuote:"\u2019",clubs:"\u2663",clubsuit:"\u2663",Colon:"\u2237",colon:":",Colone:"\u2a74",colone:"\u2254",coloneq:"\u2254",comma:",",commat:"@",comp:"\u2201",compfn:"\u2218",complement:"\u2201",complexes:"\u2102",cong:"\u2245",congdot:"\u2a6d",Congruent:"\u2261",Conint:"\u222f",conint:"\u222e",ContourIntegral:"\u222e",Copf:"\u2102",copf:"\u{1d554}",coprod:"\u2210",Coproduct:"\u2210",COPY:"\xa9",copy:"\xa9",copysr:"\u2117",CounterClockwiseContourIntegral:"\u2233",crarr:"\u21b5",Cross:"\u2a2f",cross:"\u2717",Cscr:"\u{1d49e}",cscr:"\u{1d4b8}",csub:"\u2acf",csube:"\u2ad1",csup:"\u2ad0",csupe:"\u2ad2",ctdot:"\u22ef",cudarrl:"\u2938",cudarrr:"\u2935",cuepr:"\u22de",cuesc:"\u22df",cularr:"\u21b6",cularrp:"\u293d",Cup:"\u22d3",cup:"\u222a",cupbrcap:"\u2a48",CupCap:"\u224d",cupcap:"\u2a46",cupcup:"\u2a4a",cupdot:"\u228d",cupor:"\u2a45",cups:"\u222a\ufe00",curarr:"\u21b7",curarrm:"\u293c",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",curlyvee:"\u22ce",curlywedge:"\u22cf",curren:"\xa4",curvearrowleft:"\u21b6",curvearrowright:"\u21b7",cuvee:"\u22ce",cuwed:"\u22cf",cwconint:"\u2232",cwint:"\u2231",cylcty:"\u232d",Dagger:"\u2021",dagger:"\u2020",daleth:"\u2138",Darr:"\u21a1",dArr:"\u21d3",darr:"\u2193",dash:"\u2010",Dashv:"\u2ae4",dashv:"\u22a3",dbkarow:"\u290f",dblac:"\u02dd",Dcaron:"\u010e",dcaron:"\u010f",Dcy:"\u0414",dcy:"\u0434",DD:"\u2145",dd:"\u2146",ddagger:"\u2021",ddarr:"\u21ca",DDotrahd:"\u2911",ddotseq:"\u2a77",deg:"\xb0",Del:"\u2207",Delta:"\u0394",delta:"\u03b4",demptyv:"\u29b1",dfisht:"\u297f",Dfr:"\u{1d507}",dfr:"\u{1d521}",dHar:"\u2965",dharl:"\u21c3",dharr:"\u21c2",DiacriticalAcute:"\xb4",DiacriticalDot:"\u02d9",DiacriticalDoubleAcute:"\u02dd",DiacriticalGrave:"`",DiacriticalTilde:"\u02dc",diam:"\u22c4",Diamond:"\u22c4",diamond:"\u22c4",diamondsuit:"\u2666",diams:"\u2666",die:"\xa8",DifferentialD:"\u2146",digamma:"\u03dd",disin:"\u22f2",div:"\xf7",divide:"\xf7",divideontimes:"\u22c7",divonx:"\u22c7",DJcy:"\u0402",djcy:"\u0452",dlcorn:"\u231e",dlcrop:"\u230d",dollar:"$",Dopf:"\u{1d53b}",dopf:"\u{1d555}",Dot:"\xa8",dot:"\u02d9",DotDot:"\u20dc",doteq:"\u2250",doteqdot:"\u2251",DotEqual:"\u2250",dotminus:"\u2238",dotplus:"\u2214",dotsquare:"\u22a1",doublebarwedge:"\u2306",DoubleContourIntegral:"\u222f",DoubleDot:"\xa8",DoubleDownArrow:"\u21d3",DoubleLeftArrow:"\u21d0",DoubleLeftRightArrow:"\u21d4",DoubleLeftTee:"\u2ae4",DoubleLongLeftArrow:"\u27f8",DoubleLongLeftRightArrow:"\u27fa",DoubleLongRightArrow:"\u27f9",DoubleRightArrow:"\u21d2",DoubleRightTee:"\u22a8",DoubleUpArrow:"\u21d1",DoubleUpDownArrow:"\u21d5",DoubleVerticalBar:"\u2225",DownArrow:"\u2193",Downarrow:"\u21d3",downarrow:"\u2193",DownArrowBar:"\u2913",DownArrowUpArrow:"\u21f5",DownBreve:"\u0311",downdownarrows:"\u21ca",downharpoonleft:"\u21c3",downharpoonright:"\u21c2",DownLeftRightVector:"\u2950",DownLeftTeeVector:"\u295e",DownLeftVector:"\u21bd",DownLeftVectorBar:"\u2956",DownRightTeeVector:"\u295f",DownRightVector:"\u21c1",DownRightVectorBar:"\u2957",DownTee:"\u22a4",DownTeeArrow:"\u21a7",drbkarow:"\u2910",drcorn:"\u231f",drcrop:"\u230c",Dscr:"\u{1d49f}",dscr:"\u{1d4b9}",DScy:"\u0405",dscy:"\u0455",dsol:"\u29f6",Dstrok:"\u0110",dstrok:"\u0111",dtdot:"\u22f1",dtri:"\u25bf",dtrif:"\u25be",duarr:"\u21f5",duhar:"\u296f",dwangle:"\u29a6",DZcy:"\u040f",dzcy:"\u045f",dzigrarr:"\u27ff",Eacute:"\xc9",eacute:"\xe9",easter:"\u2a6e",Ecaron:"\u011a",ecaron:"\u011b",ecir:"\u2256",Ecirc:"\xca",ecirc:"\xea",ecolon:"\u2255",Ecy:"\u042d",ecy:"\u044d",eDDot:"\u2a77",Edot:"\u0116",eDot:"\u2251",edot:"\u0117",ee:"\u2147",efDot:"\u2252",Efr:"\u{1d508}",efr:"\u{1d522}",eg:"\u2a9a",Egrave:"\xc8",egrave:"\xe8",egs:"\u2a96",egsdot:"\u2a98",el:"\u2a99",Element:"\u2208",elinters:"\u23e7",ell:"\u2113",els:"\u2a95",elsdot:"\u2a97",Emacr:"\u0112",emacr:"\u0113",empty:"\u2205",emptyset:"\u2205",EmptySmallSquare:"\u25fb",emptyv:"\u2205",EmptyVerySmallSquare:"\u25ab",emsp:"\u2003",emsp13:"\u2004",emsp14:"\u2005",ENG:"\u014a",eng:"\u014b",ensp:"\u2002",Eogon:"\u0118",eogon:"\u0119",Eopf:"\u{1d53c}",eopf:"\u{1d556}",epar:"\u22d5",eparsl:"\u29e3",eplus:"\u2a71",epsi:"\u03b5",Epsilon:"\u0395",epsilon:"\u03b5",epsiv:"\u03f5",eqcirc:"\u2256",eqcolon:"\u2255",eqsim:"\u2242",eqslantgtr:"\u2a96",eqslantless:"\u2a95",Equal:"\u2a75",equals:"=",EqualTilde:"\u2242",equest:"\u225f",Equilibrium:"\u21cc",equiv:"\u2261",equivDD:"\u2a78",eqvparsl:"\u29e5",erarr:"\u2971",erDot:"\u2253",Escr:"\u2130",escr:"\u212f",esdot:"\u2250",Esim:"\u2a73",esim:"\u2242",Eta:"\u0397",eta:"\u03b7",ETH:"\xd0",eth:"\xf0",Euml:"\xcb",euml:"\xeb",euro:"\u20ac",excl:"!",exist:"\u2203",Exists:"\u2203",expectation:"\u2130",ExponentialE:"\u2147",exponentiale:"\u2147",fallingdotseq:"\u2252",Fcy:"\u0424",fcy:"\u0444",female:"\u2640",ffilig:"\ufb03",fflig:"\ufb00",ffllig:"\ufb04",Ffr:"\u{1d509}",ffr:"\u{1d523}",filig:"\ufb01",FilledSmallSquare:"\u25fc",FilledVerySmallSquare:"\u25aa",fjlig:"fj",flat:"\u266d",fllig:"\ufb02",fltns:"\u25b1",fnof:"\u0192",Fopf:"\u{1d53d}",fopf:"\u{1d557}",ForAll:"\u2200",forall:"\u2200",fork:"\u22d4",forkv:"\u2ad9",Fouriertrf:"\u2131",fpartint:"\u2a0d",frac12:"\xbd",frac13:"\u2153",frac14:"\xbc",frac15:"\u2155",frac16:"\u2159",frac18:"\u215b",frac23:"\u2154",frac25:"\u2156",frac34:"\xbe",frac35:"\u2157",frac38:"\u215c",frac45:"\u2158",frac56:"\u215a",frac58:"\u215d",frac78:"\u215e",frasl:"\u2044",frown:"\u2322",Fscr:"\u2131",fscr:"\u{1d4bb}",gacute:"\u01f5",Gamma:"\u0393",gamma:"\u03b3",Gammad:"\u03dc",gammad:"\u03dd",gap:"\u2a86",Gbreve:"\u011e",gbreve:"\u011f",Gcedil:"\u0122",Gcirc:"\u011c",gcirc:"\u011d",Gcy:"\u0413",gcy:"\u0433",Gdot:"\u0120",gdot:"\u0121",gE:"\u2267",ge:"\u2265",gEl:"\u2a8c",gel:"\u22db",geq:"\u2265",geqq:"\u2267",geqslant:"\u2a7e",ges:"\u2a7e",gescc:"\u2aa9",gesdot:"\u2a80",gesdoto:"\u2a82",gesdotol:"\u2a84",gesl:"\u22db\ufe00",gesles:"\u2a94",Gfr:"\u{1d50a}",gfr:"\u{1d524}",Gg:"\u22d9",gg:"\u226b",ggg:"\u22d9",gimel:"\u2137",GJcy:"\u0403",gjcy:"\u0453",gl:"\u2277",gla:"\u2aa5",glE:"\u2a92",glj:"\u2aa4",gnap:"\u2a8a",gnapprox:"\u2a8a",gnE:"\u2269",gne:"\u2a88",gneq:"\u2a88",gneqq:"\u2269",gnsim:"\u22e7",Gopf:"\u{1d53e}",gopf:"\u{1d558}",grave:"`",GreaterEqual:"\u2265",GreaterEqualLess:"\u22db",GreaterFullEqual:"\u2267",GreaterGreater:"\u2aa2",GreaterLess:"\u2277",GreaterSlantEqual:"\u2a7e",GreaterTilde:"\u2273",Gscr:"\u{1d4a2}",gscr:"\u210a",gsim:"\u2273",gsime:"\u2a8e",gsiml:"\u2a90",Gt:"\u226b",GT:">",gt:">",gtcc:"\u2aa7",gtcir:"\u2a7a",gtdot:"\u22d7",gtlPar:"\u2995",gtquest:"\u2a7c",gtrapprox:"\u2a86",gtrarr:"\u2978",gtrdot:"\u22d7",gtreqless:"\u22db",gtreqqless:"\u2a8c",gtrless:"\u2277",gtrsim:"\u2273",gvertneqq:"\u2269\ufe00",gvnE:"\u2269\ufe00",Hacek:"\u02c7",hairsp:"\u200a",half:"\xbd",hamilt:"\u210b",HARDcy:"\u042a",hardcy:"\u044a",hArr:"\u21d4",harr:"\u2194",harrcir:"\u2948",harrw:"\u21ad",Hat:"^",hbar:"\u210f",Hcirc:"\u0124",hcirc:"\u0125",hearts:"\u2665",heartsuit:"\u2665",hellip:"\u2026",hercon:"\u22b9",Hfr:"\u210c",hfr:"\u{1d525}",HilbertSpace:"\u210b",hksearow:"\u2925",hkswarow:"\u2926",hoarr:"\u21ff",homtht:"\u223b",hookleftarrow:"\u21a9",hookrightarrow:"\u21aa",Hopf:"\u210d",hopf:"\u{1d559}",horbar:"\u2015",HorizontalLine:"\u2500",Hscr:"\u210b",hscr:"\u{1d4bd}",hslash:"\u210f",Hstrok:"\u0126",hstrok:"\u0127",HumpDownHump:"\u224e",HumpEqual:"\u224f",hybull:"\u2043",hyphen:"\u2010",Iacute:"\xcd",iacute:"\xed",ic:"\u2063",Icirc:"\xce",icirc:"\xee",Icy:"\u0418",icy:"\u0438",Idot:"\u0130",IEcy:"\u0415",iecy:"\u0435",iexcl:"\xa1",iff:"\u21d4",Ifr:"\u2111",ifr:"\u{1d526}",Igrave:"\xcc",igrave:"\xec",ii:"\u2148",iiiint:"\u2a0c",iiint:"\u222d",iinfin:"\u29dc",iiota:"\u2129",IJlig:"\u0132",ijlig:"\u0133",Im:"\u2111",Imacr:"\u012a",imacr:"\u012b",image:"\u2111",ImaginaryI:"\u2148",imagline:"\u2110",imagpart:"\u2111",imath:"\u0131",imof:"\u22b7",imped:"\u01b5",Implies:"\u21d2",in:"\u2208",incare:"\u2105",infin:"\u221e",infintie:"\u29dd",inodot:"\u0131",Int:"\u222c",int:"\u222b",intcal:"\u22ba",integers:"\u2124",Integral:"\u222b",intercal:"\u22ba",Intersection:"\u22c2",intlarhk:"\u2a17",intprod:"\u2a3c",InvisibleComma:"\u2063",InvisibleTimes:"\u2062",IOcy:"\u0401",iocy:"\u0451",Iogon:"\u012e",iogon:"\u012f",Iopf:"\u{1d540}",iopf:"\u{1d55a}",Iota:"\u0399",iota:"\u03b9",iprod:"\u2a3c",iquest:"\xbf",Iscr:"\u2110",iscr:"\u{1d4be}",isin:"\u2208",isindot:"\u22f5",isinE:"\u22f9",isins:"\u22f4",isinsv:"\u22f3",isinv:"\u2208",it:"\u2062",Itilde:"\u0128",itilde:"\u0129",Iukcy:"\u0406",iukcy:"\u0456",Iuml:"\xcf",iuml:"\xef",Jcirc:"\u0134",jcirc:"\u0135",Jcy:"\u0419",jcy:"\u0439",Jfr:"\u{1d50d}",jfr:"\u{1d527}",jmath:"\u0237",Jopf:"\u{1d541}",jopf:"\u{1d55b}",Jscr:"\u{1d4a5}",jscr:"\u{1d4bf}",Jsercy:"\u0408",jsercy:"\u0458",Jukcy:"\u0404",jukcy:"\u0454",Kappa:"\u039a",kappa:"\u03ba",kappav:"\u03f0",Kcedil:"\u0136",kcedil:"\u0137",Kcy:"\u041a",kcy:"\u043a",Kfr:"\u{1d50e}",kfr:"\u{1d528}",kgreen:"\u0138",KHcy:"\u0425",khcy:"\u0445",KJcy:"\u040c",kjcy:"\u045c",Kopf:"\u{1d542}",kopf:"\u{1d55c}",Kscr:"\u{1d4a6}",kscr:"\u{1d4c0}",lAarr:"\u21da",Lacute:"\u0139",lacute:"\u013a",laemptyv:"\u29b4",lagran:"\u2112",Lambda:"\u039b",lambda:"\u03bb",Lang:"\u27ea",lang:"\u27e8",langd:"\u2991",langle:"\u27e8",lap:"\u2a85",Laplacetrf:"\u2112",laquo:"\xab",Larr:"\u219e",lArr:"\u21d0",larr:"\u2190",larrb:"\u21e4",larrbfs:"\u291f",larrfs:"\u291d",larrhk:"\u21a9",larrlp:"\u21ab",larrpl:"\u2939",larrsim:"\u2973",larrtl:"\u21a2",lat:"\u2aab",lAtail:"\u291b",latail:"\u2919",late:"\u2aad",lates:"\u2aad\ufe00",lBarr:"\u290e",lbarr:"\u290c",lbbrk:"\u2772",lbrace:"{",lbrack:"[",lbrke:"\u298b",lbrksld:"\u298f",lbrkslu:"\u298d",Lcaron:"\u013d",lcaron:"\u013e",Lcedil:"\u013b",lcedil:"\u013c",lceil:"\u2308",lcub:"{",Lcy:"\u041b",lcy:"\u043b",ldca:"\u2936",ldquo:"\u201c",ldquor:"\u201e",ldrdhar:"\u2967",ldrushar:"\u294b",ldsh:"\u21b2",lE:"\u2266",le:"\u2264",LeftAngleBracket:"\u27e8",LeftArrow:"\u2190",Leftarrow:"\u21d0",leftarrow:"\u2190",LeftArrowBar:"\u21e4",LeftArrowRightArrow:"\u21c6",leftarrowtail:"\u21a2",LeftCeiling:"\u2308",LeftDoubleBracket:"\u27e6",LeftDownTeeVector:"\u2961",LeftDownVector:"\u21c3",LeftDownVectorBar:"\u2959",LeftFloor:"\u230a",leftharpoondown:"\u21bd",leftharpoonup:"\u21bc",leftleftarrows:"\u21c7",LeftRightArrow:"\u2194",Leftrightarrow:"\u21d4",leftrightarrow:"\u2194",leftrightarrows:"\u21c6",leftrightharpoons:"\u21cb",leftrightsquigarrow:"\u21ad",LeftRightVector:"\u294e",LeftTee:"\u22a3",LeftTeeArrow:"\u21a4",LeftTeeVector:"\u295a",leftthreetimes:"\u22cb",LeftTriangle:"\u22b2",LeftTriangleBar:"\u29cf",LeftTriangleEqual:"\u22b4",LeftUpDownVector:"\u2951",LeftUpTeeVector:"\u2960",LeftUpVector:"\u21bf",LeftUpVectorBar:"\u2958",LeftVector:"\u21bc",LeftVectorBar:"\u2952",lEg:"\u2a8b",leg:"\u22da",leq:"\u2264",leqq:"\u2266",leqslant:"\u2a7d",les:"\u2a7d",lescc:"\u2aa8",lesdot:"\u2a7f",lesdoto:"\u2a81",lesdotor:"\u2a83",lesg:"\u22da\ufe00",lesges:"\u2a93",lessapprox:"\u2a85",lessdot:"\u22d6",lesseqgtr:"\u22da",lesseqqgtr:"\u2a8b",LessEqualGreater:"\u22da",LessFullEqual:"\u2266",LessGreater:"\u2276",lessgtr:"\u2276",LessLess:"\u2aa1",lesssim:"\u2272",LessSlantEqual:"\u2a7d",LessTilde:"\u2272",lfisht:"\u297c",lfloor:"\u230a",Lfr:"\u{1d50f}",lfr:"\u{1d529}",lg:"\u2276",lgE:"\u2a91",lHar:"\u2962",lhard:"\u21bd",lharu:"\u21bc",lharul:"\u296a",lhblk:"\u2584",LJcy:"\u0409",ljcy:"\u0459",Ll:"\u22d8",ll:"\u226a",llarr:"\u21c7",llcorner:"\u231e",Lleftarrow:"\u21da",llhard:"\u296b",lltri:"\u25fa",Lmidot:"\u013f",lmidot:"\u0140",lmoust:"\u23b0",lmoustache:"\u23b0",lnap:"\u2a89",lnapprox:"\u2a89",lnE:"\u2268",lne:"\u2a87",lneq:"\u2a87",lneqq:"\u2268",lnsim:"\u22e6",loang:"\u27ec",loarr:"\u21fd",lobrk:"\u27e6",LongLeftArrow:"\u27f5",Longleftarrow:"\u27f8",longleftarrow:"\u27f5",LongLeftRightArrow:"\u27f7",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",LongRightArrow:"\u27f6",Longrightarrow:"\u27f9",longrightarrow:"\u27f6",looparrowleft:"\u21ab",looparrowright:"\u21ac",lopar:"\u2985",Lopf:"\u{1d543}",lopf:"\u{1d55d}",loplus:"\u2a2d",lotimes:"\u2a34",lowast:"\u2217",lowbar:"_",LowerLeftArrow:"\u2199",LowerRightArrow:"\u2198",loz:"\u25ca",lozenge:"\u25ca",lozf:"\u29eb",lpar:"(",lparlt:"\u2993",lrarr:"\u21c6",lrcorner:"\u231f",lrhar:"\u21cb",lrhard:"\u296d",lrm:"\u200e",lrtri:"\u22bf",lsaquo:"\u2039",Lscr:"\u2112",lscr:"\u{1d4c1}",Lsh:"\u21b0",lsh:"\u21b0",lsim:"\u2272",lsime:"\u2a8d",lsimg:"\u2a8f",lsqb:"[",lsquo:"\u2018",lsquor:"\u201a",Lstrok:"\u0141",lstrok:"\u0142",Lt:"\u226a",LT:"<",lt:"<",ltcc:"\u2aa6",ltcir:"\u2a79",ltdot:"\u22d6",lthree:"\u22cb",ltimes:"\u22c9",ltlarr:"\u2976",ltquest:"\u2a7b",ltri:"\u25c3",ltrie:"\u22b4",ltrif:"\u25c2",ltrPar:"\u2996",lurdshar:"\u294a",luruhar:"\u2966",lvertneqq:"\u2268\ufe00",lvnE:"\u2268\ufe00",macr:"\xaf",male:"\u2642",malt:"\u2720",maltese:"\u2720",Map:"\u2905",map:"\u21a6",mapsto:"\u21a6",mapstodown:"\u21a7",mapstoleft:"\u21a4",mapstoup:"\u21a5",marker:"\u25ae",mcomma:"\u2a29",Mcy:"\u041c",mcy:"\u043c",mdash:"\u2014",mDDot:"\u223a",measuredangle:"\u2221",MediumSpace:"\u205f",Mellintrf:"\u2133",Mfr:"\u{1d510}",mfr:"\u{1d52a}",mho:"\u2127",micro:"\xb5",mid:"\u2223",midast:"*",midcir:"\u2af0",middot:"\xb7",minus:"\u2212",minusb:"\u229f",minusd:"\u2238",minusdu:"\u2a2a",MinusPlus:"\u2213",mlcp:"\u2adb",mldr:"\u2026",mnplus:"\u2213",models:"\u22a7",Mopf:"\u{1d544}",mopf:"\u{1d55e}",mp:"\u2213",Mscr:"\u2133",mscr:"\u{1d4c2}",mstpos:"\u223e",Mu:"\u039c",mu:"\u03bc",multimap:"\u22b8",mumap:"\u22b8",nabla:"\u2207",Nacute:"\u0143",nacute:"\u0144",nang:"\u2220\u20d2",nap:"\u2249",napE:"\u2a70\u0338",napid:"\u224b\u0338",napos:"\u0149",napprox:"\u2249",natur:"\u266e",natural:"\u266e",naturals:"\u2115",nbsp:"\xa0",nbump:"\u224e\u0338",nbumpe:"\u224f\u0338",ncap:"\u2a43",Ncaron:"\u0147",ncaron:"\u0148",Ncedil:"\u0145",ncedil:"\u0146",ncong:"\u2247",ncongdot:"\u2a6d\u0338",ncup:"\u2a42",Ncy:"\u041d",ncy:"\u043d",ndash:"\u2013",ne:"\u2260",nearhk:"\u2924",neArr:"\u21d7",nearr:"\u2197",nearrow:"\u2197",nedot:"\u2250\u0338",NegativeMediumSpace:"\u200b",NegativeThickSpace:"\u200b",NegativeThinSpace:"\u200b",NegativeVeryThinSpace:"\u200b",nequiv:"\u2262",nesear:"\u2928",nesim:"\u2242\u0338",NestedGreaterGreater:"\u226b",NestedLessLess:"\u226a",NewLine:"\n",nexist:"\u2204",nexists:"\u2204",Nfr:"\u{1d511}",nfr:"\u{1d52b}",ngE:"\u2267\u0338",nge:"\u2271",ngeq:"\u2271",ngeqq:"\u2267\u0338",ngeqslant:"\u2a7e\u0338",nges:"\u2a7e\u0338",nGg:"\u22d9\u0338",ngsim:"\u2275",nGt:"\u226b\u20d2",ngt:"\u226f",ngtr:"\u226f",nGtv:"\u226b\u0338",nhArr:"\u21ce",nharr:"\u21ae",nhpar:"\u2af2",ni:"\u220b",nis:"\u22fc",nisd:"\u22fa",niv:"\u220b",NJcy:"\u040a",njcy:"\u045a",nlArr:"\u21cd",nlarr:"\u219a",nldr:"\u2025",nlE:"\u2266\u0338",nle:"\u2270",nLeftarrow:"\u21cd",nleftarrow:"\u219a",nLeftrightarrow:"\u21ce",nleftrightarrow:"\u21ae",nleq:"\u2270",nleqq:"\u2266\u0338",nleqslant:"\u2a7d\u0338",nles:"\u2a7d\u0338",nless:"\u226e",nLl:"\u22d8\u0338",nlsim:"\u2274",nLt:"\u226a\u20d2",nlt:"\u226e",nltri:"\u22ea",nltrie:"\u22ec",nLtv:"\u226a\u0338",nmid:"\u2224",NoBreak:"\u2060",NonBreakingSpace:"\xa0",Nopf:"\u2115",nopf:"\u{1d55f}",Not:"\u2aec",not:"\xac",NotCongruent:"\u2262",NotCupCap:"\u226d",NotDoubleVerticalBar:"\u2226",NotElement:"\u2209",NotEqual:"\u2260",NotEqualTilde:"\u2242\u0338",NotExists:"\u2204",NotGreater:"\u226f",NotGreaterEqual:"\u2271",NotGreaterFullEqual:"\u2267\u0338",NotGreaterGreater:"\u226b\u0338",NotGreaterLess:"\u2279",NotGreaterSlantEqual:"\u2a7e\u0338",NotGreaterTilde:"\u2275",NotHumpDownHump:"\u224e\u0338",NotHumpEqual:"\u224f\u0338",notin:"\u2209",notindot:"\u22f5\u0338",notinE:"\u22f9\u0338",notinva:"\u2209",notinvb:"\u22f7",notinvc:"\u22f6",NotLeftTriangle:"\u22ea",NotLeftTriangleBar:"\u29cf\u0338",NotLeftTriangleEqual:"\u22ec",NotLess:"\u226e",NotLessEqual:"\u2270",NotLessGreater:"\u2278",NotLessLess:"\u226a\u0338",NotLessSlantEqual:"\u2a7d\u0338",NotLessTilde:"\u2274",NotNestedGreaterGreater:"\u2aa2\u0338",NotNestedLessLess:"\u2aa1\u0338",notni:"\u220c",notniva:"\u220c",notnivb:"\u22fe",notnivc:"\u22fd",NotPrecedes:"\u2280",NotPrecedesEqual:"\u2aaf\u0338",NotPrecedesSlantEqual:"\u22e0",NotReverseElement:"\u220c",NotRightTriangle:"\u22eb",NotRightTriangleBar:"\u29d0\u0338",NotRightTriangleEqual:"\u22ed",NotSquareSubset:"\u228f\u0338",NotSquareSubsetEqual:"\u22e2",NotSquareSuperset:"\u2290\u0338",NotSquareSupersetEqual:"\u22e3",NotSubset:"\u2282\u20d2",NotSubsetEqual:"\u2288",NotSucceeds:"\u2281",NotSucceedsEqual:"\u2ab0\u0338",NotSucceedsSlantEqual:"\u22e1",NotSucceedsTilde:"\u227f\u0338",NotSuperset:"\u2283\u20d2",NotSupersetEqual:"\u2289",NotTilde:"\u2241",NotTildeEqual:"\u2244",NotTildeFullEqual:"\u2247",NotTildeTilde:"\u2249",NotVerticalBar:"\u2224",npar:"\u2226",nparallel:"\u2226",nparsl:"\u2afd\u20e5",npart:"\u2202\u0338",npolint:"\u2a14",npr:"\u2280",nprcue:"\u22e0",npre:"\u2aaf\u0338",nprec:"\u2280",npreceq:"\u2aaf\u0338",nrArr:"\u21cf",nrarr:"\u219b",nrarrc:"\u2933\u0338",nrarrw:"\u219d\u0338",nRightarrow:"\u21cf",nrightarrow:"\u219b",nrtri:"\u22eb",nrtrie:"\u22ed",nsc:"\u2281",nsccue:"\u22e1",nsce:"\u2ab0\u0338",Nscr:"\u{1d4a9}",nscr:"\u{1d4c3}",nshortmid:"\u2224",nshortparallel:"\u2226",nsim:"\u2241",nsime:"\u2244",nsimeq:"\u2244",nsmid:"\u2224",nspar:"\u2226",nsqsube:"\u22e2",nsqsupe:"\u22e3",nsub:"\u2284",nsubE:"\u2ac5\u0338",nsube:"\u2288",nsubset:"\u2282\u20d2",nsubseteq:"\u2288",nsubseteqq:"\u2ac5\u0338",nsucc:"\u2281",nsucceq:"\u2ab0\u0338",nsup:"\u2285",nsupE:"\u2ac6\u0338",nsupe:"\u2289",nsupset:"\u2283\u20d2",nsupseteq:"\u2289",nsupseteqq:"\u2ac6\u0338",ntgl:"\u2279",Ntilde:"\xd1",ntilde:"\xf1",ntlg:"\u2278",ntriangleleft:"\u22ea",ntrianglelefteq:"\u22ec",ntriangleright:"\u22eb",ntrianglerighteq:"\u22ed",Nu:"\u039d",nu:"\u03bd",num:"#",numero:"\u2116",numsp:"\u2007",nvap:"\u224d\u20d2",nVDash:"\u22af",nVdash:"\u22ae",nvDash:"\u22ad",nvdash:"\u22ac",nvge:"\u2265\u20d2",nvgt:">\u20d2",nvHarr:"\u2904",nvinfin:"\u29de",nvlArr:"\u2902",nvle:"\u2264\u20d2",nvlt:"<\u20d2",nvltrie:"\u22b4\u20d2",nvrArr:"\u2903",nvrtrie:"\u22b5\u20d2",nvsim:"\u223c\u20d2",nwarhk:"\u2923",nwArr:"\u21d6",nwarr:"\u2196",nwarrow:"\u2196",nwnear:"\u2927",Oacute:"\xd3",oacute:"\xf3",oast:"\u229b",ocir:"\u229a",Ocirc:"\xd4",ocirc:"\xf4",Ocy:"\u041e",ocy:"\u043e",odash:"\u229d",Odblac:"\u0150",odblac:"\u0151",odiv:"\u2a38",odot:"\u2299",odsold:"\u29bc",OElig:"\u0152",oelig:"\u0153",ofcir:"\u29bf",Ofr:"\u{1d512}",ofr:"\u{1d52c}",ogon:"\u02db",Ograve:"\xd2",ograve:"\xf2",ogt:"\u29c1",ohbar:"\u29b5",ohm:"\u03a9",oint:"\u222e",olarr:"\u21ba",olcir:"\u29be",olcross:"\u29bb",oline:"\u203e",olt:"\u29c0",Omacr:"\u014c",omacr:"\u014d",Omega:"\u03a9",omega:"\u03c9",Omicron:"\u039f",omicron:"\u03bf",omid:"\u29b6",ominus:"\u2296",Oopf:"\u{1d546}",oopf:"\u{1d560}",opar:"\u29b7",OpenCurlyDoubleQuote:"\u201c",OpenCurlyQuote:"\u2018",operp:"\u29b9",oplus:"\u2295",Or:"\u2a54",or:"\u2228",orarr:"\u21bb",ord:"\u2a5d",order:"\u2134",orderof:"\u2134",ordf:"\xaa",ordm:"\xba",origof:"\u22b6",oror:"\u2a56",orslope:"\u2a57",orv:"\u2a5b",oS:"\u24c8",Oscr:"\u{1d4aa}",oscr:"\u2134",Oslash:"\xd8",oslash:"\xf8",osol:"\u2298",Otilde:"\xd5",otilde:"\xf5",Otimes:"\u2a37",otimes:"\u2297",otimesas:"\u2a36",Ouml:"\xd6",ouml:"\xf6",ovbar:"\u233d",OverBar:"\u203e",OverBrace:"\u23de",OverBracket:"\u23b4",OverParenthesis:"\u23dc",par:"\u2225",para:"\xb6",parallel:"\u2225",parsim:"\u2af3",parsl:"\u2afd",part:"\u2202",PartialD:"\u2202",Pcy:"\u041f",pcy:"\u043f",percnt:"%",period:".",permil:"\u2030",perp:"\u22a5",pertenk:"\u2031",Pfr:"\u{1d513}",pfr:"\u{1d52d}",Phi:"\u03a6",phi:"\u03c6",phiv:"\u03d5",phmmat:"\u2133",phone:"\u260e",Pi:"\u03a0",pi:"\u03c0",pitchfork:"\u22d4",piv:"\u03d6",planck:"\u210f",planckh:"\u210e",plankv:"\u210f",plus:"+",plusacir:"\u2a23",plusb:"\u229e",pluscir:"\u2a22",plusdo:"\u2214",plusdu:"\u2a25",pluse:"\u2a72",PlusMinus:"\xb1",plusmn:"\xb1",plussim:"\u2a26",plustwo:"\u2a27",pm:"\xb1",Poincareplane:"\u210c",pointint:"\u2a15",Popf:"\u2119",popf:"\u{1d561}",pound:"\xa3",Pr:"\u2abb",pr:"\u227a",prap:"\u2ab7",prcue:"\u227c",prE:"\u2ab3",pre:"\u2aaf",prec:"\u227a",precapprox:"\u2ab7",preccurlyeq:"\u227c",Precedes:"\u227a",PrecedesEqual:"\u2aaf",PrecedesSlantEqual:"\u227c",PrecedesTilde:"\u227e",preceq:"\u2aaf",precnapprox:"\u2ab9",precneqq:"\u2ab5",precnsim:"\u22e8",precsim:"\u227e",Prime:"\u2033",prime:"\u2032",primes:"\u2119",prnap:"\u2ab9",prnE:"\u2ab5",prnsim:"\u22e8",prod:"\u220f",Product:"\u220f",profalar:"\u232e",profline:"\u2312",profsurf:"\u2313",prop:"\u221d",Proportion:"\u2237",Proportional:"\u221d",propto:"\u221d",prsim:"\u227e",prurel:"\u22b0",Pscr:"\u{1d4ab}",pscr:"\u{1d4c5}",Psi:"\u03a8",psi:"\u03c8",puncsp:"\u2008",Qfr:"\u{1d514}",qfr:"\u{1d52e}",qint:"\u2a0c",Qopf:"\u211a",qopf:"\u{1d562}",qprime:"\u2057",Qscr:"\u{1d4ac}",qscr:"\u{1d4c6}",quaternions:"\u210d",quatint:"\u2a16",quest:"?",questeq:"\u225f",QUOT:'"',quot:'"',rAarr:"\u21db",race:"\u223d\u0331",Racute:"\u0154",racute:"\u0155",radic:"\u221a",raemptyv:"\u29b3",Rang:"\u27eb",rang:"\u27e9",rangd:"\u2992",range:"\u29a5",rangle:"\u27e9",raquo:"\xbb",Rarr:"\u21a0",rArr:"\u21d2",rarr:"\u2192",rarrap:"\u2975",rarrb:"\u21e5",rarrbfs:"\u2920",rarrc:"\u2933",rarrfs:"\u291e",rarrhk:"\u21aa",rarrlp:"\u21ac",rarrpl:"\u2945",rarrsim:"\u2974",Rarrtl:"\u2916",rarrtl:"\u21a3",rarrw:"\u219d",rAtail:"\u291c",ratail:"\u291a",ratio:"\u2236",rationals:"\u211a",RBarr:"\u2910",rBarr:"\u290f",rbarr:"\u290d",rbbrk:"\u2773",rbrace:"}",rbrack:"]",rbrke:"\u298c",rbrksld:"\u298e",rbrkslu:"\u2990",Rcaron:"\u0158",rcaron:"\u0159",Rcedil:"\u0156",rcedil:"\u0157",rceil:"\u2309",rcub:"}",Rcy:"\u0420",rcy:"\u0440",rdca:"\u2937",rdldhar:"\u2969",rdquo:"\u201d",rdquor:"\u201d",rdsh:"\u21b3",Re:"\u211c",real:"\u211c",realine:"\u211b",realpart:"\u211c",reals:"\u211d",rect:"\u25ad",REG:"\xae",reg:"\xae",ReverseElement:"\u220b",ReverseEquilibrium:"\u21cb",ReverseUpEquilibrium:"\u296f",rfisht:"\u297d",rfloor:"\u230b",Rfr:"\u211c",rfr:"\u{1d52f}",rHar:"\u2964",rhard:"\u21c1",rharu:"\u21c0",rharul:"\u296c",Rho:"\u03a1",rho:"\u03c1",rhov:"\u03f1",RightAngleBracket:"\u27e9",RightArrow:"\u2192",Rightarrow:"\u21d2",rightarrow:"\u2192",RightArrowBar:"\u21e5",RightArrowLeftArrow:"\u21c4",rightarrowtail:"\u21a3",RightCeiling:"\u2309",RightDoubleBracket:"\u27e7",RightDownTeeVector:"\u295d",RightDownVector:"\u21c2",RightDownVectorBar:"\u2955",RightFloor:"\u230b",rightharpoondown:"\u21c1",rightharpoonup:"\u21c0",rightleftarrows:"\u21c4",rightleftharpoons:"\u21cc",rightrightarrows:"\u21c9",rightsquigarrow:"\u219d",RightTee:"\u22a2",RightTeeArrow:"\u21a6",RightTeeVector:"\u295b",rightthreetimes:"\u22cc",RightTriangle:"\u22b3",RightTriangleBar:"\u29d0",RightTriangleEqual:"\u22b5",RightUpDownVector:"\u294f",RightUpTeeVector:"\u295c",RightUpVector:"\u21be",RightUpVectorBar:"\u2954",RightVector:"\u21c0",RightVectorBar:"\u2953",ring:"\u02da",risingdotseq:"\u2253",rlarr:"\u21c4",rlhar:"\u21cc",rlm:"\u200f",rmoust:"\u23b1",rmoustache:"\u23b1",rnmid:"\u2aee",roang:"\u27ed",roarr:"\u21fe",robrk:"\u27e7",ropar:"\u2986",Ropf:"\u211d",ropf:"\u{1d563}",roplus:"\u2a2e",rotimes:"\u2a35",RoundImplies:"\u2970",rpar:")",rpargt:"\u2994",rppolint:"\u2a12",rrarr:"\u21c9",Rrightarrow:"\u21db",rsaquo:"\u203a",Rscr:"\u211b",rscr:"\u{1d4c7}",Rsh:"\u21b1",rsh:"\u21b1",rsqb:"]",rsquo:"\u2019",rsquor:"\u2019",rthree:"\u22cc",rtimes:"\u22ca",rtri:"\u25b9",rtrie:"\u22b5",rtrif:"\u25b8",rtriltri:"\u29ce",RuleDelayed:"\u29f4",ruluhar:"\u2968",rx:"\u211e",Sacute:"\u015a",sacute:"\u015b",sbquo:"\u201a",Sc:"\u2abc",sc:"\u227b",scap:"\u2ab8",Scaron:"\u0160",scaron:"\u0161",sccue:"\u227d",scE:"\u2ab4",sce:"\u2ab0",Scedil:"\u015e",scedil:"\u015f",Scirc:"\u015c",scirc:"\u015d",scnap:"\u2aba",scnE:"\u2ab6",scnsim:"\u22e9",scpolint:"\u2a13",scsim:"\u227f",Scy:"\u0421",scy:"\u0441",sdot:"\u22c5",sdotb:"\u22a1",sdote:"\u2a66",searhk:"\u2925",seArr:"\u21d8",searr:"\u2198",searrow:"\u2198",sect:"\xa7",semi:";",seswar:"\u2929",setminus:"\u2216",setmn:"\u2216",sext:"\u2736",Sfr:"\u{1d516}",sfr:"\u{1d530}",sfrown:"\u2322",sharp:"\u266f",SHCHcy:"\u0429",shchcy:"\u0449",SHcy:"\u0428",shcy:"\u0448",ShortDownArrow:"\u2193",ShortLeftArrow:"\u2190",shortmid:"\u2223",shortparallel:"\u2225",ShortRightArrow:"\u2192",ShortUpArrow:"\u2191",shy:"\xad",Sigma:"\u03a3",sigma:"\u03c3",sigmaf:"\u03c2",sigmav:"\u03c2",sim:"\u223c",simdot:"\u2a6a",sime:"\u2243",simeq:"\u2243",simg:"\u2a9e",simgE:"\u2aa0",siml:"\u2a9d",simlE:"\u2a9f",simne:"\u2246",simplus:"\u2a24",simrarr:"\u2972",slarr:"\u2190",SmallCircle:"\u2218",smallsetminus:"\u2216",smashp:"\u2a33",smeparsl:"\u29e4",smid:"\u2223",smile:"\u2323",smt:"\u2aaa",smte:"\u2aac",smtes:"\u2aac\ufe00",SOFTcy:"\u042c",softcy:"\u044c",sol:"/",solb:"\u29c4",solbar:"\u233f",Sopf:"\u{1d54a}",sopf:"\u{1d564}",spades:"\u2660",spadesuit:"\u2660",spar:"\u2225",sqcap:"\u2293",sqcaps:"\u2293\ufe00",sqcup:"\u2294",sqcups:"\u2294\ufe00",Sqrt:"\u221a",sqsub:"\u228f",sqsube:"\u2291",sqsubset:"\u228f",sqsubseteq:"\u2291",sqsup:"\u2290",sqsupe:"\u2292",sqsupset:"\u2290",sqsupseteq:"\u2292",squ:"\u25a1",Square:"\u25a1",square:"\u25a1",SquareIntersection:"\u2293",SquareSubset:"\u228f",SquareSubsetEqual:"\u2291",SquareSuperset:"\u2290",SquareSupersetEqual:"\u2292",SquareUnion:"\u2294",squarf:"\u25aa",squf:"\u25aa",srarr:"\u2192",Sscr:"\u{1d4ae}",sscr:"\u{1d4c8}",ssetmn:"\u2216",ssmile:"\u2323",sstarf:"\u22c6",Star:"\u22c6",star:"\u2606",starf:"\u2605",straightepsilon:"\u03f5",straightphi:"\u03d5",strns:"\xaf",Sub:"\u22d0",sub:"\u2282",subdot:"\u2abd",subE:"\u2ac5",sube:"\u2286",subedot:"\u2ac3",submult:"\u2ac1",subnE:"\u2acb",subne:"\u228a",subplus:"\u2abf",subrarr:"\u2979",Subset:"\u22d0",subset:"\u2282",subseteq:"\u2286",subseteqq:"\u2ac5",SubsetEqual:"\u2286",subsetneq:"\u228a",subsetneqq:"\u2acb",subsim:"\u2ac7",subsub:"\u2ad5",subsup:"\u2ad3",succ:"\u227b",succapprox:"\u2ab8",succcurlyeq:"\u227d",Succeeds:"\u227b",SucceedsEqual:"\u2ab0",SucceedsSlantEqual:"\u227d",SucceedsTilde:"\u227f",succeq:"\u2ab0",succnapprox:"\u2aba",succneqq:"\u2ab6",succnsim:"\u22e9",succsim:"\u227f",SuchThat:"\u220b",Sum:"\u2211",sum:"\u2211",sung:"\u266a",Sup:"\u22d1",sup:"\u2283",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",supdot:"\u2abe",supdsub:"\u2ad8",supE:"\u2ac6",supe:"\u2287",supedot:"\u2ac4",Superset:"\u2283",SupersetEqual:"\u2287",suphsol:"\u27c9",suphsub:"\u2ad7",suplarr:"\u297b",supmult:"\u2ac2",supnE:"\u2acc",supne:"\u228b",supplus:"\u2ac0",Supset:"\u22d1",supset:"\u2283",supseteq:"\u2287",supseteqq:"\u2ac6",supsetneq:"\u228b",supsetneqq:"\u2acc",supsim:"\u2ac8",supsub:"\u2ad4",supsup:"\u2ad6",swarhk:"\u2926",swArr:"\u21d9",swarr:"\u2199",swarrow:"\u2199",swnwar:"\u292a",szlig:"\xdf",Tab:"\t",target:"\u2316",Tau:"\u03a4",tau:"\u03c4",tbrk:"\u23b4",Tcaron:"\u0164",tcaron:"\u0165",Tcedil:"\u0162",tcedil:"\u0163",Tcy:"\u0422",tcy:"\u0442",tdot:"\u20db",telrec:"\u2315",Tfr:"\u{1d517}",tfr:"\u{1d531}",there4:"\u2234",Therefore:"\u2234",therefore:"\u2234",Theta:"\u0398",theta:"\u03b8",thetasym:"\u03d1",thetav:"\u03d1",thickapprox:"\u2248",thicksim:"\u223c",ThickSpace:"\u205f\u200a",thinsp:"\u2009",ThinSpace:"\u2009",thkap:"\u2248",thksim:"\u223c",THORN:"\xde",thorn:"\xfe",Tilde:"\u223c",tilde:"\u02dc",TildeEqual:"\u2243",TildeFullEqual:"\u2245",TildeTilde:"\u2248",times:"\xd7",timesb:"\u22a0",timesbar:"\u2a31",timesd:"\u2a30",tint:"\u222d",toea:"\u2928",top:"\u22a4",topbot:"\u2336",topcir:"\u2af1",Topf:"\u{1d54b}",topf:"\u{1d565}",topfork:"\u2ada",tosa:"\u2929",tprime:"\u2034",TRADE:"\u2122",trade:"\u2122",triangle:"\u25b5",triangledown:"\u25bf",triangleleft:"\u25c3",trianglelefteq:"\u22b4",triangleq:"\u225c",triangleright:"\u25b9",trianglerighteq:"\u22b5",tridot:"\u25ec",trie:"\u225c",triminus:"\u2a3a",TripleDot:"\u20db",triplus:"\u2a39",trisb:"\u29cd",tritime:"\u2a3b",trpezium:"\u23e2",Tscr:"\u{1d4af}",tscr:"\u{1d4c9}",TScy:"\u0426",tscy:"\u0446",TSHcy:"\u040b",tshcy:"\u045b",Tstrok:"\u0166",tstrok:"\u0167",twixt:"\u226c",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",Uacute:"\xda",uacute:"\xfa",Uarr:"\u219f",uArr:"\u21d1",uarr:"\u2191",Uarrocir:"\u2949",Ubrcy:"\u040e",ubrcy:"\u045e",Ubreve:"\u016c",ubreve:"\u016d",Ucirc:"\xdb",ucirc:"\xfb",Ucy:"\u0423",ucy:"\u0443",udarr:"\u21c5",Udblac:"\u0170",udblac:"\u0171",udhar:"\u296e",ufisht:"\u297e",Ufr:"\u{1d518}",ufr:"\u{1d532}",Ugrave:"\xd9",ugrave:"\xf9",uHar:"\u2963",uharl:"\u21bf",uharr:"\u21be",uhblk:"\u2580",ulcorn:"\u231c",ulcorner:"\u231c",ulcrop:"\u230f",ultri:"\u25f8",Umacr:"\u016a",umacr:"\u016b",uml:"\xa8",UnderBar:"_",UnderBrace:"\u23df",UnderBracket:"\u23b5",UnderParenthesis:"\u23dd",Union:"\u22c3",UnionPlus:"\u228e",Uogon:"\u0172",uogon:"\u0173",Uopf:"\u{1d54c}",uopf:"\u{1d566}",UpArrow:"\u2191",Uparrow:"\u21d1",uparrow:"\u2191",UpArrowBar:"\u2912",UpArrowDownArrow:"\u21c5",UpDownArrow:"\u2195",Updownarrow:"\u21d5",updownarrow:"\u2195",UpEquilibrium:"\u296e",upharpoonleft:"\u21bf",upharpoonright:"\u21be",uplus:"\u228e",UpperLeftArrow:"\u2196",UpperRightArrow:"\u2197",Upsi:"\u03d2",upsi:"\u03c5",upsih:"\u03d2",Upsilon:"\u03a5",upsilon:"\u03c5",UpTee:"\u22a5",UpTeeArrow:"\u21a5",upuparrows:"\u21c8",urcorn:"\u231d",urcorner:"\u231d",urcrop:"\u230e",Uring:"\u016e",uring:"\u016f",urtri:"\u25f9",Uscr:"\u{1d4b0}",uscr:"\u{1d4ca}",utdot:"\u22f0",Utilde:"\u0168",utilde:"\u0169",utri:"\u25b5",utrif:"\u25b4",uuarr:"\u21c8",Uuml:"\xdc",uuml:"\xfc",uwangle:"\u29a7",vangrt:"\u299c",varepsilon:"\u03f5",varkappa:"\u03f0",varnothing:"\u2205",varphi:"\u03d5",varpi:"\u03d6",varpropto:"\u221d",vArr:"\u21d5",varr:"\u2195",varrho:"\u03f1",varsigma:"\u03c2",varsubsetneq:"\u228a\ufe00",varsubsetneqq:"\u2acb\ufe00",varsupsetneq:"\u228b\ufe00",varsupsetneqq:"\u2acc\ufe00",vartheta:"\u03d1",vartriangleleft:"\u22b2",vartriangleright:"\u22b3",Vbar:"\u2aeb",vBar:"\u2ae8",vBarv:"\u2ae9",Vcy:"\u0412",vcy:"\u0432",VDash:"\u22ab",Vdash:"\u22a9",vDash:"\u22a8",vdash:"\u22a2",Vdashl:"\u2ae6",Vee:"\u22c1",vee:"\u2228",veebar:"\u22bb",veeeq:"\u225a",vellip:"\u22ee",Verbar:"\u2016",verbar:"|",Vert:"\u2016",vert:"|",VerticalBar:"\u2223",VerticalLine:"|",VerticalSeparator:"\u2758",VerticalTilde:"\u2240",VeryThinSpace:"\u200a",Vfr:"\u{1d519}",vfr:"\u{1d533}",vltri:"\u22b2",vnsub:"\u2282\u20d2",vnsup:"\u2283\u20d2",Vopf:"\u{1d54d}",vopf:"\u{1d567}",vprop:"\u221d",vrtri:"\u22b3",Vscr:"\u{1d4b1}",vscr:"\u{1d4cb}",vsubnE:"\u2acb\ufe00",vsubne:"\u228a\ufe00",vsupnE:"\u2acc\ufe00",vsupne:"\u228b\ufe00",Vvdash:"\u22aa",vzigzag:"\u299a",Wcirc:"\u0174",wcirc:"\u0175",wedbar:"\u2a5f",Wedge:"\u22c0",wedge:"\u2227",wedgeq:"\u2259",weierp:"\u2118",Wfr:"\u{1d51a}",wfr:"\u{1d534}",Wopf:"\u{1d54e}",wopf:"\u{1d568}",wp:"\u2118",wr:"\u2240",wreath:"\u2240",Wscr:"\u{1d4b2}",wscr:"\u{1d4cc}",xcap:"\u22c2",xcirc:"\u25ef",xcup:"\u22c3",xdtri:"\u25bd",Xfr:"\u{1d51b}",xfr:"\u{1d535}",xhArr:"\u27fa",xharr:"\u27f7",Xi:"\u039e",xi:"\u03be",xlArr:"\u27f8",xlarr:"\u27f5",xmap:"\u27fc",xnis:"\u22fb",xodot:"\u2a00",Xopf:"\u{1d54f}",xopf:"\u{1d569}",xoplus:"\u2a01",xotime:"\u2a02",xrArr:"\u27f9",xrarr:"\u27f6",Xscr:"\u{1d4b3}",xscr:"\u{1d4cd}",xsqcup:"\u2a06",xuplus:"\u2a04",xutri:"\u25b3",xvee:"\u22c1",xwedge:"\u22c0",Yacute:"\xdd",yacute:"\xfd",YAcy:"\u042f",yacy:"\u044f",Ycirc:"\u0176",ycirc:"\u0177",Ycy:"\u042b",ycy:"\u044b",yen:"\xa5",Yfr:"\u{1d51c}",yfr:"\u{1d536}",YIcy:"\u0407",yicy:"\u0457",Yopf:"\u{1d550}",yopf:"\u{1d56a}",Yscr:"\u{1d4b4}",yscr:"\u{1d4ce}",YUcy:"\u042e",yucy:"\u044e",Yuml:"\u0178",yuml:"\xff",Zacute:"\u0179",zacute:"\u017a",Zcaron:"\u017d",zcaron:"\u017e",Zcy:"\u0417",zcy:"\u0437",Zdot:"\u017b",zdot:"\u017c",zeetrf:"\u2128",ZeroWidthSpace:"\u200b",Zeta:"\u0396",zeta:"\u03b6",Zfr:"\u2128",zfr:"\u{1d537}",ZHcy:"\u0416",zhcy:"\u0436",zigrarr:"\u21dd",Zopf:"\u2124",zopf:"\u{1d56b}",Zscr:"\u{1d4b5}",zscr:"\u{1d4cf}",zwj:"\u200d",zwnj:"\u200c"}),e.entityMap=e.HTML_ENTITIES},238:t=>{function e(t){return Promise.resolve().then((()=>{var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}))}e.keys=()=>[],e.resolve=e,e.id=238,t.exports=e},268:(t,e,n)=>{"use strict";var r,o;n.d(e,{Al:()=>c,Gb:()=>a,Oe:()=>h});var i=n(546),s=n(999);const a=r||(r=n.t(i,2)),c=(new a.DOMImplementation).createDocument("",""),l=(null===(o||(o=n.t(s,2)))||void 0===(o||(o=n.t(s,2)))?void 0:s.install)||window.wgxpath.install,u=function(){const t={document:{},XPathResult:{}};return l(t),t.document.XPathResult=t.XPathResult,t.document}(),h={currentDocument:c,evaluate:u.evaluate,result:u.XPathResult,createNSResolver:u.createNSResolver}},546:(t,e,n)=>{"use strict";var r=n(870);e.assign=r.assign,e.hasDefaultHTMLNamespace=r.hasDefaultHTMLNamespace,e.isHTMLMimeType=r.isHTMLMimeType,e.isValidMimeType=r.isValidMimeType,e.MIME_TYPE=r.MIME_TYPE,e.NAMESPACE=r.NAMESPACE;var o=n(767);e.DOMException=o.DOMException,e.DOMExceptionName=o.DOMExceptionName,e.ExceptionCode=o.ExceptionCode,e.ParseError=o.ParseError;var i=n(786);e.Attr=i.Attr,e.CDATASection=i.CDATASection,e.CharacterData=i.CharacterData,e.Comment=i.Comment,e.Document=i.Document,e.DocumentFragment=i.DocumentFragment,e.DocumentType=i.DocumentType,e.DOMImplementation=i.DOMImplementation,e.Element=i.Element,e.Entity=i.Entity,e.EntityReference=i.EntityReference,e.LiveNodeList=i.LiveNodeList,e.NamedNodeMap=i.NamedNodeMap,e.Node=i.Node,e.NodeList=i.NodeList,e.Notation=i.Notation,e.ProcessingInstruction=i.ProcessingInstruction,e.Text=i.Text,e.XMLSerializer=i.XMLSerializer;var s=n(576);e.DOMParser=s.DOMParser,e.normalizeLineEndings=s.normalizeLineEndings,e.onErrorStopParsing=s.onErrorStopParsing,e.onWarningStopParsing=s.onWarningStopParsing},576:(t,e,n)=>{"use strict";var r=n(870),o=n(786),i=n(767),s=n(159),a=n(802),c=o.DOMImplementation,l=r.hasDefaultHTMLNamespace,u=r.isHTMLMimeType,h=r.isValidMimeType,d=r.MIME_TYPE,f=r.NAMESPACE,p=i.ParseError,m=a.XMLReader;function g(t){return t.replace(/\r[\n\u0085]/g,"\n").replace(/[\r\u0085\u2028\u2029]/g,"\n")}function E(t){if(void 0===(t=t||{}).locator&&(t.locator=!0),this.assign=t.assign||r.assign,this.domHandler=t.domHandler||N,this.onError=t.onError||t.errorHandler,t.errorHandler&&"function"!=typeof t.errorHandler)throw new TypeError("errorHandler object is no longer supported, switch to onError!");t.errorHandler&&t.errorHandler("warning","The `errorHandler` option has been deprecated, use `onError` instead!",this),this.normalizeLineEndings=t.normalizeLineEndings||g,this.locator=!!t.locator,this.xmlns=this.assign(Object.create(null),t.xmlns)}function N(t){var e=t||{};this.mimeType=e.mimeType||d.XML_APPLICATION,this.defaultNamespace=e.defaultNamespace||null,this.cdata=!1,this.currentElement=void 0,this.doc=void 0,this.locator=void 0,this.onError=e.onError}function S(t,e){e.lineNumber=t.lineNumber,e.columnNumber=t.columnNumber}function T(t,e,n){return"string"==typeof t?t.substr(e,n):t.length>=e+n||e?new java.lang.String(t,e,n)+"":t}function I(t,e){t.currentElement?t.currentElement.appendChild(e):t.doc.appendChild(e)}E.prototype.parseFromString=function(t,e){if(!h(e))throw new TypeError('DOMParser.parseFromString: the provided mimeType "'+e+'" is not valid.');var n=this.assign(Object.create(null),this.xmlns),o=s.XML_ENTITIES,i=n[""]||null;l(e)?(o=s.HTML_ENTITIES,i=f.HTML):e===d.XML_SVG_IMAGE&&(i=f.SVG),n[""]=i,n.xml=n.xml||f.XML;var a=new this.domHandler({mimeType:e,defaultNamespace:i,onError:this.onError}),c=this.locator?{}:void 0;this.locator&&a.setDocumentLocator(c);var u=new m;return u.errorHandler=a,u.domBuilder=a,!r.isHTMLMimeType(e)&&"string"!=typeof t&&u.errorHandler.fatalError("source is not a string"),u.parse(this.normalizeLineEndings(String(t)),n,o),a.doc.documentElement||u.errorHandler.fatalError("missing root element"),a.doc},N.prototype={startDocument:function(){var t=new c;this.doc=u(this.mimeType)?t.createHTMLDocument(!1):t.createDocument(this.defaultNamespace,"")},startElement:function(t,e,n,r){var o=this.doc,i=o.createElementNS(t,n||e),s=r.length;I(this,i),this.currentElement=i,this.locator&&S(this.locator,i);for(var a=0;a<s;a++){t=r.getURI(a);var c=r.getValue(a),l=(n=r.getQName(a),o.createAttributeNS(t,n));this.locator&&S(r.getLocator(a),l),l.value=l.nodeValue=c,i.setAttributeNode(l)}},endElement:function(t,e,n){this.currentElement=this.currentElement.parentNode},startPrefixMapping:function(t,e){},endPrefixMapping:function(t){},processingInstruction:function(t,e){var n=this.doc.createProcessingInstruction(t,e);this.locator&&S(this.locator,n),I(this,n)},ignorableWhitespace:function(t,e,n){},characters:function(t,e,n){if(t=T.apply(this,arguments)){if(this.cdata)var r=this.doc.createCDATASection(t);else r=this.doc.createTextNode(t);this.currentElement?this.currentElement.appendChild(r):/^\s*$/.test(t)&&this.doc.appendChild(r),this.locator&&S(this.locator,r)}},skippedEntity:function(t){},endDocument:function(){this.doc.normalize()},setDocumentLocator:function(t){t&&(t.lineNumber=0),this.locator=t},comment:function(t,e,n){t=T.apply(this,arguments);var r=this.doc.createComment(t);this.locator&&S(this.locator,r),I(this,r)},startCDATA:function(){this.cdata=!0},endCDATA:function(){this.cdata=!1},startDTD:function(t,e,n,r){var o=this.doc.implementation;if(o&&o.createDocumentType){var i=o.createDocumentType(t,e,n,r);this.locator&&S(this.locator,i),I(this,i),this.doc.doctype=i}},reportError:function(t,e){if("function"==typeof this.onError)try{this.onError(t,e,this)}catch(n){throw new p("Reporting "+t+' "'+e+'" caused '+n,this.locator)}else console.error("[xmldom "+t+"]\t"+e,function(t){if(t)return"\n@#[line:"+t.lineNumber+",col:"+t.columnNumber+"]"}(this.locator))},warning:function(t){this.reportError("warning",t)},error:function(t){this.reportError("error",t)},fatalError:function(t){throw this.reportError("fatalError",t),new p(t,this.locator)}},"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g,(function(t){N.prototype[t]=function(){return null}})),e.__DOMHandler=N,e.DOMParser=E,e.normalizeLineEndings=g,e.onErrorStopParsing=function(t){if("error"===t)throw"onErrorStopParsing"},e.onWarningStopParsing=function(){throw"onWarningStopParsing"}},581:(t,e,n)=>{"use strict";n.r(e),n.d(e,{engineReady:()=>Ju,engineSetup:()=>zu,exit:()=>ph,file:()=>uh,localeLoader:()=>Zu,localePath:()=>bh,move:()=>fh,number:()=>ih,numericOrdinal:()=>ah,ordinal:()=>sh,processFile:()=>hh,resetEngine:()=>Qu,setupEngine:()=>Ku,toDescription:()=>rh,toEnriched:()=>oh,toJson:()=>nh,toSemantic:()=>eh,toSpeech:()=>th,toSpeechStructure:()=>mh,variables:()=>Ah,version:()=>Wu,vulgar:()=>ch,walk:()=>dh,workerLocalePreferences:()=>Sh,workerNextRules:()=>Eh,workerNextStyle:()=>Nh,workerRelevantPreferences:()=>Th,workerSpeech:()=>gh});var r,o,i,s={};n.r(s),n.d(s,{NodeType:()=>x,cloneNode:()=>K,createElement:()=>G,createElementNS:()=>V,createTextNode:()=>H,formatXml:()=>j,parseInput:()=>P,querySelectorAll:()=>Y,querySelectorAllByAttr:()=>$,querySelectorAllByAttrValue:()=>X,replaceNode:()=>B,serializeXml:()=>z,tagName:()=>W,toArray:()=>D}),function(t){t.DOMAIN="domain",t.STYLE="style",t.LOCALE="locale",t.TOPIC="topic",t.MODALITY="modality"}(r||(r={}));class a{static createProp(...t){const e=c.DEFAULT_ORDER,n={};for(let r=0,o=t.length,i=e.length;r<o&&r<i;r++)n[e[r]]=t[r];return new a(n)}constructor(t,e=Object.keys(t)){this.properties=t,this.order=e}getProperties(){return this.properties}getOrder(){return this.order}getAxes(){return this.order}getProperty(t){return this.properties[t]}updateProperties(t){this.properties=t}allProperties(){const t=[];return this.order.forEach((e=>t.push(this.getProperty(e).slice()))),t}toString(){const t=[];return this.order.forEach((e=>t.push(e+": "+this.getProperty(e).toString()))),t.join("\n")}}class c extends a{static createCstr(...t){const e=c.DEFAULT_ORDER,n={};for(let r=0,o=t.length,i=e.length;r<o&&r<i;r++)n[e[r]]=t[r];return new c(n)}static defaultCstr(){return c.createCstr.apply(null,c.DEFAULT_ORDER.map((function(t){return c.DEFAULT_VALUES[t]})))}static validOrder(t){const e=c.DEFAULT_ORDER.slice();return t.every((t=>{const n=e.indexOf(t);return-1!==n&&e.splice(n,1)}))}constructor(t,e){const n={};for(const[e,r]of Object.entries(t))n[e]=[r];super(n,e),this.components=t}getComponents(){return this.components}getValue(t){return this.components[t]}getValues(){return this.order.map((t=>this.getValue(t)))}allProperties(){const t=super.allProperties();for(let e,n,r=0;e=t[r],n=this.order[r];r++){const t=this.getValue(n);-1===e.indexOf(t)&&e.unshift(t)}return t}toString(){return this.getValues().join(".")}equal(t){const e=t.getAxes();if(this.order.length!==e.length)return!1;for(let n,r=0;n=e[r];r++){const e=this.getValue(n);if(!e||t.getValue(n)!==e)return!1}return!0}}c.DEFAULT_ORDER=[r.LOCALE,r.MODALITY,r.DOMAIN,r.STYLE,r.TOPIC],c.BASE_LOCALE="base",c.DEFAULT_VALUE="default",c.DEFAULT_VALUES={[r.LOCALE]:"en",[r.DOMAIN]:c.DEFAULT_VALUE,[r.STYLE]:c.DEFAULT_VALUE,[r.TOPIC]:c.DEFAULT_VALUE,[r.MODALITY]:"speech"};class l{constructor(t){this.order=t}parse(t){const e=t.split("."),n={};if(e.length>this.order.length)throw new Error("Invalid dynamic constraint: "+n);let r=0;for(let t,o=0;t=this.order[o],e.length;o++,r++){const r=e.shift();n[t]=r}return new c(n,this.order.slice(0,r))}}class u{constructor(t,e=new a(t.getProperties(),t.getOrder())){this.reference=t,this.fallback=e,this.order=this.reference.getOrder()}getReference(){return this.reference}setReference(t,e){this.reference=t,this.fallback=e||new a(t.getProperties(),t.getOrder()),this.order=this.reference.getOrder()}match(t){const e=t.getAxes();return e.length===this.reference.getAxes().length&&e.every((e=>{const n=t.getValue(e);return n===this.reference.getValue(e)||-1!==this.fallback.getProperty(e).indexOf(n)}))}compare(t,e){let n=!1;for(let r,o=0;r=this.order[o];o++){const o=t.getValue(r),i=e.getValue(r);if(!n){const t=this.reference.getValue(r);if(t===o&&t!==i)return-1;if(t===i&&t!==o)return 1;if(t===o&&t===i)continue;t!==o&&t!==i&&(n=!0)}const s=this.fallback.getProperty(r),a=s.indexOf(o),c=s.indexOf(i);if(a<c)return-1;if(c<a)return 1}return 0}toString(){return this.reference.toString()+"\n"+this.fallback.toString()}}!function(t){t.SYNC="sync",t.ASYNC="async",t.HTTP="http"}(o||(o={})),function(t){t.PITCH="pitch",t.RATE="rate",t.VOLUME="volume",t.PAUSE="pause",t.JOIN="join",t.LAYOUT="layout"}(i||(i={}));const h=[i.PITCH,i.RATE,i.VOLUME,i.PAUSE,i.JOIN];var d,f;!function(t){t.NONE="none",t.SHALLOW="shallow",t.DEEP="deep"}(d||(d={})),function(t){t.NONE="none",t.LAYOUT="layout",t.COUNTING="counting",t.PUNCTUATION="punctuation",t.SSML="ssml",t.ACSS="acss",t.SABLE="sable",t.VOICEXML="voicexml"}(f||(f={}));const p={mathspeak:"default",clearspeak:"default"};var m=n(74);function g(t){return t.match("/$")?t:t+"/"}function E(t,e="json"){return g(m.f.jsonPath)+t+(e.match(/^\./)?e:"."+e)}class N{static getInstance(){return N.instance=N.instance||new N,N.instance}init(t){return t&&this.startDebugFile_(t),this.isActive_=!0,this.fileHandle}output(...t){this.isActive_&&this.output_(t)}generate(t){this.isActive_&&this.output_(t.apply(t,[]))}exit(t=()=>{}){this.fileHandle.then((()=>{this.isActive_&&this.stream_&&this.stream_.end("","",t)}))}constructor(){this.isActive_=!1,this.fileHandle=Promise.resolve(),this.stream_=null}startDebugFile_(t){this.fileHandle=m.f.fs.promises.open(t,"w"),this.fileHandle=this.fileHandle.then((e=>{this.stream_=e.createWriteStream(t),this.outputFunction=function(...t){this.stream_.write(t.join(" ")),this.stream_.write("\n")}.bind(this),this.stream_.on("error",function(t){console.info("Invalid log file. Debug information sent to console."),this.outputFunction=console.info}.bind(this)),this.stream_.on("finish",(function(){console.info("Finalizing debug file.")}))}))}output_(t){this.stream_?this.fileHandle.then((()=>this.outputFunction.apply(this.outputFunction,["Speech Rule Engine Debugger:"].concat(t)))):console.info.apply(console,["Speech Rule Engine Debugger:"].concat(t))}}var S=n(714);class T{constructor(t={}){this.delay=!1,this.domain="clearspeak",this.style=c.DEFAULT_VALUES[r.STYLE],this.locale=c.DEFAULT_VALUES[r.LOCALE],this.subiso="",this.modality=c.DEFAULT_VALUES[r.MODALITY],this.speech=d.NONE,this.markup=f.NONE,this.mark=!0,this.automark=!1,this.character=!0,this.cleanpause=!0,this.cayleyshort=!0,this.linebreaks=!1,this.rate="100",this.walker="Table",this.structure=!1,this.aria=!1,this.tree=!1,this.strict=!1,this.pprint=!1,this.rules="",this.prune="",this.set(t)}set(t){this.ensureDomain(t);for(const[e,n]of Object.entries(t))(T.BINARY_FEATURES.includes(e)||T.STRING_FEATURES.includes(e))&&(this[e]=n)}json(){const t={};return[].concat(T.STRING_FEATURES,T.BINARY_FEATURES).forEach((e=>t[e]=this[e])),t}ensureDomain(t){if(t.modality&&"speech"!==t.modality||!t.modality&&"speech"!==this.modality)return;if(!t.domain&&!t.locale)return;if("default"===t.domain)return void(t.domain="mathspeak");const e=t.locale||this.locale,n=t.domain||this.domain;-1===I.indexOf(e)||"mathspeak"===n?"en"!==e?"mathspeak"!==n&&"clearspeak"!==n&&(t.domain="mathspeak"):-1===b.indexOf(n)&&(t.domain="mathspeak"):t.domain="mathspeak"}}T.BINARY_FEATURES=["automark","mark","character","cleanpause","strict","structure","aria","pprint","cayleyshort","linebreaks","tree"],T.STRING_FEATURES=["markup","style","domain","speech","walker","locale","delay","modality","rate","rules","subiso","prune"];const I=["ca","da","es"],b=["chromevox","clearspeak","mathspeak","emacspeak","html"];class A extends Error{constructor(t=""){super(),this.message=t,this.name="SRE Error"}}class R{set defaultLocale(t){this._defaultLocale=S.u.ensureLocale(t,this._defaultLocale)}get defaultLocale(){return this._defaultLocale}static getInstance(){return R.instance=R.instance||new R,R.instance}static defaultEvaluator(t,e){return t}static evaluateNode(t){return R.nodeEvaluator(t)}getRate(){const t=parseInt(this.options.rate,10);return isNaN(t)?100:t}setDynamicCstr(t){if(this.defaultLocale&&(c.DEFAULT_VALUES[r.LOCALE]=this.defaultLocale),t){const e=Object.keys(t);for(let n=0;n<e.length;n++){const r=e[n];if(-1!==c.DEFAULT_ORDER.indexOf(r)){const e=t[r];this.options[r]=e}}}const e=[this.options.locale,this.options.modality,this.options.domain,this.options.style].join("."),n=a.createProp([c.DEFAULT_VALUES[r.LOCALE]],[c.DEFAULT_VALUES[r.MODALITY]],[c.DEFAULT_VALUES[r.DOMAIN]],[c.DEFAULT_VALUES[r.STYLE]]),o=this.comparators[this.options.domain],i=this.parsers[this.options.domain];this.parser=i||this.defaultParser,this.dynamicCstr=this.parser.parse(e),this.dynamicCstr.updateProperties(n.getProperties()),this.comparator=o?o():new u(this.dynamicCstr)}constructor(){this.options=new T,this.config=!1,this.customLoader=null,this.parsers={},this.comparator=null,this.mode=o.SYNC,this.init=!0,this.comparators={},this._defaultLocale=c.DEFAULT_VALUES[r.LOCALE],this.options.locale=this.defaultLocale,this.evaluator=R.defaultEvaluator,this.defaultParser=new l(c.DEFAULT_ORDER),this.parser=this.defaultParser,this.dynamicCstr=c.defaultCstr()}configurate(t){this.mode!==o.HTTP||m.f.webworker||this.config||(!function(t){const e=document.documentElement.querySelectorAll('script[type="text/x-sre-config"]');for(let n=0,r=e.length;n<r;n++){let r;try{r=e[n].innerHTML;const o=JSON.parse(r);for(const[e,n]of Object.entries(o))t[e]=n}catch(t){N.getInstance().output("Illegal configuration ",r)}}}(t),this.config=!0),function(t){if("undefined"!=typeof SREfeature)for(const[e,n]of Object.entries(SREfeature))t[e]=n}(t)}setCustomLoader(t){t&&(this.customLoader=t)}setup(t){void 0!==t.mode&&(this.mode=t.mode),this.configurate(t),this.options.set(t),t.json&&(m.f.jsonPath=g(t.json)),this.setCustomLoader(t.custom)}json(){return Object.assign({mode:this.mode},this.options.json())}reset(){this.options=new T}}R.nodeEvaluator=function(t){return[]};class O{static get(t=R.getInstance().options.locale){return O.promises[t]||Promise.resolve("")}static getall(){return Promise.all(Object.values(O.promises))}}O.loaded={},O.promises={};const C=m.f.xpath,y={xhtml:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",mml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg"};function L(t){return y[t]||null}class M{constructor(){this.lookupNamespaceURI=L}}function _(t,e,n){return C.evaluate(t,e,new M,n,null)}function v(t,e){let n;try{n=_(t,e,C.result.ORDERED_NODE_ITERATOR_TYPE)}catch(t){return[]}const r=[];for(let t=n.iterateNext();t;t=n.iterateNext())r.push(t);return r}function w(t){if(R.getInstance().mode!==o.HTTP)return;let e=t;for(;e&&!e.evaluate;)e=e.parentNode;e&&e.evaluate?C.currentDocument=e:t.ownerDocument&&(C.currentDocument=t.ownerDocument)}function D(t){const e=[];for(let n=0,r=t.length;n<r;n++)e.push(t[n]);return e}function P(t){const e=new m.f.xmldom.DOMParser,n=function(t){return(t=t.replace(/&nbsp;/g,"\xa0")).replace(/>[ \f\n\r\t\v\u200b]+</g,"><").trim()}(t),r=!!n.match(/&(?!lt|gt|amp|quot|apos)\w+;/g);if(!n)throw new Error("Empty input!");try{const t=e.parseFromString(n,r?"text/html":"text/xml");return R.getInstance().mode===o.HTTP?(C.currentDocument=t,r?t.body.childNodes[0]:t.documentElement):t.documentElement}catch(t){throw new A("Illegal input: "+t.message)}}var x,U,F,k;function B(t,e){t.parentNode&&(t.parentNode.insertBefore(e,t),t.parentNode.removeChild(t))}function G(t){return m.f.document.createElement(t)}function V(t,e){return m.f.document.createElementNS(t,e)}function H(t){return m.f.document.createTextNode(t)}function j(t){let e="",n=/(>)(<)(\/*)/g,r=0,o=(t=t.replace(n,"$1\r\n$2$3")).split("\r\n");for(n=/(\.)*(<)(\/*)/g,o=o.map((t=>t.replace(n,"$1\r\n$2$3").split("\r\n"))).reduce(((t,e)=>t.concat(e)),[]);o.length;){let t=o.shift();if(!t)continue;let n=0;if(t.match(/^<\w[^>/]*>[^>]+$/)){const e=q(t,o[0]);e[0]?e[1]?(t+=o.shift().slice(0,-e[1].length),e[1].trim()&&o.unshift(e[1])):t+=o.shift():n=1}else if(t.match(/^<\/\w/))0!==r&&(r-=1);else if(t.match(/^<\w[^>]*[^/]>.*$/))n=1;else if(t.match(/^<\w[^>]*\/>.+$/)){const e=t.indexOf(">")+1,n=t.slice(e);n.trim()&&o.unshift(),t=t.slice(0,e)+n}else n=0;e+=new Array(r+1).join("  ")+t+"\r\n",r+=n}return e}function q(t,e){if(!e)return[!1,""];const n=t.match(/^<([^> ]+).*>/),r=e.match(/^<\/([^>]+)>(.*)/);return n&&r&&n[1]===r[1]?[!0,r[2]]:[!1,""]}function $(t,e){return t.querySelectorAll?D(t.querySelectorAll(`[${e}]`)):v(`.//*[@${e}]`,t)}function X(t,e,n){return t.querySelectorAll?D(t.querySelectorAll(`[${e}="${n}"]`)):v(`.//*[@${e}="${n}"]`,t)}function Y(t,e){return t.querySelectorAll?D(t.querySelectorAll(e)):v(`.//${e}`,t)}function W(t){return t.tagName.toUpperCase()}function K(t){return t.cloneNode(!0)}function z(t){return(new m.f.xmldom.XMLSerializer).serializeToString(t)}function Q(t){const e=t.toString(16).toUpperCase();return e.length>3?e:("000"+e).slice(-4)}function J([t,e],n){const r=parseInt(t,16),o=parseInt(e,16),i=[];for(let t=r;t<=o;t++){let e=Q(t);!1!==n[e]&&(e=n[e]||e,i.push(e))}return i}function Z(t,e={}){return J(t,e).map((t=>String.fromCodePoint(parseInt(t,16))))}!function(t){t[t.ELEMENT_NODE=1]="ELEMENT_NODE",t[t.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",t[t.TEXT_NODE=3]="TEXT_NODE",t[t.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",t[t.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",t[t.ENTITY_NODE=6]="ENTITY_NODE",t[t.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",t[t.COMMENT_NODE=8]="COMMENT_NODE",t[t.DOCUMENT_NODE=9]="DOCUMENT_NODE",t[t.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",t[t.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE",t[t.NOTATION_NODE=12]="NOTATION_NODE"}(x||(x={})),function(t){t.BOLD="bold",t.BOLDFRAKTUR="bold-fraktur",t.BOLDITALIC="bold-italic",t.BOLDSCRIPT="bold-script",t.DOUBLESTRUCK="double-struck",t.DOUBLESTRUCKITALIC="double-struck-italic",t.FULLWIDTH="fullwidth",t.FRAKTUR="fraktur",t.ITALIC="italic",t.MONOSPACE="monospace",t.NORMAL="normal",t.SCRIPT="script",t.SANSSERIF="sans-serif",t.SANSSERIFITALIC="sans-serif-italic",t.SANSSERIFBOLD="sans-serif-bold",t.SANSSERIFBOLDITALIC="sans-serif-bold-italic"}(U||(U={})),function(t){t.SUPER="super",t.SUB="sub",t.CIRCLED="circled",t.PARENTHESIZED="parenthesized",t.PERIOD="period",t.NEGATIVECIRCLED="negative-circled",t.DOUBLECIRCLED="double-circled",t.CIRCLEDSANSSERIF="circled-sans-serif",t.NEGATIVECIRCLEDSANSSERIF="negative-circled-sans-serif",t.COMMA="comma",t.SQUARED="squared",t.NEGATIVESQUARED="negative-squared"}(F||(F={})),function(t){t.LATINCAP="latinCap",t.LATINSMALL="latinSmall",t.GREEKCAP="greekCap",t.GREEKSMALL="greekSmall",t.DIGIT="digit"}(k||(k={}));const tt=[{interval:["1D400","1D419"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.BOLD},{interval:["1D41A","1D433"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.BOLD},{interval:["1D56C","1D585"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.BOLDFRAKTUR},{interval:["1D586","1D59F"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.BOLDFRAKTUR},{interval:["1D468","1D481"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.BOLDITALIC},{interval:["1D482","1D49B"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.BOLDITALIC},{interval:["1D4D0","1D4E9"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.BOLDSCRIPT},{interval:["1D4EA","1D503"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.BOLDSCRIPT},{interval:["1D538","1D551"],base:k.LATINCAP,subst:{"1D53A":"2102","1D53F":"210D","1D545":"2115","1D547":"2119","1D548":"211A","1D549":"211D","1D551":"2124"},capital:!0,category:"Lu",font:U.DOUBLESTRUCK},{interval:["1D552","1D56B"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.DOUBLESTRUCK},{interval:["1D504","1D51D"],base:k.LATINCAP,subst:{"1D506":"212D","1D50B":"210C","1D50C":"2111","1D515":"211C","1D51D":"2128"},capital:!0,category:"Lu",font:U.FRAKTUR},{interval:["1D51E","1D537"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.FRAKTUR},{interval:["FF21","FF3A"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.FULLWIDTH},{interval:["FF41","FF5A"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.FULLWIDTH},{interval:["1D434","1D44D"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.ITALIC},{interval:["1D44E","1D467"],base:k.LATINSMALL,subst:{"1D455":"210E"},capital:!1,category:"Ll",font:U.ITALIC},{interval:["1D670","1D689"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.MONOSPACE},{interval:["1D68A","1D6A3"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.MONOSPACE},{interval:["0041","005A"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.NORMAL},{interval:["0061","007A"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.NORMAL},{interval:["1D49C","1D4B5"],base:k.LATINCAP,subst:{"1D49D":"212C","1D4A0":"2130","1D4A1":"2131","1D4A3":"210B","1D4A4":"2110","1D4A7":"2112","1D4A8":"2133","1D4AD":"211B"},capital:!0,category:"Lu",font:U.SCRIPT},{interval:["1D4B6","1D4CF"],base:k.LATINSMALL,subst:{"1D4BA":"212F","1D4BC":"210A","1D4C4":"2134"},capital:!1,category:"Ll",font:U.SCRIPT},{interval:["1D5A0","1D5B9"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.SANSSERIF},{interval:["1D5BA","1D5D3"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.SANSSERIF},{interval:["1D608","1D621"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.SANSSERIFITALIC},{interval:["1D622","1D63B"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.SANSSERIFITALIC},{interval:["1D5D4","1D5ED"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.SANSSERIFBOLD},{interval:["1D5EE","1D607"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.SANSSERIFBOLD},{interval:["1D63C","1D655"],base:k.LATINCAP,subst:{},capital:!0,category:"Lu",font:U.SANSSERIFBOLDITALIC},{interval:["1D656","1D66F"],base:k.LATINSMALL,subst:{},capital:!1,category:"Ll",font:U.SANSSERIFBOLDITALIC},{interval:["0391","03A9"],base:k.GREEKCAP,subst:{"03A2":"03F4"},capital:!0,category:"Lu",font:U.NORMAL},{interval:["03B0","03D0"],base:k.GREEKSMALL,subst:{"03B0":"2207","03CA":"2202","03CB":"03F5","03CC":"03D1","03CD":"03F0","03CE":"03D5","03CF":"03F1","03D0":"03D6"},capital:!1,category:"Ll",font:U.NORMAL},{interval:["1D6A8","1D6C0"],base:k.GREEKCAP,subst:{},capital:!0,category:"Lu",font:U.BOLD},{interval:["1D6C1","1D6E1"],base:k.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:U.BOLD},{interval:["1D6E2","1D6FA"],base:k.GREEKCAP,subst:{},capital:!0,category:"Lu",font:U.ITALIC},{interval:["1D6FB","1D71B"],base:k.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:U.ITALIC},{interval:["1D71C","1D734"],base:k.GREEKCAP,subst:{},capital:!0,category:"Lu",font:U.BOLDITALIC},{interval:["1D735","1D755"],base:k.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:U.BOLDITALIC},{interval:["1D756","1D76E"],base:k.GREEKCAP,subst:{},capital:!0,category:"Lu",font:U.SANSSERIFBOLD},{interval:["1D76F","1D78F"],base:k.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:U.SANSSERIFBOLD},{interval:["1D790","1D7A8"],base:k.GREEKCAP,subst:{},capital:!0,category:"Lu",font:U.SANSSERIFBOLDITALIC},{interval:["1D7A9","1D7C9"],base:k.GREEKSMALL,subst:{},capital:!1,category:"Ll",font:U.SANSSERIFBOLDITALIC},{interval:["0030","0039"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.NORMAL},{interval:["2070","2079"],base:k.DIGIT,subst:{2071:"00B9",2072:"00B2",2073:"00B3"},offset:0,category:"No",font:F.SUPER},{interval:["2080","2089"],base:k.DIGIT,subst:{},offset:0,category:"No",font:F.SUB},{interval:["245F","2473"],base:k.DIGIT,subst:{"245F":"24EA"},offset:0,category:"No",font:F.CIRCLED},{interval:["3251","325F"],base:k.DIGIT,subst:{},offset:21,category:"No",font:F.CIRCLED},{interval:["32B1","32BF"],base:k.DIGIT,subst:{},offset:36,category:"No",font:F.CIRCLED},{interval:["2474","2487"],base:k.DIGIT,subst:{},offset:1,category:"No",font:F.PARENTHESIZED},{interval:["2487","249B"],base:k.DIGIT,subst:{2487:"1F100"},offset:0,category:"No",font:F.PERIOD},{interval:["2775","277F"],base:k.DIGIT,subst:{2775:"24FF"},offset:0,category:"No",font:F.NEGATIVECIRCLED},{interval:["24EB","24F4"],base:k.DIGIT,subst:{},offset:11,category:"No",font:F.NEGATIVECIRCLED},{interval:["24F5","24FE"],base:k.DIGIT,subst:{},offset:1,category:"No",font:F.DOUBLECIRCLED},{interval:["277F","2789"],base:k.DIGIT,subst:{"277F":"1F10B"},offset:0,category:"No",font:F.CIRCLEDSANSSERIF},{interval:["2789","2793"],base:k.DIGIT,subst:{2789:"1F10C"},offset:0,category:"No",font:F.NEGATIVECIRCLEDSANSSERIF},{interval:["FF10","FF19"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.FULLWIDTH},{interval:["1D7CE","1D7D7"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.BOLD},{interval:["1D7D8","1D7E1"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.DOUBLESTRUCK},{interval:["1D7E2","1D7EB"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.SANSSERIF},{interval:["1D7EC","1D7F5"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.SANSSERIFBOLD},{interval:["1D7F6","1D7FF"],base:k.DIGIT,subst:{},offset:0,category:"Nd",font:U.MONOSPACE},{interval:["1F101","1F10A"],base:k.DIGIT,subst:{},offset:0,category:"No",font:F.COMMA},{interval:["24B6","24CF"],base:k.LATINCAP,subst:{},capital:!0,category:"So",font:F.CIRCLED},{interval:["24D0","24E9"],base:k.LATINSMALL,subst:{},capital:!1,category:"So",font:F.CIRCLED},{interval:["1F110","1F129"],base:k.LATINCAP,subst:{},capital:!0,category:"So",font:F.PARENTHESIZED},{interval:["249C","24B5"],base:k.LATINSMALL,subst:{},capital:!1,category:"So",font:F.PARENTHESIZED},{interval:["1F130","1F149"],base:k.LATINCAP,subst:{},capital:!0,category:"So",font:F.SQUARED},{interval:["1F170","1F189"],base:k.LATINCAP,subst:{},capital:!0,category:"So",font:F.NEGATIVESQUARED},{interval:["1F150","1F169"],base:k.LATINCAP,subst:{},capital:!0,category:"So",font:F.NEGATIVECIRCLED}],et=new Map;function nt(t,e){return t+e.split("-").map((t=>t[0].toUpperCase()+t.slice(1))).join("")}for(const t of tt){const e=nt(t.base,t.font),n=Z(t.interval,t.subst);let r=et.get(e);r?r.unicode=r.unicode.concat(n):(r=t,r.unicode=n,et.set(e,r))}var rt;!function(t){t.PUNCTUATION="punctuation",t.FENCE="fence",t.NUMBER="number",t.IDENTIFIER="identifier",t.TEXT="text",t.OPERATOR="operator",t.RELATION="relation",t.LARGEOP="largeop",t.FUNCTION="function",t.ACCENT="accent",t.FENCED="fenced",t.FRACTION="fraction",t.PUNCTUATED="punctuated",t.RELSEQ="relseq",t.MULTIREL="multirel",t.INFIXOP="infixop",t.PREFIXOP="prefixop",t.POSTFIXOP="postfixop",t.APPL="appl",t.INTEGRAL="integral",t.BIGOP="bigop",t.SQRT="sqrt",t.ROOT="root",t.LIMUPPER="limupper",t.LIMLOWER="limlower",t.LIMBOTH="limboth",t.SUBSCRIPT="subscript",t.SUPERSCRIPT="superscript",t.UNDERSCORE="underscore",t.OVERSCORE="overscore",t.TENSOR="tensor",t.TABLE="table",t.MULTILINE="multiline",t.MATRIX="matrix",t.VECTOR="vector",t.CASES="cases",t.ROW="row",t.LINE="line",t.CELL="cell",t.ENCLOSE="enclose",t.INFERENCE="inference",t.RULELABEL="rulelabel",t.CONCLUSION="conclusion",t.PREMISES="premises",t.UNKNOWN="unknown",t.EMPTY="empty"}(rt||(rt={}));const ot=Object.assign({},rt);var it;!function(t){t.COMMA="comma",t.SEMICOLON="semicolon",t.ELLIPSIS="ellipsis",t.FULLSTOP="fullstop",t.QUESTION="question",t.EXCLAMATION="exclamation",t.QUOTES="quotes",t.DASH="dash",t.TILDE="tilde",t.PRIME="prime",t.DEGREE="degree",t.VBAR="vbar",t.COLON="colon",t.OPENFENCE="openfence",t.CLOSEFENCE="closefence",t.APPLICATION="application",t.DUMMY="dummy",t.UNIT="unit",t.LABEL="label",t.OPEN="open",t.CLOSE="close",t.TOP="top",t.BOTTOM="bottom",t.NEUTRAL="neutral",t.METRIC="metric",t.LATINLETTER="latinletter",t.GREEKLETTER="greekletter",t.OTHERLETTER="otherletter",t.NUMBERSET="numbersetletter",t.INTEGER="integer",t.FLOAT="float",t.OTHERNUMBER="othernumber",t.INFTY="infty",t.MIXED="mixed",t.MULTIACCENT="multiaccent",t.OVERACCENT="overaccent",t.UNDERACCENT="underaccent",t.UNDEROVER="underover",t.SUBSUP="subsup",t.LEFTSUB="leftsub",t.LEFTSUPER="leftsuper",t.RIGHTSUB="rightsub",t.RIGHTSUPER="rightsuper",t.LEFTRIGHT="leftright",t.ABOVEBELOW="abovebelow",t.INTERVAL="interval",t.SETEMPTY="set empty",t.SETEXT="set extended",t.SETSINGLE="set singleton",t.SETCOLLECT="set collection",t.STRING="string",t.SPACE="space",t.ANNOTATION="annotation",t.TEXT="text",t.SEQUENCE="sequence",t.ENDPUNCT="endpunct",t.STARTPUNCT="startpunct",t.NEGATIVE="negative",t.POSITIVE="positive",t.NEGATION="negation",t.MULTIOP="multiop",t.PREFIXOP="prefix operator",t.POSTFIXOP="postfix operator",t.LIMFUNC="limit function",t.INFIXFUNC="infix function",t.PREFIXFUNC="prefix function",t.POSTFIXFUNC="postfix function",t.SIMPLEFUNC="simple function",t.COMPFUNC="composed function",t.SUM="sum",t.INTEGRAL="integral",t.GEOMETRY="geometry",t.BOX="box",t.BLOCK="block",t.ADDITION="addition",t.MULTIPLICATION="multiplication",t.SUBTRACTION="subtraction",t.IMPLICIT="implicit",t.DIVISION="division",t.VULGAR="vulgar",t.EQUALITY="equality",t.INEQUALITY="inequality",t.ARROW="arrow",t.ELEMENT="element",t.NONELEMENT="nonelement",t.REELEMENT="reelement",t.RENONELEMENT="renonelement",t.SET="set",t.DETERMINANT="determinant",t.ROWVECTOR="rowvector",t.BINOMIAL="binomial",t.SQUAREMATRIX="squarematrix",t.CYCLE="cycle",t.MULTILINE="multiline",t.MATRIX="matrix",t.VECTOR="vector",t.CASES="cases",t.TABLE="table",t.CAYLEY="cayley",t.PROOF="proof",t.LEFT="left",t.RIGHT="right",t.UP="up",t.DOWN="down",t.FINAL="final",t.SINGLE="single",t.HYP="hyp",t.AXIOM="axiom",t.LOGIC="logic",t.UNKNOWN="unknown",t.MGLYPH="mglyph"}(it||(it={}));const st=Object.assign({},it);var at;!function(t){t.CALIGRAPHIC="caligraphic",t.CALIGRAPHICBOLD="caligraphic-bold",t.OLDSTYLE="oldstyle",t.OLDSTYLEBOLD="oldstyle-bold",t.UNKNOWN="unknown"}(at||(at={}));const ct=Object.assign(Object.assign(Object.assign({},U),at),F);var lt;!function(t){t.ALLLETTERS="allLetters",t.D="d",t.BAR="bar",t.TILDE="tilde"}(lt||(lt={}));const ut=Object.assign(Object.assign({},k),lt);function ht(t,e){return t.toString()}function dt(t){return t.toString()}function ft(t,e){return t+e.toLowerCase()}const pt={};function mt(t,e=""){if(!t.childNodes||!t.childNodes[0]||!t.childNodes[0].childNodes||t.childNodes[0].childNodes.length<2||t.childNodes[0].childNodes[0].tagName!==ot.NUMBER||t.childNodes[0].childNodes[0].getAttribute("role")!==st.INTEGER||t.childNodes[0].childNodes[1].tagName!==ot.NUMBER||t.childNodes[0].childNodes[1].getAttribute("role")!==st.INTEGER)return{convertible:!1,content:t.textContent};const n=t.childNodes[0].childNodes[1].textContent,r=t.childNodes[0].childNodes[0].textContent,o=Number(n),i=Number(r);return isNaN(o)||isNaN(i)?{convertible:!1,content:`${r} ${e} ${n}`}:{convertible:!0,enumerator:i,denominator:o}}function gt(t,e,n){const r=mt(t);if(r.convertible){const t=r.enumerator,o=r.denominator;return t>0&&t<e&&o>0&&o<n}return!1}function Et(t={}){return Object.assign({zero:"zero",ones:[],tens:[],large:[],special:{},wordOrdinal:dt,numericOrdinal:dt,numberToWords:dt,numberToOrdinal:ht,vulgarSep:" ",numSep:" "},t)}pt.identityCombiner=function(t,e,n){return t+e+n},pt.prefixCombiner=function(t,e,n){return t=n?n+" "+t:t,e?e+" "+t:t},pt.postfixCombiner=function(t,e,n){return t=n?n+" "+t:t,e?t+" "+e:t},pt.romanceCombiner=function(t,e,n){return t=n?t+" "+n:t,e?t+" "+e:t};const Nt=St();function St(){return{FUNCTIONS:{fracNestDepth:t=>gt(t,10,100),radicalNestDepth:t=>"",combineRootIndex:function(t,e){return t},combineNestedFraction:pt.identityCombiner,combineNestedRadical:pt.identityCombiner,fontRegexp:function(t){return new RegExp("^"+t.split(/ |-/).join("( |-)")+"( |-)")},si:ft,plural:dt},MESSAGES:{MS:{},MSroots:{},font:{},embellish:{},role:{},enclose:{},navigate:{},regexp:{},unitTimes:""},ALPHABETS:{latinSmall:[],latinCap:[],greekSmall:[],greekCap:[],capPrefix:{default:""},smallPrefix:{default:""},digitPrefix:{default:""},languagePrefix:{},digitTrans:{default:dt,mathspeak:dt,clearspeak:dt},letterTrans:{default:dt},combiner:(t,e,n)=>t},NUMBERS:Et(),COMBINERS:{},CORRECTIONS:{},SUBISO:{default:"",current:"",all:[]}}}function Tt(t){switch(t){case 1:return Nt.MESSAGES.MS.ONCE||"";case 2:return Nt.MESSAGES.MS.TWICE;default:return t.toString()}}function It(t,e){return t===Nt.MESSAGES.MS.ROOTINDEX||t===Nt.MESSAGES.MS.INDEX?t:t+" "+e}function bt(t){return At(Nt.MESSAGES.font[t],t)}function At(t,e){return void 0===t?e:"string"==typeof t?t:t[0]}const Rt="grammar";class Ot{static getInstance(){return Ot.instance=Ot.instance||new Ot,Ot.instance}static parseInput(t){const e={},n=t.split(":");for(const t of n){const n=t.split("="),r=n[0].trim();n[1]?e[r]=n[1].trim():r.match(/^!/)?e[r.slice(1)]=!1:e[r]=!0}return e}static parseState(t){const e={},n=t.split(" ");for(const t of n){const n=t.split(":"),r=n[0],o=n[1];e[r]=o||!0}return e}static translateString(t){if(t.match(/:unit$/))return Ot.translateUnit(t);const e=R.getInstance();let n=e.evaluator(t,e.dynamicCstr);return n=null===n?t:n,Ot.getInstance().getParameter("plural")&&(n=Nt.FUNCTIONS.plural(n)),n}static translateUnit(t){t=Ot.prepareUnit(t);const e=R.getInstance(),n=Ot.getInstance().getParameter("plural"),r=e.options.strict,o=`${e.options.locale}.${e.options.modality}.default`;let i,s;return e.options.strict=!0,n&&(i=e.defaultParser.parse(o+".plural"),s=e.evaluator(t,i)),s?(e.options.strict=r,s):(i=e.defaultParser.parse(o+".default"),s=e.evaluator(t,i),e.options.strict=r,s?(n&&(s=Nt.FUNCTIONS.plural(s)),s):Ot.cleanUnit(t))}static prepareUnit(t){const e=t.match(/:unit$/);return e?t.slice(0,e.index).replace(/\s+/g," ")+t.slice(e.index):t}static cleanUnit(t){return t.match(/:unit$/)?t.replace(/:unit$/,""):t}clear(){this.parameters_={},this.stateStack_=[]}setParameter(t,e){const n=this.parameters_[t];return e?this.parameters_[t]=e:delete this.parameters_[t],n}getParameter(t){return this.parameters_[t]}setCorrection(t,e){this.corrections_[t]=e}setPreprocessor(t,e){this.preprocessors_[t]=e}getCorrection(t){return this.corrections_[t]}getState(){const t=[];for(const[e,n]of Object.entries(this.parameters_))t.push("string"==typeof n?e+":"+n:e);return t.join(" ")}processSingles(){const t={};for(const e of this.singles)t[e]=!1;this.singles=[],this.pushState(t)}pushState(t){for(let[e,n]of Object.entries(t))e.match(/^\?/)&&(delete t[e],e=e.slice(1),this.singles.push(e)),t[e]=this.setParameter(e,n);this.stateStack_.push(t)}popState(){const t=this.stateStack_.pop();for(const[e,n]of Object.entries(t))this.setParameter(e,n)}setAttribute(t){if(t&&t.nodeType===x.ELEMENT_NODE){const e=this.getState();e&&t.setAttribute(Rt,e)}}preprocess(t){return this.runProcessors(t,this.preprocessors_)}correct(t){return this.runProcessors(t,this.corrections_)}apply(t,e){return this.currentFlags=e||{},t=this.currentFlags.adjust||this.currentFlags.preprocess?Ot.getInstance().preprocess(t):t,(this.parameters_.translate||this.currentFlags.translate)&&(t=Ot.translateString(t)),t=this.currentFlags.adjust||this.currentFlags.correct?Ot.getInstance().correct(t):t,this.currentFlags={},t}runProcessors(t,e){for(const[n,r]of Object.entries(this.parameters_)){const o=e[n];o&&(t=!0===r?o(t):o(t,r))}return t}constructor(){this.currentFlags={},this.parameters_={},this.corrections_={},this.preprocessors_={},this.stateStack_=[],this.singles=[]}}function Ct(t,e){if(!e||!t)return t;const n=Nt.FUNCTIONS.fontRegexp(bt(e));return t.replace(n,"")}function yt(t){let e=t%1e3,n="",r=_t.ones[Math.floor(e/100)];if(n+=r?r+_t.numSep+"honderd":"",e%=100,e)if(n+=n?_t.numSep:"",r=_t.ones[e],r)n+=r;else{const t=_t.tens[Math.floor(e/10)];r=_t.ones[e%10],n+=r?r+"-en-"+t:t}return n}function Lt(t){if(0===t)return _t.zero;if(t>=Math.pow(10,36))return t.toString();let e=0,n="";for(;t>0;){if(t%1e3){const r=yt(t%1e3);if(e){const t=_t.large[e];n=r+_t.numSep+t+(n?_t.numSep+n:"")}else n=r+(n?_t.numSep+n:"")}t=Math.floor(t/1e3),e++}return n}function Mt(t){if(1===t)return"eerste";if(3===t)return"derde";if(8===t)return"agste";if(9===t)return"negende";return Lt(t)+(t<19?"de":"ste")}Ot.getInstance().setCorrection("localFont",bt),Ot.getInstance().setCorrection("localRole",(function(t){return At(Nt.MESSAGES.role[t],t)})),Ot.getInstance().setCorrection("localEnclose",(function(t){return At(Nt.MESSAGES.enclose[t],t)})),Ot.getInstance().setCorrection("ignoreFont",Ct),Ot.getInstance().setPreprocessor("annotation",(function(t,e){return t+":"+e})),Ot.getInstance().setPreprocessor("noTranslateText",(function(t){return t.match(new RegExp("^["+Nt.MESSAGES.regexp.TEXT+"]+$"))&&(Ot.getInstance().currentFlags.translate=!1),t})),Ot.getInstance().setCorrection("ignoreCaps",(function(t){let e=Nt.ALPHABETS.capPrefix[R.getInstance().options.domain];return void 0===e&&(e=Nt.ALPHABETS.capPrefix.default),Ct(t,e)})),Ot.getInstance().setPreprocessor("numbers2alpha",(function(t){return t.match(/\d+/)?Nt.NUMBERS.numberToWords(parseInt(t,10)):t}));const _t=Et({wordOrdinal:Mt,numericOrdinal:function(t){return t.toString()+"."},numberToWords:Lt,numberToOrdinal:function(t,e){return 1===t?"enkel":2===t?e?"helftes":"helfte":4===t?e?"kwarte":"kwart":Mt(t)+(e?"s":"")}}),vt=function(t,e,n){return t=n?n+" "+t:t,e?t+" "+e:t};let wt=null;function Dt(t){const e=t%1e3,n=Math.floor(e/100),r=n?1===n?"cent":Ut.ones[n]+"-cents":"",o=function(t){const e=t%100;if(e<20)return Ut.ones[e];const n=Math.floor(e/10),r=Ut.tens[n],o=Ut.ones[e%10];return r&&o?r+(2===n?"-i-":"-")+o:r||o}(e%100);return r&&o?r+Ut.numSep+o:r||o}function Pt(t){if(0===t)return Ut.zero;if(t>=Math.pow(10,36))return t.toString();let e=0,n="";for(;t>0;){const r=t%(e>1?1e6:1e3);if(r){let t=Ut.large[e];if(e)if(1===e)n=(1===r?"":Dt(r)+Ut.numSep)+t+(n?Ut.numSep+n:"");else{const e=Pt(r);t=1===r?t:t.replace(/\u00f3$/,"ons"),n=e+Ut.numSep+t+(n?Ut.numSep+n:"")}else n=Dt(r)}t=Math.floor(t/(e>1?1e6:1e3)),e++}return n}function xt(t){const e=Ot.getInstance().getParameter("gender");return t.toString()+("f"===e?"a":"n")}const Ut=Et({numericOrdinal:xt,numberToWords:Pt,numberToOrdinal:function(t,e){if(t>1999)return xt(t);if(t<=10)return Ut.special.onesOrdinals[t-1];const n=Pt(t);return n.match(/mil$/)?n.replace(/mil$/,"mil\xb7l\xe8sima"):n.match(/u$/)?n.replace(/u$/,"vena"):n.match(/a$/)?n.replace(/a$/,"ena"):n+(n.match(/e$/)?"na":"ena")}}),Ft=function(t,e,n){return t="sans serif "+(n?n+" "+t:t),e?t+" "+e:t};let kt=null;function Bt(t,e=!1){return t===jt.ones[1]?e?"et":"en":t}function Gt(t,e=!1){let n=t%1e3,r="",o=jt.ones[Math.floor(n/100)];if(r+=o?Bt(o,!0)+" hundrede":"",n%=100,n)if(r+=r?" og ":"",o=e?jt.special.smallOrdinals[n]:jt.ones[n],o)r+=o;else{const t=e?jt.special.tensOrdinals[Math.floor(n/10)]:jt.tens[Math.floor(n/10)];o=jt.ones[n%10],r+=o?Bt(o)+"og"+t:t}return r}function Vt(t,e=!1){if(0===t)return jt.zero;if(t>=Math.pow(10,36))return t.toString();let n=0,r="";for(;t>0;){const o=t%1e3;if(o){const t=Gt(o,e&&!n);if(n){const e=jt.large[n],i=o>1?"er":"";r=Bt(t,n<=1)+" "+e+i+(r?" og ":"")+r}else r=Bt(t)+r}t=Math.floor(t/1e3),n++}return r}function Ht(t){if(t%100)return Vt(t,!0);const e=Vt(t);return e.match(/e$/)?e:e+"e"}const jt=Et({wordOrdinal:Ht,numericOrdinal:function(t){return t.toString()+"."},numberToWords:Vt,numberToOrdinal:function(t,e){return 1===t?e?"hel":"hele":2===t?e?"halv":"halve":Ht(t)+(e?"dele":"del")}});let qt=null;function $t(t,e=!1){return t===Kt.ones[1]?e?"eine":"ein":t}function Xt(t){let e=t%1e3,n="",r=Kt.ones[Math.floor(e/100)];if(n+=r?$t(r)+"hundert":"",e%=100,e)if(n+=n?Kt.numSep:"",r=Kt.ones[e],r)n+=r;else{const t=Kt.tens[Math.floor(e/10)];r=Kt.ones[e%10],n+=r?$t(r)+"und"+t:t}return n}function Yt(t){if(0===t)return Kt.zero;if(t>=Math.pow(10,36))return t.toString();let e=0,n="";for(;t>0;){const r=t%1e3;if(r){const o=Xt(t%1e3);if(e){const t=Kt.large[e],i=e>1&&r>1?t.match(/e$/)?"n":"en":"";n=$t(o,e>1)+t+i+n}else n=$t(o,e>1)+n}t=Math.floor(t/1e3),e++}return n.replace(/ein$/,"eins")}function Wt(t){if(1===t)return"erste";if(3===t)return"dritte";if(7===t)return"siebte";if(8===t)return"achte";return Yt(t)+(t<19?"te":"ste")}const Kt=Et({wordOrdinal:Wt,numericOrdinal:function(t){return t.toString()+"."},numberToWords:Yt,numberToOrdinal:function(t,e){return 1===t?"eintel":2===t?e?"halbe":"halb":Wt(t)+"l"}}),zt=function(t,e,n){return"s"===n&&(e=e.split(" ").map((function(t){return t.replace(/s$/,"")})).join(" "),n=""),t=n?n+" "+t:t,e?e+" "+t:t},Qt=function(t,e,n){return t=n&&"s"!==n?n+" "+t:t,e?t+" "+e:t};let Jt=null;function Zt(t){let e=t%1e3,n="";return n+=ne.ones[Math.floor(e/100)]?ne.ones[Math.floor(e/100)]+ne.numSep+"hundred":"",e%=100,e&&(n+=n?ne.numSep:"",n+=ne.ones[e]||ne.tens[Math.floor(e/10)]+(e%10?ne.numSep+ne.ones[e%10]:"")),n}function te(t){if(0===t)return ne.zero;if(t>=Math.pow(10,36))return t.toString();let e=0,n="";for(;t>0;){t%1e3&&(n=Zt(t%1e3)+(e?"-"+ne.large[e]+"-":"")+n),t=Math.floor(t/1e3),e++}return n.replace(/-$/,"")}function ee(t){let e=te(t);return e.match(/one$/)?e=e.slice(0,-3)+"first":e.match(/two$/)?e=e.slice(0,-3)+"second":e.match(/three$/)?e=e.slice(0,-5)+"third":e.match(/five$/)?e=e.slice(0,-4)+"fifth":e.match(/eight$/)?e=e.slice(0,-5)+"eighth":e.match(/nine$/)?e=e.slice(0,-4)+"ninth":e.match(/twelve$/)?e=e.slice(0,-6)+"twelfth":e.match(/ty$/)?e=e.slice(0,-2)+"tieth":e+="th",e}const ne=Et({wordOrdinal:ee,numericOrdinal:function(t){const e=t%100,n=t.toString();if(e>10&&e<20)return n+"th";switch(t%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd";default:return n+"th"}},numberToWords:te,numberToOrdinal:function(t,e){if(1===t)return e?"oneths":"oneth";if(2===t)return e?"halves":"half";const n=ee(t);return e?n+"s":n}});let re=null;function oe(t){const e=t%1e3,n=Math.floor(e/100),r=ie.special.hundreds[n],o=function(t){const e=t%100;if(e<30)return ie.ones[e];const n=ie.tens[Math.floor(e/10)],r=ie.ones[e%10];return n&&r?n+" y "+r:n||r}(e%100);return 1===n?o?r+"to "+o:r:r&&o?r+" "+o:r||o}const ie=Et({numericOrdinal:function(t){const e=Ot.getInstance().getParameter("gender");return t.toString()+("f"===e?"a":"o")},numberToWords:function(t){if(0===t)return ie.zero;if(t>=Math.pow(10,36))return t.toString();let e=0,n="";for(;t>0;){const r=t%1e3;if(r){let t=ie.large[e];const o=oe(r);e?1===r?(t=t.match("/^mil( |$)/")?t:"un "+t,n=t+(n?" "+n:"")):(t=t.replace(/\u00f3n$/,"ones"),n=oe(r)+" "+t+(n?" "+n:"")):n=o}t=Math.floor(t/1e3),e++}return n},numberToOrdinal:function(t,e){if(t>1999)return t.toString()+"a";if(t<=12)return ie.special.onesOrdinals[t-1];const n=[];if(t>=1e3&&(t-=1e3,n.push("mil\xe9sima")),!t)return n.join(" ");let r=0;return r=Math.floor(t/100),r>0&&(n.push(ie.special.hundredsOrdinals[r-1]),t%=100),t<=12?n.push(ie.special.onesOrdinals[t-1]):(r=Math.floor(t/10),r>0&&(n.push(ie.special.tensOrdinals[r-1]),t%=10),t>0&&n.push(ie.special.onesOrdinals[t-1])),n.join(" ")}}),se=function(t,e,n){return t="sans serif "+(n?n+" "+t:t),e?t+" "+e:t};let ae=null;let ce=null;function le(t){let e=t%1e3,n="";if(n+=fe.ones[Math.floor(e/100)]?fe.ones[Math.floor(e/100)]+"-cent":"",e%=100,e){n+=n?"-":"";let t=fe.ones[e];if(t)n+=t;else{const r=fe.tens[Math.floor(e/10)];r.match(/-dix$/)?(t=fe.ones[e%10+10],n+=r.replace(/-dix$/,"")+"-"+t):n+=r+(e%10?"-"+fe.ones[e%10]:"")}}const r=n.match(/s-\w+$/);return r?n.replace(/s-\w+$/,r[0].slice(1)):n.replace(/-un$/,"-et-un")}function ue(t){if(0===t)return fe.zero;if(t>=Math.pow(10,36))return t.toString();fe.special["tens-"+R.getInstance().options.subiso]&&(fe.tens=fe.special["tens-"+R.getInstance().options.subiso]);let e=0,n="";for(;t>0;){const r=t%1e3;if(r){let t=fe.large[e];const o=le(r);if(t&&t.match(/^mille /)){const r=t.replace(/^mille /,"");n=n.match(RegExp(r))?o+(e?"-mille-":"")+n:n.match(RegExp(r.replace(/s$/,"")))?o+(e?"-mille-":"")+n.replace(r.replace(/s$/,""),r):o+(e?"-"+t+"-":"")+n}else t=1===r&&t?t.replace(/s$/,""):t,n=o+(e?"-"+t+"-":"")+n}t=Math.floor(t/1e3),e++}return n.replace(/-$/,"")}const he={1:"uni\xe8me",2:"demi",3:"tiers",4:"quart"};function de(t){if(1===t)return"premi\xe8re";let e=ue(t);return e.match(/^neuf$/)?e=e.slice(0,-1)+"v":e.match(/cinq$/)?e+="u":e.match(/trois$/)?e+="":(e.match(/e$/)||e.match(/s$/))&&(e=e.slice(0,-1)),e+="i\xe8me",e}const fe=Et({wordOrdinal:de,numericOrdinal:function(t){const e=Ot.getInstance().getParameter("gender");return 1===t?t.toString()+("m"===e?"er":"re"):t.toString()+"e"},numberToWords:ue,numberToOrdinal:function(t,e){const n=he[t]||de(t);return 3===t?n:e?n+"s":n}});let pe=null;function me(t){if(0===t)return Ee.zero;if(t>=Math.pow(10,32))return t.toString();let e=0,n="";const r=function(t){let e=t%1e3,n="";return n+=Ee.ones[Math.floor(e/100)]?Ee.ones[Math.floor(e/100)]+Ee.numSep+Ee.special.hundred:"",e%=100,e&&(n+=n?Ee.numSep:"",n+=Ee.ones[e]),n}(t%1e3);if(!(t=Math.floor(t/1e3)))return r;for(;t>0;){const r=t%100;r&&(n=Ee.ones[r]+Ee.numSep+Ee.large[e]+(n?Ee.numSep+n:"")),t=Math.floor(t/100),e++}return r?n+Ee.numSep+r:n}function ge(t){const e=Ot.getInstance().getParameter("gender");if(t<=0)return t.toString();if(t<10)return"f"===e?Ee.special.ordinalsFeminine[t]:Ee.special.ordinalsMasculine[t];return me(t)+("f"===e?"\u0935\u0940\u0902":"\u0935\u093e\u0901")}const Ee=Et({wordOrdinal:ge,numericOrdinal:function(t){const e=Ot.getInstance().getParameter("gender");return t>0&&t<10?"f"===e?Ee.special.simpleSmallOrdinalsFeminine[t]:Ee.special.simpleSmallOrdinalsMasculine[t]:t.toString().split("").map((function(t){const e=parseInt(t,10);return isNaN(e)?"":Ee.special.simpleNumbers[e]})).join("")+("f"===e?"\u0935\u0940\u0902":"\u0935\u093e\u0901")},numberToWords:me,numberToOrdinal:function(t,e){return t<=10?Ee.special.smallDenominators[t]:ge(t)+" \u0905\u0902\u0936"}});let Ne=null;function Se(t){let e=t%1e4,n="";return n+=Ae.ones[Math.floor(e/1e3)]?1===Math.floor(e/1e3)?"\ucc9c":Ae.ones[Math.floor(e/1e3)]+"\ucc9c":"",e%=1e3,e&&(n+=Ae.ones[Math.floor(e/100)]?1===Math.floor(e/100)?"\ubc31":Ae.ones[Math.floor(e/100)]+"\ubc31":"",e%=100,n+=Ae.tens[Math.floor(e/10)]+(e%10?Ae.ones[e%10]:"")),n}function Te(t){if(0===t)return Ae.zero;if(t>=Math.pow(10,36))return t.toString();let e=0,n="";for(;t>0;){t%1e4&&(n=Se(t%1e4)+(e?Ae.large[e]+Ae.numSep:"")+n),t=Math.floor(t/1e4),e++}return n.replace(/ $/,"")}function Ie(t,e){return 1===t?"\uccab\ubc88\uc9f8":be(t)+"\ubc88\uc9f8"}function be(t){const e=Te(t),n=Te(t%=100);if(!n||!t)return e;const r=20===t?"\uc2a4\ubb34":Ae.tens[10+Math.floor(t/10)],o=Ae.ones[10+Math.floor(t%10)];return e.slice(0,-n.length)+r+o}const Ae=Et({wordOrdinal:be,numericOrdinal:function(t){return Ie(t)},numberToWords:Te,numberToOrdinal:Ie});let Re=null;function Oe(t){let e=t%1e3,n="";if(n+=Le.ones[Math.floor(e/100)]?Le.ones[Math.floor(e/100)]+Le.numSep+"cento":"",e%=100,e){n+=n?Le.numSep:"";const t=Le.ones[e];if(t)n+=t;else{let t=Le.tens[Math.floor(e/10)];const r=e%10;1!==r&&8!==r||(t=t.slice(0,-1)),n+=t,n+=r?Le.numSep+Le.ones[e%10]:""}}return n}function Ce(t){if(0===t)return Le.zero;if(t>=Math.pow(10,36))return t.toString();if(1===t&&Ot.getInstance().getParameter("fraction"))return"un";let e=0,n="";for(;t>0;){t%1e3&&(n=Oe(t%1e3)+(e?"-"+Le.large[e]+"-":"")+n),t=Math.floor(t/1e3),e++}return n.replace(/-$/,"")}function ye(t){const e="m"===Ot.getInstance().getParameter("gender")?"o":"a";let n=Le.special.onesOrdinals[t];return n?n.slice(0,-1)+e:(n=Ce(t),n.slice(0,-1)+"esim"+e)}const Le=Et({wordOrdinal:ye,numericOrdinal:function(t){const e=Ot.getInstance().getParameter("gender");return t.toString()+("m"===e?"o":"a")},numberToWords:Ce,numberToOrdinal:function(t,e){if(2===t)return e?"mezzi":"mezzo";const n=ye(t);if(!e)return n;const r=n.match(/o$/)?"i":"e";return n.slice(0,-1)+r}}),Me=function(t,e,n){return t.match(/^[a-zA-Z]$/)&&(e=e.replace("cerchiato","cerchiata")),t=n?t+" "+n:t,e?t+" "+e:t};let _e=null;function ve(t,e=!1){let n=t%1e3,r="";const o=Math.floor(n/100),i=Pe.ones[o];if(r+=i?(1===o?"":i)+"hundre":"",n%=100,n){if(r+=r?"og":"",e){const t=Pe.special.smallOrdinals[n];if(t)return r+t;if(n%10)return r+Pe.tens[Math.floor(n/10)]+Pe.special.smallOrdinals[n%10]}r+=Pe.ones[n]||Pe.tens[Math.floor(n/10)]+(n%10?Pe.ones[n%10]:"")}return e?we(r):r}function we(t){const e=Pe.special.endOrdinal[0];return"a"===e&&t.match(/en$/)?t.slice(0,-2)+Pe.special.endOrdinal:t.match(/(d|n)$/)||t.match(/hundre$/)?t+"de":t.match(/i$/)?t+Pe.special.endOrdinal:"a"===e&&t.match(/e$/)?t.slice(0,-1)+Pe.special.endOrdinal:(t.match(/e$/),t+"nde")}function De(t){return Fe(t,!0)}const Pe=Et({wordOrdinal:De,numericOrdinal:function(t){return t.toString()+"."},numberToWords:Fe,numberToOrdinal:function(t,e){return De(t)}});function xe(t,e=!1){return t===Pe.ones[1]?"ein"===t?"eitt ":e?"et":"ett":t}function Ue(t,e=!1){let n=t%1e3,r="",o=Pe.ones[Math.floor(n/100)];if(r+=o?xe(o)+"hundre":"",n%=100,n){if(r+=r?"og":"",e){const t=Pe.special.smallOrdinals[n];if(t)return r+t}if(o=Pe.ones[n],o)r+=o;else{const t=Pe.tens[Math.floor(n/10)];o=Pe.ones[n%10],r+=o?o+"og"+t:t}}return e?we(r):r}function Fe(t,e=!1){const n="alt"===R.getInstance().options.subiso?function(t,e=!1){if(0===t)return e?Pe.special.smallOrdinals[0]:Pe.zero;if(t>=Math.pow(10,36))return t.toString();let n=0,r="";for(;t>0;){const o=t%1e3;if(o){const i=Ue(t%1e3,!n&&e);!n&&e&&(e=!e),r=(1===n?xe(i,!0):i)+(n>1?Pe.numSep:"")+(n?Pe.large[n]+(n>1&&o>1?"er":""):"")+(n>1&&r?Pe.numSep:"")+r}t=Math.floor(t/1e3),n++}return e?r+(r.match(/tusen$/)?"de":"te"):r}(t,e):function(t,e=!1){if(0===t)return e?Pe.special.smallOrdinals[0]:Pe.zero;if(t>=Math.pow(10,36))return t.toString();let n=0,r="";for(;t>0;){const o=t%1e3;if(o){const i=ve(t%1e3,!n&&e);!n&&e&&(e=!e),r=i+(n?" "+Pe.large[n]+(n>1&&o>1?"er":"")+(r?" ":""):"")+r}t=Math.floor(t/1e3),n++}return e?r+(r.match(/tusen$/)?"de":"te"):r}(t,e);return n}let ke=null;function Be(t){return t.toString().split("").map((function(t){return Ge.ones[parseInt(t,10)]})).join("")}const Ge=Et({numberToWords:Be,numberToOrdinal:Be}),Ve=function(t){return t.match(RegExp("^"+Ye.ALPHABETS.languagePrefix.english))?t.slice(1):t},He=function(t,e,n){return t=Ve(t),e?t+e:t},je=function(t,e,n){return e+Ve(t)},qe=function(t,e,n){return e+(n||"")+(t=Ve(t))+"\u283b"},$e=function(t,e,n){return e+(n||"")+(t=Ve(t))+"\u283b\u283b"},Xe=function(t,e,n){return e+(t=Ve(t))+"\u283e"};let Ye=null;let We=null;function Ke(t){let e=t%1e3,n="";const r=Math.floor(e/100);return n+=Je.ones[r]?(1===r?"":Je.ones[r]+Je.numSep)+"hundra":"",e%=100,e&&(n+=n?Je.numSep:"",n+=Je.ones[e]||Je.tens[Math.floor(e/10)]+(e%10?Je.numSep+Je.ones[e%10]:"")),n}function ze(t,e=!1){if(0===t)return Je.zero;if(t>=Math.pow(10,36))return t.toString();let n=0,r="";for(;t>0;){const o=t%1e3;if(o){const i=Je.large[n],s=o>1&&n>1&&!e?"er":"";r=(1===n&&1===o?"":(n>1&&1===o?"en":Ke(t%1e3))+(n>1?" ":""))+(n?i+s+(n>1?" ":""):"")+r}t=Math.floor(t/1e3),n++}return r.replace(/ $/,"")}function Qe(t){let e=ze(t,!0);return e.match(/^noll$/)?e="nollte":e.match(/ett$/)?e=e.replace(/ett$/,"f\xf6rsta"):e.match(/tv\xe5$/)?e=e.replace(/tv\xe5$/,"andra"):e.match(/tre$/)?e=e.replace(/tre$/,"tredje"):e.match(/fyra$/)?e=e.replace(/fyra$/,"fj\xe4rde"):e.match(/fem$/)?e=e.replace(/fem$/,"femte"):e.match(/sex$/)?e=e.replace(/sex$/,"sj\xe4tte"):e.match(/sju$/)?e=e.replace(/sju$/,"sjunde"):e.match(/\xe5tta$/)?e=e.replace(/\xe5tta$/,"\xe5ttonde"):e.match(/nio$/)?e=e.replace(/nio$/,"nionde"):e.match(/tio$/)?e=e.replace(/tio$/,"tionde"):e.match(/elva$/)?e=e.replace(/elva$/,"elfte"):e.match(/tolv$/)?e=e.replace(/tolv$/,"tolfte"):e.match(/tusen$/)?e=e.replace(/tusen$/,"tusonde"):e.match(/jard$/)||e.match(/jon$/)?e+="te":e+="de",e}const Je=Et({wordOrdinal:Qe,numericOrdinal:function(t){const e=t.toString();return e.match(/11$|12$/)?e+":e":e+(e.match(/1$|2$/)?":a":":e")},numberToWords:ze,numberToOrdinal:function(t,e){if(1===t)return"hel";if(2===t)return e?"halva":"halv";let n=Qe(t);return n=n.match(/de$/)?n.replace(/de$/,""):n,n+(e?"delar":"del")}});let Ze=null;const tn={af:function(){return wt||(wt=function(){const t=St();return t.NUMBERS=_t,t.COMBINERS.germanPostfix=vt,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.plural=t=>/.*s$/.test(t)?t:t+"s",t.FUNCTIONS.fontRegexp=function(t){return new RegExp("((^"+t+" )|( "+t+"$))")},t.ALPHABETS.combiner=pt.prefixCombiner,t.ALPHABETS.digitTrans.default=_t.numberToWords,t.CORRECTIONS.article=t=>Ot.getInstance().getParameter("noArticle")?"":t,t}()),wt},ca:function(){return kt||(kt=function(){const t=St();return t.NUMBERS=Ut,t.COMBINERS.sansserif=Ft,t.FUNCTIONS.fracNestDepth=t=>!1,t.FUNCTIONS.combineRootIndex=It,t.FUNCTIONS.combineNestedRadical=(t,e,n)=>t+n,t.FUNCTIONS.fontRegexp=t=>RegExp("^"+t+" "),t.FUNCTIONS.plural=t=>/.*os$/.test(t)?t+"sos":/.*s$/.test(t)?t+"os":/.*ga$/.test(t)?t.slice(0,-2)+"gues":/.*\xe7a$/.test(t)?t.slice(0,-2)+"ces":/.*ca$/.test(t)?t.slice(0,-2)+"ques":/.*ja$/.test(t)?t.slice(0,-2)+"ges":/.*qua$/.test(t)?t.slice(0,-3)+"q\xfces":/.*a$/.test(t)?t.slice(0,-1)+"es":/.*(e|i)$/.test(t)?t+"ns":/.*\xed$/.test(t)?t.slice(0,-1)+"ins":t+"s",t.FUNCTIONS.si=(t,e)=>(e.match(/^metre/)&&(t=t.replace(/a$/,"\xe0").replace(/o$/,"\xf2").replace(/i$/,"\xed")),t+e),t.ALPHABETS.combiner=pt.prefixCombiner,t}()),kt},da:function(){return qt||(qt=function(){const t=St();return t.NUMBERS=jt,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.fontRegexp=e=>e===t.ALPHABETS.capPrefix.default?RegExp("^"+e+" "):RegExp(" "+e+"$"),t.ALPHABETS.combiner=pt.postfixCombiner,t.ALPHABETS.digitTrans.default=jt.numberToWords,t}()),qt},de:function(){return Jt||(Jt=function(){const t=St();return t.NUMBERS=Kt,t.COMBINERS.germanPostfix=Qt,t.ALPHABETS.combiner=zt,t.FUNCTIONS.radicalNestDepth=e=>e>1?t.NUMBERS.numberToWords(e)+"fach":"",t.FUNCTIONS.combineRootIndex=(t,e)=>{const n=e?e+"wurzel":"";return t.replace("Wurzel",n)},t.FUNCTIONS.combineNestedRadical=(t,e,n)=>{const r=(e?e+" ":"")+(t=n.match(/exponent$/)?t+"r":t);return n.match(/ /)?n.replace(/ /," "+r+" "):r+" "+n},t.FUNCTIONS.fontRegexp=function(t){return t=t.split(" ").map((function(t){return t.replace(/s$/,"(|s)")})).join(" "),new RegExp("((^"+t+" )|( "+t+"$))")},t.CORRECTIONS.correctOne=t=>t.replace(/^eins$/,"ein"),t.CORRECTIONS.localFontNumber=t=>bt(t).split(" ").map((function(t){return t.replace(/s$/,"")})).join(" "),t.CORRECTIONS.lowercase=t=>t.toLowerCase(),t.CORRECTIONS.article=t=>{const e=Ot.getInstance().getParameter("case"),n=Ot.getInstance().getParameter("plural");return"dative"===e?{der:"dem",die:n?"den":"der",das:"dem"}[t]:t},t.CORRECTIONS.masculine=t=>"dative"===Ot.getInstance().getParameter("case")?t+"n":t,t}()),Jt},en:function(){return re||(re=function(){const t=St();return t.NUMBERS=ne,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.plural=t=>/.*s$/.test(t)?t:t+"s",t.ALPHABETS.combiner=pt.prefixCombiner,t.ALPHABETS.digitTrans.default=ne.numberToWords,t.CORRECTIONS.article=t=>Ot.getInstance().getParameter("noArticle")?"":t,t}()),re},es:function(){return ae||(ae=function(){const t=St();return t.NUMBERS=ie,t.COMBINERS.sansserif=se,t.FUNCTIONS.fracNestDepth=t=>!1,t.FUNCTIONS.combineRootIndex=It,t.FUNCTIONS.combineNestedRadical=(t,e,n)=>t+n,t.FUNCTIONS.fontRegexp=t=>RegExp("^"+t+" "),t.FUNCTIONS.plural=t=>/.*(a|e|i|o|u)$/.test(t)?t+"s":/.*z$/.test(t)?t.slice(0,-1)+"ces":/.*c$/.test(t)?t.slice(0,-1)+"ques":/.*g$/.test(t)?t+"ues":/.*\u00f3n$/.test(t)?t.slice(0,-2)+"ones":t+"es",t.FUNCTIONS.si=(t,e)=>(e.match(/^metro/)&&(t=t.replace(/a$/,"\xe1").replace(/o$/,"\xf3").replace(/i$/,"\xed")),t+e),t.ALPHABETS.combiner=pt.prefixCombiner,t}()),ae},euro:function(){return ce||(ce=St()),ce},fr:function(){return pe||(pe=function(){const t=St();return t.NUMBERS=fe,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.combineRootIndex=It,t.FUNCTIONS.combineNestedFraction=(t,e,n)=>n.replace(/ $/g,"")+e+t,t.FUNCTIONS.combineNestedRadical=(t,e,n)=>n+" "+t,t.FUNCTIONS.fontRegexp=t=>RegExp(" (en |)"+t+"$"),t.FUNCTIONS.plural=t=>/.*s$/.test(t)?t:t+"s",t.CORRECTIONS.article=t=>Ot.getInstance().getParameter("noArticle")?"":t,t.ALPHABETS.combiner=pt.romanceCombiner,t.SUBISO={default:"fr",current:"fr",all:["fr","be","ch"]},t}()),pe},hi:function(){return Ne||(Ne=function(){const t=St();return t.NUMBERS=Ee,t.ALPHABETS.combiner=pt.prefixCombiner,t.FUNCTIONS.radicalNestDepth=Tt,t}()),Ne},it:function(){return _e||(_e=function(){const t=St();return t.NUMBERS=Le,t.COMBINERS.italianPostfix=Me,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.combineRootIndex=It,t.FUNCTIONS.combineNestedFraction=(t,e,n)=>n.replace(/ $/g,"")+e+t,t.FUNCTIONS.combineNestedRadical=(t,e,n)=>n+" "+t,t.FUNCTIONS.fontRegexp=t=>RegExp(" (en |)"+t+"$"),t.ALPHABETS.combiner=pt.romanceCombiner,t}()),_e},ko:function(){return Re||(Re=function(){const t=St();return t.NUMBERS=Ae,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.plural=function(t){return t},t.FUNCTIONS.si=(t,e)=>t+e,t.FUNCTIONS.combineRootIndex=function(t,e){return t+e},t.ALPHABETS.combiner=pt.prefixCombiner,t.ALPHABETS.digitTrans.default=Ae.numberToWords,t.CORRECTIONS.postposition=t=>{if(["\uac19\ub2e4","\ub294","\uc640","\ub97c","\ub85c"].includes(t))return t;const e=t.slice(-1);let n=(e.charCodeAt(0)-44032)%28>0;return e.match(/[r,l,n,m,1,3,6,7,8,0]/i)&&(n=!0),Ot.getInstance().setParameter("final",n),t},t.CORRECTIONS.article=t=>{const e=Ot.getInstance().getParameter("final");e&&Ot.getInstance().setParameter("final",!1),"\uac19\ub2e4"===t&&(t="\ub294");const n={\ub294:"\uc740",\uc640:"\uacfc",\ub97c:"\uc744",\ub85c:"\uc73c\ub85c"}[t];return void 0!==n&&e?n:t},t}()),Re},nb:function(){return ke||(ke=function(){const t=St();return t.NUMBERS=Pe,t.ALPHABETS.combiner=pt.prefixCombiner,t.ALPHABETS.digitTrans.default=Pe.numberToWords,t.FUNCTIONS.radicalNestDepth=Tt,t}()),ke},nn:function(){return We||(We=function(){const t=St();return t.NUMBERS=Pe,t.ALPHABETS.combiner=pt.prefixCombiner,t.ALPHABETS.digitTrans.default=Pe.numberToWords,t.FUNCTIONS.radicalNestDepth=Tt,t.SUBISO={default:"",current:"",all:["","alt"]},t}()),We},sv:function(){return Ze||(Ze=function(){const t=St();return t.NUMBERS=Je,t.FUNCTIONS.radicalNestDepth=Tt,t.FUNCTIONS.fontRegexp=function(t){return new RegExp("((^"+t+" )|( "+t+"$))")},t.ALPHABETS.combiner=pt.prefixCombiner,t.ALPHABETS.digitTrans.default=Je.numberToWords,t.CORRECTIONS.correctOne=t=>t.replace(/^ett$/,"en"),t}()),Ze},nemeth:function(){return Ye||(Ye=function(){const t=St();return t.NUMBERS=Ge,t.COMBINERS={postfixCombiner:He,germanCombiner:je,embellishCombiner:qe,doubleEmbellishCombiner:$e,parensCombiner:Xe},t.FUNCTIONS.fracNestDepth=t=>!1,t.FUNCTIONS.fontRegexp=t=>RegExp("^"+t),t.FUNCTIONS.si=dt,t.ALPHABETS.combiner=(t,e,n)=>e?e+n+t:Ve(t),t.ALPHABETS.digitTrans={default:Ge.numberToWords},t}()),Ye}};function en(){const t=function(){const t=S.u.ensureLocale(R.getInstance().options.locale,R.getInstance().defaultLocale);return R.getInstance().options.locale=t,tn[t]()}();if(function(t){const e=R.getInstance().options.subiso;-1===t.SUBISO.all.indexOf(e)&&(R.getInstance().options.subiso=t.SUBISO.default);t.SUBISO.current=R.getInstance().options.subiso}(t),t){for(const e of Object.getOwnPropertyNames(t))Nt[e]=t[e];for(const[e,n]of Object.entries(t.CORRECTIONS))Ot.getInstance().setCorrection(e,n)}}var nn=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]])}return n};const rn={functionApplication:String.fromCodePoint(8289),invisibleTimes:String.fromCodePoint(8290),invisibleComma:String.fromCodePoint(8291),invisiblePlus:String.fromCodePoint(8292)};class on extends Map{get(t){return super.get(t)||{role:st.UNKNOWN,type:ot.UNKNOWN,font:ct.UNKNOWN}}}class sn extends Map{set(t,e,n=""){return super.set(this.secKey(e,t),n||e),this}has(t,e){return super.has(this.secKey(e,t))}get(t,e){return super.get(this.secKey(e,t))}secKey(t,e){return e?`${t} ${e}`:`${t}`}}const an={Meaning:new on,Secondary:new sn,FencesHoriz:new Map,FencesVert:new Map,LatexCommands:new Map};function cn(t,e){for(const n of t)an.Meaning.set(n,{role:e.role||st.UNKNOWN,type:e.type||ot.UNKNOWN,font:e.font||ct.UNKNOWN}),e.secondary&&an.Secondary.set(n,e.secondary)}function ln(t,e,n=1){const r={},o=function(t){let e=[];for(const n of t)Array.isArray(n)?e=e.concat(J(n,{}).map((t=>parseInt(t,16)))):e.push(parseInt(n,16));return e}(e);for(const e of o)r[e]||(t.set(String.fromCodePoint(e),String.fromCodePoint(e+n)),r[e]=!0,r[e+n]=!0)}const un=["cos","cot","csc","sec","sin","tan","arccos","arccot","arccsc","arcsec","arcsin","arctan"].concat(["cosh","coth","csch","sech","sinh","tanh","arcosh","arcoth","arcsch","arsech","arsinh","artanh"],["deg","det","dim","hom","ker","Tr"],["log","ln","lg","exp","gcd","lcm","arg","im","re","Pr"]);function hn(t,e,n,r,o,i=[],s={},a={}){const c=et.get(nt(t,r));c&&(c.unicode.forEach((t=>{an.Meaning.set(t,{type:e,role:n,font:o}),i.forEach((e=>an.Secondary.set(t,e)))})),function(t,e){for(const[n,r]of Object.entries(e)){const e=t[n];void 0!==e&&an.Meaning.set(e,r)}}(c.unicode,s),function(t,e){for(const[n,r]of Object.entries(e)){const e=t[n];void 0!==e&&an.Secondary.set(e,r)}}(c.unicode,a))}[{set:["23","26","40","5c","a1","a7","b6","bf","2017",["2022","2025"],"2027","203b","203c",["2041","2043"],["2047","2049"],["204b","204d"],"2050","2055","2056",["2058","205e"],"2234","2235","fe45","fe46","fe5f","fe60","fe68","fe6b","ff03","ff06","ff0f","ff20","ff3c"],type:ot.PUNCTUATION,role:st.UNKNOWN},{set:["22","ab","bb","2dd",["2018","201f"],"2039","203a",["301d","301f"],"fe10","ff02","ff07"],type:ot.PUNCTUATION,role:st.QUOTES},{set:["3b","204f","2a1f","2a3e","fe14","fe54","ff1b"],type:ot.PUNCTUATION,role:st.SEMICOLON},{set:["3f","203d","fe16","fe56","ff1f"],type:ot.PUNCTUATION,role:st.QUESTION},{set:["21","fe15","fe57","ff01"],type:ot.PUNCTUATION,role:st.EXCLAMATION},{set:["5e","60","a8","aa","b4","ba","2c7",["2d8","2da"],"2040","207a","207d","207e","ff3e","ff40"],type:ot.PUNCTUATION,role:st.OVERACCENT},{set:["b8","2db","2038","203f","2054","208a","208d","208e"],type:ot.PUNCTUATION,role:st.UNDERACCENT},{set:["3a","2982","fe13","fe30","fe55","ff1a"],type:ot.PUNCTUATION,role:st.COLON},{set:["2c","2063","fe50","ff0c"],type:ot.PUNCTUATION,role:st.COMMA},{set:["2026",["22ee","22f1"],"fe19"],type:ot.PUNCTUATION,role:st.ELLIPSIS},{set:["2e","fe52","ff0e"],type:ot.PUNCTUATION,role:st.FULLSTOP},{set:["2d","207b","208b","2212","2796","fe63","ff0d"],type:ot.OPERATOR,role:st.DASH,secondary:ut.BAR},{set:["5f","af",["2010","2015"],"203e","208b",["fe49","fe4f"],"fe58","ff3f","ffe3"],type:ot.PUNCTUATION,role:st.DASH,secondary:ut.BAR},{set:["7e","2dc","2f7","303","330","334","2053","223c","223d","301c","ff5e"],type:ot.OPERATOR,role:st.TILDE,secondary:ut.TILDE},{set:["27","2b9","2ba",["2032","2037"],"2057"],type:ot.PUNCTUATION,role:st.PRIME},{set:["b0"],type:ot.PUNCTUATION,role:st.DEGREE},{set:["2b","b1","2064","2213","2214","2228","222a",["228c","228e"],"2294","2295","229d","229e","22bb","22bd","22c4","22ce","22d3","2304","271b","271c","2795","27cf","29fa","29fb","29fe",["2a22","2a28"],"2a2d","2a2e","2a39","2a42","2a45","2a46","2a48","2a4a","2a4c","2a4f","2a50","2a52","2a54","2a56","2a57","2a59","2a5b","2a5d",["2a61","2a63"],"2adc","2add","fe62","ff0b"],type:ot.OPERATOR,role:st.ADDITION},{set:["2a","b7","d7","2020","2021","204e","2051","2062",["2217","2219"],"2227","2229","2240","2293","2297",["2299","229b"],"22a0","22a1","22b9","22bc",["22c5","22cc"],"22cf","22d2","22d4","2303","2305","2306","25cb","2715","2716","27ce","27d1",["29d1","29d7"],"29e2","2a1d",["2a2f","2a37"],["2a3b","2a3d"],"2a40","2a43","2a44","2a47","2a49","2a4b","2a4d","2a4e","2a51","2a53","2a55","2a58","2a5a","2a5c",["2a5e","2a60"],"2ada","2adb","fe61","ff0a"],type:ot.OPERATOR,role:st.MULTIPLICATION},{set:["2d","af","2010","2011","2052","207b","208b","2212","2216","2238","2242","2296","229f","2796","29ff",["2a29","2a2c"],"2a3a","2a41","fe63","ff0d"],type:ot.OPERATOR,role:st.SUBTRACTION},{set:["2f","f7","2044","2215","2298","2797","27cc","29bc",["29f5","29f9"],"2a38"],type:ot.OPERATOR,role:st.DIVISION},{set:["25","2030","2031","ff05","fe6a"],type:ot.OPERATOR,role:st.POSTFIXOP},{set:["ac","2200","2201","2203","2204","2206",["221a","221c"],"2310","ffe2"],type:ot.OPERATOR,role:st.PREFIXOP},{set:["2320","2321","23aa","23ae","23af","23b2","23b3","23b6","23b7"],type:ot.OPERATOR,role:st.PREFIXOP},{set:["1d7ca","1d7cb"],type:ot.OPERATOR,role:st.PREFIXOP,font:ct.BOLD},{set:["3d","7e","207c","208c","221d","2237",["223a","223f"],"2243","2245","2248",["224a","224e"],["2251","225f"],"2261","2263","229c","22cd","22d5","29e4","29e6","2a66","2a67",["2a6a","2a6c"],["2a6c","2a78"],"fe66","ff1d"],type:ot.RELATION,role:st.EQUALITY},{set:["3c","3e","2241","2242","2244","2246","2247","2249","224f","2250","2260","2262",["2264","2281"],"22b0","22b1",["22d6","22e1"],["22e6","22e9"],["2976","2978"],"29c0","29c1","29e1","29e3","29e5",["2a79","2abc"],["2af7","2afa"],"fe64","fe65","ff1c","ff1e"],type:ot.RELATION,role:st.INEQUALITY},{set:[["2282","228b"],["228f","2292"],["22b2","22b8"],"22d0","22d1",["22e2","22e5"],["22ea","22ed"],"27c3","27c4",["27c7","27c9"],["27d5","27d7"],"27dc",["2979","297b"],"29df",["2abd","2ad8"]],type:ot.RELATION,role:st.SET},{set:["2236",["27e0","27e5"],"292b","292c",["29b5","29bb"],"29be","29bf",["29c2","29d0"]],type:ot.RELATION,role:st.UNKNOWN},{set:["2205",["29b0","29b4"]],type:ot.IDENTIFIER,role:st.SETEMPTY},{set:["1ab2","221e",["29dc","29de"]],type:ot.IDENTIFIER,role:st.INFTY},{set:["22a2","22a3",["22a6","22af"],"27da","27db","27dd","27de","2ade",["2ae2","2ae6"],"2aec","2aed"],type:ot.RELATION,role:st.LOGIC},{set:["22a4","22a5","22ba","27d8","27d9","27df","2adf","2ae0",["2ae7","2aeb"],"2af1"],type:ot.IDENTIFIER,role:st.LOGIC},{set:[["2190","21ff"],"2301","2324","238b","2794",["2798","27af"],["27b1","27be"],["27f0","27ff"],["2900","292a"],["292d","2975"],["297c","297f"],["2b00","2b11"],["2b30","2b4c"],["ffe9","ffec"]],type:ot.RELATION,role:st.ARROW},{set:["2208","220a",["22f2","22f9"],"22ff","27d2","2ad9"],type:ot.OPERATOR,role:st.ELEMENT},{set:["2209"],type:ot.OPERATOR,role:st.NONELEMENT},{set:["220b","220d",["22fa","22fe"]],type:ot.OPERATOR,role:st.REELEMENT},{set:["220c"],type:ot.OPERATOR,role:st.RENONELEMENT},{set:[["220f","2211"],["22c0","22c3"],["2a00","2a0b"],"2a3f","2afc","2aff"],type:ot.LARGEOP,role:st.SUM},{set:["2140"],type:ot.LARGEOP,role:st.SUM,font:ct.DOUBLESTRUCK},{set:[["222b","2233"],["2a0c","2a17"],["2a17","2a1c"]],type:ot.LARGEOP,role:st.INTEGRAL},{set:[["2500","257F"]],type:ot.RELATION,role:st.BOX},{set:[["2580","259F"]],type:ot.IDENTIFIER,role:st.BLOCK},{set:[["25A0","25FF"],["2B12","2B2F"],["2B50","2B59"]],type:ot.RELATION,role:st.GEOMETRY},{set:["220e","2300","2302","2311","29bd","29e0",["29e8","29f3"],"2a1e","2afe","ffed","ffee"],type:ot.OPERATOR,role:st.GEOMETRY},{set:[["221f","2222"],"22be","22bf",["2312","2314"],"237c","27c0",["299b","29af"]],type:ot.OPERATOR,role:st.GEOMETRY},{set:["24",["a2","a5"],"b5","2123",["2125","2127"],"212a","212b","fe69","ff04","ffe0","ffe1","ffe5","ffe6"],type:ot.IDENTIFIER,role:st.UNKNOWN},{set:["a9","ae","210f","2114","2116","2117",["211e","2122"],"212e","2132",["2139","213b"],["2141","2144"],"214d","214e",["1f12a","1f12c"],"1f18a"],type:ot.IDENTIFIER,role:st.OTHERLETTER},{set:["2224","2226","2239","2307","27b0","27bf","27c1","27c2","27ca","27cb","27cd","27d0","27d3","27d4","2981","2999","299a","29e7","29f4","2a20","2a21","2a64","2a65","2a68","2a69","2ae1",["2aee","2af0"],"2af2","2af3","2af5","2af6","2afb","2afd"],type:ot.OPERATOR,role:st.UNKNOWN},{set:["2605","2606","26aa","26ab",["2720","274d"]],type:ot.OPERATOR,role:st.UNKNOWN},{set:[["2145","2149"]],type:ot.IDENTIFIER,role:st.LATINLETTER,font:ct.DOUBLESTRUCKITALIC,secondary:ut.ALLLETTERS},{set:[["213c","213f"]],type:ot.IDENTIFIER,role:st.GREEKLETTER,font:ct.DOUBLESTRUCK,secondary:ut.ALLLETTERS},{set:["3d0","3d7","3f6",["1d26","1d2a"],"1d5e","1d60",["1d66","1d6a"]],type:ot.IDENTIFIER,role:st.GREEKLETTER,font:ct.NORMAL,secondary:ut.ALLLETTERS},{set:[["2135","2138"]],type:ot.IDENTIFIER,role:st.OTHERLETTER,font:ct.NORMAL,secondary:ut.ALLLETTERS},{set:["131","237"],type:ot.IDENTIFIER,role:st.LATINLETTER,font:ct.NORMAL},{set:["1d6a4","1d6a5"],type:ot.IDENTIFIER,role:st.LATINLETTER,font:ct.ITALIC},{set:["2113","2118"],type:ot.IDENTIFIER,role:st.LATINLETTER,font:ct.SCRIPT},{set:[["c0","d6"],["d8","f6"],["f8","1bf"],["1c4","2af"],["1d00","1d25"],["1d6b","1d9a"],["1e00","1ef9"],["363","36f"],["1dd3","1de6"],["1d62","1d65"],"1dca","2071","207f",["2090","209c"],"2c7c"],type:ot.IDENTIFIER,role:st.LATINLETTER,font:ct.NORMAL},{set:[["00bc","00be"],["2150","215f"],"2189"],type:ot.NUMBER,role:st.FLOAT},{set:["23E8",["3248","324f"]],type:ot.NUMBER,role:st.INTEGER},{set:[["214A","214C"],"2705","2713","2714","2717","2718"],type:ot.IDENTIFIER,role:st.UNKNOWN},{set:["20","a0","ad",["2000","200f"],["2028","202f"],["205f","2060"],"206a","206b","206e","206f","feff",["fff9","fffb"]],type:ot.TEXT,role:st.SPACE},{set:["7c","a6","2223","23b8","23b9","23d0","2758",["fe31","fe34"],"ff5c","ffe4","ffe8"],type:ot.FENCE,role:st.NEUTRAL},{set:["2016","2225","2980","2af4"],type:ot.FENCE,role:st.METRIC}].forEach((t=>{var{set:e}=t,n=nn(t,["set"]);return cn(function(t){let e=[];for(const n of t)Array.isArray(n)?e=e.concat(Z(n)):e.push(String.fromCodePoint(parseInt(n,16)));return e}(e),n)})),ln(an.FencesVert,["23b4",["23dc","23e1"],["fe35","fe44"],"fe47"]),ln(an.FencesHoriz,["28","2045","207D","208D",["2308","230f"],["231c","231f"],"2329","23b0",["2768","2775"],"27c5",["27e6","27ef"],["2983","2998"],["29d8","29db"],"29fc",["2e22","2e29"],["3008","3011"],["3014","301b"],"fd3e","fe17",["fe59","fe5e"],"ff08","ff5f","ff62"]),ln(an.FencesHoriz,["5b","7b","ff3b","ff5b"],2),ln(an.FencesHoriz,[["239b","23a6"]],3),ln(an.FencesHoriz,[["23a7","23a9"]],4),cn([...an.FencesHoriz.keys()],{type:ot.FENCE,role:st.OPEN}),cn([...an.FencesHoriz.values()],{type:ot.FENCE,role:st.CLOSE}),cn([...an.FencesVert.keys()],{type:ot.FENCE,role:st.TOP}),cn([...an.FencesVert.values()],{type:ot.FENCE,role:st.BOTTOM}),function(){for(const[t,e]of Object.entries(ct)){const n=!!F[t]?ct.UNKNOWN:e===ct.FULLWIDTH?ct.NORMAL:e;hn(k.LATINCAP,ot.IDENTIFIER,st.LATINLETTER,e,n,[ut.ALLLETTERS]),hn(k.LATINSMALL,ot.IDENTIFIER,st.LATINLETTER,e,n,[ut.ALLLETTERS],{},{3:ut.D}),hn(k.GREEKCAP,ot.IDENTIFIER,st.GREEKLETTER,e,n,[ut.ALLLETTERS]),hn(k.GREEKSMALL,ot.IDENTIFIER,st.GREEKLETTER,e,n,[ut.ALLLETTERS],{0:{type:ot.OPERATOR,role:st.PREFIXOP,font:n},26:{type:ot.OPERATOR,role:st.PREFIXOP,font:n}}),hn(k.DIGIT,ot.NUMBER,st.INTEGER,e,n)}}(),cn(["inf","lim","liminf","limsup","max","min","sup","injlim","projlim"],{type:ot.FUNCTION,role:st.LIMFUNC}),cn(un,{type:ot.FUNCTION,role:st.PREFIXFUNC}),cn(["mod","rem"],{type:ot.OPERATOR,role:st.PREFIXFUNC});class dn{constructor(){this.rules=new Map}static parseUnicode(t){const e=parseInt(t,16);return String.fromCodePoint(e)}static testDynamicConstraints_(t,e){return R.getInstance().options.strict?e.cstr.equal(t):R.getInstance().comparator.match(e.cstr)}defineRulesFromMappings(t,e,n){for(const[r,o]of Object.entries(n))for(const[n,i]of Object.entries(o))this.defineRuleFromStrings(t,e,r,n,i)}getRules(t){let e=this.rules.get(t);return e||(e=[],this.rules.set(t,e)),e}defineRuleFromStrings(t,e,n,r,o){let i=this.getRules(t);const s=R.getInstance().parsers[n]||R.getInstance().defaultParser,a=R.getInstance().comparators[n],c=`${t}.${e}.${n}.${r}`,l=s.parse(c),u=a?a():R.getInstance().comparator,h=u.getReference();u.setReference(l);const d={cstr:l,action:o};i=i.filter((t=>!l.equal(t.cstr))),i.push(d),this.rules.set(t,i),u.setReference(h)}lookupRule(t,e){let n=this.getRules(e.getValue(r.LOCALE));return n=n.filter((function(t){return dn.testDynamicConstraints_(e,t)})),1===n.length?n[0]:n.length?n.sort(((t,e)=>R.getInstance().comparator.compare(t.cstr,e.cstr)))[0]:null}}let fn=c.DEFAULT_VALUES[r.LOCALE],pn=c.DEFAULT_VALUES[r.MODALITY];function mn(t){return!(!t.locale&&!t.modality)&&(fn=t.locale||fn,pn=t.modality||pn,!0)}let gn={};function En(t){gn=t}const Nn=new Map,Sn=new Map;function Tn(t,e){let n=Nn.get(e);return n||(n=new dn,n.base=Sn.get(t),Nn.set(e,n),n)}function In(t){const e=Sn.get(t.key);if(!e)return;const n=t.names;Object.assign(t,e),n&&e.names&&(t.names=t.names.concat(n))}function bn(t,e,n){Tn(t,e).defineRulesFromMappings(fn,pn,n)}function An(t,e,n,r){Tn(n,n).defineRuleFromStrings(fn,pn,t,e,r)}function Rn(t){if(!mn(t))for(const[e,n]of Object.entries(t))An("default","default",e,n)}function On(t){for(let e,n=0;e=t.names[n];n++)bn(t.key,e,t.mappings)}function Cn(t){for(const e of Object.keys(gn)){const n=Object.assign({},t);n.mappings={};const r=gn[e];n.names=n.names.map((function(t){return e+t}));for(const e of Object.keys(t.mappings)){n.mappings[e]={};for(const o of Object.keys(t.mappings[e]))n.mappings[e][o]=tn[fn]().FUNCTIONS.si(r,t.mappings[e][o])}On(n)}}function yn(t){const e=Nn.get(t);return(null==e?void 0:e.base)?e.base.category:""}function Ln(t,e){return e=e||{},t.length?(e[t[0]]=Ln(t.slice(1),e[t[0]]),e):e}R.getInstance().evaluator=function(t,e){const n=function(t,e){const n=Nn.get(t);return n?n.lookupRule(null,e):null}(t,e);return n?n.action:null};class Mn{constructor(t,e){this.speech=t,this.attributes=e}static empty(){return new Mn("",{})}static stringEmpty(t){return new Mn(t,{})}static stringAttr(t,e){return new Mn(t,e)}static singleton(t,e={}){return[Mn.stringAttr(t,e)]}static node(t,e,n={}){const r=Mn.getAttributes(e);return Object.assign(r,n),new Mn(t,r)}static getAttributes(t){const e={};for(const n of Mn.attributeList)t.hasAttribute(n)&&(e[n]=t.getAttribute(n));return e}}Mn.attributeList=["id","extid"];class _n{constructor(t=null){this.data=t,this.prev=null,this.next=null}}class vn extends Set{constructor(t){super(),this.annotations=[],this.anchor=new _n,this.anchor.next=this.anchor,this.anchor.prev=this.anchor,t.forEach((t=>{const e=new _n(t);t.annotation&&this.annotations.push(e),this.push(e)}))}first(){return this.empty?null:this.anchor.next}last(){return this.empty?null:this.anchor.prev}push(t){t.next=this.anchor,t.prev=this.anchor.prev,t.prev.next=t,this.anchor.prev=t,super.add(t)}pop(){const t=this.last();return t?(this.delete(t),t):null}delete(t){return!!this.has(t)&&(super.delete(t),t.prev.next=t.next,t.next=t.prev,!0)}insertAfter(t,e){this.insertBefore(t,e.next)}insertBefore(t,e){const n=new _n(t);e&&this.has(e)?(e.prev.next=n,n.prev=e.prev,n.next=e,e.prev=n):this.push(n)}prevText(t){do{t=t.prev}while(t!==this.anchor&&!t.data.text);return t===this.anchor?null:t}*[Symbol.iterator](){let t=this.anchor.next;for(;t!==this.anchor;)yield t,t=t.next}nextText(t){for(;t!==this.anchor&&!t.data.text;)t=t.next;return t}clear(){this.anchor.next=this.anchor,this.anchor.prev=this.anchor,super.clear()}empty(){return this.anchor.prev===this.anchor&&this.anchor===this.anchor.next}toList(){const t=[];let e=this.anchor.next;for(;e!==this.anchor;)t.push(e.data),e=e.next;return t}}class wn{static create(t,e={}){return t.text=Ot.getInstance().apply(t.text,e),new wn(t)}constructor({context:t,text:e,userValue:n,annotation:r,attributes:o,personality:i,layout:s}){this.context=t||"",this.text=e||"",this.userValue=n||"",this.annotation=r||"",this.attributes=o||{},this.personality=i||{},this.layout=s||""}isEmpty(){return 0===this.context.length&&0===this.text.length&&0===this.userValue.length&&0===this.annotation.length}clone(){let t,e;if(this.personality){t={};for(const[e,n]of Object.entries(this.personality))t[e]=n}if(this.attributes){e={};for(const[t,n]of Object.entries(this.attributes))e[t]=n}return new wn({context:this.context,text:this.text,userValue:this.userValue,annotation:this.annotation,personality:t,attributes:e,layout:this.layout})}toString(){return'AuditoryDescription(context="'+this.context+'"  text="'+this.text+'"  userValue="'+this.userValue+'"  annotation="'+this.annotation+'")'}descriptionString(){return this.context&&this.text?this.context+" "+this.text:this.context||this.text}descriptionSpan(){return Mn.stringAttr(this.descriptionString(),this.attributes)}equals(t){return this.context===t.context&&this.text===t.text&&this.userValue===t.userValue&&this.annotation===t.annotation}}const Dn=new Map;function Pn(t,e,n){const r={};if(e){const t=Dn.get(e)||{};Object.assign(r,t)}Dn.set(t,Object.assign(r,n))}function xn(t,e){const n=t.length;let r=0,o=e;return e||(o=""),function(){return r<n&&(r+=1),o+" "+r}}function Un(t,e){const n=parseFloat(e),r=isNaN(n)?e:n;return function(){return[wn.create({text:"",personality:{pause:r}})]}}function Fn(t,e){let n;return n=t.length>0?v("../../content/*",t[0]):[],function(){const t=n.shift(),r=e?[wn.create({text:e},{translate:!0})]:[];if(!t)return r;const o=R.evaluateNode(t);return r.concat(o)}}function kn(t,e){const n=e.split("-"),r=xn(t,n[0]||""),o=n[1]||"",i=n[2]||"";let s=!0;return function(){const t=r();return s?(s=!1,i+t+o):t+o}}function Bn(t){if(function(t){if(!t.hasAttribute("annotation"))return!1;const e=t.getAttribute("annotation");return!!/clearspeak:simple$|clearspeak:simple;/.exec(e)}(t))return!0;if(t.tagName!==ot.SUBSCRIPT)return!1;const e=t.childNodes[0].childNodes,n=e[1];return e[0].tagName===ot.IDENTIFIER&&(Gn(n)||n.tagName===ot.INFIXOP&&n.hasAttribute("role")&&n.getAttribute("role")===st.IMPLICIT&&function(t){const e=v("children/*",t);return e.every((t=>Gn(t)||t.tagName===ot.IDENTIFIER))}(n))}function Gn(t){return t.tagName===ot.NUMBER&&t.hasAttribute("role")&&t.getAttribute("role")===st.INTEGER}function Vn(t){return v(t.tagName===ot.MATRIX?"children/row/children/cell/children/*":"children/line/children/*",t).every(Bn)?[t]:[]}function Hn(t){return gt(t,20,11)?[t]:[]}function jn(t){const e=parseInt(t.textContent,10);return[Mn.stringEmpty(isNaN(e)?t.textContent:e>10?Nt.NUMBERS.numericOrdinal(e):Nt.NUMBERS.wordOrdinal(e))]}let qn=null;function $n(t){let e=0;const n=t.textContent,r="open"===t.getAttribute("role")?0:1;let o=t.parentNode;for(;o;)o.tagName===ot.FENCED&&o.childNodes[0].childNodes[r].textContent===n&&e++,o=o.parentNode;return qn=e>1?Nt.NUMBERS.wordOrdinal(e):"",[Mn.stringEmpty(qn)]}function Xn(t){const e=t.previousSibling;let n,r;return e?(n=e,r=t):(n=t,r=t.nextSibling),r&&function(t,e){const n=an.Meaning.get(t);return n.type===ot.FENCE&&(n.role===st.NEUTRAL||n.role===st.METRIC?t===e:an.FencesHoriz.get(t)===e||an.FencesVert.get(t)===e)}(n.textContent,r.textContent)?[t]:[]}function Yn(t){const e=D(t.parentNode.childNodes),n=v("../../children/*",t),r=e.indexOf(t);return zn(n[r])||zn(n[r+1])?[t]:[]}function Wn(t){const e=D(t.parentNode.childNodes),n=v("../../children/*",t),r=e.indexOf(t);return Kn(n[r])&&n[r+1]&&(Kn(n[r+1])||n[r+1].tagName===ot.ROOT||n[r+1].tagName===ot.SQRT||n[r+1].tagName===ot.SUPERSCRIPT&&n[r+1].childNodes[0].childNodes[0]&&(n[r+1].childNodes[0].childNodes[0].tagName===ot.NUMBER||n[r+1].childNodes[0].childNodes[0].tagName===ot.IDENTIFIER)&&("2"===n[r+1].childNodes[0].childNodes[1].textContent||"3"===n[r+1].childNodes[0].childNodes[1].textContent))?[t]:[]}function Kn(t){return!!t&&(t.tagName===ot.NUMBER||t.tagName===ot.IDENTIFIER||t.tagName===ot.FUNCTION||t.tagName===ot.APPL||t.tagName===ot.FRACTION)}function zn(t){return t&&(t.tagName===ot.FENCED||t.hasAttribute("role")&&t.getAttribute("role")===st.LEFTRIGHT||function(t){return!!t&&(t.tagName===ot.MATRIX||t.tagName===ot.VECTOR)}(t))}function Qn(t){return[Mn.stringEmpty(Nt.NUMBERS.wordOrdinal(parseInt(t.textContent,10)))]}function Jn(t){return yn(t.textContent+":unit")?[t]:[]}function Zn(t,e){const n=[];for(;t.length||e.length;)t.length&&n.push(t.shift()),e.length&&n.push(e.shift());return n}function tr(t,e){return t?e?t.filter((t=>e.indexOf(t)<0)):t:[]}Ot.getInstance().setCorrection("insertNesting",(function(t,e){if(!e||!t)return t;const n=t.match(/^(open|close) /);return n?n[0]+e+" "+t.substring(n[0].length):e+" "+t}));const er={factory:null,options:new T,updateFactory:function(t){er.factory=t},heuristics:new Map,flags:{combine_juxtaposition:!0,convert_juxtaposition:!0,multioperator:!0},blacklist:{},add:function(t){const e=t.name;er.heuristics.set(e,t),er.flags[e]||(er.flags[e]=!1)},run:function(t,e,n){const r=er.heuristics.get(t);return r&&!er.blacklist[t]&&(er.flags[t]||r.applicable(e))?r.apply(e):n?n(e):e}},nr=[];function rr(t,e){for(let n,r=0;n=nr[r];r++){const r=n.compare(t,e);if(0!==r)return r}return 0}function or(t){if(t.length<=1)return t;const e=t.slice();!function(t){t.sort(rr)}(e);const n=[];let r;do{r=e.pop(),n.push(r)}while(r&&e.length&&0===rr(e[e.length-1],r));return n}function ir(t,e){return t.match(/^.+:.+$/)||!e?t:t+":"+e}new class{constructor(t,e=null){this.comparator=t,this.type=e,function(t){nr.push(t)}(this)}compare(t,e){return this.type&&this.type===t.type&&this.type===e.type?this.comparator(t,e):0}}((function(t,e){return t.role===st.SIMPLEFUNC?1:e.role===st.SIMPLEFUNC?-1:0}),ot.IDENTIFIER);class sr extends Map{set(t,e){return super.set(ir(t,e.font),e),this}setNode(t){this.set(t.textContent,t.meaning())}get(t,e=null){return super.get(ir(t,e))}getNode(t){return this.get(t.textContent,t.font)}}class ar extends Map{add(t,e){const n=this.get(t);n?n.push(e):super.set(t,[e])}get(t,e=null){return super.get(ir(t,e))}getNode(t){return this.get(t.textContent,t.font)}minimize(){for(const[t,e]of this)1===e.length&&this.delete(t)}isMultiValued(){for(const t of this.values())if(t.length>1)return!0;return!1}}class cr extends ar{add(t,e){super.add(ir(t,e.font),e)}addNode(t){this.add(t.textContent,t)}toString(){const t=[];for(const[e,n]of this){const r=Array(e.length+3).join(" "),o=n.map((t=>t.toString())).join("\n"+r);t.push(e+": "+o)}return t.join("\n")}collateMeaning(){const t=new lr;for(const[e,n]of this)t.set(e,n.map((t=>t.meaning())));return t}}class lr extends ar{add(t,e){const n=this.get(t,e.font);n&&n.find((function(t){return r=e,(n=t).type===r.type&&n.role===r.role&&n.font===r.font;var n,r}))||super.add(ir(t,e.font),e)}addNode(t){this.add(t.textContent,t.meaning())}toString(){const t=[];for(const[e,n]of this){const r=Array(e.length+3).join(" "),o=n.map((t=>`{type: ${t.type}, role: ${t.role}, font: ${t.font}}`)).join("\n"+r);t.push(e+": "+o)}return t.join("\n")}reduce(){for(const[t,e]of this)1!==e.length&&this.set(t,or(e))}default(){const t=new sr;for(const[e,n]of this)1===n.length&&t.set(e,n[0]);return t}newDefault(){const t=this.default();this.reduce();const e=this.default();return t.size!==e.size?e:null}}var ur;!function(t){t.ANNOTATION="ANNOTATION",t.ANNOTATIONXML="ANNOTATION-XML",t.MACTION="MACTION",t.MALIGNGROUP="MALIGNGROUP",t.MALIGNMARK="MALIGNMARK",t.MATH="MATH",t.MENCLOSE="MENCLOSE",t.MERROR="MERROR",t.MFENCED="MFENCED",t.MFRAC="MFRAC",t.MGLYPH="MGLYPH",t.MI="MI",t.MLABELEDTR="MLABELEDTR",t.MMULTISCRIPTS="MMULTISCRIPTS",t.MN="MN",t.MO="MO",t.MOVER="MOVER",t.MPADDED="MPADDED",t.MPHANTOM="MPHANTOM",t.MPRESCRIPTS="MPRESCRIPTS",t.MROOT="MROOT",t.MROW="MROW",t.MS="MS",t.MSPACE="MSPACE",t.MSQRT="MSQRT",t.MSTYLE="MSTYLE",t.MSUB="MSUB",t.MSUBSUP="MSUBSUP",t.MSUP="MSUP",t.MTABLE="MTABLE",t.MTD="MTD",t.MTEXT="MTEXT",t.MTR="MTR",t.MUNDER="MUNDER",t.MUNDEROVER="MUNDEROVER",t.NONE="NONE",t.SEMANTICS="SEMANTICS"}(ur||(ur={}));const hr=Object.values(ur),dr=[ur.MO,ur.MI,ur.MN,ur.MTEXT,ur.MS,ur.MSPACE],fr=[ur.MERROR,ur.MPHANTOM,ur.MALIGNGROUP,ur.MALIGNMARK,ur.MPRESCRIPTS,ur.ANNOTATION,ur.ANNOTATIONXML],pr=[ur.MATH,ur.MROW,ur.MPADDED,ur.MACTION,ur.NONE,ur.MSTYLE,ur.SEMANTICS],mr=[ur.MROOT,ur.MSQRT],gr=["aria-label","exact-speech","alt"];function Er(t){return!!t&&W(t)===ur.MATH}function Nr(t){return!!t&&(fr.includes(W(t))||!hr.includes(W(t)))}function Sr(t){return!!t&&pr.includes(W(t))}function Tr(t){return!!t&&W(t)===ur.MGLYPH&&!function(t){return!!t&&dr.includes(W(t))}(t.parentNode)}function Ir(t){const e=[];for(let n,r=0;n=t[r];r++){if(n.nodeType!==x.ELEMENT_NODE)continue;const t=W(n);fr.includes(t)||(pr.includes(t)&&0===n.childNodes.length||e.push(n))}return e}function br(t,e){var n;if(null===(n=e.attributes)||void 0===n?void 0:n.length){const n=e.attributes;for(let e=n.length-1;e>=0;e--){const r=n[e].name;r.match(/^ext/)&&(t.attributes[r]=n[e].value,t.nobreaking=!0),gr.includes(r)&&(t.attributes["ext-speech"]=n[e].value,t.nobreaking=!0),r.match(/texclass$/)&&(t.attributes.texclass=n[e].value),"data-latex"===r.toLowerCase()&&(t.attributes.latex=n[e].value),"href"===r&&(t.attributes.href=n[e].value,t.nobreaking=!0)}}}function Ar(t){return t&&t.embellished&&t.childNodes.length>0?Ar(t.childNodes[0]):t}function Rr(t,e,n){n&&t.reverse();const r=[];for(let o,i=0;o=t[i];i++){if(e(o))return n?{head:t.slice(i+1).reverse(),div:o,tail:r.reverse()}:{head:r,div:o,tail:t.slice(i+1)};r.push(o)}return n?{head:[],div:null,tail:r.reverse()}:{head:r,div:null,tail:[]}}function Or(t,e){let n=t;const r=[],o=[];let i=null;do{i=Rr(n,e),o.push(i.head),r.push(i.div),n=i.tail}while(i.div);return r.pop(),{rel:r,comp:o}}var Cr;!function(t){t.EMBELLISHED="embellished",t.FENCEPOINTER="fencepointer",t.FONT="font",t.ID="id",t.ANNOTATION="annotation",t.ROLE="role",t.TYPE="type",t.CHILDREN="children",t.CONTENT="content",t.TEXT="$t"}(Cr||(Cr={}));class yr{static fromXml(t){const e=parseInt(t.getAttribute("id"),10),n=new yr(e);return n.type=t.tagName,yr.setAttribute(n,t,"role"),yr.setAttribute(n,t,"font"),yr.setAttribute(n,t,"embellished"),yr.setAttribute(n,t,"fencepointer","fencePointer"),t.getAttribute("annotation")&&n.parseAnnotation(t.getAttribute("annotation")),br(n,t),yr.processChildren(n,t),n}static setAttribute(t,e,n,r){r=r||n;const o=e.getAttribute(n);o&&(t[r]=o)}static processChildren(t,e){for(const n of D(e.childNodes)){if(n.nodeType===x.TEXT_NODE){t.textContent=n.textContent;continue}const e=D(n.childNodes).map(yr.fromXml);e.forEach((e=>e.parent=t)),"CONTENT"===W(n)?t.contentNodes=e:t.childNodes=e}}constructor(t){this.id=t,this.mathml=[],this.parent=null,this.type=ot.UNKNOWN,this.role=st.UNKNOWN,this.font=ct.UNKNOWN,this.embellished=null,this.fencePointer="",this.childNodes=[],this.textContent="",this.mathmlTree=null,this.contentNodes=[],this.annotation={},this.attributes={},this.nobreaking=!1}querySelectorAll(t){let e=[];for(let n,r=0;n=this.childNodes[r];r++)e=e.concat(n.querySelectorAll(t));for(let n,r=0;n=this.contentNodes[r];r++)e=e.concat(n.querySelectorAll(t));return t(this)&&e.unshift(this),e}xml(t,e){const n=function(n,r){const o=r.map((function(n){return n.xml(t,e)})),i=t.createElementNS("",n);for(let t,e=0;t=o[e];e++)i.appendChild(t);return i},r=t.createElementNS("",this.type);return e||this.xmlAttributes(r),r.textContent=this.textContent,this.contentNodes.length>0&&r.appendChild(n(Cr.CONTENT,this.contentNodes)),this.childNodes.length>0&&r.appendChild(n(Cr.CHILDREN,this.childNodes)),r}toString(t=!1){const e=P("<snode/>");return z(this.xml(e.ownerDocument,t))}allAttributes(){const t=[];return t.push([Cr.ROLE,this.role]),this.font!==ct.UNKNOWN&&t.push([Cr.FONT,this.font]),Object.keys(this.annotation).length&&t.push([Cr.ANNOTATION,this.annotationXml()]),this.embellished&&t.push([Cr.EMBELLISHED,this.embellished]),this.fencePointer&&t.push([Cr.FENCEPOINTER,this.fencePointer]),t.push([Cr.ID,this.id.toString()]),t}annotationXml(){const t=[];for(const[e,n]of Object.entries(this.annotation))n.forEach((n=>t.push(e+":"+n)));return t.join(";")}attributesXml(){const t=[];for(const[e,n]of Object.entries(this.attributes))t.push(e+":"+yr.escapeValue(n));return t.join(";")}toJson(){const t={};t[Cr.TYPE]=this.type;const e=this.allAttributes();for(let n,r=0;n=e[r];r++)t[n[0]]=n[1].toString();return this.textContent&&(t[Cr.TEXT]=this.textContent),this.childNodes.length&&(t[Cr.CHILDREN]=this.childNodes.map((function(t){return t.toJson()}))),this.contentNodes.length&&(t[Cr.CONTENT]=this.contentNodes.map((function(t){return t.toJson()}))),t}updateContent(t,e){const n=e?t.replace(/^[ \f\n\r\t\v\u200b]*/,"").replace(/[ \f\n\r\t\v\u200b]*$/,""):t.trim();if(t=t&&!n?t:n,this.textContent===t)return;const r=an.Meaning.get(t.replace(/\s/g," "));this.textContent=t,this.role=r.role,this.type=r.type,this.font=r.font}addMathmlNodes(t){for(let e,n=0;e=t[n];n++)-1===this.mathml.indexOf(e)&&this.mathml.push(e)}appendChild(t){this.childNodes.push(t),this.addMathmlNodes(t.mathml),t.parent=this}replaceChild(t,e){const n=this.childNodes.indexOf(t);if(-1===n)return;t.parent=null,e.parent=this,this.childNodes[n]=e;const r=t.mathml.filter((function(t){return-1===e.mathml.indexOf(t)})),o=e.mathml.filter((function(e){return-1===t.mathml.indexOf(e)}));this.removeMathmlNodes(r),this.addMathmlNodes(o)}appendContentNode(t){t&&(this.contentNodes.push(t),this.addMathmlNodes(t.mathml),t.parent=this)}removeContentNode(t){if(t){const e=this.contentNodes.indexOf(t);-1!==e&&this.contentNodes.slice(e,1)}}equals(t){if(!t)return!1;if(this.type!==t.type||this.role!==t.role||this.textContent!==t.textContent||this.childNodes.length!==t.childNodes.length||this.contentNodes.length!==t.contentNodes.length)return!1;for(let e,n,r=0;e=this.childNodes[r],n=t.childNodes[r];r++)if(!e.equals(n))return!1;for(let e,n,r=0;e=this.contentNodes[r],n=t.contentNodes[r];r++)if(!e.equals(n))return!1;return!0}displayTree(){console.info(this.displayTree_(0))}addAnnotation(t,e){e&&this.addAnnotation_(t,e)}getAnnotation(t){const e=this.annotation[t];return e||[]}hasAnnotation(t,e){const n=this.annotation[t];return!!n&&-1!==n.indexOf(e)}parseAnnotation(t){const e=t.split(";");for(let t=0,n=e.length;t<n;t++){const n=e[t].split(":");this.addAnnotation(n[0],n[1])}}meaning(){return{type:this.type,role:this.role,font:this.font}}xmlAttributes(t){const e=this.allAttributes();for(let n,r=0;n=e[r];r++)t.setAttribute(n[0],n[1]);this.addExternalAttributes(t)}addExternalAttributes(t){for(const[e,n]of Object.entries(this.attributes))t.setAttribute(e,n)}static escapeValue(t){return t.replace(/;/g,"\\0003B")}parseAttributes(t){if(!t)return;const e=t.split(";");for(let t=0,n=e.length;t<n;t++){const[n,...r]=e[t].split(":");n&&(this.attributes[n]=r.join("").replace(/\\0003B/g,";"))}}removeMathmlNodes(t){const e=this.mathml;for(let n,r=0;n=t[r];r++){const t=e.indexOf(n);-1!==t&&e.splice(t,1)}this.mathml=e}displayTree_(t){t++;const e=Array(t).join("  ");let n="";n+="\n"+e+this.toString(),n+="\n"+e+"MathmlTree:",n+="\n"+e+this.mathmlTreeString(),n+="\n"+e+"MathML:";for(let t,r=0;t=this.mathml[r];r++)n+="\n"+e+t.toString();return n+="\n"+e+"Begin Content",this.contentNodes.forEach((function(e){n+=e.displayTree_(t)})),n+="\n"+e+"End Content",n+="\n"+e+"Begin Children",this.childNodes.forEach((function(e){n+=e.displayTree_(t)})),n+="\n"+e+"End Children",n}mathmlTreeString(){return this.mathmlTree?this.mathmlTree.toString():"EMPTY"}addAnnotation_(t,e){const n=this.annotation[t];n&&!n.includes(e)?n.push(e):this.annotation[t]=[e]}}class Lr{constructor(){this.leafMap=new cr,this.defaultMap=new sr,this.idCounter_=-1}makeNode(t){return this.createNode_(t)}makeUnprocessed(t){const e=this.createNode_();return e.mathml=[t],e.mathmlTree=t,e}makeEmptyNode(){const t=this.createNode_();return t.type=ot.EMPTY,t}makeContentNode(t){const e=this.createNode_();return e.updateContent(t),e}makeMultipleContentNodes(t,e){const n=[];for(let r=0;r<t;r++)n.push(this.makeContentNode(e));return n}makeLeafNode(t,e){if(!t)return this.makeEmptyNode();const n=this.makeContentNode(t);n.font=e||n.font;const r=this.defaultMap.getNode(n);return r&&(n.type=r.type,n.role=r.role,n.font=r.font),this.leafMap.addNode(n),n}makeBranchNode(t,e,n,r){const o=this.createNode_();return r&&o.updateContent(r),o.type=t,o.childNodes=e,o.contentNodes=n,e.concat(n).forEach((function(t){t.parent=o,o.addMathmlNodes(t.mathml)})),o}createNode_(t){return void 0!==t?this.idCounter_=Math.max(this.idCounter_,t):t=++this.idCounter_,new yr(t)}}function Mr(t,e){return t.type===e}function _r(t,e){return t.embellished===e}function vr(t,e){return t.role===e}function wr(t){return Mr(t,ot.FENCE)||Mr(t,ot.PUNCTUATION)||Mr(t,ot.OPERATOR)||Mr(t,ot.RELATION)}function Dr(t){return Br(t)&&!vr(t,st.DIVISION)||Mr(t,ot.APPL)||Fr(t)}function Pr(t){return Br(t)||Fr(t)}function xr(t,e){return!!e&&Mr(e,ot.IDENTIFIER)&&an.Secondary.has(t.textContent,ut.D)}function Ur(t){if(Mr(t,ot.IDENTIFIER)){const e=t.textContent[0];return e&&t.textContent[1]&&an.Secondary.has(e,ut.D)}return!1}function Fr(t){return Gr(t)||Vr(t)}function kr(t){return t.embellished?t.embellished:(e=t.type)===ot.OPERATOR||e===ot.RELATION||e===ot.FENCE||e===ot.PUNCTUATION?t.type:null;var e}function Br(t){return Mr(t,ot.OPERATOR)||_r(t,ot.OPERATOR)}function Gr(t){return Mr(t,ot.RELATION)||_r(t,ot.RELATION)}function Vr(t){return Mr(t,ot.PUNCTUATION)||_r(t,ot.PUNCTUATION)}function Hr(t){return Mr(t,ot.FENCE)||_r(t,ot.FENCE)}function jr(t){return!(!t||!Hr(t))&&(!t.embellished||qr(t))}function qr(t){return!t.embellished||!function(t){return Mr(t,ot.TENSOR)&&(!Mr(t.childNodes[1],ot.EMPTY)||!Mr(t.childNodes[2],ot.EMPTY))&&(!Mr(t.childNodes[3],ot.EMPTY)||!Mr(t.childNodes[4],ot.EMPTY))}(t)&&((!vr(t,st.CLOSE)||!Mr(t,ot.TENSOR))&&((!vr(t,st.OPEN)||!Mr(t,ot.SUBSCRIPT)&&!Mr(t,ot.SUPERSCRIPT))&&qr(t.childNodes[0])))}function $r(t){return!!t&&(Mr(t,ot.TABLE)||Mr(t,ot.MULTILINE))}function Xr(t){return!!t&&Yr(t)&&$r(t.childNodes[0])}function Yr(t){return!!t&&Mr(t,ot.FENCED)&&(vr(t,st.LEFTRIGHT)||ao(t))&&1===t.childNodes.length}function Wr(t,e){return e.length>0&&vr(e[e.length-1],st.OPENFENCE)}function Kr(t){return t.childNodes.every((function(t){return t.childNodes.length<=1}))}function zr(t){return Mr(t,ot.LARGEOP)||Mr(t,ot.LIMBOTH)||Mr(t,ot.LIMLOWER)||Mr(t,ot.LIMUPPER)||Mr(t,ot.FUNCTION)&&vr(t,st.LIMFUNC)||(Mr(t,ot.OVERSCORE)||Mr(t,ot.UNDERSCORE))&&zr(t.childNodes[0])}function Qr(t,e,n){return 1===e.length&&(t[n].type===ot.PUNCTUATION||t[n].embellished===ot.PUNCTUATION)&&t[n]===e[0]}function Jr(t){return Mr(t,ot.IDENTIFIER)&&vr(t,st.SIMPLEFUNC)}const Zr=[ot.PUNCTUATION,ot.PUNCTUATED,ot.RELSEQ,ot.MULTIREL,ot.TABLE,ot.MULTILINE,ot.CASES,ot.INFERENCE],to=[ot.LIMUPPER,ot.LIMLOWER,ot.LIMBOTH,ot.SUBSCRIPT,ot.SUPERSCRIPT,ot.UNDERSCORE,ot.OVERSCORE,ot.TENSOR];function eo(t){const e=t.type;return-1===Zr.indexOf(e)&&(e!==ot.INFIXOP||t.role===st.IMPLICIT)&&(e===ot.FENCED?t.role!==st.LEFTRIGHT||eo(t.childNodes[0]):-1===to.indexOf(e)||eo(t.childNodes[0]))}function no(t){return function(t){return t.type===ot.NUMBER&&(t.role===st.INTEGER||t.role===st.FLOAT)}(t)||t.role===st.VULGAR||t.role===st.MIXED}function ro(t){const e=t.childNodes;return t.role===st.UNIT&&(!e.length||e[0].role===st.UNIT)}function oo(t){const e=t.childNodes;return t.type===ot.INFIXOP&&(t.role===st.MULTIPLICATION||t.role===st.IMPLICIT)&&e.length&&(ro(e[0])||no(e[0]))&&t.childNodes.slice(1).every(ro)}function io(t){return t.type===ot.INFIXOP&&(t.role===st.IMPLICIT||t.role===st.UNIT&&!!t.contentNodes.length&&t.contentNodes[0].textContent===rn.invisibleTimes)}function so(t){return t.type===ot.INFIXOP&&t.role===st.IMPLICIT}function ao(t){return t.role===st.NEUTRAL||t.role===st.METRIC}function co(t,e){return ao(t)&&ao(e)&&Ar(t).textContent===Ar(e).textContent}function lo(t){return!!ao(t)&&(!t.embellished||t.type!==ot.SUPERSCRIPT&&t.type!==ot.SUBSCRIPT&&(t.type!==ot.TENSOR||t.childNodes[3].type===ot.EMPTY&&t.childNodes[4].type===ot.EMPTY))}function uo(t){return!!ao(t)&&(!t.embellished||(t.type!==ot.TENSOR||t.childNodes[1].type===ot.EMPTY&&t.childNodes[2].type===ot.EMPTY))}function ho(t){return[st.ELEMENT,st.NONELEMENT,st.REELEMENT,st.RENONELEMENT].includes(t.role)}class fo{static getInstance(){return fo.instance=fo.instance||new fo,fo.instance}static tableToMultiline(t){if(!Kr(t))return er.run("rewrite_subcases",t,fo.classifyTable);t.type=ot.MULTILINE;for(let e,n=0;e=t.childNodes[n];n++)fo.rowToLine_(e,st.MULTILINE);var e;return 1!==t.childNodes.length||Mr(e=t.childNodes[0],ot.LINE)&&e.contentNodes.length&&vr(e.contentNodes[0],st.LABEL)||!Yr(t.childNodes[0].childNodes[0])||fo.tableToMatrixOrVector_(fo.rewriteFencedLine_(t)),fo.binomialForm_(t),fo.classifyMultiline(t),t}static number(t){t.type!==ot.UNKNOWN&&t.type!==ot.IDENTIFIER||(t.type=ot.NUMBER),fo.meaningFromContent(t,fo.numberRole_),fo.exprFont_(t)}static classifyMultiline(t){let e=0;const n=t.childNodes.length;let r;for(;e<n&&(!(r=t.childNodes[e])||!r.childNodes.length);)e++;if(e>=n)return;const o=r.childNodes[0].role;o!==st.UNKNOWN&&t.childNodes.every((function(t){const e=t.childNodes[0];return!e||e.role===o&&(Mr(e,ot.RELATION)||Mr(e,ot.RELSEQ))}))&&(t.role=o)}static classifyTable(t){const e=fo.computeColumns_(t);return fo.classifyByColumns_(t,e,st.EQUALITY)||fo.classifyByColumns_(t,e,st.INEQUALITY,[st.EQUALITY])||fo.classifyByColumns_(t,e,st.ARROW)||fo.detectCaleyTable(t),t}static detectCaleyTable(t){if(!t.mathmlTree)return!1;const e=t.mathmlTree,n=e.getAttribute("columnlines"),r=e.getAttribute("rowlines");return!(!n||!r)&&(!(!fo.cayleySpacing(n)||!fo.cayleySpacing(r))&&(t.role=st.CAYLEY,!0))}static cayleySpacing(t){const e=t.split(" ");return("solid"===e[0]||"dashed"===e[0])&&e.slice(1).every((t=>"none"===t))}static proof(t,e,n){const r=fo.separateSemantics(e);return fo.getInstance().proof(t,r,n)}static findSemantics(t,e,n){const r=null==n?null:n,o=fo.getSemantics(t);return!!o&&(!!o[e]&&(null==r||o[e]===r))}static getSemantics(t){const e=t.getAttribute("semantics");return e?fo.separateSemantics(e):null}static removePrefix(t){const[,...e]=t.split("_");return e.join("_")}static separateSemantics(t){const e={};return t.split(";").forEach((function(t){const[n,r]=t.split(":");e[fo.removePrefix(n)]=r})),e}static matchSpaces_(t,e){for(let n,r=0;n=e[r];r++){const e=t[r].mathmlTree,o=t[r+1].mathmlTree;if(!e||!o)continue;const i=e.nextSibling;if(!i||i===o)continue;const s=fo.getSpacer_(i);s&&(n.mathml.push(s),n.mathmlTree=s,n.role=st.SPACE)}}static getSpacer_(t){if(W(t)===ur.MSPACE)return t;for(;Sr(t)&&1===t.childNodes.length;)if(W(t=t.childNodes[0])===ur.MSPACE)return t;return null}static fenceToPunct_(t){const e=fo.FENCE_TO_PUNCT_[t.role];if(e){for(;t.embellished;)t.embellished=ot.PUNCTUATION,vr(t,st.SUBSUP)||vr(t,st.UNDEROVER)||(t.role=e),t=t.childNodes[0];t.type=ot.PUNCTUATION,t.role=e}}static classifyFunction_(t,e){if(t.type===ot.APPL||t.type===ot.BIGOP||t.type===ot.INTEGRAL)return"";if(e[0]&&e[0].textContent===rn.functionApplication){fo.getInstance().funcAppls[t.id]=e.shift();let n=st.SIMPLEFUNC;return er.run("simple2prefix",t),t.role!==st.PREFIXFUNC&&t.role!==st.LIMFUNC||(n=t.role),fo.propagateFunctionRole_(t,n),"prefix"}const n=fo.CLASSIFY_FUNCTION_[t.role];return n||((r=t).type===ot.IDENTIFIER||r.role===st.LATINLETTER||r.role===st.GREEKLETTER||r.role===st.OTHERLETTER?"simple":"");var r}static propagateFunctionRole_(t,e){if(t){if(t.type===ot.INFIXOP)return;vr(t,st.SUBSUP)||vr(t,st.UNDEROVER)||(t.role=e),fo.propagateFunctionRole_(t.childNodes[0],e)}}static getFunctionOp_(t,e){if(e(t))return t;for(let n,r=0;n=t.childNodes[r];r++){const t=fo.getFunctionOp_(n,e);if(t)return t}return null}static tableToMatrixOrVector_(t){const e=t.childNodes[0];Mr(e,ot.MULTILINE)?fo.tableToVector_(t):fo.tableToMatrix_(t),t.contentNodes.forEach(e.appendContentNode.bind(e));for(let t,n=0;t=e.childNodes[n];n++)fo.assignRoleToRow_(t,fo.getComponentRoles_(e));return e.parent=null,e}static tableToVector_(t){const e=t.childNodes[0];e.type=ot.VECTOR,1!==e.childNodes.length?fo.binomialForm_(e):fo.tableToSquare_(t)}static binomialForm_(t){vr(t,st.UNKNOWN)&&(2===t.childNodes.length&&(t.role=st.BINOMIAL,t.childNodes[0].role=st.BINOMIAL,t.childNodes[1].role=st.BINOMIAL))}static tableToMatrix_(t){const e=t.childNodes[0];e.type=ot.MATRIX,e.childNodes&&e.childNodes.length>0&&e.childNodes[0].childNodes&&e.childNodes.length===e.childNodes[0].childNodes.length?fo.tableToSquare_(t):e.childNodes&&1===e.childNodes.length&&(e.role=st.ROWVECTOR)}static tableToSquare_(t){const e=t.childNodes[0];vr(e,st.UNKNOWN)&&(ao(t)?e.role=st.DETERMINANT:e.role=st.SQUAREMATRIX)}static getComponentRoles_(t){const e=t.role;return e&&e!==st.UNKNOWN?e:t.type.toLowerCase()||st.UNKNOWN}static tableToCases_(t,e){for(let e,n=0;e=t.childNodes[n];n++)fo.assignRoleToRow_(e,st.CASES);return t.type=ot.CASES,t.appendContentNode(e),Kr(t)&&fo.binomialForm_(t),t}static rewriteFencedLine_(t){const e=t.childNodes[0],n=t.childNodes[0].childNodes[0],r=t.childNodes[0].childNodes[0].childNodes[0];return n.parent=t.parent,t.parent=n,r.parent=e,n.childNodes=[t],e.childNodes=[r],n}static rowToLine_(t,e){const n=e||st.UNKNOWN;Mr(t,ot.ROW)&&(t.type=ot.LINE,t.role=n,1===t.childNodes.length&&Mr(t.childNodes[0],ot.CELL)&&(t.childNodes=t.childNodes[0].childNodes,t.childNodes.forEach((function(e){e.parent=t}))))}static assignRoleToRow_(t,e){Mr(t,ot.LINE)?t.role=e:Mr(t,ot.ROW)&&(t.role=e,t.childNodes.forEach((function(t){Mr(t,ot.CELL)&&(t.role=e)})))}static nextSeparatorFunction_(t){let e;if(t){if(t.match(/^\s+$/))return null;e=t.replace(/\s/g,"").split("").filter((function(t){return t}))}else e=[","];return function(){return e.length>1?e.shift():e[0]}}static meaningFromContent(t,e){const n=[...t.textContent].filter((t=>t.match(/[^\s]/))),r=n.map((t=>an.Meaning.get(t)));e(t,n,r)}static numberRole_(t,e,n){if(t.role===st.UNKNOWN)return n.every((function(t){return t.type===ot.NUMBER&&t.role===st.INTEGER||t.type===ot.PUNCTUATION&&t.role===st.COMMA}))?(t.role=st.INTEGER,void("0"===e[0]&&t.addAnnotation("general","basenumber"))):void(n.every((function(t){return t.type===ot.NUMBER&&t.role===st.INTEGER||t.type===ot.PUNCTUATION}))?t.role=st.FLOAT:t.role=st.OTHERNUMBER)}static exprFont_(t){t.font===ct.UNKNOWN&&fo.compSemantics(t,"font",ct)}static compSemantics(t,e,n){const r=[...t.textContent].map((t=>an.Meaning.get(t))).reduce((function(t,r){return t&&r[e]&&r[e]!==n.UNKNOWN&&r[e]!==t?t===n.UNKNOWN?r[e]:null:t}),n.UNKNOWN);r&&(t[e]=r)}static purgeFences_(t){const e=t.rel,n=t.comp,r=[],o=[];for(;e.length>0;){const t=e.shift();let i=n.shift();jr(t)?(r.push(t),o.push(i)):(fo.fenceToPunct_(t),i.push(t),i=i.concat(n.shift()),n.unshift(i))}return o.push(n.shift()),{rel:r,comp:o}}static rewriteFencedNode_(t){const e=t.contentNodes[0],n=t.contentNodes[1];let r=fo.rewriteFence_(t,e);return t.contentNodes[0]=r.fence,r=fo.rewriteFence_(r.node,n),t.contentNodes[1]=r.fence,t.contentNodes[0].parent=t,t.contentNodes[1].parent=t,r.node.parent=null,r.node}static rewriteFence_(t,e){if(!e.embellished)return{node:t,fence:e};const n=e.childNodes[0],r=fo.rewriteFence_(t,n);return Mr(e,ot.SUPERSCRIPT)||Mr(e,ot.SUBSCRIPT)||Mr(e,ot.TENSOR)?(vr(e,st.SUBSUP)||(e.role=t.role),n!==r.node&&(e.replaceChild(n,r.node),n.parent=t),fo.propagateFencePointer_(e,n),{node:e,fence:r.fence}):(e.replaceChild(n,r.fence),e.mathmlTree&&-1===e.mathml.indexOf(e.mathmlTree)&&e.mathml.push(e.mathmlTree),{node:r.node,fence:e})}static propagateFencePointer_(t,e){t.fencePointer=e.fencePointer||e.id.toString(),t.embellished=null}static classifyByColumns_(t,e,n,r=[]){const o=[n].concat(r);return!!(3===e.length&&fo.testColumns_(e,1,(t=>fo.isPureRelation_(t,o)))||2===e.length&&(fo.testColumns_(e,1,(t=>fo.isEndRelation_(t,o)||fo.isPureRelation_(t,o)))||fo.testColumns_(e,0,(t=>fo.isEndRelation_(t,o,!0)||fo.isPureRelation_(t,o)))))&&(t.role=n,!0)}static isEndRelation_(t,e,n){const r=n?t.childNodes.length-1:0;return Mr(t,ot.RELSEQ)&&e.some((e=>vr(t,e)))&&Mr(t.childNodes[r],ot.EMPTY)}static isPureRelation_(t,e){return Mr(t,ot.RELATION)&&e.some((e=>vr(t,e)))}static computeColumns_(t){const e=[];for(let n,r=0;n=t.childNodes[r];r++)for(let t,r=0;t=n.childNodes[r];r++){e[r]?e[r].push(t):e[r]=[t]}return e}static testColumns_(t,e,n){const r=t[e];return!!r&&(r.some((function(t){return t.childNodes.length&&n(t.childNodes[0])}))&&r.every((function(t){return!t.childNodes.length||n(t.childNodes[0])})))}setNodeFactory(t){fo.getInstance().factory_=t,er.updateFactory(fo.getInstance().factory_)}getNodeFactory(){return fo.getInstance().factory_}identifierNode(t,e,n){if("MathML-Unit"===n)t.type=ot.IDENTIFIER,t.role=st.UNIT;else if(!e&&1===t.textContent.length&&(t.role===st.INTEGER||t.role===st.LATINLETTER||t.role===st.GREEKLETTER)&&t.font===ct.NORMAL)return t.font=ct.ITALIC,er.run("simpleNamedFunction",t);return t.type===ot.UNKNOWN&&(t.type=ot.IDENTIFIER),fo.exprFont_(t),er.run("simpleNamedFunction",t)}implicitNode(t){if(t=fo.getInstance().getMixedNumbers_(t),1===(t=fo.getInstance().combineUnits_(t)).length)return t[0];const e=fo.getInstance().implicitNode_(t);return er.run("combine_juxtaposition",e)}text(t,e){return fo.exprFont_(t),t.type=ot.TEXT,e===ur.ANNOTATIONXML?(t.role=st.ANNOTATION,t):e===ur.MS?(t.role=st.STRING,t):e===ur.MSPACE||t.textContent.match(/^\s*$/)?(t.role=st.SPACE,t):/\s/.exec(t.textContent)?(t.role=st.TEXT,t):(t.role=st.UNKNOWN,t)}row(t){return 0===(t=t.filter((function(t){return!Mr(t,ot.EMPTY)}))).length?fo.getInstance().factory_.makeEmptyNode():(t=fo.getInstance().getFencesInRow_(t),t=fo.getInstance().tablesInRow(t),t=fo.getInstance().getPunctuationInRow_(t),t=fo.getInstance().getTextInRow_(t),t=fo.getInstance().getFunctionsInRow_(t),fo.getInstance().relationsInRow_(t))}limitNode(t,e){if(!e.length)return fo.getInstance().factory_.makeEmptyNode();let n,r=e[0],o=ot.UNKNOWN;if(!e[1])return r;if(er.run("op_with_limits",e),zr(r)){n=fo.MML_TO_LIMIT_[t];const i=n.length;if(o=n.type,e=e.slice(0,n.length+1),1===i&&wr(e[1])||2===i&&wr(e[1])&&wr(e[2]))return n=fo.MML_TO_BOUNDS_[t],fo.getInstance().accentNode_(r,e,n.type,n.length,n.accent);if(2===i){if(wr(e[1]))return r=fo.getInstance().accentNode_(r,[r,e[1]],{MSUBSUP:ot.SUBSCRIPT,MUNDEROVER:ot.UNDERSCORE}[t],1,!0),e[2]?fo.getInstance().makeLimitNode_(r,[r,e[2]],null,ot.LIMUPPER):r;if(e[2]&&wr(e[2]))return r=fo.getInstance().accentNode_(r,[r,e[2]],{MSUBSUP:ot.SUPERSCRIPT,MUNDEROVER:ot.OVERSCORE}[t],1,!0),fo.getInstance().makeLimitNode_(r,[r,e[1]],null,ot.LIMLOWER);e[i]||(o=ot.LIMLOWER)}return fo.getInstance().makeLimitNode_(r,e,null,o)}return n=fo.MML_TO_BOUNDS_[t],fo.getInstance().accentNode_(r,e,n.type,n.length,n.accent)}tablesInRow(t){let e=Or(t,Xr),n=[];for(let t,r=0;t=e.rel[r];r++)n=n.concat(e.comp.shift()),n.push(fo.tableToMatrixOrVector_(t));n=n.concat(e.comp.shift()),e=Or(n,$r),n=[];for(let t,r=0;t=e.rel[r];r++){const r=e.comp.shift();Wr(0,r)&&fo.tableToCases_(t,r.pop()),n=n.concat(r),n.push(t)}return n.concat(e.comp.shift())}mfenced(t,e,n,r){if(n&&r.length>0){const t=fo.nextSeparatorFunction_(n),e=[r.shift()];r.forEach((n=>{e.push(fo.getInstance().factory_.makeContentNode(t())),e.push(n)})),r=e}return t&&e?fo.getInstance().horizontalFencedNode_(fo.getInstance().factory_.makeContentNode(t),fo.getInstance().factory_.makeContentNode(e),r):(t&&r.unshift(fo.getInstance().factory_.makeContentNode(t)),e&&r.push(fo.getInstance().factory_.makeContentNode(e)),fo.getInstance().row(r))}fractionLikeNode(t,e,n,r){let o;if(!r&&function(t){if(!t)return!1;if(["negativeveryverythinmathspace","negativeverythinmathspace","negativethinmathspace","negativemediummathspace","negativethickmathspace","negativeverythickmathspace","negativeveryverythickmathspace"].includes(t))return!0;const e=t.match(/[0-9.]+/);return!!e&&0===parseFloat(e[0])}(n)){const n=fo.getInstance().factory_.makeBranchNode(ot.LINE,[t],[]),r=fo.getInstance().factory_.makeBranchNode(ot.LINE,[e],[]);return o=fo.getInstance().factory_.makeBranchNode(ot.MULTILINE,[n,r],[]),fo.binomialForm_(o),fo.classifyMultiline(o),o}return o=fo.getInstance().fractionNode_(t,e),r&&o.addAnnotation("general","bevelled"),o}tensor(t,e,n,r,o){const i=fo.getInstance().factory_.makeBranchNode(ot.TENSOR,[t,fo.getInstance().scriptNode_(e,st.LEFTSUB),fo.getInstance().scriptNode_(n,st.LEFTSUPER),fo.getInstance().scriptNode_(r,st.RIGHTSUB),fo.getInstance().scriptNode_(o,st.RIGHTSUPER)],[]);return i.role=t.role,i.embellished=kr(t),i}pseudoTensor(t,e,n){const r=t=>!Mr(t,ot.EMPTY),o=e.filter(r).length,i=n.filter(r).length;if(!o&&!i)return t;const s=o?i?ur.MSUBSUP:ur.MSUB:ur.MSUP,a=[t];return o&&a.push(fo.getInstance().scriptNode_(e,st.RIGHTSUB,!0)),i&&a.push(fo.getInstance().scriptNode_(n,st.RIGHTSUPER,!0)),fo.getInstance().limitNode(s,a)}font(t){const e=fo.MATHJAX_FONTS[t];return e||t}proof(t,e,n){if(e.inference||e.axiom||console.log("Noise"),e.axiom){const e=fo.getInstance().cleanInference(t.childNodes),r=e.length?fo.getInstance().factory_.makeBranchNode(ot.INFERENCE,n(e),[]):fo.getInstance().factory_.makeEmptyNode();return r.role=st.AXIOM,r.mathmlTree=t,r}const r=fo.getInstance().inference(t,e,n);return e.proof&&(r.role=st.PROOF,r.childNodes[0].role=st.FINAL),r}inference(t,e,n){if(e.inferenceRule){const e=fo.getInstance().getFormulas(t,[],n);return fo.getInstance().factory_.makeBranchNode(ot.INFERENCE,[e.conclusion,e.premises],[])}const r=e.labelledRule,o=D(t.childNodes),i=[];"left"!==r&&"both"!==r||i.push(fo.getInstance().getLabel(t,o,n,st.LEFT)),"right"!==r&&"both"!==r||i.push(fo.getInstance().getLabel(t,o,n,st.RIGHT));const s=fo.getInstance().getFormulas(t,o,n),a=fo.getInstance().factory_.makeBranchNode(ot.INFERENCE,[s.conclusion,s.premises],i);return a.mathmlTree=t,a}getLabel(t,e,n,r){const o=fo.getInstance().findNestedRow(e,"prooflabel",r),i=fo.getInstance().factory_.makeBranchNode(ot.RULELABEL,n(D(o.childNodes)),[]);return i.role=r,i.mathmlTree=o,i}getFormulas(t,e,n){const r=e.length?fo.getInstance().findNestedRow(e,"inferenceRule"):t,o="up"===fo.getSemantics(r).inferenceRule,i=o?r.childNodes[1]:r.childNodes[0],s=o?r.childNodes[0]:r.childNodes[1],a=i.childNodes[0].childNodes[0],c=D(a.childNodes[0].childNodes),l=[];let u=1;for(const t of c)u%2&&l.push(t.childNodes[0]),u++;const h=n(l),d=n(D(s.childNodes[0].childNodes))[0],f=fo.getInstance().factory_.makeBranchNode(ot.PREMISES,h,[]);f.mathmlTree=a;const p=fo.getInstance().factory_.makeBranchNode(ot.CONCLUSION,[d],[]);return p.mathmlTree=s.childNodes[0].childNodes[0],{conclusion:p,premises:f}}findNestedRow(t,e,n){return fo.getInstance().findNestedRow_(t,e,0,n)}cleanInference(t){return D(t).filter((function(t){return"MSPACE"!==W(t)}))}operatorNode(t){return t.type===ot.UNKNOWN&&(t.type=ot.OPERATOR),er.run("multioperator",t)}constructor(){this.funcAppls={},this.splitRoles=new Map([[st.SUBTRACTION,st.NEGATIVE],[st.ADDITION,st.POSITIVE]]),this.splitOps=["\u2212","-","\u2010","\u2011","+"],this.factory_=new Lr,er.updateFactory(this.factory_)}implicitNode_(t){const e=fo.getInstance().factory_.makeMultipleContentNodes(t.length-1,rn.invisibleTimes);fo.matchSpaces_(t,e);const n=fo.getInstance().infixNode_(t,e[0]);return n.role=st.IMPLICIT,e.forEach((function(t){t.parent=n})),n.contentNodes=e,n}infixNode_(t,e){let n=fo.getInstance().factory_.makeBranchNode(ot.INFIXOP,t,[e],Ar(e).textContent);return n.role=e.role,n=er.run("propagateInterval",n),er.run("propagateSimpleFunction",n)}explicitMixed_(t){const e=Or(t,(function(t){return t.textContent===rn.invisiblePlus}));if(!e.rel.length)return t;let n=[];for(let t,r=0;t=e.rel[r];r++){const o=e.comp[r],i=e.comp[r+1],s=o.length-1;if(o[s]&&i[0]&&Mr(o[s],ot.NUMBER)&&!vr(o[s],st.MIXED)&&Mr(i[0],ot.FRACTION)){const t=fo.getInstance().factory_.makeBranchNode(ot.NUMBER,[o[s],i[0]],[]);t.role=st.MIXED,n=n.concat(o.slice(0,s)),n.push(t),i.shift()}else n=n.concat(o),n.push(t)}return n.concat(e.comp[e.comp.length-1])}concatNode_(t,e,n){if(0===e.length)return t;const r=e.map((function(t){return Ar(t).textContent})).join(" "),o=fo.getInstance().factory_.makeBranchNode(n,[t],e,r);return e.length>1&&(o.role=st.MULTIOP),o}prefixNode_(t,e){const n=this.splitSingles(e);let r=t;for(;n.length>0;){const t=n.pop();r=fo.getInstance().concatNode_(r,t,ot.PREFIXOP),1===t.length&&-1!==this.splitOps.indexOf(t[0].textContent)&&(r.role=this.splitRoles.get(t[0].role))}return r}splitSingles(t){let e=0;const n=[];let r=0;for(;r<t.length;){const o=t[r];!this.splitRoles.has(o.role)||t[r-1]&&t[r-1].role===o.role||t[r+1]&&t[r+1].role===o.role||-1===this.splitOps.indexOf(o.textContent)||(n.push(t.slice(e,r)),n.push(t.slice(r,r+1)),e=r+1),r++}return e<r&&n.push(t.slice(e,r)),n}postfixNode_(t,e){return e.length?fo.getInstance().concatNode_(t,e,ot.POSTFIXOP):t}combineUnits_(t){const e=Or(t,(function(t){return!vr(t,st.UNIT)}));if(t.length===e.rel.length)return e.rel;const n=[];let r,o;do{const t=e.comp.shift();r=e.rel.shift();let i=null;o=n.pop(),o&&(t.length&&no(o)?t.unshift(o):n.push(o)),1===t.length&&(i=t.pop()),t.length>1&&(i=fo.getInstance().implicitNode_(t),i.role=st.UNIT),i&&n.push(i),r&&n.push(r)}while(r);return n}getMixedNumbers_(t){const e=Or(t,(function(t){return Mr(t,ot.FRACTION)&&vr(t,st.VULGAR)}));if(!e.rel.length)return t;let n=[];for(let t,r=0;t=e.rel[r];r++){const o=e.comp[r],i=o.length-1;if(o[i]&&Mr(o[i],ot.NUMBER)&&(vr(o[i],st.INTEGER)||vr(o[i],st.FLOAT))){const e=fo.getInstance().factory_.makeBranchNode(ot.NUMBER,[o[i],t],[]);e.role=st.MIXED,n=n.concat(o.slice(0,i)),n.push(e)}else n=n.concat(o),n.push(t)}return n.concat(e.comp[e.comp.length-1])}getTextInRow_(t){if(0===t.length)return t;if(1===t.length)return t[0].type===ot.TEXT&&t[0].role===st.UNKNOWN&&(t[0].role=st.ANNOTATION),t;const{rel:e,comp:n}=Or(t,(t=>Mr(t,ot.TEXT)));if(0===e.length)return t;const r=[];let o=n.shift();for(;e.length>0;){let t=e.shift(),i=n.shift();const s=[];for(;!i.length&&e.length&&t.role!==st.SPACE&&e[0].role!==st.SPACE;)s.push(t),t=e.shift(),i=n.shift();if(s.length){o.length&&r.push(fo.getInstance().row(o)),s.push(t);const e=fo.getInstance().dummyNode_(s);r.push(e),o=i;continue}if(t.role!==st.UNKNOWN){o.length&&r.push(fo.getInstance().row(o)),r.push(t),o=i;continue}const a=an.Meaning.get(t.textContent);a.type!==ot.PUNCTUATION?a.type===ot.UNKNOWN?(fo.meaningFromContent(t,((t,e,n)=>{if(t.role===st.UNKNOWN){if(fo.numberRole_(t,e,n),t.role===st.OTHERNUMBER)return n.some((t=>t.type!==ot.NUMBER&&t.type!==ot.IDENTIFIER))?(t.type=ot.TEXT,void(t.role=st.ANNOTATION)):void(t.role=st.UNKNOWN);t.type=ot.NUMBER}})),t.type!==ot.TEXT||t.role===st.UNKNOWN?(t.role===st.UNKNOWN&&(e.length||i.length?i.length&&i[0].type===ot.FENCED?(t.type=ot.FUNCTION,t.role=st.PREFIXFUNC):t.role=st.TEXT:(t.type=ot.IDENTIFIER,t.role=st.UNIT)),o.push(t),o=o.concat(i)):(o.length&&r.push(fo.getInstance().row(o)),r.push(t),o=i)):(t.type=a.type,t.role=a.role,t.font=a.font,t.addAnnotation("general","text"),o.push(t),o=o.concat(i)):(t.role=a.role,t.font=a.font,o.length&&r.push(fo.getInstance().row(o)),r.push(t),o=i)}return o.length>0&&r.push(fo.getInstance().row(o)),r.length>1?[fo.getInstance().dummyNode_(r)]:r}relationsInRow_(t){const e=Or(t,Gr),n=e.rel[0];if(!n)return fo.getInstance().operationsInRow_(t);if(1===t.length)return t[0];const r=e.comp.map(fo.getInstance().operationsInRow_);let o;return e.rel.some((function(t){return!t.equals(n)}))?(o=fo.getInstance().factory_.makeBranchNode(ot.MULTIREL,r,e.rel),e.rel.every((function(t){return t.role===n.role}))&&(o.role=n.role),o):(o=fo.getInstance().factory_.makeBranchNode(ot.RELSEQ,r,e.rel,Ar(n).textContent),o.role=n.role,o)}operationsInRow_(t){if(0===t.length)return fo.getInstance().factory_.makeEmptyNode();if(1===(t=fo.getInstance().explicitMixed_(t)).length)return t[0];const e=[];for(;t.length>0&&Br(t[0]);)e.push(t.shift());if(0===t.length)return fo.getInstance().prefixNode_(e.pop(),e);if(1===t.length)return fo.getInstance().prefixNode_(t[0],e);const n=Rr(t=er.run("convert_juxtaposition",t),Br),r=fo.getInstance().wrapFactor(e,n);return fo.getInstance().addFactor(r,n)}wrapPostfix(t){var e;(null===(e=t.div)||void 0===e?void 0:e.role)===st.POSTFIXOP&&(t.tail.length&&t.tail[0].type!==ot.OPERATOR?t.div.role=st.DIVISION:(t.head=[fo.getInstance().postfixNode_(fo.getInstance().implicitNode(t.head),[t.div])],t.div=t.tail.shift(),fo.getInstance().wrapPostfix(t)))}wrapFactor(t,e){return fo.getInstance().wrapPostfix(e),fo.getInstance().prefixNode_(fo.getInstance().implicitNode(e.head),t)}addFactor(t,e){return e.div?fo.getInstance().operationsTree_(e.tail,t,e.div):(oo(t)&&(t.role=st.UNIT),t)}operationsTree_(t,e,n,r=[]){if(0===t.length){if(r.unshift(n),e.type===ot.INFIXOP){const t=fo.getInstance().postfixNode_(e.childNodes.pop(),r);return e.appendChild(t),e}return fo.getInstance().postfixNode_(e,r)}const o=Rr(t,Br);if(0===o.head.length)return r.push(o.div),fo.getInstance().operationsTree_(o.tail,e,n,r);const i=fo.getInstance().wrapFactor(r,o),s=fo.getInstance().appendOperand_(e,n,i);return fo.getInstance().addFactor(s,o)}appendOperand_(t,e,n){if(t.type!==ot.INFIXOP)return fo.getInstance().infixNode_([t,n],e);const r=fo.getInstance().appendDivisionOp_(t,e,n);return r||(fo.getInstance().appendExistingOperator_(t,e,n)?t:e.role===st.MULTIPLICATION?fo.getInstance().appendMultiplicativeOp_(t,e,n):fo.getInstance().appendAdditiveOp_(t,e,n))}appendDivisionOp_(t,e,n){return e.role===st.DIVISION?io(t)?fo.getInstance().infixNode_([t,n],e):fo.getInstance().appendLastOperand_(t,e,n):t.role===st.DIVISION?fo.getInstance().infixNode_([t,n],e):null}appendLastOperand_(t,e,n){let r=t,o=t.childNodes[t.childNodes.length-1];for(;o&&o.type===ot.INFIXOP&&!io(o);)r=o,o=r.childNodes[t.childNodes.length-1];const i=fo.getInstance().infixNode_([r.childNodes.pop(),n],e);return r.appendChild(i),t}appendMultiplicativeOp_(t,e,n){if(io(t))return fo.getInstance().infixNode_([t,n],e);let r=t,o=t.childNodes[t.childNodes.length-1];for(;o&&o.type===ot.INFIXOP&&!io(o);)r=o,o=r.childNodes[t.childNodes.length-1];const i=fo.getInstance().infixNode_([r.childNodes.pop(),n],e);return r.appendChild(i),t}appendAdditiveOp_(t,e,n){return fo.getInstance().infixNode_([t,n],e)}appendExistingOperator_(t,e,n){return!(!t||t.type!==ot.INFIXOP||io(t))&&(t.contentNodes[0].equals(e)?(t.appendContentNode(e),t.appendChild(n),!0):fo.getInstance().appendExistingOperator_(t.childNodes[t.childNodes.length-1],e,n))}getFencesInRow_(t){let e=Or(t,Hr);e=fo.purgeFences_(e);const n=e.comp.shift();return fo.getInstance().fences_(e.rel,e.comp,[],[n])}fences_(t,e,n,r){if(0===t.length&&0===n.length)return r[0];const o=er.run("bracketed_interval",[t[0],t[1],...e[0]||[]],(()=>null));if(o){t.shift(),t.shift(),e.shift();const i=r.pop()||[];return r.push([...i,o,...e.shift()]),fo.getInstance().fences_(t,e,n,r)}const i=t=>vr(t,st.OPEN);if(0===t.length){const t=r.shift();for(;n.length>0;){if(i(n[0])){const e=n.shift();fo.fenceToPunct_(e),t.push(e)}else{const e=Rr(n,i),o=e.head.length-1,s=fo.getInstance().neutralFences_(e.head,r.slice(0,o));r=r.slice(o),t.push(...s),e.div&&e.tail.unshift(e.div),n=e.tail}t.push(...r.shift())}return t}const s=n[n.length-1],a=t[0].role;if(a===st.OPEN||ao(t[0])&&(!s||!co(t[0],s))){n.push(t.shift());const o=e.shift();return o&&r.push(o),fo.getInstance().fences_(t,e,n,r)}if(s&&a===st.CLOSE&&s.role===st.OPEN){const o=fo.getInstance().horizontalFencedNode_(n.pop(),t.shift(),r.pop());return r.push(r.pop().concat([o],e.shift())),fo.getInstance().fences_(t,e,n,r)}if(s&&co(t[0],s)){if(!lo(s)||!uo(t[0])){n.push(t.shift());const o=e.shift();return o&&r.push(o),fo.getInstance().fences_(t,e,n,r)}const o=fo.getInstance().horizontalFencedNode_(n.pop(),t.shift(),r.pop());return r.push(r.pop().concat([o],e.shift())),fo.getInstance().fences_(t,e,n,r)}if(s&&a===st.CLOSE&&ao(s)&&n.some(i)){const o=Rr(n,i,!0),s=r.pop(),a=r.length-o.tail.length+1,c=fo.getInstance().neutralFences_(o.tail,r.slice(a));r=r.slice(0,a);const l=fo.getInstance().horizontalFencedNode_(o.div,t.shift(),r.pop().concat(c,s));return r.push(r.pop().concat([l],e.shift())),fo.getInstance().fences_(t,e,o.head,r)}const c=t.shift();return fo.fenceToPunct_(c),r.push(r.pop().concat([c],e.shift())),fo.getInstance().fences_(t,e,n,r)}neutralFences_(t,e){if(0===t.length)return t;if(1===t.length)return fo.fenceToPunct_(t[0]),t;const n=t.shift();if(!lo(n)){fo.fenceToPunct_(n);const r=e.shift();return r.unshift(n),r.concat(fo.getInstance().neutralFences_(t,e))}const r=Rr(t,(function(t){return co(t,n)}));if(!r.div){fo.fenceToPunct_(n);const r=e.shift();return r.unshift(n),r.concat(fo.getInstance().neutralFences_(t,e))}if(!uo(r.div))return fo.fenceToPunct_(r.div),t.unshift(n),fo.getInstance().neutralFences_(t,e);const o=fo.getInstance().combineFencedContent_(n,r.div,r.head,e);if(r.tail.length>0){const t=o.shift(),e=fo.getInstance().neutralFences_(r.tail,o);return t.concat(e)}return o[0]}combineFencedContent_(t,e,n,r){if(0===n.length){const n=fo.getInstance().horizontalFencedNode_(t,e,r.shift());return r.length>0?r[0].unshift(n):r=[[n]],r}const o=r.shift(),i=n.length-1,s=r.slice(0,i),a=(r=r.slice(i)).shift(),c=fo.getInstance().neutralFences_(n,s);o.push(...c),o.push(...a);const l=fo.getInstance().horizontalFencedNode_(t,e,o);return r.length>0?r[0].unshift(l):r=[[l]],r}horizontalFencedNode_(t,e,n){const r=fo.getInstance().row(n);let o=fo.getInstance().factory_.makeBranchNode(ot.FENCED,[r],[t,e]);return t.role===st.OPEN?(fo.getInstance().classifyHorizontalFence_(o),o=er.run("propagateComposedFunction",o)):o.role=t.role,o=er.run("detect_cycle",o),fo.rewriteFencedNode_(o)}classifyHorizontalFence_(t){if(er.run("interval_heuristic",t),t.role===st.INTERVAL)return;t.role=st.LEFTRIGHT;const e=t.childNodes;if(!function(t){return function(t){return!!t&&-1!==["{","\ufe5b","\uff5b"].indexOf(t.textContent)}(t.contentNodes[0])&&function(t){return!!t&&-1!==["}","\ufe5c","\uff5d"].indexOf(t.textContent)}(t.contentNodes[1])}(t)||e.length>1)return;if(0===e.length||e[0].type===ot.EMPTY)return void(t.role=st.SETEMPTY);const n=e[0].type;if(1===e.length&&eo(e[0]))return void(t.role=st.SETSINGLE);const r=e[0].role;if(n===ot.PUNCTUATED&&r===st.SEQUENCE){if(e[0].contentNodes[0].role!==st.COMMA)return 1!==e[0].contentNodes.length||e[0].contentNodes[0].role!==st.VBAR&&e[0].contentNodes[0].role!==st.COLON?void 0:(t.role=st.SETEXT,void fo.getInstance().setExtension_(t));t.role=st.SETCOLLECT}}setExtension_(t){const e=t.childNodes[0].childNodes[0];e&&e.type===ot.INFIXOP&&1===e.contentNodes.length&&ho(e.contentNodes[0])&&(e.addAnnotation("set","intensional"),e.contentNodes[0].addAnnotation("set","intensional"))}getPunctuationInRow_(t){if(t.length<=1)return t;const e=t=>{const e=t.type;return"punctuation"===e||"text"===e||"operator"===e||"relation"===e},n=Or(t,(function(n){if(!Vr(n))return!1;if(Vr(n)&&!vr(n,st.ELLIPSIS))return!0;const r=t.indexOf(n);if(0===r)return!t[1]||!e(t[1]);const o=t[r-1];if(r===t.length-1)return!e(o);const i=t[r+1];return!e(o)||!e(i)}));if(0===n.rel.length)return t;let r=[],o=n.comp.shift();o.length>0&&r.push(fo.getInstance().row(o));let i=0;for(;n.comp.length>0;){let t=[];const e=i;do{t.push(n.rel[i++]),o=n.comp.shift()}while(n.rel[i]&&o&&0===o.length);t=er.run("ellipses",t),n.rel.splice(e,i-e,...t),i=e+t.length,r=r.concat(t),o&&o.length>0&&r.push(fo.getInstance().row(o))}return 1===r.length&&1===n.rel.length?r:[fo.getInstance().punctuatedNode_(r,n.rel)]}punctuatedNode_(t,e){const n=fo.getInstance().factory_.makeBranchNode(ot.PUNCTUATED,t,e);if(e.length===t.length){const t=e[0].role;if(t!==st.UNKNOWN&&e.every((function(e){return e.role===t})))return n.role=t,n}const r=e[0];return Qr(t,e,0)?n.role=r.childNodes.length&&!r.embellished?r.role:st.STARTPUNCT:Qr(t,e,t.length-1)?n.role=r.childNodes.length&&!r.embellished?r.role:st.ENDPUNCT:e.every((t=>vr(t,st.DUMMY)))?n.role=st.TEXT:e.every((t=>vr(t,st.SPACE)))?n.role=st.SPACE:n.role=st.SEQUENCE,n}dummyNode_(t){const e=fo.getInstance().factory_.makeMultipleContentNodes(t.length-1,rn.invisibleComma);return e.forEach((function(t){t.role=st.DUMMY})),fo.getInstance().punctuatedNode_(t,e)}accentRole_(t,e){if(!wr(t))return!1;const n=t.textContent,r=an.Secondary.get(n,ut.BAR)||an.Secondary.get(n,ut.TILDE)||t.role;return t.role=e===ot.UNDERSCORE?st.UNDERACCENT:st.OVERACCENT,t.addAnnotation("accent",r),!0}accentNode_(t,e,n,r,o){const i=(e=e.slice(0,r+1))[1],s=e[2];let a;if(!o&&s&&(a=fo.getInstance().factory_.makeBranchNode(ot.SUBSCRIPT,[t,i],[]),a.role=st.SUBSUP,e=[a,s],n=ot.SUPERSCRIPT),o){const r=fo.getInstance().accentRole_(i,n);if(s){fo.getInstance().accentRole_(s,ot.OVERSCORE)&&!r?(a=fo.getInstance().factory_.makeBranchNode(ot.OVERSCORE,[t,s],[]),e=[a,i],n=ot.UNDERSCORE):(a=fo.getInstance().factory_.makeBranchNode(ot.UNDERSCORE,[t,i],[]),e=[a,s],n=ot.OVERSCORE),a.role=st.UNDEROVER}}return fo.getInstance().makeLimitNode_(t,e,a,n)}makeLimitNode_(t,e,n,r){if(r===ot.LIMUPPER&&t.type===ot.LIMLOWER)return t.childNodes.push(e[1]),e[1].parent=t,t.type=ot.LIMBOTH,t;if(r===ot.LIMLOWER&&t.type===ot.LIMUPPER)return t.childNodes.splice(1,-1,e[1]),e[1].parent=t,t.type=ot.LIMBOTH,t;const o=fo.getInstance().factory_.makeBranchNode(r,e,[]),i=kr(t);return n&&(n.embellished=i),o.embellished=i,o.role=t.role,o}getFunctionsInRow_(t,e){const n=e||[];if(0===t.length)return n;const r=t.shift(),o=fo.classifyFunction_(r,t);if(!o)return n.push(r),fo.getInstance().getFunctionsInRow_(t,n);const i=fo.getInstance().getFunctionsInRow_(t,[]),s=fo.getInstance().getFunctionArgs_(r,i,o);return n.concat(s)}getFunctionArgs_(t,e,n){let r,o,i;switch(n){case"integral":{const n=fo.getInstance().getIntegralArgs_(e);if(!n.intvar&&!n.integrand.length)return n.rest.unshift(t),n.rest;const r=fo.getInstance().row(n.integrand);return i=fo.getInstance().integralNode_(t,r,n.intvar),er.run("intvar_from_fraction",i),n.rest.unshift(i),n.rest}case"prefix":if(e[0]&&e[0].type===ot.FENCED){const n=e.shift();return ao(n)||(n.role=st.LEFTRIGHT),i=fo.getInstance().functionNode_(t,n),e.unshift(i),e}if(r=Rr(e,Dr),r.head.length)o=fo.getInstance().row(r.head),r.div&&r.tail.unshift(r.div);else{if(!r.div||!Mr(r.div,ot.APPL))return e.unshift(t),e;o=r.div}return i=fo.getInstance().functionNode_(t,o),r.tail.unshift(i),r.tail;case"bigop":return r=Rr(e,Pr),r.head.length?(o=fo.getInstance().row(r.head),i=fo.getInstance().bigOpNode_(t,o),r.div&&r.tail.unshift(r.div),r.tail.unshift(i),r.tail):(e.unshift(t),e);default:{if(0===e.length)return[t];const n=e[0];return n.type===ot.FENCED&&!ao(n)&&function(t){const e=t.childNodes;if(0===e.length)return!0;if(e.length>1)return!1;const n=e[0];if(n.type===ot.INFIXOP){if(n.role!==st.IMPLICIT)return!1;if(n.childNodes.some((t=>Mr(t,ot.INFIXOP))))return!1}return!0}(n)?(n.role=st.LEFTRIGHT,fo.propagateFunctionRole_(t,st.SIMPLEFUNC),i=fo.getInstance().functionNode_(t,e.shift()),e.unshift(i),e):(e.unshift(t),e)}}}getIntegralArgs_(t,e=[]){if(0===t.length){const t=Rr(e,Pr);return t.div&&t.tail.unshift(t.div),{integrand:t.head,intvar:null,rest:t.tail}}er.run("intvar_from_implicit",t);const n=t[0];if(Fr(n)){const{integrand:n,rest:r}=fo.getInstance().getIntegralArgs_(e);return{integrand:n,intvar:null,rest:r.concat(t)}}if(Ur(n))return n.role=st.INTEGRAL,{integrand:e,intvar:n,rest:t.slice(1)};if(t[1]&&xr(n,t[1])){const r=fo.getInstance().prefixNode_(t[1],[n]);return r.role=st.INTEGRAL,{integrand:e,intvar:r,rest:t.slice(2)}}return e.push(t.shift()),fo.getInstance().getIntegralArgs_(t,e)}functionNode_(t,e){const n=fo.getInstance().factory_.makeContentNode(rn.functionApplication),r=fo.getInstance().funcAppls[t.id];r&&(n.mathmlTree=r.mathmlTree,n.mathml=r.mathml,n.annotation=r.annotation,n.attributes=r.attributes,delete fo.getInstance().funcAppls[t.id]),n.type=ot.PUNCTUATION,n.role=st.APPLICATION;const o=fo.getFunctionOp_(t,(function(t){return Mr(t,ot.FUNCTION)||Mr(t,ot.IDENTIFIER)&&vr(t,st.SIMPLEFUNC)}));return fo.getInstance().functionalNode_(ot.APPL,[t,e],o,[n])}bigOpNode_(t,e){const n=fo.getFunctionOp_(t,(t=>Mr(t,ot.LARGEOP)));return fo.getInstance().functionalNode_(ot.BIGOP,[t,e],n,[])}integralNode_(t,e,n){e=e||fo.getInstance().factory_.makeEmptyNode(),n=n||fo.getInstance().factory_.makeEmptyNode();const r=fo.getFunctionOp_(t,(t=>Mr(t,ot.LARGEOP)));return fo.getInstance().functionalNode_(ot.INTEGRAL,[t,e,n],r,[])}functionalNode_(t,e,n,r){const o=e[0];let i;n&&(i=n.parent,r.push(n));const s=fo.getInstance().factory_.makeBranchNode(t,e,r);return s.role=o.role,i&&(n.parent=i),s}fractionNode_(t,e){const n=fo.getInstance().factory_.makeBranchNode(ot.FRACTION,[t,e],[]);return n.role=n.childNodes.every((function(t){return Mr(t,ot.NUMBER)&&vr(t,st.INTEGER)}))?st.VULGAR:n.childNodes.every(ro)?st.UNIT:st.DIVISION,er.run("propagateSimpleFunction",n)}scriptNode_(t,e,n){let r;switch(t.length){case 0:r=fo.getInstance().factory_.makeEmptyNode();break;case 1:if(r=t[0],n)return r;break;default:r=fo.getInstance().dummyNode_(t)}return r.role=e,r}findNestedRow_(t,e,n,r){if(n>3)return null;for(let o,i=0;o=t[i];i++){const t=W(o);if(t!==ur.MSPACE){if(t===ur.MROW)return fo.getInstance().findNestedRow_(D(o.childNodes),e,n+1,r);if(fo.findSemantics(o,e,r))return o}}return null}}fo.FENCE_TO_PUNCT_={[st.METRIC]:st.METRIC,[st.NEUTRAL]:st.VBAR,[st.OPEN]:st.OPENFENCE,[st.CLOSE]:st.CLOSEFENCE},fo.MML_TO_LIMIT_={[ur.MSUB]:{type:ot.LIMLOWER,length:1},[ur.MUNDER]:{type:ot.LIMLOWER,length:1},[ur.MSUP]:{type:ot.LIMUPPER,length:1},[ur.MOVER]:{type:ot.LIMUPPER,length:1},[ur.MSUBSUP]:{type:ot.LIMBOTH,length:2},[ur.MUNDEROVER]:{type:ot.LIMBOTH,length:2}},fo.MML_TO_BOUNDS_={[ur.MSUB]:{type:ot.SUBSCRIPT,length:1,accent:!1},[ur.MSUP]:{type:ot.SUPERSCRIPT,length:1,accent:!1},[ur.MSUBSUP]:{type:ot.SUBSCRIPT,length:2,accent:!1},[ur.MUNDER]:{type:ot.UNDERSCORE,length:1,accent:!0},[ur.MOVER]:{type:ot.OVERSCORE,length:1,accent:!0},[ur.MUNDEROVER]:{type:ot.UNDERSCORE,length:2,accent:!0}},fo.CLASSIFY_FUNCTION_={[st.INTEGRAL]:"integral",[st.SUM]:"bigop",[st.PREFIXFUNC]:"prefix",[st.LIMFUNC]:"prefix",[st.SIMPLEFUNC]:"prefix",[st.COMPFUNC]:"prefix"},fo.MATHJAX_FONTS={"-tex-caligraphic":ct.CALIGRAPHIC,"-tex-caligraphic-bold":ct.CALIGRAPHICBOLD,"-tex-calligraphic":ct.CALIGRAPHIC,"-tex-calligraphic-bold":ct.CALIGRAPHICBOLD,"-tex-oldstyle":ct.OLDSTYLE,"-tex-oldstyle-bold":ct.OLDSTYLEBOLD,"-tex-mathit":ct.ITALIC};let po={};function mo(t){return Array.from(t.textContent).map(Mn.stringEmpty)}function go(t,e){const n=Array.from(t.textContent),r=[],o=fo.getInstance(),i=t.ownerDocument;for(let t,s=0;t=n[s];s++){const n=o.getNodeFactory().makeLeafNode(t,ct.UNKNOWN),s=o.identifierNode(n,ct.UNKNOWN,"");e(s),r.push(s.xml(i))}return r}function Eo(t){return go(t,(function(t){t.textContent.match(/\W/)||(t.type=ot.NUMBER)}))}function No(t){return go(t,(function(t){t.font=ct.UNKNOWN,t.type=ot.IDENTIFIER}))}const So=[ot.CASES,ot.CELL,ot.INTEGRAL,ot.LINE,ot.MATRIX,ot.MULTILINE,ot.OVERSCORE,ot.ROOT,ot.ROW,ot.SQRT,ot.SUBSCRIPT,ot.SUPERSCRIPT,ot.TABLE,ot.UNDERSCORE,ot.VECTOR];function To(t){return po={},[t]}function Io(t,e,n,r,o,i){r=r||So,o=o||{},i=i||function(t){return!1};const s=z(e);if(po[t]||(po[t]={}),po[t][s])return po[t][s];if(i(e)||n.indexOf(e.tagName)<0)return 0;const a=bo(e,n,tr(r,n),o,i,0);return po[t][s]=a,a}function bo(t,e,n,r,o,i){if(o(t)||n.indexOf(t.tagName)>-1||function(t,e){if(!t.attributes)return!1;const n=D(t.attributes);for(let t,r=0;t=n[r];r++)if(e[t.nodeName]===t.nodeValue)return!0;return!1}(t,r))return i;if(e.indexOf(t.tagName)>-1&&i++,!t.childNodes||0===t.childNodes.length)return i;const s=D(t.childNodes);return Math.max.apply(null,s.map((function(t){return bo(t,e,n,r,o,i)})))}function Ao(t){return Io("fraction",t,["fraction"],So,{},Nt.FUNCTIONS.fracNestDepth)}function Ro(t,e,n){const r=Ao(t),o=Array(r).fill(e);return n&&o.push(n),o.join(Nt.MESSAGES.regexp.JOINER_FRAC)}function Oo(t){return Mn.singleton(Ro(t,Nt.MESSAGES.MS.START,Nt.MESSAGES.MS.FRAC_V))}function Co(t){return Mn.singleton(Ro(t,Nt.MESSAGES.MS.END,Nt.MESSAGES.MS.FRAC_V),{kind:"LAST"})}function yo(t){return Mn.singleton(Ro(t,Nt.MESSAGES.MS.FRAC_OVER),{})}function Lo(t){return Mn.singleton(Ro(t,Nt.MESSAGES.MS.START,Nt.MESSAGES.MS.FRAC_B))}function Mo(t){return Mn.singleton(Ro(t,Nt.MESSAGES.MS.END,Nt.MESSAGES.MS.FRAC_B),{kind:"LAST"})}function _o(t){const e=Ao(t);return Mn.singleton(1===e?Nt.MESSAGES.MS.FRAC_S:Nt.FUNCTIONS.combineNestedFraction(Nt.MESSAGES.MS.NEST_FRAC,Nt.FUNCTIONS.radicalNestDepth(e-1),Nt.MESSAGES.MS.FRAC_S))}function vo(t){const e=Ao(t);return Mn.singleton(1===e?Nt.MESSAGES.MS.ENDFRAC:Nt.FUNCTIONS.combineNestedFraction(Nt.MESSAGES.MS.NEST_FRAC,Nt.FUNCTIONS.radicalNestDepth(e-1),Nt.MESSAGES.MS.ENDFRAC),{kind:"LAST"})}function wo(t){const e=Ao(t);return Mn.singleton(1===e?Nt.MESSAGES.MS.FRAC_OVER:Nt.FUNCTIONS.combineNestedFraction(Nt.MESSAGES.MS.NEST_FRAC,Nt.FUNCTIONS.radicalNestDepth(e-1),Nt.MESSAGES.MS.FRAC_OVER))}function Do(t){return Nt.FUNCTIONS.fracNestDepth(t)?[t]:[]}function Po(t,e,n){for(;t.parentNode;){const r=t.parentNode,o=r.parentNode;if(!o)break;const i=t.getAttribute&&t.getAttribute("role");(o.tagName===ot.SUBSCRIPT&&t===r.childNodes[1]||o.tagName===ot.TENSOR&&i&&(i===st.LEFTSUB||i===st.RIGHTSUB))&&(e=n.sub+Nt.MESSAGES.regexp.JOINER_SUBSUPER+e),(o.tagName===ot.SUPERSCRIPT&&t===r.childNodes[1]||o.tagName===ot.TENSOR&&i&&(i===st.LEFTSUPER||i===st.RIGHTSUPER))&&(e=n.sup+Nt.MESSAGES.regexp.JOINER_SUBSUPER+e),t=o}return e.trim()}function xo(t){return Mn.singleton(Po(t,Nt.MESSAGES.MS.SUBSCRIPT,{sup:Nt.MESSAGES.MS.SUPER,sub:Nt.MESSAGES.MS.SUB}))}function Uo(t){return Mn.singleton(Po(t,Nt.MESSAGES.MS.SUB,{sup:Nt.MESSAGES.MS.SUP,sub:Nt.MESSAGES.MS.SUB}))}function Fo(t){return Mn.singleton(Po(t,Nt.MESSAGES.MS.SUPERSCRIPT,{sup:Nt.MESSAGES.MS.SUPER,sub:Nt.MESSAGES.MS.SUB}))}function ko(t){return Mn.singleton(Po(t,Nt.MESSAGES.MS.SUP,{sup:Nt.MESSAGES.MS.SUP,sub:Nt.MESSAGES.MS.SUB}))}function Bo(t){const e=Po(t,"",{sup:Nt.MESSAGES.MS.SUPER,sub:Nt.MESSAGES.MS.SUB});return Mn.singleton(e?e.replace(new RegExp(Nt.MESSAGES.MS.SUB+"$"),Nt.MESSAGES.MS.SUBSCRIPT).replace(new RegExp(Nt.MESSAGES.MS.SUPER+"$"),Nt.MESSAGES.MS.SUPERSCRIPT):Nt.MESSAGES.MS.BASELINE)}function Go(t){const e=Po(t,"",{sup:Nt.MESSAGES.MS.SUP,sub:Nt.MESSAGES.MS.SUB});return Mn.singleton(e||Nt.MESSAGES.MS.BASE)}function Vo(t){return Io("radical",t,["sqrt","root"],So,{})}function Ho(t,e,n){const r=Vo(t),o=function(t){const e="sqrt"===t.tagName?"2":v("children/*[1]",t)[0].textContent.trim();return Nt.MESSAGES.MSroots[e]||""}(t);return n=o?Nt.FUNCTIONS.combineRootIndex(n,o):n,1===r?n:Nt.FUNCTIONS.combineNestedRadical(e,Nt.FUNCTIONS.radicalNestDepth(r-1),n)}function jo(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NESTED,Nt.MESSAGES.MS.STARTROOT))}function qo(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NESTED,Nt.MESSAGES.MS.ENDROOT))}function $o(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NESTED,Nt.MESSAGES.MS.ROOTINDEX))}function Xo(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.STARTROOT))}function Yo(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.ENDROOT))}function Wo(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.ROOTINDEX))}function Ko(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.ROOT))}function zo(t){return Mn.singleton(Ho(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.INDEX))}function Qo(t){const e=function(t){return Io("underscore",t,["underscore"],So,{},(function(t){return t.tagName&&t.tagName===ot.UNDERSCORE&&t.childNodes[0].childNodes[1].getAttribute("role")===st.UNDERACCENT}))}(t);return Mn.singleton(Array(e).join(Nt.MESSAGES.MS.UNDER)+Nt.MESSAGES.MS.UNDERSCRIPT)}function Jo(t){return Mn.singleton(Nt.MESSAGES.MS.ENDSCRIPTS)}function Zo(t){const e=function(t){return Io("overscore",t,["overscore"],So,{},(function(t){return t.tagName&&t.tagName===ot.OVERSCORE&&t.childNodes[0].childNodes[1].getAttribute("role")===st.OVERACCENT}))}(t);return Mn.singleton(Array(e).join(Nt.MESSAGES.MS.OVER)+Nt.MESSAGES.MS.OVERSCRIPT)}function ti(t){if(t.tagName!==ot.MATRIX||t.getAttribute("role")!==st.DETERMINANT)return[];const e=v("children/row/children/cell/children/*",t);for(let t,n=0;t=e[n];n++)if(t.tagName!==ot.NUMBER){if(t.tagName===ot.IDENTIFIER){const e=t.getAttribute("role");if(e===st.LATINLETTER||e===st.GREEKLETTER||e===st.OTHERLETTER)continue}return[]}return[t]}function ei(){const t=t=>t.map((t=>"ancestor::"+t)),e=t=>"not("+t+")",n=e(t(["subscript","superscript","tensor"]).join(" or ")),r=t(["relseq","multrel"]),o=t(["fraction","punctuation","fenced","sqrt","root"]);let i=[];for(let t,e=0;t=o[e];e++)i=i.concat(r.map((function(e){return t+"/"+e})));return[["ancestor::*/following-sibling::*",n,e(i.join(" | "))].join(" and ")]}function ni(t){if(!t.childNodes.length||!t.childNodes[0].childNodes.length||!t.childNodes[0].childNodes[0].childNodes.length)return Mn.singleton("");const e=t.childNodes[0].childNodes[0].childNodes[0].textContent;return Mn.singleton(e.match(/^\(.+\)$/)?e.slice(1,-1):e)}const ri=new Map([[3,"CSFleftsuperscript"],[4,"CSFleftsubscript"],[2,"CSFbaseline"],[1,"CSFrightsubscript"],[0,"CSFrightsuperscript"]]),oi=new Map([[4,2],[3,3],[2,1],[1,4],[0,5]]);function ii(t){const e=[];let n="",r="",o=parseInt(t,2);for(let t=0;t<5;t++){const i="children/*["+oi.get(t)+"]";if(1&o){const e=ri.get(t%5);n="[t] "+e+"Verbose; [n] "+i+";"+n,r="[t] "+e+"Brief; [n] "+i+";"+r}else e.unshift("name("+i+')="empty"');o>>=1}return[e,n,r]}function si(t,e=!0){const n=["11111","11110","11101","11100","10111","10110","10101","10100","01111","01110","01101","01100"];for(const r of n){let n="tensor"+r,[o,i,s]=ii(r);if(t.defineRule(n,"default",i,"self::tensor",...o),e&&(t.defineRule(n,"brief",s,"self::tensor",...o),t.defineRule(n,"sbrief",s,"self::tensor",...o)),!(3&parseInt(r,2)))continue;const a=ri.get(2);i+="; [t]"+a+"Verbose",s+="; [t]"+a+"Brief",n+="-baseline";const c="((.//*[not(*)])[last()]/@id)!=(((.//ancestor::fraction|ancestor::root|ancestor::sqrt|ancestor::cell|ancestor::line|ancestor::stree)[1]//*[not(*)])[last()]/@id)";t.defineRule(n,"default",i,"self::tensor",c,...o),e&&(t.defineRule(n,"brief",s,"self::tensor",c,...o),t.defineRule(n,"sbrief",s,"self::tensor",c,...o))}}function ai(t){let e=Object.keys(Nt.MESSAGES.MSroots).length;if(!e)return[];if(e++,!t.childNodes||0===t.childNodes.length||!t.childNodes[0].childNodes)return[];const n=t.childNodes[0].childNodes[0].textContent;if(!/^\d+$/.test(n))return[];const r=parseInt(n,10);return r>1&&r<=e?[t]:[]}function ci(t,e){let n=0;return function(){return Nt.NUMBERS.numericOrdinal(++n)+" "+e}}function li(t,e){let n=0;return function(){return Nt.NUMBERS.numberToOrdinal(++n,!1)+" "+e}}function ui(t){const e=mt(t,Nt.MESSAGES.MS.FRAC_OVER);return e.convertible&&e.enumerator&&e.denominator?[Mn.node(Nt.NUMBERS.numberToWords(e.enumerator),t.childNodes[0].childNodes[0],{separator:""}),Mn.stringAttr(Nt.NUMBERS.vulgarSep,{separator:""}),Mn.node(Nt.NUMBERS.numberToOrdinal(e.denominator,1!==e.enumerator),t.childNodes[0].childNodes[1])]:[Mn.node(e.content||"",t)]}function hi(t){const e=D(t.parentNode.childNodes);return Mn.singleton(Nt.NUMBERS.numericOrdinal(e.indexOf(t)+1).toString())}function di(t){const e=Bo(t);return e[0].speech=e[0].speech.replace(/-$/,""),e}function fi(t){const e=Go(t);return e[0].speech=e[0].speech.replace(/-$/,""),e}function pi(t){const e=Fo(t);return e[0].speech=e[0].speech.replace(/^exposant/,"exposant gauche"),e}function mi(t){const e=xo(t);return e[0].speech=e[0].speech.replace(/^indice/,"indice gauche"),e}function gi(t){const e=ko(t);return e[0].speech=e[0].speech.replace(/^sup/,"sup gauche"),e}function Ei(t){const e=Uo(t);return e[0].speech=e[0].speech.replace(/^sub/,"sub gauche"),e}function Ni(t,e,n){const r=Ao(t),o=[...Array(r)].map((t=>e));return n&&o.unshift(n),o.join(Nt.MESSAGES.regexp.JOINER_FRAC)}function Si(t){return Mn.singleton(Ni(t,Nt.MESSAGES.MS.START,Nt.MESSAGES.MS.FRAC_V))}function Ti(t){return Mn.singleton(Ni(t,Nt.MESSAGES.MS.END,Nt.MESSAGES.MS.FRAC_V))}function Ii(t){return Mn.singleton(Ni(t,Nt.MESSAGES.MS.START,Nt.MESSAGES.MS.FRAC_B))}function bi(t){return Mn.singleton(Ni(t,Nt.MESSAGES.MS.END,Nt.MESSAGES.MS.FRAC_B))}function Ai(t){const e=Ao(t);return 1===e?Mn.singleton(Nt.MESSAGES.MS.FRAC_S):Mn.singleton(Nt.FUNCTIONS.combineNestedFraction(Nt.FUNCTIONS.radicalNestDepth(e-1),Nt.MESSAGES.MS.NEST_FRAC,Nt.MESSAGES.MS.FRAC_S))}function Ri(t){const e=Ao(t);return 1===e?Mn.singleton(Nt.MESSAGES.MS.ENDFRAC):Mn.singleton(Nt.FUNCTIONS.combineNestedFraction(Nt.FUNCTIONS.radicalNestDepth(e-1),Nt.MESSAGES.MS.NEST_FRAC,Nt.MESSAGES.MS.ENDFRAC))}function Oi(t){const e=Ao(t);return 1===e?Mn.singleton(Nt.MESSAGES.MS.FRAC_OVER):Mn.singleton(Nt.FUNCTIONS.combineNestedFraction(Nt.FUNCTIONS.radicalNestDepth(e-1),Nt.MESSAGES.MS.NEST_FRAC,Nt.MESSAGES.MS.FRAC_OVER))}function Ci(t){return 1===v("children/*[1]",t)[0].toString().match(/[^>\u2062>]+<\/[^>]*>/g).length?[t]:[]}function yi(t,e,n){const r=Vo(t);return 1===r?n:Nt.FUNCTIONS.combineNestedRadical(Nt.FUNCTIONS.radicalNestDepth(r-1),e,n)}function Li(t){return Mn.singleton(yi(t,Nt.MESSAGES.MS.NESTED,Nt.MESSAGES.MS.STARTROOT))}function Mi(t){return Mn.singleton(yi(t,Nt.MESSAGES.MS.NESTED,Nt.MESSAGES.MS.ENDROOT))}function _i(t){return Mn.singleton(yi(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.STARTROOT))}function vi(t){return Mn.singleton(yi(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.ENDROOT))}function wi(t){return Mn.singleton(yi(t,Nt.MESSAGES.MS.NEST_ROOT,Nt.MESSAGES.MS.ROOT))}function Di(t,e){const n=function(t){const e=v("children/*[1]",t)[0].textContent.trim();return Nt.MESSAGES.MSroots[e]||e+"\uc81c\uacf1\uadfc"}(t);return n||e}function Pi(t){return Mn.singleton(Di(t,Nt.MESSAGES.MS.ROOTINDEX))}function xi(t){return Mn.singleton(Di(t,Nt.MESSAGES.MS.ROOTINDEX))}function Ui(t){return Mn.singleton(Di(t,Nt.MESSAGES.MS.INDEX))}function Fi(t){const e=v("children/*",t);return Mn.singleton(Nt.NUMBERS.wordOrdinal(e.length))}function ki(t){const e=v("children/*",t);return Mn.singleton(Nt.NUMBERS.wordOrdinal(e.length-1))}function Bi(t){const e=v("children/*",t),n=v("content/*",t);return Mn.singleton(Nt.NUMBERS.wordOrdinal(e.length-n.length))}function Gi(t,e){const n=t;let r=0;return function(){const t=wn.create({text:Hi(n[r])&&ji(n[r+1])?Nt.MESSAGES.unitTimes:""},{});return r++,[t]}}const Vi=[ot.SUPERSCRIPT,ot.SUBSCRIPT,ot.OVERSCORE,ot.UNDERSCORE];function Hi(t){for(;t;){if("unit"===t.getAttribute("role"))return!0;const e=t.tagName,n=v("children/*",t);t=-1!==Vi.indexOf(e)?n[0]:n[n.length-1]}return!1}function ji(t){for(;t;){if("unit"===t.getAttribute("role"))return!0;t=v("children/*",t)[0]}return!1}function qi(t){for(;t;){if("number"===t.tagName&&"1"===t.textContent)return[t];if("infixop"!==t.tagName||"multiplication"!==t.getAttribute("role")&&"implicit"!==t.getAttribute("role"))return[];t=v("children/*",t)[0]}return[]}function $i(t){const e=Ao(t);return Mn.singleton(new Array(e).join(Nt.MESSAGES.MS.FRACTION_REPEAT)+Nt.MESSAGES.MS.FRACTION_START)}function Xi(t){const e=Ao(t);return Mn.singleton(new Array(e).join(Nt.MESSAGES.MS.FRACTION_REPEAT)+Nt.MESSAGES.MS.FRACTION_END)}function Yi(t){const e=Ao(t);return Mn.singleton(new Array(e).join(Nt.MESSAGES.MS.FRACTION_REPEAT)+Nt.MESSAGES.MS.FRACTION_OVER)}function Wi(t){const e=Ao(t);return Mn.singleton(new Array(e).join(Nt.MESSAGES.MS.FRACTION_REPEAT)+"\u2838"+Nt.MESSAGES.MS.FRACTION_OVER)}function Ki(t){return Nt.MESSAGES.regexp.HYPER===Ao(t).toString()?[t]:[]}function zi(t,e){const n=Qi(t);return Mn.singleton(1===n?e:new Array(n).join(Nt.MESSAGES.MS.NESTED)+e)}function Qi(t,e){const n=e||0;return t.parentNode?Qi(t.parentNode,"root"===t.tagName||"sqrt"===t.tagName?n+1:n):n}function Ji(t){return zi(t,Nt.MESSAGES.MS.STARTROOT)}function Zi(t){return zi(t,Nt.MESSAGES.MS.ENDROOT)}function ts(t){return zi(t,Nt.MESSAGES.MS.ROOTINDEX)}function es(t,e){var n;const r=t.slice(0);let o=!0;const i=t[0].parentNode.parentNode,s=null===(n=i.getAttribute("annotation"))||void 0===n?void 0:n.match(/depth:(\d+)/),a=s?s[1]:"";let c;return c=t.length>0?v("./content/*",i):[],function(){const t=c.shift(),n=r.shift(),s=r[0],l=e?[wn.create({text:e},{translate:!0})]:[];if(!t)return l;const u=n?Po(n,"",{sup:Nt.MESSAGES.MS.SUPER,sub:Nt.MESSAGES.MS.SUB}):"",h=n&&"EMPTY"!==W(n)||o&&i&&i.previousSibling?[wn.create({text:Nt.MESSAGES.regexp.SPACE+u},{})]:[],d=s&&"EMPTY"!==W(s)||!c.length&&i&&i.nextSibling?[wn.create({text:Nt.MESSAGES.regexp.SPACE},{})]:[],f=R.evaluateNode(t);return f.unshift(new wn({text:"",layout:`beginrel${a}`})),f.push(new wn({text:"",layout:`endrel${a}`})),o=!1,l.concat(h,f,d)}}function ns(t,e){const n=t.slice(0);let r;return r=t.length>0?v("../../content/*",t[0]):[],function(){const t=n.shift(),o=n[0],i=r.shift(),s=e?[wn.create({text:e},{translate:!0})]:[];if(!i)return s;const a=t&&"NUMBER"===W(t),c=o&&"NUMBER"===W(o);return s.concat(a&&c&&i.getAttribute("role")===st.SPACE?[wn.create({text:Nt.MESSAGES.regexp.SPACE},{})]:[])}}function rs(t,e){var n;const r=Fn(t,e),o=null===(n=t[0].parentNode.parentNode.getAttribute("annotation"))||void 0===n?void 0:n.match(/depth:(\d+)/),i=o?o[1]:"";return function(){const t=r();return t.unshift(new wn({text:"",layout:`beginrel${i}`})),t.push(new wn({text:"",layout:`endrel${i}`})),t}}Ot.getInstance().setCorrection("enlargeFence",(function(t){const e="\u2820";if(1===t.length)return e+t;const n=t.split("");return n.every((function(t){return"\u2833"===t}))?e+n.join(e):t.slice(0,-1)+e+t.slice(-1)})),Ot.getInstance().setCorrection("ignoreEnglish",(function(t){return Ct(t,Nt.ALPHABETS.languagePrefix.english)})),Ot.getInstance().setCorrection("literal",(function(t){return Array.from(t).map((t=>R.getInstance().evaluator(t,R.getInstance().dynamicCstr))).join("")}));let os=!1;function is(){os||(Pn(c.BASE_LOCALE+".speech.mathspeak","",{CQFspaceoutNumber:Eo,CQFspaceoutIdentifier:No,CSFspaceoutText:mo,CSFopenFracVerbose:Oo,CSFcloseFracVerbose:Co,CSFoverFracVerbose:yo,CSFopenFracBrief:Lo,CSFcloseFracBrief:Mo,CSFopenFracSbrief:_o,CSFcloseFracSbrief:vo,CSFoverFracSbrief:wo,CSFvulgarFraction:ui,CQFvulgarFractionSmall:Do,CSFopenRadicalVerbose:jo,CSFcloseRadicalVerbose:qo,CSFindexRadicalVerbose:$o,CSFopenRadicalBrief:Xo,CSFcloseRadicalBrief:Yo,CSFindexRadicalBrief:Wo,CSFopenRadicalSbrief:Ko,CSFindexRadicalSbrief:zo,CQFisSmallRoot:ai,CSFsuperscriptVerbose:Fo,CSFsuperscriptBrief:ko,CSFsubscriptVerbose:xo,CSFsubscriptBrief:Uo,CSFbaselineVerbose:Bo,CSFbaselineBrief:Go,CSFleftsuperscriptVerbose:Fo,CSFleftsubscriptVerbose:xo,CSFrightsuperscriptVerbose:Fo,CSFrightsubscriptVerbose:xo,CSFleftsuperscriptBrief:ko,CSFleftsubscriptBrief:Uo,CSFrightsuperscriptBrief:ko,CSFrightsubscriptBrief:Uo,CSFunderscript:Qo,CSFoverscript:Zo,CSFendscripts:Jo,CTFordinalCounter:ci,CTFwordCounter:li,CTFcontentIterator:Fn,CQFdetIsSimple:ti,CSFRemoveParens:ni,CQFresetNesting:To,CGFbaselineConstraint:ei,CGFtensorRules:si}),Pn("es.speech.mathspeak",c.BASE_LOCALE+".speech.mathspeak",{CTFunitMultipliers:Gi,CQFoneLeft:qi}),Pn("fr.speech.mathspeak",c.BASE_LOCALE+".speech.mathspeak",{CSFbaselineVerbose:di,CSFbaselineBrief:fi,CSFleftsuperscriptVerbose:pi,CSFleftsubscriptVerbose:mi,CSFleftsuperscriptBrief:gi,CSFleftsubscriptBrief:Ei}),Pn("ko.speech.mathspeak",c.BASE_LOCALE+".speech.mathspeak",{CSFopenFracVerbose:Si,CSFcloseFracVerbose:Ti,CSFopenFracBrief:Ii,CSFcloseFracBrief:bi,CSFopenFracSbrief:Ai,CSFoverFracSbrief:Oi,CSFcloseFracSbrief:Ri,CQFisSimpleIndex:Ci,CSFindexRadicalVerbose:Pi,CSFindexRadicalBrief:xi,CSFindexRadicalSbrief:Ui,CSFopenRadicalVerbose:Li,CSFcloseRadicalVerbose:Mi,CSFopenRadicalBrief:_i,CSFcloseRadicalBrief:vi,CSFopenRadicalSbrief:wi}),Pn(c.BASE_LOCALE+".speech.clearspeak","",{CTFpauseSeparator:Un,CTFnodeCounter:kn,CTFcontentIterator:Fn,CSFvulgarFraction:ui,CQFvulgarFractionSmall:Hn,CQFcellsSimple:Vn,CSFordinalExponent:jn,CSFwordOrdinal:Qn,CQFmatchingFences:Xn,CSFnestingDepth:$n,CQFfencedArguments:Yn,CQFsimpleArguments:Wn,CQFspaceoutNumber:Eo,CQFisUnit:Jn}),Pn("en.prefix.default","",{CSFordinalPosition:hi}),Pn("en.speech.chromevox","",{CTFnodeCounter:xn,CTFcontentIterator:Fn}),Pn("en.speech.emacspeak","en.speech.chromevox",{CQFvulgarFractionSmall:Do,CSFvulgarFraction:ui}),Pn("ko.summary.","ko.speech.mathspeak",{CSFordinalConversion:Fi,CSFdecreasedOrdinalConversion:ki,CSFlistOrdinalConversion:Bi}),Pn("nemeth.braille.default",c.BASE_LOCALE+".speech.mathspeak",{CSFopenFraction:$i,CSFcloseFraction:Xi,CSFoverFraction:Yi,CSFoverBevFraction:Wi,CQFhyperFraction:Ki,CSFopenRadical:Ji,CSFcloseRadical:Zi,CSFindexRadical:ts,CSFsubscript:xo,CSFsuperscript:Fo,CSFbaseline:Bo,CGFtensorRules:t=>si(t,!1),CTFcontentIterator:rs,CTFrelationIterator:es,CTFimplicitIterator:ns}),Pn("euro.braille.default","nemeth.braille.default",{}),os=!0)}class ss{constructor(t,e,n){this.domain=t,this.name=e,this.func=n,this.active=!1}annotate(t){t.childNodes.forEach(this.annotate.bind(this)),t.contentNodes.forEach(this.annotate.bind(this)),t.addAnnotation(this.domain,this.func(t))}}class as{constructor(t,e,n,r={}){this.domain=t,this.name=e,this.func=n,this.def=r,this.active=!1}visit(t,e){let n=this.func(t,e);t.addAnnotation(this.domain,n[0]);for(let e,r=0;e=t.childNodes[r];r++)n=this.visit(e,n[1]);for(let e,r=0;e=t.contentNodes[r];r++)n=this.visit(e,n[1]);return n}}const cs=new Map,ls=new Map;function us(t){const e=t.domain+":"+t.name;t instanceof ss?cs.set(e,t):ls.set(e,t)}function hs(t,e){const n=t+":"+e,r=cs.get(n)||ls.get(n);r&&(r.active=!0)}class ds{constructor(t,e,n,r){this.name=t,this.dynamicCstr=e,this.precondition=n,this.action=r,this.context=null}toString(){return this.name+" | "+this.dynamicCstr.toString()+" | "+this.precondition.toString()+" ==> "+this.action.toString()}}var fs,ps;function ms(t){switch(t){case"[n]":return fs.NODE;case"[m]":return fs.MULTI;case"[t]":return fs.TEXT;case"[p]":return fs.PERSONALITY;default:throw"Parse error: "+t}}!function(t){t.NODE="NODE",t.MULTI="MULTI",t.TEXT="TEXT",t.PERSONALITY="PERSONALITY"}(fs||(fs={}));class gs{static grammarFromString(t){return Ot.parseInput(t)}static fromString(t){const e={type:ms(t.substring(0,3))};let n=t.slice(3).trim();if(!n)throw new Ss("Missing content.");switch(e.type){case fs.TEXT:if('"'===n[0]){const t=Ts(n,"\\(")[0].trim();if('"'!==t.slice(-1))throw new Ss("Invalid string syntax.");e.content=t,n=n.slice(t.length).trim(),-1===n.indexOf("(")&&(n="");break}case fs.NODE:case fs.MULTI:{const t=n.indexOf(" (");if(-1===t){e.content=n.trim(),n="";break}e.content=n.substring(0,t).trim(),n=n.slice(t).trim()}}if(n){const t=gs.attributesFromString(n);t.grammar&&(e.grammar=t.grammar,delete t.grammar),Object.keys(t).length&&(e.attributes=t)}return new gs(e)}static attributesFromString(t){if("("!==t[0]||")"!==t.slice(-1))throw new Ss("Invalid attribute expression: "+t);const e={},n=Ts(t.slice(1,-1),",");for(const t of n){const n=t.indexOf(":");if(-1===n)e[t.trim()]="true";else{const r=t.substring(0,n).trim(),o=t.slice(n+1).trim();e[r]=r===Rt?gs.grammarFromString(o):o}}return e}constructor({type:t,content:e,attributes:n,grammar:r}){this.type=t,this.content=e,this.attributes=n,this.grammar=r}toString(){let t="";t+=function(t){switch(t){case fs.NODE:return"[n]";case fs.MULTI:return"[m]";case fs.TEXT:return"[t]";case fs.PERSONALITY:return"[p]";default:throw"Unknown type error: "+t}}(this.type),t+=this.content?" "+this.content:"";const e=this.attributesToString();return t+=e?" "+e:"",t}grammarToString(){return this.getGrammar().join(":")}getGrammar(){if(!this.grammar)return[];const t=[];for(const[e,n]of Object.entries(this.grammar))t.push(!0===n?e:!1===n?`!${e}`:`${e}=${n}`);return t}attributesToString(){const t=this.getAttributes(),e=this.grammarToString();return e&&t.push("grammar:"+e),t.length>0?"("+t.join(", ")+")":""}getAttributes(){if(!this.attributes)return[];const t=[];for(const[e,n]of Object.entries(this.attributes))t.push("true"===n?e:`${e}:${n}`);return t}}class Es{static fromString(t){const e=Ts(t,";").filter((function(t){return t.match(/\S/)})).map((function(t){return t.trim()})),n=[];for(let t=0,r=e.length;t<r;t++){const r=gs.fromString(e[t]);r&&n.push(r)}return Es.naiveSpan(n),new Es(n)}static naiveSpan(t){var e;let n=!1;for(let r,o=0;r=t[o];o++){if(n&&(r.type!==fs.TEXT||'"'!==r.content[0]&&!r.content.match(/^CSF/)))continue;if(!n&&r.type===fs.PERSONALITY)continue;if(!n){n=!0;continue}if(null===(e=r.attributes)||void 0===e?void 0:e.span)continue;const i=t[o+1];i&&i.type!==fs.NODE||Es.addNaiveSpan(r,i?i.content:"LAST")}}static addNaiveSpan(t,e){t.attributes||(t.attributes={}),t.attributes.span=e}constructor(t){this.components=t}toString(){return this.components.map((function(t){return t.toString()})).join("; ")}}class Ns{static constraintValue(t,e){for(let n,r=0;n=e[r];r++)if(t.match(n))return++r;return 0}toString(){const t=this.constraints.join(", ");return`${this.query}, ${t} (${this.priority}, ${this.rank})`}constructor(t,...e){this.query=t,this.constraints=e;const[n,r]=this.presetPriority();this.priority=n?r:this.calculatePriority()}calculatePriority(){const t=Ns.constraintValue(this.query,Ns.queryPriorities);if(!t)return 0;const e=this.query.match(/^self::.+\[(.+)\]/);let n=0;if((null==e?void 0:e.length)&&e[1]){const t=e[1];n=Ns.constraintValue(t,Ns.attributePriorities)}return 100*t+10*n}presetPriority(){if(!this.constraints.length)return[!1,0];const t=this.constraints[this.constraints.length-1].match(/^priority=(.*$)/);if(!t)return[!1,0];this.constraints.pop();const e=parseFloat(t[1]);return[!0,isNaN(e)?0:e]}}Ns.queryPriorities=[/^self::\*$/,/^self::[\w-]+$/,/^self::\*\[.+\]$/,/^self::[\w-]+\[.+\]$/],Ns.attributePriorities=[/^@[\w-]+$/,/^@[\w-]+!=".+"$/,/^not\(contains\(@[\w-]+,\s*".+"\)\)$/,/^contains\(@[\w-]+,".+"\)$/,/^@[\w-]+=".+"$/];class Ss extends A{constructor(t){super(t),this.name="RuleError"}}function Ts(t,e){const n=[];let r="";for(;""!==t;){const o=t.search(e);if(-1===o){if((t.match(/"/g)||[]).length%2!=0)throw new Ss("Invalid string in expression: "+t);n.push(r+t),r="",t=""}else if((t.substring(0,o).match(/"/g)||[]).length%2==0)n.push(r+t.substring(0,o)),r="",t=t.substring(o+1);else{const e=t.substring(o).search('"');if(-1===e)throw new Ss("Invalid string in expression: "+t);r+=t.substring(0,o+e+1),t=t.substring(o+e+1)}}return r&&n.push(r),n}class Is{constructor(t,e){this.prefix=t,this.store=e}add(t,e){this.checkCustomFunctionSyntax_(t)&&(this.store[t]=e)}addStore(t){const e=Object.keys(t.store);for(let n,r=0;n=e[r];r++)this.add(n,t.store[n])}lookup(t){return this.store[t]}checkCustomFunctionSyntax_(t){const e=new RegExp("^"+this.prefix);return!!t.match(e)||(console.error("FunctionError: Invalid function name. Expected prefix "+this.prefix),!1)}}class bs extends Is{constructor(){super("CQF",{})}}class As extends Is{constructor(){super("CSF",{})}}class Rs extends Is{constructor(){super("CTF",{})}}class Os extends Is{constructor(){super("CGF",{})}}class Cs{constructor(){this.customQueries=new bs,this.customStrings=new As,this.contextFunctions=new Rs,this.customGenerators=new Os}applyCustomQuery(t,e){const n=this.customQueries.lookup(e);return n?n(t):null}applySelector(t,e){return this.applyCustomQuery(t,e)||v(e,t)}applyQuery(t,e){const n=this.applySelector(t,e);return n.length>0?n[0]:null}applyConstraint(t,e){return!!this.applyQuery(t,e)||function(t,e){let n;try{n=_(t,e,C.result.BOOLEAN_TYPE)}catch(t){return!1}return n.booleanValue}(e,t)}constructString(t,e){const n=this.constructString_(t,e);return Array.isArray(n)?n.map((t=>t.speech)).join(""):n}constructSpan(t,e,n){const r=this.constructString_(t,e);if(Array.isArray(r)){const t=r[r.length-1];return t.attributes=Object.assign({},n,t.attributes),r}return[Mn.node(r,t,n)]}constructString_(t,e){if(!e)return"";if('"'===e.charAt(0))return e.slice(1,-1);const n=this.customStrings.lookup(e);return n?n(t):function(t,e){let n;try{n=_(t,e,C.result.STRING_TYPE)}catch(t){return""}return n.stringValue}(e,t)}parse(t){const e=Array.isArray(t)?t:Object.entries(t);for(const t of e){switch(t[0].slice(0,3)){case"CQF":this.customQueries.add(t[0],t[1]);break;case"CSF":this.customStrings.add(t[0],t[1]);break;case"CTF":this.contextFunctions.add(t[0],t[1]);break;case"CGF":this.customGenerators.add(t[0],t[1]);break;default:console.error("FunctionError: Invalid function name "+t[0])}}}}class ys{static compareStaticConstraints_(t,e){if(t.length!==e.length)return!1;for(let n,r=0;n=t[r];r++)if(-1===e.indexOf(n))return!1;return!0}static comparePreconditions_(t,e){const n=t.precondition,r=e.precondition;return n.query===r.query&&ys.compareStaticConstraints_(n.constraints,r.constraints)}constructor(){this.context=new Cs,this.parseOrder=c.DEFAULT_ORDER,this.parser=new l(this.parseOrder),this.locale=c.DEFAULT_VALUES[r.LOCALE],this.modality=c.DEFAULT_VALUES[r.MODALITY],this.domain="",this.initialized=!1,this.inherits=null,this.kind="standard",this.customTranscriptions={},this.preconditions=new Map,this.speechRules_=[],this.rank=0,this.parseMethods={Rule:this.defineRule,Generator:this.generateRules,Action:this.defineAction,Precondition:this.definePrecondition,Ignore:this.ignoreRules}}defineRule(t,e,n,r,...o){const i=this.parseAction(n),s=this.parsePrecondition(r,o),a=this.parseCstr(e);if(!(i&&s&&a))return console.error(`Rule Error: ${r}, (${e}): ${n}`),null;const c=new ds(t,a,s,i);return c.precondition.rank=this.rank++,this.addRule(c),c}addRule(t){t.context=this.context,this.speechRules_.unshift(t)}deleteRule(t){const e=this.speechRules_.indexOf(t);-1!==e&&this.speechRules_.splice(e,1)}findRule(t){for(let e,n=0;e=this.speechRules_[n];n++)if(t(e))return e;return null}findAllRules(t){return this.speechRules_.filter(t)}evaluateDefault(t){const e=t.textContent.slice(0);return e.match(/^\s+$/)?this.evaluateWhitespace(e):this.evaluateString(e)}evaluateWhitespace(t){return[]}evaluateCustom(t){const e=this.customTranscriptions[t];return void 0!==e?wn.create({text:e},{adjust:!0,translate:!1}):null}evaluateCharacter(t){return this.evaluateCustom(t)||wn.create({text:t},{adjust:!0,translate:!0})}removeDuplicates(t){for(let e,n=this.speechRules_.length-1;e=this.speechRules_[n];n--)e!==t&&t.dynamicCstr.equal(e.dynamicCstr)&&ys.comparePreconditions_(e,t)&&this.speechRules_.splice(n,1)}getSpeechRules(){return this.speechRules_}setSpeechRules(t){this.speechRules_=t}getPreconditions(){return this.preconditions}parseCstr(t){try{return this.parser.parse(this.locale+"."+this.modality+(this.domain?"."+this.domain:"")+"."+t)}catch(e){if("RuleError"===e.name)return console.error("Rule Error ",`Illegal Dynamic Constraint: ${t}.`,e.message),null;throw e}}parsePrecondition(t,e){try{const n=this.parsePrecondition_(t);t=n[0];let r=n.slice(1);for(const t of e)r=r.concat(this.parsePrecondition_(t));return new Ns(t,...r)}catch(n){if("RuleError"===n.name)return console.error("Rule Error ",`Illegal preconditions: ${t}, ${e}.`,n.message),null;throw n}}parseAction(t){try{return Es.fromString(t)}catch(e){if("RuleError"===e.name)return console.error("Rule Error ",`Illegal action: ${t}.`,e.message),null;throw e}}parse(t){this.modality=t.modality||this.modality,this.locale=t.locale||this.locale,this.domain=t.domain||this.domain,this.context.parse(t.functions||[]),"actions"!==t.kind&&(this.kind=t.kind||this.kind,this.inheritRules()),this.parseRules(t.rules||[])}parseRules(t){for(let e,n=0;e=t[n];n++){const t=e[0],n=this.parseMethods[t];t&&n&&n.apply(this,e.slice(1))}}generateRules(t){const e=this.context.customGenerators.lookup(t);e&&e(this)}defineAction(t,e){let n;try{n=Es.fromString(e)}catch(t){if("RuleError"===t.name)return void console.error("Action Error ",e,t.message);throw t}const r=this.getFullPreconditions(t);if(!r)return void console.error(`Action Error: No precondition for action ${t}`);this.ignoreRules(t);const o=new RegExp("^\\w+\\.\\w+\\."+(this.domain?"\\w+\\.":""));r.conditions.forEach((([e,r])=>{const i=this.parseCstr(e.toString().replace(o,""));this.addRule(new ds(t,i,r,n))}))}getFullPreconditions(t){const e=this.preconditions.get(t);return e||!this.inherits?e:this.inherits.getFullPreconditions(t)}definePrecondition(t,e,n,...r){const o=this.parsePrecondition(n,r),i=this.parseCstr(e);o&&i?(o.rank=this.rank++,this.preconditions.set(t,new Ls(i,o))):console.error(`Precondition Error: ${n}, (${e})`)}inheritRules(){if(!this.inherits||!this.inherits.getSpeechRules().length)return;const t=new RegExp("^\\w+\\.\\w+\\."+(this.domain?"\\w+\\.":""));this.inherits.getSpeechRules().forEach((e=>{const n=this.parseCstr(e.dynamicCstr.toString().replace(t,""));this.addRule(new ds(e.name,n,e.precondition,e.action))}))}ignoreRules(t,...e){let n=this.findAllRules((e=>e.name===t));if(!e.length)return void n.forEach(this.deleteRule.bind(this));let r=[];for(const t of e){const e=this.parseCstr(t);for(const t of n)e.equal(t.dynamicCstr)?this.deleteRule(t):r.push(t);n=r,r=[]}}parsePrecondition_(t){const e=this.context.customGenerators.lookup(t);return e?e():[t]}}class Ls{constructor(t,e){this.base=t,this._conditions=[],this.constraints=[],this.allCstr={},this.constraints.push(t),this.addCondition(t,e)}get conditions(){return this._conditions}addConstraint(t){if(this.constraints.filter((e=>e.equal(t))).length)return;this.constraints.push(t);const e=[];for(const[n,r]of this.conditions)this.base.equal(n)&&e.push([t,r]);this._conditions=this._conditions.concat(e)}addBaseCondition(t){this.addCondition(this.base,t)}addFullCondition(t){this.constraints.forEach((e=>this.addCondition(e,t)))}addCondition(t,e){const n=t.toString()+" "+e.toString();this.allCstr.condStr||(this.allCstr[n]=!0,this._conditions.push([t,e]))}}class Ms extends ys{constructor(){super(),this.annotators=[],this.parseMethods.Alias=this.defineAlias,this.parseMethods.SpecializedRule=this.defineSpecializedRule,this.parseMethods.Specialized=this.defineSpecialized}initialize(){this.initialized||(this.annotations(),this.initialized=!0)}annotations(){for(let t,e=0;t=this.annotators[e];e++)hs(this.domain,t)}defineAlias(t,e,...n){const r=this.parsePrecondition(e,n);if(!r)return void console.error(`Precondition Error: ${e} ${n}`);const o=this.preconditions.get(t)||this.getFullPreconditions(t);o?o.addFullCondition(r):console.error(`Alias Error: No precondition by the name of ${t}`)}defineRulesAlias(t,e,...n){const r=this.findAllRules((function(e){return e.name===t}));if(0===r.length)throw new Ss("Rule with name "+t+" does not exist.");const o=[];r.forEach((t=>{(t=>{const e=t.dynamicCstr.toString(),n=t.action.toString();for(let t,r=0;t=o[r];r++)if(t.action===n&&t.cstr===e)return!1;return o.push({cstr:e,action:n}),!0})(t)&&this.addAlias_(t,e,n)}))}defineSpecializedRule(t,e,n,r){const o=this.parseCstr(e),i=this.findRule((e=>e.name===t&&o.equal(e.dynamicCstr))),s=this.parseCstr(n);if(!i&&r)throw new Ss("Rule named "+t+" with style "+e+" does not exist.");const a=r?Es.fromString(r):i.action,c=new ds(i.name,s,i.precondition,a);this.addRule(c)}defineSpecialized(t,e,n){const r=this.parseCstr(n);if(!r)return void console.error(`Dynamic Constraint Error: ${n}`);const o=this.preconditions.get(t);o?o.addConstraint(r):console.error(`Alias Error: No precondition by the name of ${t}`)}evaluateString(t){const e=[];if(t.match(/^\s+$/))return e;let n=this.matchNumber(t);if(n&&n.length===t.length)return e.push(this.evaluateCharacter(n.number)),e;const r=t.replace(/\s/g," ").split(" ").filter((t=>t));for(let t,o=0;t=r[o];o++)if(1===t.length)e.push(this.evaluateCharacter(t));else if(t.match(new RegExp("^["+Nt.MESSAGES.regexp.TEXT+"]+$")))e.push(this.evaluateCharacter(t));else{let r=t;for(;r;){n=this.matchNumber(r);const t=r.match(new RegExp("^["+Nt.MESSAGES.regexp.TEXT+"]+"));if(n)e.push(this.evaluateCharacter(n.number)),r=r.substring(n.length);else if(t)e.push(this.evaluateCharacter(t[0])),r=r.substring(t[0].length);else{const t=Array.from(r),n=t[0];e.push(this.evaluateCharacter(n)),r=t.slice(1).join("")}}}return e}parse(t){super.parse(t),this.annotators=t.annotators||[]}addAlias_(t,e,n){const r=this.parsePrecondition(e,n),o=new ds(t.name,t.dynamicCstr,r,t.action);o.name=t.name,this.addRule(o)}matchNumber(t){const e=t.match(new RegExp("^"+Nt.MESSAGES.regexp.NUMBER)),n=t.match(new RegExp("^"+Ms.regexp.NUMBER));if(!e&&!n)return null;const r=n&&n[0]===t;if(e&&e[0]===t||!r)return e?{number:e[0],length:e[0].length}:null;return{number:n[0].replace(new RegExp(Ms.regexp.DIGIT_GROUP,"g"),"X").replace(new RegExp(Ms.regexp.DECIMAL_MARK,"g"),Nt.MESSAGES.regexp.DECIMAL_MARK).replace(/X/g,Nt.MESSAGES.regexp.DIGIT_GROUP.replace(/\\/g,"")),length:n[0].length}}}Ms.regexp={NUMBER:"((\\d{1,3})(?=(,| ))((,| )\\d{3})*(\\.\\d+)?)|^\\d*\\.\\d+|^\\d+",DECIMAL_MARK:"\\.",DIGIT_GROUP:","};class _s extends Ms{constructor(){super(...arguments),this.modality="braille",this.customTranscriptions={"\u22ca":"\u2808\u2821\u2833"}}evaluateString(t){const e=[],n=Array.from(t);for(let t=0;t<n.length;t++)e.push(this.evaluateCharacter(n[t]));return e}annotations(){for(let t,e=0;t=this.annotators[e];e++)hs(this.locale,t)}}class vs extends _s{constructor(){super(...arguments),this.locale="euro",this.customTranscriptions={},this.customCommands={"\\cdot":"*","\\lt":"<","\\gt":">"},this.lastSpecial=!1,this.specialChars=["^","_","{","}"]}evaluateString(t){const e=t.split(/(\\[a-z]+|\\{|\\}|\\\\)/i),n=this.cleanup(e);return super.evaluateString(n)}cleanup(t){const e=[];let n=!1,r=null;for(let o of t){if(o.match(/^\\/)){"\\text"===o&&(n=!0),this.addSpace(an.LatexCommands.get(o))&&e.push(" "),o=this.customCommands[o]||o;const t=o.match(/^\\/);t&&o.match(/^\\[a-zA-Z]+$/)&&r&&e.push(" "),r=t?o:null,e.push(o);continue}const t=o.split("");for(const o of t)n?(e.push(o),n="}"!==o,r=null):o.match(/[a-z]/i)&&r?(r=null,e.push(" "),e.push(o)):o.match(/\s/)||(this.addSpace(o)&&e.push(" "),e.push(o),r=null)}return e.join("")}addSpace(t){if(!t)return!1;if(-1!==this.specialChars.indexOf(t))return this.lastSpecial=!0,!1;if(this.lastSpecial)return this.lastSpecial=!1,!1;const e=an.Meaning.get(t);return e.type===ot.OPERATOR||e.type===ot.RELATION||e.type===ot.PUNCTUATION&&e.role===st.COLON}}!function(t){t.ROOT="root",t.DYNAMIC="dynamic",t.QUERY="query",t.BOOLEAN="boolean",t.STATIC="static"}(ps||(ps={}));class ws{constructor(t,e){this.constraint=t,this.test=e,this.children_={},this.kind=ps.ROOT}getConstraint(){return this.constraint}getKind(){return this.kind}applyTest(t){return this.test(t)}addChild(t){const e=t.getConstraint(),n=this.children_[e];return this.children_[e]=t,n}getChild(t){return this.children_[t]}getChildren(){const t=[];for(const e of Object.values(this.children_))t.push(e);return t}findChildren(t){const e=[];for(const n of Object.values(this.children_))n.applyTest(t)&&e.push(n);return e}removeChild(t){delete this.children_[t]}toString(){return this.constraint}}class Ds extends ws{constructor(t,e){super(t,e),this.rule_=null,this.kind=ps.STATIC}getRule(){return this.rule_}setRule(t){this.rule_&&N.getInstance().output("Replacing rule "+this.rule_+" with "+t),this.rule_=t}toString(){return this.getRule()?this.constraint+"\n==> "+this.getRule().action:this.constraint}}function Ps(t,e,n){switch(t){case ps.ROOT:return new xs;case ps.DYNAMIC:return new Us(e);case ps.QUERY:return new Bs(e,n);case ps.BOOLEAN:return new Gs(e,n);default:return null}}class xs extends ws{constructor(){super("",(()=>!0)),this.kind=ps.ROOT}}class Us extends ws{constructor(t){super(t,(e=>e===t)),this.kind=ps.DYNAMIC}}const Fs={"=":(t,e)=>t===e,"!=":(t,e)=>t!==e,"<":(t,e)=>t<e,">":(t,e)=>t>e,"<=":(t,e)=>t<=e,">=":(t,e)=>t>=e};function ks(t){if(t.match(/^self::\*$/))return t=>!0;if(t.match(/^self::\w+$/)){const e=t.slice(6).toUpperCase();return t=>t.tagName&&W(t)===e}if(t.match(/^self::\w+:\w+$/)){const e=t.split(":"),n=L(e[2]);if(!n)return null;const r=e[3].toUpperCase();return t=>t.localName&&t.localName.toUpperCase()===r&&t.namespaceURI===n}if(t.match(/^@\w+$/)){const e=t.slice(1);return t=>t.hasAttribute&&t.hasAttribute(e)}if(t.match(/^@\w+="[\w\d ]+"$/)){const e=t.split("="),n=e[0].slice(1),r=e[1].slice(1,-1);return t=>t.hasAttribute&&t.hasAttribute(n)&&t.getAttribute(n)===r}if(t.match(/^@\w+!="[\w\d ]+"$/)){const e=t.split("!="),n=e[0].slice(1),r=e[1].slice(1,-1);return t=>!t.hasAttribute||!t.hasAttribute(n)||t.getAttribute(n)!==r}if(t.match(/^contains\(\s*@grammar\s*,\s*"[\w\d ]+"\s*\)$/)){const e=t.split('"')[1];return t=>!!Ot.getInstance().getParameter(e)}if(t.match(/^not\(\s*contains\(\s*@grammar\s*,\s*"[\w\d ]+"\s*\)\s*\)$/)){const e=t.split('"')[1];return t=>!Ot.getInstance().getParameter(e)}if(t.match(/^name\(\.\.\/\.\.\)="\w+"$/)){const e=t.split('"')[1].toUpperCase();return t=>{var n,r;return(null===(r=null===(n=t.parentNode)||void 0===n?void 0:n.parentNode)||void 0===r?void 0:r.tagName)&&W(t.parentNode.parentNode)===e}}if(t.match(/^count\(preceding-sibling::\*\)=\d+$/)){const e=t.split("="),n=parseInt(e[1],10);return t=>{var e;return(null===(e=t.parentNode)||void 0===e?void 0:e.childNodes[n])===t}}if(t.match(/^.+\[@category!?=".+"\]$/)){let[,e,n,r]=t.match(/^(.+)\[@category(!?=)"(.+)"\]$/);const o=r.match(/^unit:(.+)$/);let i="";return o&&(r=o[1],i=":unit"),t=>{const o=v(e,t)[0];if(o){const t=yn(o.textContent+i);return"="===n?t===r:t!==r}return!1}}if(t.match(/^string-length\(.+\)\W+\d+/)){const[,e,n,r]=t.match(/^string-length\((.+)\)(\W+)(\d+)/),o=Fs[n]||Fs["="],i=parseInt(r,10);return t=>{const n=v(e,t)[0];return!!n&&o(Array.from(n.textContent).length,i)}}return null}class Bs extends Ds{constructor(t,e){super(t,ks(t)),this.context=e,this.kind=ps.QUERY}applyTest(t){return this.test?this.test(t):this.context.applyQuery(t,this.constraint)===t}}class Gs extends Ds{constructor(t,e){super(t,ks(t)),this.context=e,this.kind=ps.BOOLEAN}applyTest(t){return this.test?this.test(t):this.context.applyConstraint(t,this.constraint)}}class Vs{static collectRules_(t){const e=[];let n=[t];for(;n.length;){const t=n.shift();if(t.getKind()===ps.QUERY||t.getKind()===ps.BOOLEAN){const n=t.getRule();n&&e.unshift(n)}n=n.concat(t.getChildren())}return e}static printWithDepth_(t,e,n){n+=new Array(e+2).join(e.toString())+": "+t.toString()+"\n";const r=t.getChildren();for(let t,o=0;t=r[o];o++)n=Vs.printWithDepth_(t,e+1,n);return n}static order_(t){const e=t.getChildren();if(!e.length)return 0;const n=Math.max.apply(null,e.map(Vs.order_));return Math.max(e.length,n)}constructor(){this.root=Ps(ps.ROOT,"",null)}addRule(t){let e=this.root;const n=t.context,r=t.dynamicCstr.getValues();for(let t=0,o=r.length;t<o;t++)e=this.addNode_(e,r[t],ps.DYNAMIC,n);e=this.addNode_(e,t.precondition.query,ps.QUERY,n);const o=t.precondition.constraints;for(let t=0,r=o.length;t<r;t++)e=this.addNode_(e,o[t],ps.BOOLEAN,n);e.setRule(t)}lookupRules(t,e){let n=[this.root];const r=[];for(;e.length;){const t=e.shift(),r=[];for(;n.length;){n.shift().getChildren().forEach((e=>{e.getKind()===ps.DYNAMIC&&-1===t.indexOf(e.getConstraint())||r.push(e)}))}n=r.slice()}for(;n.length;){const e=n.shift();if(e.getRule){const t=e.getRule();t&&r.push(t)}const o=e.findChildren(t);n=n.concat(o)}return r}hasSubtrie(t){let e=this.root;for(let n=0,r=t.length;n<r;n++){const r=t[n];if(e=e.getChild(r),!e)return!1}return!0}toString(){return Vs.printWithDepth_(this.root,0,"")}collectRules(t=this.root){return Vs.collectRules_(t)}order(){return Vs.order_(this.root)}enumerate(t){return this.enumerate_(this.root,t)}byConstraint(t){let e=this.root;for(;t.length&&e;){const n=t.shift();e=e.getChild(n)}return e||null}enumerate_(t,e){e=e||{};const n=t.getChildren();for(let t,r=0;t=n[r];r++)t.kind===ps.DYNAMIC&&(e[t.getConstraint()]=this.enumerate_(t,e[t.getConstraint()]));return e}addNode_(t,e,n,r){let o=t.getChild(e);return o||(o=Ps(n,e,r),t.addChild(o)),o}}var Hs;!function(t){t[t.ENTER=13]="ENTER",t[t.ESC=27]="ESC",t[t.SPACE=32]="SPACE",t[t.PAGE_UP=33]="PAGE_UP",t[t.PAGE_DOWN=34]="PAGE_DOWN",t[t.END=35]="END",t[t.HOME=36]="HOME",t[t.LEFT=37]="LEFT",t[t.UP=38]="UP",t[t.RIGHT=39]="RIGHT",t[t.DOWN=40]="DOWN",t[t.TAB=9]="TAB",t[t.LESS=188]="LESS",t[t.GREATER=190]="GREATER",t[t.DASH=189]="DASH",t[t.ZERO=48]="ZERO",t[t.ONE=49]="ONE",t[t.TWO=50]="TWO",t[t.THREE=51]="THREE",t[t.FOUR=52]="FOUR",t[t.FIVE=53]="FIVE",t[t.SIX=54]="SIX",t[t.SEVEN=55]="SEVEN",t[t.EIGHT=56]="EIGHT",t[t.NINE=57]="NINE",t[t.A=65]="A",t[t.B=66]="B",t[t.C=67]="C",t[t.D=68]="D",t[t.E=69]="E",t[t.F=70]="F",t[t.G=71]="G",t[t.H=72]="H",t[t.I=73]="I",t[t.J=74]="J",t[t.K=75]="K",t[t.L=76]="L",t[t.M=77]="M",t[t.N=78]="N",t[t.O=79]="O",t[t.P=80]="P",t[t.Q=81]="Q",t[t.R=82]="R",t[t.S=83]="S",t[t.T=84]="T",t[t.U=85]="U",t[t.V=86]="V",t[t.W=87]="W",t[t.X=88]="X",t[t.Y=89]="Y",t[t.Z=90]="Z"}(Hs||(Hs={}));const js=new Map([[13,"ENTER"],[27,"ESC"],[32,"SPACE"],[33,"PAGE_UP"],[34,"PAGE_DOWN"],[35,"END"],[36,"HOME"],[37,"LEFT"],[38,"UP"],[39,"RIGHT"],[40,"DOWN"],[9,"TAB"],[188,"LESS"],[190,"GREATER"],[189,"DASH"],[48,"ZERO"],[49,"ONE"],[50,"TWO"],[51,"THREE"],[52,"FOUR"],[53,"FIVE"],[54,"SIX"],[55,"SEVEN"],[56,"EIGHT"],[57,"NINE"],[65,"A"],[66,"B"],[67,"C"],[68,"D"],[69,"E"],[70,"F"],[71,"G"],[72,"H"],[73,"I"],[74,"J"],[75,"K"],[76,"L"],[77,"M"],[78,"N"],[79,"O"],[80,"P"],[81,"Q"],[82,"R"],[83,"S"],[84,"T"],[85,"U"],[86,"V"],[87,"W"],[88,"X"],[89,"Y"],[90,"Z"]]);var qs;!function(t){t.CLICK="click",t.DBLCLICK="dblclick",t.MOUSEDOWN="mousedown",t.MOUSEUP="mouseup",t.MOUSEOVER="mouseover",t.MOUSEOUT="mouseout",t.MOUSEMOVE="mousemove",t.SELECTSTART="selectstart",t.KEYPRESS="keypress",t.KEYDOWN="keydown",t.KEYUP="keyup",t.TOUCHSTART="touchstart",t.TOUCHMOVE="touchmove",t.TOUCHEND="touchend",t.TOUCHCANCEL="touchcancel"}(qs||(qs={}));function $s(t,e,n){return(n||function(n,r){return"number"==typeof n&&"number"==typeof r?n+r:"number"==typeof n?r:"number"==typeof r?n:[t,e].sort()[0]}).call(null,t,e)}function Xs(t,e){delete t.open,e.close.forEach((e=>delete t[e])),e.open.forEach((n=>t[n]=e[n]));const n=Object.keys(t);t.open=n}function Ys(t,e){if(t.length<=1)return t;const n=[];for(let r,o=0;r=e[o],t.length;o++)r.close&&r.close.length&&r.close.forEach((function(e){const r=t.indexOf(e);-1!==r&&(n.unshift(e),t.splice(r,1))}));return n}let Ws={},Ks=[];function zs(t){t=t.map((t=>t.clone())),Ws={},Ks=[];let e=[];const n={};for(let r,o=0;r=t[o];o++){let t=null;const o=r.descriptionSpan(),s=r.personality,a=s[i.JOIN];delete s[i.JOIN],void 0!==s[i.PAUSE]&&(t={[i.PAUSE]:s[i.PAUSE]},delete s[i.PAUSE]);na(e,o,ra(s,n),a,t,!0)}return e=e.concat(function(){const t=[];for(let e=Ks.length-1;e>=0;e--){const n=Ks[e];if(n.length){const e={open:[],close:[]};for(let t=0;t<n.length;t++){const r=n[t];e.close.push(r),e[r]=0}t.push(e)}}return t}()),e=function(t){const e={},n=[];for(let r,o=0;r=t[o];o++){if(!Zs(r)){Qs(n,r);continue}if(!r.close||1!==r.close.length||r.open.length){Js(r,e),n.push(r);continue}let i=t[o+1];if(!i||ea(i)){Js(r,e),n.push(r);continue}const s=ta(i)?i:null;s&&(i=t[o+2]),i&&Zs(i)&&i.open[0]===r.close[0]&&!i.close.length&&i[i.open[0]]===e[i.open[0]]?s?(Qs(n,s),o+=2):o+=1:(Js(r,e),n.push(r))}return n}(e),e=R.getInstance().options.cleanpause?function(t){for(;ta(t[0]);)t.shift();for(;ta(t[t.length-1]);)t.pop();return t}(e):e,e}function Qs(t,e){const n=t[t.length-1];if(n){if(ea(e)&&ea(n)){if(void 0===n.join)return void(n.span=n.span.concat(e.span));const t=n.span.pop(),r=e.span.shift();return n.span.push(t+n.join+r),n.span=n.span.concat(e.span),void(n.join=e.join)}ta(e)&&ta(n)?n.pause=$s(n.pause,e.pause):t.push(e)}else t.push(e)}function Js(t,e){t.rate&&(e.rate=t.rate),t.pitch&&(e.pitch=t.pitch),t.volume&&(e.volume=t.volume)}function Zs(t){return"object"==typeof t&&t.open}function ta(t){return"object"==typeof t&&1===Object.keys(t).length&&Object.keys(t)[0]===i.PAUSE}function ea(t){const e=Object.keys(t);return"object"==typeof t&&(1===e.length&&"span"===e[0]||2===e.length&&("span"===e[0]&&"join"===e[1]||"span"===e[1]&&"join"===e[0]))}function na(t,e,n,r,o,s=!1){if(s){const s=t[t.length-1];let a;if(s&&(a=s[i.JOIN]),s&&!e.speech&&o&&ta(s)){const t=i.PAUSE;s[t]=$s(s[t],o[t]),o=null}if(s&&e.speech&&0===Object.keys(n).length&&ea(s)){if(void 0!==a){const t=s.span.pop();e=Mn.stringAttr(t.speech+a+e.speech,t.attributes)}s.span.push(e),e=Mn.empty(),s[i.JOIN]=r}}0!==Object.keys(n).length&&t.push(n),e.speech&&t.push({span:[e],join:r}),o&&t.push(o)}function ra(t,e){if(!e)return t;const n={};for(const r of h){const o=t[r],i=e[r];if(!o&&!i||o&&i&&o===i)continue;const s=o||0;Zs(n)||(n.open=[],n.close=[]),o||n.close.push(r),i||n.open.push(r),i&&o&&(n.close.push(r),n.open.push(r)),e[r]=s,n[r]=s,Ws[r]?Ws[r].push(s):Ws[r]=[s]}if(Zs(n)){let t=n.close.slice();for(;t.length>0;){let r=Ks.pop();const o=tr(r,t);if(t=tr(t,r),r=o,0!==t.length){if(0!==r.length){n.close=n.close.concat(r),n.open=n.open.concat(r);for(let t,o=0;t=r[o];o++)n[t]=e[t]}}else 0!==r.length&&Ks.push(r)}Ks.push(n.open)}return n}class oa{constructor(){this.separator_=" "}set separator(t){this.separator_=t}get separator(){return"braille"===R.getInstance().options.modality?"":this.separator_}error(t){return null}merge(t){let e="";const n=t.length-1;for(let r,o=0;r=t[o];o++)if(e+=r.speech,o<n){const t=r.attributes.separator;e+=void 0!==t?t:this.separator}return e}finalize(t){return t}pauseValue(t){let e;switch(t){case"long":e=750;break;case"medium":e=500;break;case"short":e=250;break;default:e=parseInt(t,10)}return Math.floor(e*R.getInstance().getRate()/100)}}class ia extends oa{constructor(){super(...arguments),this.ignoreElements=[i.LAYOUT],this.scaleFunction=null}setScaleFunction(t,e,n,r,o=0){this.scaleFunction=i=>{const s=(i-t)/(e-t),a=n*(1-s)+r*s;return+(Math.round(a+"e+"+o)+"e-"+o)}}applyScaleFunction(t){return this.scaleFunction?this.scaleFunction(t):t}ignoreElement(t){return-1!==this.ignoreElements.indexOf(t)}}class sa extends ia{markup(t){this.setScaleFunction(-2,2,-100,100,2);const e=zs(t),n=[],r=[];for(let t,o=0;t=e[o];o++)if(t.span)n.push(this.merge(t.span));else if(ta(t))n.push(this.pause(t));else{if(t.close.length)for(let e=0;e<t.close.length;e++){const e=r.pop();if(-1===t.close.indexOf(e))throw new A("Unknown closing markup element: "+e);n.push(this.closeTag(e))}if(t.open.length){Ys(t.open.slice(),e.slice(o+1)).forEach((e=>{n.push(this.prosodyElement(e,t[e])),r.push(e)}))}}return n.join(" ")}}class aa extends sa{constructor(){super(...arguments),this.values=new Map}finalize(t){return function(t){if(ha.clear(),!t)return;const e=Object.keys(t).map((t=>parseInt(t))).sort();for(let t,n=0;t=e[n];n++)ha.set(t,n+1)}(this.values.get("rel")),function(t){ca="";const e=P(`<all>${t}</all>`);return N.getInstance().generate((()=>[j(e.toString())])),ca=fa(e),ca}(t)}pause(t){return""}prosodyElement(t,e){return t===i.LAYOUT?`<${e}>`:""}closeTag(t){return`</${t}>`}markup(t){this.values.clear();const e=[];let n=[];for(const r of t){if(!r.layout){n.push(r);continue}e.push(this.processContent(n)),n=[];const[t,o,i]=this.layoutValue(r.layout);"begin"!==t?"end"!==t?console.warn("Something went wrong with layout markup: "+o):e.push("</"+o+">"):e.push("<"+o+(i?` value="${i}"`:"")+">")}return e.push(this.processContent(n)),e.join("")}processContent(t){const e=[],n=zs(t);for(let t,r=0;t=n[r];r++)t.span?e.push(this.merge(t.span)):ta(t);return e.join("")}layoutValue(t){const e=t.match(/^(begin|end|)(.*\D)(\d*)$/),n=e[3];return n?(t=e[2],this.values.has(t)||this.values.set(t,{}),this.values.get(t)[n]=!0,[e[1],t,n]):[e[1],e[2],""]}}aa.options={cayleyshort:R.getInstance().options.cayleyshort,linebreaks:R.getInstance().options.linebreaks};let ca="";const la={TABLE:function(t){let e=Ea(t);e.forEach((t=>{t.cells=t.cells.slice(1).slice(0,-1),t.width=t.width.slice(1).slice(0,-1)}));const[n,r]=Na(e);return e=Sa(e,r),Ta(e,n)},CASES:function(t){let e=Ea(t);e.forEach((t=>{t.cells=t.cells.slice(0,-1),t.width=t.width.slice(0,-1)}));const[n,r]=Na(e);return e=Sa(e,r),Ta(e,n)},CAYLEY:function(t){let e=Ea(t);e.forEach((t=>{t.cells=t.cells.slice(1).slice(0,-1),t.width=t.width.slice(1).slice(0,-1),t.sep=t.sep+t.sep}));const[n,r]=Na(e),o={lfence:"",rfence:"",cells:r.map((t=>"\u2810"+new Array(t).join("\u2812"))),width:r,height:1,sep:e[0].sep};R.getInstance().options.cayleyshort&&"\u2800"===e[0].cells[0]&&(o.cells[0]="\u2800");return e.splice(1,0,o),e=Sa(e,r),Ta(e,n)},MATRIX:function(t){let e=Ea(t);const[n,r]=Na(e);return e=Sa(e,r),Ta(e,n)},CELL:fa,FENCE:fa,ROW:fa,FRACTION:function(t){const[e,n,,r,o]=Array.from(t.childNodes),i=ua(n),s=ua(r),a=ma(i),c=ma(s);let l=Math.max(a,c);const u=e+new Array(l+1).join("\u2812")+o;return l=u.length,`${Ra(i,l)}\n${u}\n${Ra(s,l)}`},NUMERATOR:Oa,DENOMINATOR:Oa,REL:Ca,OP:Ca};function ua(t){const e=W(t),n=la[e];return n?n(t):t.textContent}const ha=new Map;function da(t,e){if(!t||!e)return t+e;const n=pa(t),r=pa(e),o=n-r;t=o<0?ga(t,r,ma(t)):t,e=o>0?ga(e,n,ma(e)):e;const i=t.split(/\r\n|\r|\n/),s=e.split(/\r\n|\r|\n/),a=[];for(let t=0;t<i.length;t++)a.push(i[t]+s[t]);return a.join("\n")}function fa(t){let e="";for(const n of Array.from(t.childNodes))e=n.nodeType!==x.TEXT_NODE?da(e,ua(n)):da(e,n.textContent);return e}function pa(t){return t.split(/\r\n|\r|\n/).length}function ma(t){return t.split(/\r\n|\r|\n/).reduce(((t,e)=>Math.max(e.length,t)),0)}function ga(t,e,n){return t=function(t,e){const n=e-pa(t);return t+(n>0?new Array(n+1).join("\n"):"")}(t,e),function(t,e){const n=t.split(/\r\n|\r|\n/),r=[];for(const t of n){const n=e-t.length;r.push(t+(n>0?new Array(n+1).join("\u2800"):""))}return r.join("\n")}(t,n)}function Ea(t){const e=Array.from(t.childNodes),n=[];for(const t of e)t.nodeType===x.ELEMENT_NODE&&n.push(Aa(t));return n}function Na(t){const e=t.reduce(((t,e)=>Math.max(e.height,t)),0),n=[];for(let e=0;e<t[0].width.length;e++)n.push(t.map((t=>t.width[e])).reduce(((t,e)=>Math.max(t,e)),0));return[e,n]}function Sa(t,e){const n=[];for(const r of t){if(0===r.height)continue;const t=[];for(let n=0;n<r.cells.length;n++)t.push(ga(r.cells[n],r.height,e[n]));r.cells=t,n.push(r)}return n}function Ta(t,e){if(1===e)return t.map((t=>t.lfence+t.cells.join(t.sep)+t.rfence)).join("\n");const n=[];for(const e of t){const t=Ia(e.sep,e.height);let r=e.cells.shift();for(;e.cells.length;)r=da(r,t),r=da(r,e.cells.shift());r=da(Ia(e.lfence,e.height),r),r=da(r,Ia(e.rfence,e.height)),n.push(r),n.push(e.lfence+new Array(ma(r)-3).join(e.sep)+e.rfence)}return n.slice(0,-1).join("\n")}function Ia(t,e){let n="";for(;e;)n+=t+"\n",e--;return n.slice(0,-1)}function ba(t){return t.nodeType===x.ELEMENT_NODE&&"FENCE"===W(t)?ua(t):""}function Aa(t){const e=Array.from(t.childNodes),n=ba(e[0]),r=ba(e[e.length-1]);n&&e.shift(),r&&e.pop();let o="";const i=[];for(const t of e){if(t.nodeType===x.TEXT_NODE){o=t.textContent;continue}const e=ua(t);i.push(e)}return{lfence:n,rfence:r,sep:o,cells:i,height:i.reduce(((t,e)=>Math.max(pa(e),t)),0),width:i.map(ma)}}function Ra(t,e){const n=(e-ma(t))/2,[r,o]=Math.floor(n)===n?[n,n]:[Math.floor(n),Math.ceil(n)],i=t.split(/\r\n|\r|\n/),s=[],[a,c]=[new Array(r+1).join("\u2800"),new Array(o+1).join("\u2800")];for(const t of i)s.push(a+t+c);return s.join("\n")}function Oa(t){const e=t.firstChild,n=fa(t);if(e&&e.nodeType===x.ELEMENT_NODE){if("ENGLISH"===W(e))return"\u2830"+n;if("NUMBER"===W(e))return"\u283c"+n}return n}function Ca(t){if(!R.getInstance().options.linebreaks)return fa(t);const e=ha.get(parseInt(t.getAttribute("value")));return(e?`<br value="${e}"/>`:"")+fa(t)}class ya extends oa{markup(t){const e=zs(t);let n="",r=null,o=!1;for(let t,s=0;t=e[s];s++)Zs(t)||(ta(t)?r=t:(r&&(n+=this.pause(r[i.PAUSE]),r=null),n+=(o?this.separator:"")+this.merge(t.span),o=!0));return n}pause(t){let e;return e="number"==typeof t?t<=250?"short":t<=500?"medium":"long":t,ya.PAUSE_PUNCTUATION.get(e)||""}}ya.PAUSE_PUNCTUATION=new Map([["short",","],["medium",";"],["long","."]]);class La extends sa{finalize(t){return`<?xml version="1.0"?><speak version="1.1" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="${R.getInstance().options.locale}"><prosody rate="`+R.getInstance().getRate()+'%">'+this.separator+t+this.separator+"</prosody></speak>"}pause(t){return'<break time="'+this.pauseValue(t[i.PAUSE])+'ms"/>'}prosodyElement(t,e){const n=(e=Math.floor(this.applyScaleFunction(e)))<0?e.toString():"+"+e.toString();return"<prosody "+t.toLowerCase()+'="'+n+(t===i.VOLUME?">":'%">')}closeTag(t){return"</prosody>"}markup(t){return La.MARKS={},super.markup(t)}merge(t){const e=[];let n="";for(let r=0;r<t.length;r++){const o=t[r];if(this.isEmptySpan(o))continue;const i=La.MARK_KIND?o.attributes.kind:"",s=R.getInstance().options.automark?o.attributes.id:R.getInstance().options.mark?o.attributes.extid:"";!s||s===n||La.MARK_ONCE&&La.MARKS[s]||(e.push(i?`<mark name="${s}" kind="${i}"/>`:`<mark name="${s}"/>`),n=s,La.MARKS[s]=!0),R.getInstance().options.character&&1===o.speech.length&&o.speech.match(/[a-zA-Z]/)?e.push('<say-as interpret-as="'+La.CHARACTER_ATTR+'">'+o.speech+"</say-as>"):e.push(o.speech)}return e.join(this.separator)}isEmptySpan(t){const e=t.attributes.separator;return t.speech.match(/^\s*$/)&&(!e||e.match(/^\s*$/))}}La.MARK_ONCE=!1,La.MARK_KIND=!0,La.CHARACTER_ATTR="character",La.MARKS={};class Ma extends oa{markup(t){let e="";const n=zs(t).filter((t=>t.span));if(!n.length)return e;const r=n.length-1;for(let t,o=0;t=n[o];o++){if(t.span&&(e+=this.merge(t.span)),o>=r)continue;const n=t.join;e+=void 0===n?this.separator:n}return e}}const _a=new La,va=new Map([[f.NONE,new Ma],[f.COUNTING,new class extends Ma{finalize(t){const e=super.finalize(t),n="braille"===R.getInstance().options.modality?"\u28ff\u2800\u28ff\u2800\u28ff\u2800\u28ff\u2800\u28ff\u2800":"0123456789";let r=new Array(Math.trunc(e.length/10)+1).join(n);return r+=n.slice(0,e.length%10),e+"\n"+r}}],[f.PUNCTUATION,new ya],[f.LAYOUT,new aa],[f.ACSS,new class extends ia{markup(t){this.setScaleFunction(-2,2,0,10,0);const e=zs(t),n=[],r={open:[]};let o=null,i=!1;for(let t,l=0;t=e[l];l++){if(Zs(t)){Xs(r,t);continue}if(ta(t)){i&&(s=o,a=t,c=Math.max,o=s?{pause:$s(s.pause,a.pause,c)}:a);continue}const e='"'+this.merge(t.span)+'"';i=!0,o&&(n.push(this.pause(o)),o=null);const l=this.prosody_(r);n.push(l?"(text ("+l+") "+e+")":e)}var s,a,c;return"(exp "+n.join(" ")+")"}error(t){return'(error "'+js.get(t)+'")'}prosodyElement(t,e){switch(e=this.applyScaleFunction(e),t){case i.RATE:return"(richness . "+e+")";case i.PITCH:return"(average-pitch . "+e+")";case i.VOLUME:return"(stress . "+e+")"}return"(value . "+e+")"}pause(t){return"(pause . "+this.pauseValue(t[i.PAUSE])+")"}prosody_(t){const e=t.open,n=[];for(let r,o=0;r=e[o];o++)n.push(this.prosodyElement(r,t[r]));return n.join(" ")}}],[f.SABLE,new class extends sa{finalize(t){return'<?xml version="1.0"?><!DOCTYPE SABLE PUBLIC "-//SABLE//DTD SABLE speech mark up//EN" "Sable.v0_2.dtd" []><SABLE>'+this.separator+t+this.separator+"</SABLE>"}pause(t){return'<BREAK MSEC="'+this.pauseValue(t[i.PAUSE])+'"/>'}prosodyElement(t,e){switch(e=this.applyScaleFunction(e),t){case i.PITCH:return'<PITCH RANGE="'+e+'%">';case i.RATE:return'<RATE SPEED="'+e+'%">';case i.VOLUME:return'<VOLUME LEVEL="'+e+'%">';default:return"<"+t.toUpperCase()+' VALUE="'+e+'">'}}closeTag(t){return"</"+t.toUpperCase()+">"}}],[f.VOICEXML,_a],[f.SSML,_a]]);function wa(t){const e=va.get(R.getInstance().options.markup);return e?e.markup(t):""}function Da(t){const e=va.get(R.getInstance().options.markup);return e?e.finalize(t):t}class Pa{getSpeechMap(t){let e=this.speechMaps.get(t);return e||(e=new Map,this.speechMaps.set(t,e)),e}setMap(t,e,n){this.getSpeechMap(e).set(t,n.map((t=>t.clone())))}constructor(t){this.node=t,this.speechMaps=new Map,this.nodeMap=null}addNode(t,e,n="speech"){t.nodeType===x.ELEMENT_NODE&&t.hasAttribute("id")&&this.setMap(n,t.getAttribute("id"),e)}get(t){return this.speechMaps.get(t)}getNodeMap(){if(this.nodeMap)return this.nodeMap;this.nodeMap=new Map;for(const t of $(this.node,"id")){const e=t.getAttribute("id");if(!this.nodeMap.has(e)){this.nodeMap.set(t.getAttribute("id"),t);continue}const n=t.parentNode.tagName;"children"!==n&&"stree"!==n||this.nodeMap.set(e,t)}return this.nodeMap}completeModality(t,e){const n=R.getInstance().options.modality;R.getInstance().options.modality=t;for(const[n,r]of this.getNodeMap()){this.getSpeechMap(n).has(t)||e(r)}R.getInstance().options.modality=n}json(t=["none"]){const e={},n=R.getInstance().options.markup;for(const[n,r]of this.speechMaps){const o={};for(const i of t)R.getInstance().options.markup=i,r.forEach(((t,e)=>o[`${e}-${i}`]=wa(t))),e[n]=o}return R.getInstance().options.markup=n,e}}class xa{static getInstance(){return xa.instance=xa.instance||new xa,xa.instance}static debugSpeechRule(t,e){const n=t.precondition,r=t.context.applyQuery(e,n.query);N.getInstance().output(n.query,r?r.toString():r),n.constraints.forEach((n=>N.getInstance().output(n,t.context.applyConstraint(e,n))))}static debugNamedSpeechRule(t,e){const n=xa.getInstance().trie.collectRules().filter((e=>e.name==t));for(let r,o=0;r=n[o];o++)N.getInstance().output("Rule",t,"DynamicCstr:",r.dynamicCstr.toString(),"number",o),xa.debugSpeechRule(r,e)}evaluateNode(t,e=!1){w(t),this.speechStructure&&!e||(this.speechStructure=new Pa(t));const n=(new Date).getTime();let r=[];try{r=this.evaluateNode_(t)}catch(t){console.error("Something went wrong computing speech."),N.getInstance().output(t)}const o=(new Date).getTime();return N.getInstance().output("Time:",o-n),r}toString(){return this.trie.collectRules().map((t=>t.toString())).join("\n")}runInSetting(t,e){var n,r;const o=R.getInstance(),i=o.options,s=new T;for(const e of T.BINARY_FEATURES)s[e]=null!==(n=t[e])&&void 0!==n?n:i[e];for(const e of T.STRING_FEATURES)s[e]=null!==(r=t[e])&&void 0!==r?r:i[e];o.options=s,o.setDynamicCstr();const a=e();return o.options=i,o.setDynamicCstr(),a}static addStore(t){const e=function(t){const e=`${t.locale}.${t.modality}.${t.domain}`;if("actions"===t.kind){const n=Ua.get(e);return n.parse(t),n}is(),t&&!t.functions&&(t.functions=function(t,e,n){return Dn.get([t,e,n].join("."))||Dn.get([c.DEFAULT_VALUES[r.LOCALE],e,n].join("."))||Dn.get([c.BASE_LOCALE,e,n].join("."))||{}}(t.locale,t.modality,t.domain));const n=function(t,e){if("braille"===e&&"euro"===t)return new vs;if("braille"===e)return new _s;return new Ms}(t.locale,t.modality);Ua.set(e,n),t.inherits&&(n.inherits=Ua.get(`${t.inherits}.${t.modality}.${t.domain}`));return n.parse(t),n.initialize(),n}(t);"abstract"!==e.kind&&e.getSpeechRules().forEach((t=>xa.getInstance().trie.addRule(t))),xa.getInstance().addEvaluator(e)}processGrammar(t,e,n){const r={};for(const[o,i]of Object.entries(n))r[o]="string"==typeof i?t.constructString(e,i):i;Ot.getInstance().pushState(r)}addEvaluator(t){const e=t.evaluateDefault.bind(t),n=this.evaluators_[t.locale];if(n)return void(n[t.modality]=e);const r={};r[t.modality]=e,this.evaluators_[t.locale]=r}getEvaluator(t,e){const n=this.evaluators_[t]||this.evaluators_[c.DEFAULT_VALUES[r.LOCALE]];return n[e]||n[c.DEFAULT_VALUES[r.MODALITY]]}enumerate(t){return this.trie.enumerate(t)}constructor(){this.trie=null,this.evaluators_={},this.speechStructure=null,this.trie=new Vs}evaluateNode_(t){if(!t)return[];this.updateConstraint_();let e=this.evaluateTree_(t);return e=function(t){const e=new vn(t);for(const t of e.annotations){const n=t.data;if("punctuation"===n.annotation){const r=e.prevText(t);if(!r)continue;const o=r.data;"punctuation"!==o.annotation&&"\u2800"!==o.text&&1===n.text.length&&-1!==Fa.indexOf(n.text)&&(n.text="\u2838"+n.text)}}return e.toList()}(e),e}evaluateTree_(t){const e=this.evaluateTreeInternal_(t);return this.speechStructure.addNode(t,e,R.getInstance().options.modality),e}evaluateTreeInternal_(t){const e=R.getInstance();let n;N.getInstance().generate((()=>[t.toString()])),Ot.getInstance().setAttribute(t);const r=this.lookupRule(t,e.dynamicCstr);if(!r)return e.options.strict?[]:(n=this.getEvaluator(e.options.locale,e.options.modality)(t),t.attributes&&this.addPersonality_(n,{},!1,t),n);N.getInstance().generate((()=>["Apply Rule:",r.name,r.dynamicCstr.toString(),e.mode===o.HTTP?z(t):t.toString()])),Ot.getInstance().processSingles();const i=r.context,s=r.action.components;n=[];for(let e,r=0;e=s[r];r++){let r=[];const o=e.content||"",s=e.attributes||{};let a=!1;e.grammar&&this.processGrammar(i,t,e.grammar);let c=null;if(s.engine){c=R.getInstance().dynamicCstr.getComponents();const t=Object.assign({},c,Ot.parseInput(s.engine));R.getInstance().setDynamicCstr(t),this.updateConstraint_()}switch(e.type){case fs.NODE:{const e=i.applyQuery(t,o);e&&(r=this.evaluateTree_(e))}break;case fs.MULTI:{a=!0;const e=i.applySelector(t,o);e.length>0&&(r=this.evaluateNodeList_(i,e,s.sepFunc,i.constructString(t,s.separator),s.ctxtFunc,i.constructString(t,s.context)))}break;case fs.TEXT:{const e=s.span;let n={};if(e){const r=v(e,t);n=r.length?Mn.getAttributes(r[0]):{kind:e}}r=i.constructSpan(t,o,n).map((function(t){return wn.create({text:t.speech,attributes:t.attributes},{adjust:!0})}))}break;case fs.PERSONALITY:default:r=[wn.create({text:o})]}r[0]&&!a&&(s.context&&(r[0].context=i.constructString(t,s.context)+(r[0].context||"")),s.annotation&&(r[0].annotation=s.annotation)),this.addLayout(r,s,a),e.grammar&&Ot.getInstance().popState(),n=n.concat(this.addPersonality_(r,s,a,t)),c&&(R.getInstance().setDynamicCstr(c),this.updateConstraint_())}return Ot.getInstance().popState(),n}evaluateNodeList_(t,e,n,r,o,i){if(!e.length)return[];const s=r||"",a=i||"",c=t.contextFunctions.lookup(o),l=c?c(e,a):function(){return a},u=t.contextFunctions.lookup(n),h=u?u(e,s):function(){return[wn.create({text:s},{translate:!0})]};let d=[];for(let t,n=0;t=e[n];n++){const r=this.evaluateTree_(t);if(r.length>0&&(r[0].context=l()+(r[0].context||""),d=d.concat(r),n<e.length-1)){const t=h();d=d.concat(t)}}return d}addLayout(t,e,n){const r=e.layout;r&&(r.match(/^begin/)?t.unshift(new wn({text:"",layout:r})):r.match(/^end/)?t.push(new wn({text:"",layout:r})):(t.unshift(new wn({text:"",layout:`begin${r}`})),t.push(new wn({text:"",layout:`end${r}`}))))}addPersonality_(t,e,n,r){const o={};let s=null;for(const t of h){const n=e[t];if(void 0===n)continue;const r=parseFloat(n),a=isNaN(r)?'"'===n.charAt(0)?n.slice(1,-1):n:r;t===i.PAUSE?s=a:o[t]=a}for(let e,n=0;e=t[n];n++)this.addRelativePersonality_(e,o),this.addExternalAttributes_(e,r);if(n&&t.length&&delete t[t.length-1].personality[i.JOIN],s&&t.length){const e=t[t.length-1];e.text||Object.keys(e.personality).length?t.push(wn.create({text:"",personality:{pause:s}})):e.personality[i.PAUSE]=s}return t}addExternalAttributes_(t,e){var n;if(void 0===t.attributes.id&&(t.attributes.id=e.getAttribute("id")),null===(n=e.attributes)||void 0===n?void 0:n.length){const n=e.attributes;for(let e=n.length-1;e>=0;e--){const r=n[e].name;!t.attributes[r]&&r.match(/^ext/)&&(t.attributes[r]=n[e].value)}}}addRelativePersonality_(t,e){if(!t.personality)return t.personality=e,t;const n=t.personality;for(const[t,r]of Object.entries(e))n[t]&&"number"==typeof n[t]&&"number"==typeof r?n[t]=(n[t]+r).toString():n[t]||(n[t]=r);return t}updateConstraint_(){const t=R.getInstance().dynamicCstr,e=R.getInstance().options.strict,n=this.trie,o={};let i=t.getValue(r.LOCALE),s=t.getValue(r.MODALITY),a=t.getValue(r.DOMAIN);n.hasSubtrie([i,s,a])||(a=c.DEFAULT_VALUES[r.DOMAIN],n.hasSubtrie([i,s,a])||(s=c.DEFAULT_VALUES[r.MODALITY],n.hasSubtrie([i,s,a])||(i=c.DEFAULT_VALUES[r.LOCALE]))),o[r.LOCALE]=[i],o[r.MODALITY]=["summary"!==s?s:c.DEFAULT_VALUES[r.MODALITY]],o[r.DOMAIN]=["speech"!==s?c.DEFAULT_VALUES[r.DOMAIN]:a];const l=t.getOrder();for(let n,r=0;n=l[r];r++)if(!o[n]){const r=t.getValue(n),i=this.makeSet_(r,t.preference),s=c.DEFAULT_VALUES[n];e||r===s||i.push(s),o[n]=i}t.updateProperties(o)}makeSet_(t,e){return e&&Object.keys(e).length?t.split(":"):[t]}lookupRule(t,e){if(!t||t.nodeType!==x.ELEMENT_NODE&&t.nodeType!==x.TEXT_NODE)return null;const n=this.lookupRules(t,e);return n.length>0?this.pickMostConstraint_(e,n):null}lookupRules(t,e){return this.trie.lookupRules(t,e.allProperties())}pickMostConstraint_(t,e){const n=R.getInstance().comparator;return e.sort((function(t,e){return n.compare(t.dynamicCstr,e.dynamicCstr)||e.precondition.priority-t.precondition.priority||e.precondition.constraints.length-t.precondition.constraints.length||e.precondition.rank-t.precondition.rank})),N.getInstance().generate((()=>["Applicable Rules:",...e.map((t=>t.name+"("+t.dynamicCstr.toString()+")"))]).bind(this)),e[0]}}const Ua=new Map;R.nodeEvaluator=xa.getInstance().evaluateNode.bind(xa.getInstance());const Fa=["\u2806","\u2812","\u2832","\u2826","\u2834","\u2804"];const ka={small:["default"],capital:["default"],digit:["default"]};function Ba(t){const e=R.getInstance().options.locale;R.getInstance().options.locale=t,en(),mn({locale:t}),function(){const t=Nt.ALPHABETS,e=(t,e)=>{const n={};return Object.keys(t).forEach((t=>n[t]=!0)),Object.keys(e).forEach((t=>n[t]=!0)),Object.keys(n)};ka.small=e(t.smallPrefix,t.letterTrans),ka.capital=e(t.capPrefix,t.letterTrans),ka.digit=e(t.digitPrefix,t.digitTrans)}();for(const t of et.values()){const e=t.unicode;if("offset"in t)Ha(e,t.font,t.offset||0);else{Va(e,Nt.ALPHABETS[t.base],t.font,!!t.capital)}}R.getInstance().options.locale=e,en()}function Ga(t){return function(t){return"string"==typeof t?{font:t,combiner:Nt.ALPHABETS.combiner}:{font:t[0],combiner:Nt.COMBINERS[t[1]]||pt[t[1]]||Nt.ALPHABETS.combiner}}("normal"===t||"fullwidth"===t?"":Nt.MESSAGES.font[t]||Nt.MESSAGES.embellish[t]||"")}function Va(t,e,n,r){const o=Ga(n);for(let n,i,s=0;n=t[s],i=e[s];s++){const t=r?Nt.ALPHABETS.capPrefix:Nt.ALPHABETS.smallPrefix,e=r?ka.capital:ka.small;ja(o.combiner,n,i,o.font,t,Nt.ALPHABETS.letterTrans,e)}}function Ha(t,e,n){const r=Ga(e);for(let e,o=0;e=t[o];o++){const t=Nt.ALPHABETS.digitPrefix,i=o+n;ja(r.combiner,e,i,r.font,t,Nt.ALPHABETS.digitTrans,ka.digit)}}function ja(t,e,n,r,o,i,s){for(let a,c=0;a=s[c];c++){const s=a in i?i[a]:i.default,c=a in o?o[a]:o.default;An(a,"default",e,t(s(n),r,c))}}var qa=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function s(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))};const $a={functions:function(t){for(const n of t)mn(n)||(e=n.key,cn(n.names||[],an.Meaning.get(e)||{type:ot.FUNCTION,role:st.PREFIXFUNC}),"base"!==fn?(In(n),On(n)):Sn.set(n.key,n));var e},symbols:function(t){for(const e of t){if(mn(e))continue;const t=dn.parseUnicode(e.key);"base"!==fn?bn(t,t,e.mappings):Sn.set(t,e)}},units:function(t){for(const e of t)mn(e)||(e.key+=":unit","base"!==fn?(In(e),e.names&&(e.names=e.names.map((function(t){return t+":unit"}))),e.si&&Cn(e),On(e)):Sn.set(e.key,e))},si:t=>t.forEach(En),messages:function(t){const e=tn[t.locale];if(!e)return void console.error("Locale "+t.locale+" does not exist!");const n=t.kind.toUpperCase(),r=t.messages;if(!r)return;const o=e();for(const[t,e]of Object.entries(r))o[n][t]=e},rules:xa.addStore,characters:t=>t.forEach(Rn)};let Xa=!1;function Ya(){return qa(this,arguments,void 0,(function*(t=R.getInstance().options.locale){return Xa||(!function(){for(const t of et.values()){const e=t.unicode;for(const n of e)Sn.set(n,{key:n,category:t.category})}}(),Wa(c.BASE_LOCALE),Xa=!0),O.promises[c.BASE_LOCALE].then((()=>qa(this,void 0,void 0,(function*(){const e=R.getInstance().defaultLocale;return e?(Wa(e),O.promises[e].then((()=>qa(this,void 0,void 0,(function*(){return Wa(t),O.promises[t]}))))):(Wa(t),O.promises[t])}))))}))}function Wa(t=R.getInstance().options.locale){O.loaded[t]||(O.loaded[t]=[!1,!1],fn=c.DEFAULT_VALUES[r.LOCALE],pn=c.DEFAULT_VALUES[r.MODALITY],function(t){const e=function(){if(R.getInstance().customLoader)return R.getInstance().customLoader;return Ka()}(),n=new Promise((n=>{e(t).then((e=>{var r;(function(t,e){let n=!0;for(let r,o=0;r=Object.keys(t)[o];o++){const o=r.split("/");e&&e!==o[0]||(n&&"symbols"===o[1]&&"base"!==o[0]&&(Ba(o[0]),n=!1),$a[o[1]](t[r]))}})("string"==typeof(r=e)?JSON.parse(r):r),O.loaded[t]=[!0,!0],n(t)}),(e=>{O.loaded[t]=[!0,!1],console.error(`Unable to load locale: ${t}`),R.getInstance().options.locale=R.getInstance().defaultLocale,n(t)}))}));O.promises[t]=n}(t))}function Ka(){switch(R.getInstance().mode){case o.ASYNC:return za;case o.HTTP:return Ja;case o.SYNC:default:return Qa}}function za(t){const e=E(t);return new Promise(((t,n)=>{m.f.fs.readFile(e,"utf8",((e,r)=>{if(e)return n(e);t(r)}))}))}function Qa(t){const e=E(t);return new Promise(((t,n)=>{let r="{}";try{r=m.f.fs.readFileSync(e,"utf8")}catch(t){return n(t)}t(r)}))}function Ja(t){const e=E(t),n=new XMLHttpRequest;return new Promise(((t,r)=>{n.onreadystatechange=function(){if(4===n.readyState){const e=n.status;0===e||e>=200&&e<400?t(n.responseText):r(e)}},n.open("GET",e,!0),n.send()}))}var Za=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function s(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))};function tc(t){return Za(this,void 0,void 0,(function*(){t.debug&&N.getInstance().init();const e=R.getInstance();return e.setup(t),en(),e.setDynamicCstr(),e.init?(O.promises.init=new Promise(((t,e)=>{setTimeout((()=>{t("init")}),10)})),e.init=!1,O.get()):e.options.delay?(e.options.delay=!1,O.get()):Ya()}))}class ec{constructor(t){this.type=t,this.factory_=new Lr}getFactory(){return this.factory_}setFactory(t){this.factory_=t}getType(){return this.type}parseList(t){const e=[];for(let n,r=0;n=t[r];r++)e.push(this.parse(n));return e}}class nc extends ec{static getAttribute_(t,e,n){if(!t.hasAttribute(e))return n;const r=t.getAttribute(e);return r.match(/^\s*$/)?null:r}constructor(t){super("MathML"),this.options=t,er.options=t,this.parseMap_=new Map([[ur.SEMANTICS,this.semantics_.bind(this)],[ur.MATH,this.rows_.bind(this)],[ur.MROW,this.rows_.bind(this)],[ur.MPADDED,this.rows_.bind(this)],[ur.MSTYLE,this.rows_.bind(this)],[ur.MFRAC,this.fraction_.bind(this)],[ur.MSUB,this.limits_.bind(this)],[ur.MSUP,this.limits_.bind(this)],[ur.MSUBSUP,this.limits_.bind(this)],[ur.MOVER,this.limits_.bind(this)],[ur.MUNDER,this.limits_.bind(this)],[ur.MUNDEROVER,this.limits_.bind(this)],[ur.MROOT,this.root_.bind(this)],[ur.MSQRT,this.sqrt_.bind(this)],[ur.MTABLE,this.table_.bind(this)],[ur.MLABELEDTR,this.tableLabeledRow_.bind(this)],[ur.MTR,this.tableRow_.bind(this)],[ur.MTD,this.tableCell_.bind(this)],[ur.MS,this.text_.bind(this)],[ur.MTEXT,this.text_.bind(this)],[ur.MSPACE,this.space_.bind(this)],[ur.ANNOTATIONXML,this.text_.bind(this)],[ur.MI,this.identifier_.bind(this)],[ur.MN,this.number_.bind(this)],[ur.MO,this.operator_.bind(this)],[ur.MFENCED,this.fenced_.bind(this)],[ur.MENCLOSE,this.enclosed_.bind(this)],[ur.MMULTISCRIPTS,this.multiscripts_.bind(this)],[ur.ANNOTATION,this.empty_.bind(this)],[ur.NONE,this.empty_.bind(this)],[ur.MACTION,this.action_.bind(this)]]);const e={type:ot.IDENTIFIER,role:st.NUMBERSET,font:ct.DOUBLESTRUCK};["C","H","N","P","Q","R","Z","\u2102","\u210d","\u2115","\u2119","\u211a","\u211d","\u2124"].forEach((t=>this.getFactory().defaultMap.set(t,e)).bind(this))}parse(t){fo.getInstance().setNodeFactory(this.getFactory());const e=D(t.childNodes),n=W(t),r=this.parseMap_.get(n),o=(r||this.dummy_.bind(this))(t,e);return br(o,t),-1!==[ur.MATH,ur.MROW,ur.MPADDED,ur.MSTYLE,ur.SEMANTICS,ur.MACTION].indexOf(n)||(o.mathml.unshift(t),o.mathmlTree=t),o}semantics_(t,e){return e.length?this.parse(e[0]):this.getFactory().makeEmptyNode()}rows_(t,e){const n=t.getAttribute("semantics");if(n&&n.match("bspr_"))return fo.proof(t,n,this.parseList.bind(this));let r;if(1===(e=Ir(e)).length)r=this.parse(e[0]),r.type!==ot.EMPTY||r.mathmlTree||(r.mathmlTree=t);else{const n=er.run("function_from_identifiers",t);r=n&&n!==t?n:fo.getInstance().row(this.parseList(e))}return r.mathml.unshift(t),r}fraction_(t,e){if(!e.length)return this.getFactory().makeEmptyNode();const n=this.parse(e[0]),r=e[1]?this.parse(e[1]):this.getFactory().makeEmptyNode();return fo.getInstance().fractionLikeNode(n,r,t.getAttribute("linethickness"),"true"===t.getAttribute("bevelled"))}limits_(t,e){return fo.getInstance().limitNode(W(t),this.parseList(e))}root_(t,e){return e[1]?this.getFactory().makeBranchNode(ot.ROOT,[this.parse(e[1]),this.parse(e[0])],[]):this.sqrt_(t,e)}sqrt_(t,e){const n=this.parseList(Ir(e));return this.getFactory().makeBranchNode(ot.SQRT,[fo.getInstance().row(n)],[])}table_(t,e){const n=t.getAttribute("semantics");if(n&&n.match("bspr_"))return fo.proof(t,n,this.parseList.bind(this));const r=this.getFactory().makeBranchNode(ot.TABLE,this.parseList(e),[]);return r.mathmlTree=t,fo.tableToMultiline(r)}tableRow_(t,e){const n=this.getFactory().makeBranchNode(ot.ROW,this.parseList(e),[]);return n.role=st.TABLE,n}tableLabeledRow_(t,e){var n;if(!e.length)return this.tableRow_(t,e);const r=this.parse(e[0]);r.role=st.LABEL,(null===(n=r.childNodes[0])||void 0===n?void 0:n.type)===ot.TEXT&&(r.childNodes[0].role=st.LABEL);const o=this.getFactory().makeBranchNode(ot.ROW,this.parseList(e.slice(1)),[r]);return o.role=st.TABLE,o}tableCell_(t,e){const n=this.parseList(Ir(e));let r;r=n.length?1===n.length&&Mr(n[0],ot.EMPTY)?n:[fo.getInstance().row(n)]:[];const o=this.getFactory().makeBranchNode(ot.CELL,r,[]);return o.role=st.TABLE,o}space_(t,e){const n=t.getAttribute("width"),r=n&&n.match(/[a-z]*$/);if(!r)return this.empty_(t,e);const o=r[0],i=parseFloat(n.slice(0,r.index)),s={cm:.4,pc:.5,em:.5,ex:1,in:.15,pt:5,mm:5}[o];if(!s||isNaN(i)||i<s)return this.empty_(t,e);const a=this.getFactory().makeUnprocessed(t);return fo.getInstance().text(a,W(t))}text_(t,e){const n=this.leaf_(t,e);return t.textContent?(n.updateContent(t.textContent,!0),fo.getInstance().text(n,W(t))):n}identifier_(t,e){const n=this.leaf_(t,e);return fo.getInstance().identifierNode(n,fo.getInstance().font(t.getAttribute("mathvariant")),t.getAttribute("class"))}number_(t,e){const n=this.leaf_(t,e);return fo.number(n),n}operator_(t,e){const n=this.leaf_(t,e);return fo.getInstance().operatorNode(n),n}fenced_(t,e){const n=this.parseList(Ir(e)),r=nc.getAttribute_(t,"separators",","),o=nc.getAttribute_(t,"open","("),i=nc.getAttribute_(t,"close",")"),s=fo.getInstance().mfenced(o,i,r,n);return fo.getInstance().tablesInRow([s])[0]}enclosed_(t,e){const n=this.parseList(Ir(e)),r=this.getFactory().makeBranchNode(ot.ENCLOSE,[fo.getInstance().row(n)],[]);return r.role=t.getAttribute("notation")||st.UNKNOWN,r}multiscripts_(t,e){if(!e.length)return this.getFactory().makeEmptyNode();const n=this.parse(e.shift());if(!e.length)return n;const r=[],o=[],i=[],s=[];let a=!1,c=0;for(let t,n=0;t=e[n];n++)W(t)!==ur.MPRESCRIPTS?(a?1&c?r.push(t):o.push(t):1&c?i.push(t):s.push(t),c++):(a=!0,c=0);return Ir(r).length||Ir(o).length?fo.getInstance().tensor(n,this.parseList(o),this.parseList(r),this.parseList(s),this.parseList(i)):fo.getInstance().pseudoTensor(n,this.parseList(s),this.parseList(i))}empty_(t,e){return this.getFactory().makeEmptyNode()}action_(t,e){const n=e[t.hasAttribute("selection")?parseInt(t.getAttribute("selection"),10)-1:0],r=this.parse(n);return r.mathmlTree=n,r}dummy_(t,e){const n=this.getFactory().makeUnprocessed(t);return n.role=t.tagName,n.textContent=t.textContent,n}leaf_(t,e){if(1===e.length&&e[0].nodeType!==x.TEXT_NODE){const n=this.getFactory().makeUnprocessed(t);return n.role=e[0].tagName,br(n,e[0]),n}const n=this.getFactory().makeLeafNode(t.textContent,fo.getInstance().font(t.getAttribute("mathvariant")));return t.hasAttribute("data-latex")&&an.LatexCommands.set(t.getAttribute("data-latex"),t.textContent),n}}class rc{constructor(t,e,n=t=>!1){this.name=t,this.apply=e,this.applicable=n}}class oc extends rc{}class ic extends rc{}const sc="data-semantic-";var ac;!function(t){t.ADDED="data-semantic-added",t.ALTERNATIVE="data-semantic-alternative",t.CHILDREN="data-semantic-children",t.COLLAPSED="data-semantic-collapsed",t.CONTENT="data-semantic-content",t.EMBELLISHED="data-semantic-embellished",t.FENCEPOINTER="data-semantic-fencepointer",t.FONT="data-semantic-font",t.ID="data-semantic-id",t.ANNOTATION="data-semantic-annotation",t.ATTRIBUTES="data-semantic-attributes",t.OPERATOR="data-semantic-operator",t.OWNS="data-semantic-owns",t.PARENT="data-semantic-parent",t.POSTFIX="data-semantic-postfix",t.PREFIX="data-semantic-prefix",t.ROLE="data-semantic-role",t.SPEECH="data-semantic-speech",t.STRUCTURE="data-semantic-structure",t.SUMMARY="data-semantic-summary",t.TYPE="data-semantic-type"}(ac||(ac={}));const cc=[ac.ADDED,ac.ALTERNATIVE,ac.CHILDREN,ac.COLLAPSED,ac.CONTENT,ac.EMBELLISHED,ac.FENCEPOINTER,ac.FONT,ac.ID,ac.ANNOTATION,ac.ATTRIBUTES,ac.OPERATOR,ac.OWNS,ac.PARENT,ac.POSTFIX,ac.PREFIX,ac.ROLE,ac.SPEECH,ac.STRUCTURE,ac.SUMMARY,ac.TYPE];function lc(t){return t.map((function(t){return t.id})).join(",")}function uc(t,e){t.setAttribute(ac.TYPE,e.type);const n=e.allAttributes();for(let e,r=0;e=n[r];r++)t.setAttribute(sc+e[0].toLowerCase(),e[1]);e.childNodes.length&&t.setAttribute(ac.CHILDREN,lc(e.childNodes)),e.contentNodes.length&&t.setAttribute(ac.CONTENT,lc(e.contentNodes)),e.parent&&t.setAttribute(ac.PARENT,e.parent.id.toString());const r=e.attributesXml();r&&t.setAttribute(ac.ATTRIBUTES,r),function(t,e){const n=[];e.role===st.MGLYPH&&n.push("image");e.attributes.href&&n.push("link");n.length&&t.setAttribute(ac.POSTFIX,n.join(" "))}(t,e)}function hc(t){return sc+t}function dc(){const t=G("mrow");return t.setAttribute(ac.ADDED,"true"),t}class fc{static fromTree(t){return fc.fromNode(t.root)}static fromNode(t){return new fc(fc.fromNode_(t))}static fromString(t){return new fc(fc.fromString_(t))}static simpleCollapseStructure(t){return"number"==typeof t}static contentCollapseStructure(t){return!!t&&!fc.simpleCollapseStructure(t)&&"c"===t[0]}static interleaveIds(t,e){return Zn(fc.collapsedLeafs(t),fc.collapsedLeafs(e))}static collapsedLeafs(...t){return t.reduce(((t,e)=>{return t.concat((n=e,fc.simpleCollapseStructure(n)?[n]:fc.contentCollapseStructure(n[1])?n.slice(2):n.slice(1)));var n}),[])}static fromStructure(t,e,n){return new fc(fc.tree_(t,e.root,n))}static combineContentChildren(t,e,n,r){switch(t){case ot.RELSEQ:case ot.INFIXOP:case ot.MULTIREL:return Zn(r,n);case ot.PREFIXOP:return n.concat(r);case ot.POSTFIXOP:return r.concat(n);case ot.MATRIX:case ot.VECTOR:case ot.FENCED:return r.unshift(n[0]),r.push(n[1]),r;case ot.CASES:return r.unshift(n[0]),r;case ot.APPL:return[r[0],n[0],r[1]];case ot.ROOT:return[r[0],r[1]];case ot.ROW:case ot.LINE:return n.length&&r.unshift(n[0]),r;default:return r}}static makeSexp_(t){return fc.simpleCollapseStructure(t)?t.toString():fc.contentCollapseStructure(t)?"(c "+t.slice(1).map(fc.makeSexp_).join(" ")+")":"("+t.map(fc.makeSexp_).join(" ")+")"}static fromString_(t){let e=t.replace(/\(/g,"[");return e=e.replace(/\)/g,"]"),e=e.replace(/ /g,","),e=e.replace(/c/g,'"c"'),JSON.parse(e)}static fromNode_(t){if(!t)return[];const e=t.contentNodes;let n;e.length&&(n=e.map(fc.fromNode_),n.unshift("c"));const r=t.childNodes;if(!r.length)return e.length?[t.id,n]:t.id;const o=r.map(fc.fromNode_);return e.length&&o.unshift(n),o.unshift(t.id),o}static tree_(t,e,n,r=0,o=1,i=1){if(!e)return[];const s=e.id,a=[s];w(t);const c=v(`.//self::*[@${ac.ID}=${s}]`,t)[0];if(!e.childNodes.length)return fc.addAria(c,r,o,i,n),e.id;const l=fc.combineContentChildren(e.type,e.role,e.contentNodes.map((function(t){return t})),e.childNodes.map((function(t){return t})));c&&fc.addOwns_(c,l);for(let e,o=0,i=l.length;e=l[o];o++)a.push(fc.tree_(t,e,n,r+1,o+1,i));return fc.addAria(c,r,o,i,n),a}static addAria(t,e,n,r,o){const i=o.tree?e?"treeitem":"tree":"treeitem";o.aria&&t&&(t.setAttribute("aria-level",e.toString()),t.setAttribute("aria-posinset",n.toString()),t.setAttribute("aria-setsize",r.toString()),t.setAttribute("role",i),t.hasAttribute(ac.OWNS)&&t.setAttribute("aria-owns",t.getAttribute(ac.OWNS)))}static addOwns_(t,e){const n=t.getAttribute(ac.COLLAPSED),r=n?fc.realLeafs_(fc.fromString(n).array):e.map((t=>t.id));t.setAttribute(ac.OWNS,r.join(" "))}static realLeafs_(t){if(fc.simpleCollapseStructure(t))return[t];if(fc.contentCollapseStructure(t))return[];let e=[];for(let n=1;n<t.length;n++)e=e.concat(fc.realLeafs_(t[n]));return e}constructor(t){this.parents=null,this.levelsMap=null,t=0===t?t:t||[],this.array=t}populate(){this.parents&&this.levelsMap||(this.parents={},this.levelsMap={},this.populate_(this.array,this.array,[]))}toString(){return fc.makeSexp_(this.array)}populate_(t,e,n){if(fc.simpleCollapseStructure(t))return this.levelsMap[t]=e,void(this.parents[t]=t===n[0]?n.slice(1):n);const r=fc.contentCollapseStructure(t)?t.slice(1):t,o=[r[0]].concat(n);for(let e=0,n=r.length;e<n;e++){const n=r[e];this.populate_(n,t,o)}}isRoot(t){return t===this.levelsMap[t][0]}directChildren(t){if(!this.isRoot(t))return[];return this.levelsMap[t].slice(1).map((t=>fc.simpleCollapseStructure(t)?t:fc.contentCollapseStructure(t)?t[1]:t[0]))}subtreeNodes(t){if(!this.isRoot(t))return[];const e=(t,n)=>{fc.simpleCollapseStructure(t)?n.push(t):(fc.contentCollapseStructure(t)&&(t=t.slice(1)),t.forEach((t=>e(t,n))))},n=this.levelsMap[t],r=[];return e(n.slice(1),r),r}}function pc(t,e,n){let r=null;if(!t.length)return r;const o=n[n.length-1],i=o&&o.length,s=e&&e.length,a=fo.getInstance();if(i&&s){if(e[0].type===ot.INFIXOP&&e[0].role===st.IMPLICIT)return r=t.pop(),o.push(a.postfixNode_(o.pop(),t)),r;r=t.shift();const n=a.prefixNode_(e.shift(),t);return e.unshift(n),r}return i?(o.push(a.postfixNode_(o.pop(),t)),r):(s&&e.unshift(a.prefixNode_(e.shift(),t)),r)}function mc(t,e,n){if(!e.length)return t;const r=t.pop(),o=e.shift(),i=n.shift();if(o.type===ot.INFIXOP&&(o.role===st.IMPLICIT||o.role===st.UNIT)){N.getInstance().output("Juxta Heuristic Case 2");const s=(r?[r,o]:[o]).concat(i);return mc(t.concat(s),e,n)}if(!r)return N.getInstance().output("Juxta Heuristic Case 3"),mc([o].concat(i),e,n);const s=i.shift();if(!s){N.getInstance().output("Juxta Heuristic Case 9");const i=er.factory.makeBranchNode(ot.INFIXOP,[r,e.shift()],[o],o.textContent);return i.role=st.IMPLICIT,er.run("combine_juxtaposition",i),e.unshift(i),mc(t,e,n)}if(Br(r)||Br(s))return N.getInstance().output("Juxta Heuristic Case 4"),mc(t.concat([r,o,s]).concat(i),e,n);let a=null;return so(r)&&so(s)?(N.getInstance().output("Juxta Heuristic Case 5"),r.contentNodes.push(o),r.contentNodes=r.contentNodes.concat(s.contentNodes),r.childNodes.push(s),r.childNodes=r.childNodes.concat(s.childNodes),s.childNodes.forEach((t=>t.parent=r)),o.parent=r,r.addMathmlNodes(o.mathml),r.addMathmlNodes(s.mathml),a=r):so(r)?(N.getInstance().output("Juxta Heuristic Case 6"),r.contentNodes.push(o),r.childNodes.push(s),s.parent=r,o.parent=r,r.addMathmlNodes(o.mathml),r.addMathmlNodes(s.mathml),a=r):so(s)?(N.getInstance().output("Juxta Heuristic Case 7"),s.contentNodes.unshift(o),s.childNodes.unshift(r),r.parent=s,o.parent=s,s.addMathmlNodes(o.mathml),s.addMathmlNodes(r.mathml),a=s):(N.getInstance().output("Juxta Heuristic Case 8"),a=er.factory.makeBranchNode(ot.INFIXOP,[r,s],[o],o.textContent),a.role=st.IMPLICIT),t.push(a),mc(t.concat(i),e,n)}function gc(t){return t.childNodes[0]&&t.childNodes[0].childNodes[0]&&W(t.childNodes[0])===ur.MPADDED&&W(t.childNodes[0].childNodes[0])===ur.MPADDED&&W(t.childNodes[0].childNodes[t.childNodes[0].childNodes.length-1])===ur.MPHANTOM}er.add(new oc("combine_juxtaposition",(function(t){for(let e,n=t.childNodes.length-1;e=t.childNodes[n];n--)so(e)&&!e.nobreaking&&(t.childNodes.splice(n,1,...e.childNodes),t.contentNodes.splice(n,0,...e.contentNodes),e.childNodes.concat(e.contentNodes).forEach((function(e){e.parent=t})),t.addMathmlNodes(e.mathml));return t}))),er.add(new oc("propagateSimpleFunction",(t=>(t.type!==ot.INFIXOP&&t.type!==ot.FRACTION||!t.childNodes.every(Jr)||(t.role=st.COMPFUNC),t)),(t=>"clearspeak"===er.options.domain))),er.add(new oc("simpleNamedFunction",(t=>(t.role!==st.UNIT&&-1!==["f","g","h","F","G","H"].indexOf(t.textContent)&&(t.role=st.SIMPLEFUNC),t)),(t=>"clearspeak"===er.options.domain))),er.add(new oc("propagateComposedFunction",(t=>(t.type===ot.FENCED&&t.childNodes[0].role===st.COMPFUNC&&(t.role=st.COMPFUNC),t)),(t=>"clearspeak"===er.options.domain))),er.add(new oc("multioperator",(t=>{t.role!==st.UNKNOWN||t.textContent.length<=1||(fo.compSemantics(t,"role",st),fo.compSemantics(t,"type",ot))}))),er.add(new ic("convert_juxtaposition",(t=>{let e=Or(t,(function(t){return t.textContent===rn.invisibleTimes&&t.type===ot.OPERATOR}));e=e.rel.length?function(t){const e=[],n=[];let r=t.comp.shift(),o=null,i=[];for(;t.comp.length;)if(i=[],r.length)o&&e.push(o),n.push(r),o=t.rel.shift(),r=t.comp.shift();else{for(o&&i.push(o);!r.length&&t.comp.length;)r=t.comp.shift(),i.push(t.rel.shift());o=pc(i,r,n)}i.length||r.length?(e.push(o),n.push(r)):(i.push(o),pc(i,r,n));return{rel:e,comp:n}}(e):e,t=e.comp[0];for(let n,r,o=1;n=e.comp[o],r=e.rel[o-1];o++)t.push(r),t=t.concat(n);return e=Or(t,(function(t){return t.textContent===rn.invisibleTimes&&(t.type===ot.OPERATOR||t.type===ot.INFIXOP)})),e.rel.length?mc(e.comp.shift(),e.rel,e.comp):t}))),er.add(new oc("simple2prefix",(t=>(t.textContent.length>1&&!t.textContent[0].match(/[A-Z]/)&&(t.role=st.PREFIXFUNC),t)),(t=>"braille"===er.options.modality&&t.type===ot.IDENTIFIER))),er.add(new oc("detect_cycle",(t=>{t.type=ot.MATRIX,t.role=st.CYCLE;const e=t.childNodes[0];return e.type=ot.ROW,e.role=st.CYCLE,e.textContent="",e.contentNodes=[],t}),(t=>t.type===ot.FENCED&&t.childNodes[0].type===ot.INFIXOP&&t.childNodes[0].role===st.IMPLICIT&&t.childNodes[0].childNodes.every((function(t){return t.type===ot.NUMBER}))&&t.childNodes[0].contentNodes.every((function(t){return t.role===st.SPACE}))))),er.add(new ic("intvar_from_implicit",(function(t){const e=t[0].childNodes;t.splice(0,1,...e)}),(t=>t[0]&&io(t[0])))),er.add(new oc("intvar_from_fraction",(function(t){const e=t.childNodes[1],n=e.childNodes[0];if(Ur(n))return void(n.role=st.INTEGRAL);if(!io(n))return;const r=n.childNodes.length,o=n.childNodes[r-2],i=n.childNodes[r-1];if(Ur(i))return void(i.role=st.INTEGRAL);if(xr(o,i)){const t=fo.getInstance().prefixNode_(i,[o]);t.role=st.INTEGRAL,2===r?e.childNodes[0]=t:(n.childNodes.pop(),n.contentNodes.pop(),n.childNodes[r-2]=t,t.parent=n)}}),(t=>{if(t.type!==ot.INTEGRAL)return!1;const[,e,n]=t.childNodes;return n.type===ot.EMPTY&&e.type===ot.FRACTION}))),er.add(new oc("rewrite_subcases",(function(t){t.addAnnotation("Emph","top");let e=[];if(t.hasAnnotation("Emph","left")){const n=Nc(t.childNodes[0].childNodes[0].childNodes[0],!0);n.forEach((t=>t.addAnnotation("Emph","left"))),e=e.concat(n);for(let e,n=0;e=t.childNodes[n];n++)e.childNodes.shift()}if(e.push(t),t.hasAnnotation("Emph","right")){const n=Nc(t.childNodes[0].childNodes[t.childNodes[0].childNodes.length-1].childNodes[0]);n.forEach((t=>t.addAnnotation("Emph","left"))),e=e.concat(n),t.childNodes[0].childNodes.pop()}fo.tableToMultiline(t);const n=fo.getInstance().row(e),r=t.annotation.Emph;return t.annotation.Emph=["table"],r.forEach((t=>n.addAnnotation("Emph",t))),n}),(t=>{let e=!0,n=!0;if(gc(t.childNodes[0].childNodes[0].mathmlTree)){for(let n,r=1;n=t.childNodes[r];r++)if(n.childNodes[0].childNodes.length){e=!1;break}}else e=!1;e&&t.addAnnotation("Emph","left");if(gc(t.childNodes[0].childNodes[t.childNodes[0].childNodes.length-1].mathmlTree)){const e=t.childNodes[0].childNodes.length;for(let r,o=1;r=t.childNodes[o];o++)if(r.childNodes.length>=e){n=!1;break}}else n=!1;return n&&t.addAnnotation("Emph","right"),e||n})));const Ec=[ot.PUNCTUATED,ot.RELSEQ,ot.MULTIREL,ot.INFIXOP,ot.PREFIXOP,ot.POSTFIXOP];function Nc(t,e){if(!t.childNodes.length)return Tc(t),[t];let n=null;if(t.type===ot.PUNCTUATED&&(e?t.role===st.ENDPUNCT:t.role===st.STARTPUNCT)){const r=t.childNodes;Tc(r[e?r.length-1:0])&&(t=r[e?0:r.length-1],n=r[e?r.length-1:0])}if(-1!==Ec.indexOf(t.type)){const r=t.childNodes;Tc(r[e?r.length-1:0]);const o=fc.combineContentChildren(t.type,t.role,t.contentNodes,t.childNodes);return n&&(e?o.push(n):o.unshift(n)),o}return n?e?[t,n]:[n,t]:[t]}const Sc={[st.METRIC]:st.METRIC,[st.VBAR]:st.NEUTRAL,[st.OPENFENCE]:st.OPEN,[st.CLOSEFENCE]:st.CLOSE};function Tc(t){if(t.type!==ot.PUNCTUATION)return!1;const e=Sc[t.role];return!!e&&(t.role=e,t.type=ot.FENCE,t.addAnnotation("Emph","fence"),!0)}function Ic(t,e,n,r=n){const o=[];for(;t&&t.role===n;)o.push(t),t=e.shift();return o.length?(t&&e.unshift(t),[1===o.length?o[0]:bc(o,r),e]):[t,e]}function bc(t,e){const n=er.factory.makeBranchNode(ot.PUNCTUATION,t,[]);return n.role=e,n}function Ac(t){return["[","\uff3b"].includes(t)}function Rc(t){return["]","\uff3d"].includes(t)}function Oc(t){return["(","\u207d","\u208d"].includes(t)}function Cc(t){return[")","\u207e","\u208e"].includes(t)}function yc(t){return t.role===st.INFTY||t.type===ot.PREFIXOP&&t.childNodes[0].role===st.INFTY}function Lc(t){const e=t.childNodes[0];if(t.type!==ot.FENCED||(null==e?void 0:e.type)!==ot.PUNCTUATED||3!==(null==e?void 0:e.childNodes.length)||1!==(null==e?void 0:e.contentNodes.length)||(null==e?void 0:e.childNodes[1].role)!==st.COMMA)return!1;const n=t.childNodes[0].childNodes[0],r=t.childNodes[0].childNodes[2],o=t.contentNodes[0].textContent,i=t.contentNodes[1].textContent;return!!(Ac(o)&&Cc(i)||Oc(o)&&Rc(i))||!(!Oc(o)||!Cc(i)||!yc(n)&&!yc(r))}function Mc(t){return Dc(t)||function(t){return vc(t)||t.type===ot.INFIXOP&&t.role===st.IMPLICIT&&(2===t.childNodes.length&&(vc(t.childNodes[0])||Dc(t.childNodes[0]))&&vc(t.childNodes[1])||3===t.childNodes.length&&Dc(t.childNodes[0])&&vc(t.childNodes[1])&&vc(t.childNodes[2]))}(t)||function(t){return t.type===ot.PUNCTUATED&&t.role===st.ENDPUNCT&&2===t.childNodes.length&&t.childNodes[1].role===st.DEGREE&&(vc(t.childNodes[0])||wc(t.childNodes[0])||t.childNodes[0].type===ot.PREFIXOP&&t.childNodes[0].role===st.NEGATIVE&&(vc(t.childNodes[0].childNodes[0])||wc(t.childNodes[0].childNodes[0])))}(t)||function(t){return t.type===ot.PREFIXOP&&t.role===st.NEGATIVE&&_c(t.childNodes[0])&&t.childNodes[0].type!==ot.PREFIXOP&&t.childNodes[0].type!==ot.APPL&&t.childNodes[0].type!==ot.PUNCTUATED}(t)||function(t){return t.type===ot.APPL&&(t.childNodes[0].role===st.PREFIXFUNC||t.childNodes[0].role===st.SIMPLEFUNC)&&(_c(t.childNodes[1])||t.childNodes[1].type===ot.FENCED&&_c(t.childNodes[1].childNodes[0]))}(t)}function _c(t){return t.hasAnnotation("clearspeak","simple")}function vc(t){return t.type===ot.IDENTIFIER&&(t.role===st.LATINLETTER||t.role===st.GREEKLETTER||t.role===st.OTHERLETTER||t.role===st.SIMPLEFUNC)}function wc(t){return t.type===ot.NUMBER&&(t.role===st.INTEGER||t.role===st.FLOAT)}function Dc(t){return wc(t)||function(t){if(Pc("Fraction_Over")||Pc("Fraction_FracOver"))return!1;if(t.type!==ot.FRACTION||t.role!==st.VULGAR)return!1;if(Pc("Fraction_Ordinal"))return!0;const e=parseInt(t.childNodes[0].textContent,10),n=parseInt(t.childNodes[1].textContent,10);return e>0&&e<20&&n>0&&n<11}(t)}function Pc(t){return R.getInstance().options.style===t}function xc(t){return t.type===ot.TEXT&&t.role!==st.LABEL||t.type===ot.PUNCTUATED&&t.role===st.TEXT&&wc(t.childNodes[0])&&function(t){for(let e=0;e<t.length-1;e++)if(t[e].type!==ot.TEXT||""!==t[e].textContent)return!1;return t[t.length-1].type===ot.TEXT}(t.childNodes.slice(1))||t.type===ot.IDENTIFIER&&t.role===st.UNIT||t.type===ot.INFIXOP&&(t.role===st.IMPLICIT||t.role===st.UNIT)}er.add(new ic("ellipses",(t=>{const e=[];let n=t.shift();for(;n;)[n,t]=Ic(n,t,st.FULLSTOP,st.ELLIPSIS),[n,t]=Ic(n,t,st.DASH),e.push(n),n=t.shift();return e}),(t=>t.length>1))),er.add(new ic("op_with_limits",(t=>{const e=t[0];return e.type=ot.LARGEOP,e.role=st.SUM,t}),(t=>t[0].type===ot.OPERATOR&&t.slice(1).some((t=>t.type===ot.RELSEQ||t.type===ot.MULTIREL||t.type===ot.INFIXOP&&t.role===st.ELEMENT||t.type===ot.PUNCTUATED&&t.role===st.SEQUENCE))))),er.add(new class extends rc{}("function_from_identifiers",(t=>{const e=D(t.childNodes).map((t=>t.textContent.trim())).join("");if(an.Meaning.get(e).type===ot.UNKNOWN)return t;const n=er.factory.makeLeafNode(e,fo.getInstance().font(t.getAttribute("mathvariant")));return n.mathmlTree=t,n}),(t=>{const e=D(t.childNodes);return!(e.length<2)&&e.every((t=>W(t)===ur.MI&&an.Meaning.get(t.textContent.trim()).role===st.LATINLETTER))}))),er.add(new ic("bracketed_interval",(t=>{const e=t[0],n=t[1],r=t.slice(2),o=fo.getInstance().row(r),i=er.factory.makeBranchNode(ot.FENCED,[o],[e,n]);return i.role=st.INTERVAL,i}),(t=>{const e=t[0],n=t[1],r=t.slice(2);if(!(e&&n&&(Rc(e.textContent)&&(Ac(n.textContent)||Rc(n.textContent))||Ac(n.textContent)&&(Ac(e.textContent)||Rc(e.textContent)))))return!1;if(1===r.length&&r[0].type===ot.PUNCTUATED&&1===r[0].contentNodes.length)return!0;const o=Or(r,Vr);return!(1!==o.rel.length||!o.comp[0].length||!o.comp[1].length)}))),er.add(new oc("interval_heuristic",(t=>(t.role=st.INTERVAL,t)),(t=>Lc(t)))),er.add(new oc("propagateInterval",(t=>(t.childNodes.forEach((t=>{Lc(t)&&(t.role=st.INTERVAL)})),t)),(t=>ho(t)))),us(new ss("clearspeak","simple",(function(t){return Mc(t)?"simple":""}))),hs("clearspeak","simple"),us(new ss("clearspeak","unit",(function(t){return xc(t)?"unit":""}))),hs("clearspeak","unit");const Uc=[ot.MULTIREL,ot.RELSEQ,ot.APPL,ot.ROW,ot.LINE],Fc=[ot.SUBSCRIPT,ot.SUPERSCRIPT,ot.OVERSCORE,ot.UNDERSCORE];function kc(t,e){const n=t.parent;if(!n)return!1;const r=n.type;return-1!==Uc.indexOf(r)||r===ot.PREFIXOP&&n.role===st.NEGATIVE&&!e.script&&!e.enclosed||r===ot.PREFIXOP&&n.role===st.GEOMETRY||!(r!==ot.PUNCTUATED||e.enclosed&&n.role!==st.TEXT)}us(new as("nemeth","number",(function(t,e){return t.childNodes.length?(-1!==Fc.indexOf(t.type)&&(e.script=!0),t.type===ot.FENCED?(e.number=!1,e.enclosed=!0,["",e]):t.type===ot.PREFIXOP&&t.role!==st.GEOMETRY&&t.role!==st.NEGATIVE?(e.number=!1,["",e]):(kc(t,e)&&(e.number=!0,e.enclosed=!1),["",e])):(kc(t,e)&&(e.number=!0,e.script=!1,e.enclosed=!1),[e.number?"number":"",{number:!1,enclosed:e.enclosed,script:e.script}])}),{number:!0})),hs("nemeth","number"),us(new as("depth","depth",(function(t){return t.parent?[parseInt(t.parent.annotation.depth[0])+1]:[1]}))),hs("depth","depth");class Bc{static empty(){const t=P("<math/>"),e=new Bc(t,new T);return e.mathml=t,e}static fromNode(t,e){const n=Bc.empty();return n.root=t,e&&(n.mathml=e),n}static fromRoot(t,e){let n=t;for(;n.parent;)n=n.parent;const r=Bc.fromNode(n);return e&&(r.mathml=e),r}static fromXml(t){const e=Bc.empty();return t.childNodes[0]&&(e.root=yr.fromXml(t.childNodes[0])),e}constructor(t,e){this.mathml=t,this.options=e,this.parser=new nc(e),this.root=this.parser.parse(t),this.collator=this.parser.getFactory().leafMap.collateMeaning();const n=this.collator.newDefault();n&&(this.parser=new nc(e),this.parser.getFactory().defaultMap=n,this.root=this.parser.parse(t)),Gc.visit(this.root,{}),function(t){for(const e of cs.values())e.active&&e.annotate(t);for(const e of ls.values())e.active&&e.visit(t,Object.assign({},e.def))}(this.root)}xml(t){const e=P("<stree></stree>"),n=this.root.xml(e.ownerDocument,t);return e.appendChild(n),e}toString(t){return z(this.xml(t))}formatXml(t){return j(this.toString(t))}displayTree(){this.root.displayTree()}replaceNode(t,e){const n=t.parent;n?n.replaceChild(t,e):this.root=e}toJson(){const t={};return t.stree=this.root.toJson(),t}}const Gc=new as("general","unit",((t,e)=>(oo(t)&&(t.role=st.UNIT),!1)));function Vc(t,e){return Hc(t,e).xml()}function Hc(t,e){return new Bc(t,e)}const jc=[],qc=!0,$c=new Map;function Xc(t){N.getInstance().generate((()=>["WALKING START: ",t.toString()]));const e=function(t){for(let e,n=0;e=jc[n];n++)if(e.test(t))return e.constr(t);return null}(t);let n;if(e)return n=e.getMathml(),N.getInstance().generate((()=>["WALKING END: ",t.toString()])),rl(n);if(1===t.mathml.length){if(N.getInstance().output("Walktree Case 0"),!t.childNodes.length)return N.getInstance().output("Walktree Case 0.1"),n=t.mathml[0],uc(n,t),N.getInstance().generate((()=>["WALKING END: ",t.toString()])),rl(n);const e=t.childNodes[0];if(1===t.childNodes.length&&e.type===ot.EMPTY)return N.getInstance().output("Walktree Case 0.2"),n=t.mathml[0],uc(n,t),n.appendChild(Xc(e)),N.getInstance().generate((()=>["WALKING END: ",t.toString()])),rl(n);t.childNodes.forEach((t=>{t.mathml.length||(t.mathml=[hl(t)])}))}const r=t.contentNodes.map(ll);dl(t,r);const o=t.childNodes.map(Xc),i=fc.combineContentChildren(t.type,t.role,r,o);if(n=t.mathmlTree,null===n)N.getInstance().output("Walktree Case 1"),n=Yc(i,t);else{const t=tl(i);N.getInstance().output("Walktree Case 2"),t?(N.getInstance().output("Walktree Case 2.1"),n=al(t)):(N.getInstance().output("Walktree Case 2.2"),n=fl(n))}return n=ul(n),function(t,e,n){if(!e.length)return;if(1===e.length&&t===e[0])return;const r=n.role===st.IMPLICIT&&er.flags.combine_juxtaposition?function(t,e,n){const r=[];let o=D(t.childNodes),i=!1;for(;o.length;){const t=o.shift();if(t.hasAttribute(ac.TYPE)){r.push(t);continue}const n=Kc(t,e);0!==n.length&&(1!==n.length?(i?t.setAttribute("AuxiliaryImplicit",!0):i=!0,o=n.concat(o)):r.push(t))}const s=[],a=n.childNodes.map((function(t){return t.mathmlTree}));for(;a.length;){const t=a.pop();if(t){if(-1!==r.indexOf(t))break;-1!==e.indexOf(t)&&s.unshift(t)}}return r.concat(s)}(t,e,n):D(t.childNodes);if(!r.length)return void e.forEach((function(e){t.appendChild(e)}));let o=0;for(;e.length;){const n=e[0];if(r[o]===n||Jc(r[o],n)){e.shift(),o++;continue}if(r[o]&&-1===e.indexOf(r[o])){o++;continue}if(Qc(n,t)){e.shift();continue}const i=r[o];if(i)zc(t,i,n),e.shift();else{if(n.parentNode){t=al(n),e.shift();continue}const r=e[1];if(r&&r.parentNode){(t=al(r)).insertBefore(n,r),e.shift(),e.shift();continue}t.insertBefore(n,null),e.shift()}}}(n,i,t),$c.has(t.id)||($c.set(t.id,!0),uc(n,t)),N.getInstance().generate((()=>["WALKING END: ",t.toString()])),rl(n)}function Yc(t,e){const n=function(t){const e=tl(t);if(!e)return{type:Zc.INVALID,node:null};const n=tl(t.slice().reverse());if(e===n)return{type:Zc.VALID,node:e};const r=el(e),o=function(t,e){let n=0;for(;t[n]&&-1===e.indexOf(t[n]);)n++;return t.slice(0,n+1)}(r,t),i=el(n,(function(t){return-1!==o.indexOf(t)})),s=i[0],a=o.indexOf(s);if(-1===a)return{type:Zc.INVALID,node:null};return{type:o.length!==r.length?Zc.PRUNED:nl(o[a+1],i[1])?Zc.VALID:Zc.INVALID,node:s}}(t);let r=n.node;const o=n.type;if(o!==Zc.VALID||!Sr(r)||!r.parentNode&&e.parent)if(N.getInstance().output("Walktree Case 1.1"),r=dc(),o===Zc.PRUNED)N.getInstance().output("Walktree Case 1.1.0"),r=function(t,e,n){let r=ol(e);if(Er(r)){N.getInstance().output("Walktree Case 1.1.0.0"),Wc(r,t),D(r.childNodes).forEach((function(e){t.appendChild(e)}));const e=t;t=r,r=e}const o=n.indexOf(e);return n[o]=r,B(r,t),t.appendChild(r),n.forEach((function(e){t.appendChild(e)})),t}(r,n.node,t);else if(t[0]){N.getInstance().output("Walktree Case 1.1.1");const e=tl(t);if(e){const n=function(t,e){const n=D(t.childNodes);let r=1/0,o=-1/0;return e.forEach((function(t){const e=n.indexOf(t);-1!==e&&(r=Math.min(r,e),o=Math.max(o,e))})),n.slice(r,o+1)}(al(e),t);B(e,r),n.forEach((function(t){r.appendChild(t)}))}else Wc(r,t[0]),r=t[0]}return e.mathmlTree||(e.mathmlTree=r),r}function Wc(t,e){for(const n of cc)t.hasAttribute(n)&&(e.setAttribute(n,t.getAttribute(n)),t.removeAttribute(n))}function Kc(t,e){const n=[];let r=D(t.childNodes);for(;r.length;){const t=r.shift();t.nodeType===x.ELEMENT_NODE&&(t.hasAttribute(ac.TYPE)||-1!==e.indexOf(t)?n.push(t):r=D(t.childNodes).concat(r))}return n}function zc(t,e,n){let r=e,o=al(r);for(;o&&o.firstChild===r&&!r.hasAttribute("AuxiliaryImplicit")&&o!==t;)r=o,o=al(r);o&&(o.insertBefore(n,r),r.removeAttribute("AuxiliaryImplicit"))}function Qc(t,e){if(!t)return!1;do{if((t=al(t))===e)return!0}while(t);return!1}function Jc(t,e){const n=rn.functionApplication;if(t&&e&&t.textContent&&e.textContent&&t.textContent===n&&e.textContent===n&&"true"===e.getAttribute(ac.ADDED)){for(let n,r=0;n=t.attributes[r];r++)e.hasAttribute(n.nodeName)||e.setAttribute(n.nodeName,n.nodeValue);return B(t,e),!0}return!1}var Zc;function tl(t){let e=0,n=null;for(;!n&&e<t.length;)t[e].parentNode&&(n=t[e]),e++;return n}function el(t,e){const n=e||(t=>!1),r=[t];for(;!n(t)&&!Er(t)&&t.parentNode;)t=al(t),r.unshift(t);return r}function nl(t,e){return!(!t||!e||t.previousSibling||e.nextSibling)}function rl(t){for(;!Er(t)&&il(t);)t=al(t);return t}function ol(t){const e=D(t.childNodes);if(!e)return t;const n=e.filter((function(t){return t.nodeType===x.ELEMENT_NODE&&!Nr(t)}));return 1===n.length&&Sr(n[0])&&!n[0].hasAttribute(ac.TYPE)?ol(n[0]):t}function il(t){const e=al(t);return!(!e||!Sr(e))&&D(e.childNodes).every((function(e){return e===t||sl(e)}))}function sl(t){if(t.nodeType!==x.ELEMENT_NODE)return!0;if(!t||Nr(t))return!0;const e=D(t.childNodes);return!(!Sr(t)&&e.length||function(t){return!!t&&mr.includes(W(t))}(t)||t.hasAttribute(ac.TYPE)||Tr(t))&&D(t.childNodes).every(sl)}function al(t){return t.parentNode}function cl(t,e){const n=new fc(e);t.setAttribute(ac.COLLAPSED,n.toString())}function ll(t){if(t.mathml.length)return Xc(t);const e=qc?hl(t):dc();return t.mathml=[e],e}function ul(t){if(W(t)!==ur.MFENCED)return t;const e=dc();for(let n,r=0;n=t.attributes[r];r++)-1===["open","close","separators"].indexOf(n.name)&&e.setAttribute(n.name,n.value);return D(t.childNodes).forEach((function(t){e.appendChild(t)})),B(t,e),e}function hl(t){const e=G("mo"),n=H(t.textContent);return e.appendChild(n),uc(e,t),e.setAttribute(ac.ADDED,"true"),e}function dl(t,e){const n=t.type+(t.textContent?","+t.textContent:"");e.forEach((function(t){fl(t).setAttribute(ac.OPERATOR,n)}))}function fl(t){const e=D(t.childNodes);if(!e)return t;const n=e.filter((function(t){return!sl(t)})),r=[];for(let t,e=0;t=n[e];e++)if(Sr(t)&&t.getAttribute(ac.TYPE)!==ot.PUNCTUATION){const e=fl(t);e&&e!==t&&r.push(e)}else r.push(t);return 1===r.length?r[0]:t}function pl(t){return j(t.toString()).toString().replace(new RegExp(sc,"g"),"")}function ml(t,e){const n=!!e,r=e||[],o=t.parent,i=t.contentNodes.map((function(t){return t.id}));i.unshift("c");const s=[t.id,i];for(let e,i=0;e=t.childNodes[i];i++){const t=Xc(e);r.push(t);const i=fl(t);o&&!n&&i.setAttribute(ac.PARENT,o.id.toString()),s.push(e.id)}return s}!function(t){t.VALID="valid",t.INVALID="invalid",t.PRUNED="pruned"}(Zc||(Zc={}));class gl{constructor(t){this.semantic=t}}class El extends gl{static test(t){return!t.mathmlTree&&t.type===ot.LINE&&t.role===st.BINOMIAL}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){if(!this.semantic.childNodes.length)return this.mml;const t=this.semantic.childNodes[0];if(this.mml=Xc(t),this.mml.hasAttribute(ac.TYPE)){const t=dc();B(this.mml,t),t.appendChild(this.mml),this.mml=t}return uc(this.mml,this.semantic),this.mml}}class Nl extends gl{static test(t){if(!t.mathmlTree||!t.childNodes.length)return!1;const e=W(t.mathmlTree),n=t.childNodes[0].role;return e===ur.MSUBSUP&&n===st.SUBSUP||e===ur.MUNDEROVER&&n===st.UNDEROVER}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){const t=this.semantic.childNodes[0],e=t.childNodes[0],n=this.semantic.childNodes[1],r=t.childNodes[1],o=Xc(n),i=Xc(e),s=Xc(r);return uc(this.mml,this.semantic),this.mml.setAttribute(ac.CHILDREN,lc([e,r,n])),[i,s,o].forEach((t=>fl(t).setAttribute(ac.PARENT,this.mml.getAttribute(ac.ID)))),this.mml.setAttribute(ac.TYPE,t.role),cl(this.mml,[this.semantic.id,[t.id,e.id,r.id],n.id]),this.mml}}class Sl extends gl{static multiscriptIndex(t){return t.type===ot.PUNCTUATED&&t.contentNodes[0].role===st.DUMMY?ml(t):(Xc(t),t.id)}static createNone_(t){const e=G("none");return t&&uc(e,t),e.setAttribute(ac.ADDED,"true"),e}constructor(t){super(t),this.mml=t.mathmlTree}completeMultiscript(t,e){const n=D(this.mml.childNodes).slice(1);let r=0;const o=t=>{for(const e of t){const t=n[r];if(t&&e===parseInt(t.getAttribute(ac.ID)))t.setAttribute(ac.PARENT,this.semantic.id.toString()),r++;else if(t&&e===parseInt(fl(t).getAttribute(ac.ID)))fl(t).setAttribute(ac.PARENT,this.semantic.id.toString()),r++;else{const n=this.semantic.querySelectorAll((t=>t.id===e));this.mml.insertBefore(Sl.createNone_(n[0]),t||null)}}};o(t),n[r]&&W(n[r])!==ur.MPRESCRIPTS?this.mml.insertBefore(n[r],G("mprescripts")):r++,o(e)}}class Tl extends Sl{static test(t){if(!t.mathmlTree)return!1;return W(t.mathmlTree)===ur.MMULTISCRIPTS&&(t.type===ot.SUPERSCRIPT||t.type===ot.SUBSCRIPT)}constructor(t){super(t)}getMathml(){let t,e,n;if(uc(this.mml,this.semantic),this.semantic.childNodes[0]&&this.semantic.childNodes[0].role===st.SUBSUP){const r=this.semantic.childNodes[0];t=r.childNodes[0],e=Sl.multiscriptIndex(this.semantic.childNodes[1]),n=Sl.multiscriptIndex(r.childNodes[1]);const o=[this.semantic.id,[r.id,t.id,n],e];cl(this.mml,o),this.mml.setAttribute(ac.TYPE,r.role),this.completeMultiscript(fc.interleaveIds(n,e),[])}else{t=this.semantic.childNodes[0],e=Sl.multiscriptIndex(this.semantic.childNodes[1]);const n=[this.semantic.id,t.id,e];cl(this.mml,n)}const r=fc.collapsedLeafs(n||[],e);return fl(Xc(t)).setAttribute(ac.PARENT,this.semantic.id.toString()),r.unshift(t.id),this.mml.setAttribute(ac.CHILDREN,r.join(",")),this.mml}}class Il extends Sl{static test(t){return!!t.mathmlTree&&t.type===ot.TENSOR}constructor(t){super(t)}getMathml(){Xc(this.semantic.childNodes[0]);const t=Sl.multiscriptIndex(this.semantic.childNodes[1]),e=Sl.multiscriptIndex(this.semantic.childNodes[2]),n=Sl.multiscriptIndex(this.semantic.childNodes[3]),r=Sl.multiscriptIndex(this.semantic.childNodes[4]);uc(this.mml,this.semantic);const o=[this.semantic.id,this.semantic.childNodes[0].id,t,e,n,r];cl(this.mml,o);const i=fc.collapsedLeafs(t,e,n,r);return i.unshift(this.semantic.childNodes[0].id),this.mml.setAttribute(ac.CHILDREN,i.join(",")),this.completeMultiscript(fc.interleaveIds(n,r),fc.interleaveIds(t,e)),this.mml}}class bl extends gl{static test(t){return!(!t.mathmlTree||!t.fencePointer||t.mathmlTree.getAttribute("data-semantic-type"))}static makeEmptyNode_(t){const e=dc(),n=new yr(t);return n.type=ot.EMPTY,n.mathmlTree=e,n}static fencedMap_(t,e){e[t.id]=t.mathmlTree,t.embellished&&bl.fencedMap_(t.childNodes[0],e)}constructor(t){super(t),this.fenced=null,this.fencedMml=null,this.fencedMmlNodes=[],this.ofence=null,this.ofenceMml=null,this.ofenceMap={},this.cfence=null,this.cfenceMml=null,this.cfenceMap={},this.parentCleanup=[]}getMathml(){this.getFenced_(),this.fencedMml=Xc(this.fenced),this.getFencesMml_(),this.fenced.type!==ot.EMPTY||this.fencedMml.parentNode||(this.fencedMml.setAttribute(ac.ADDED,"true"),this.cfenceMml.parentNode.insertBefore(this.fencedMml,this.cfenceMml)),this.getFencedMml_();return this.rewrite_()}fencedElement(t){return t.type===ot.FENCED||t.type===ot.MATRIX||t.type===ot.VECTOR}getFenced_(){let t=this.semantic;for(;!this.fencedElement(t);)t=t.childNodes[0];this.fenced=t.childNodes[0],this.ofence=t.contentNodes[0],this.cfence=t.contentNodes[1],bl.fencedMap_(this.ofence,this.ofenceMap),bl.fencedMap_(this.cfence,this.cfenceMap)}getFencedMml_(){let t=this.ofenceMml.nextSibling;for(t=t===this.fencedMml?t:this.fencedMml;t&&t!==this.cfenceMml;)this.fencedMmlNodes.push(t),t=t.nextSibling}getFencesMml_(){let t=this.semantic;const e=Object.keys(this.ofenceMap),n=Object.keys(this.cfenceMap);for(;!(this.ofenceMml&&this.cfenceMml||t===this.fenced);)-1===e.indexOf(t.fencePointer)||this.ofenceMml||(this.ofenceMml=t.mathmlTree),-1===n.indexOf(t.fencePointer)||this.cfenceMml||(this.cfenceMml=t.mathmlTree),t=t.childNodes[0];this.ofenceMml||(this.ofenceMml=this.ofence.mathmlTree),this.cfenceMml||(this.cfenceMml=this.cfence.mathmlTree),this.ofenceMml&&(this.ofenceMml=rl(this.ofenceMml)),this.cfenceMml&&(this.cfenceMml=rl(this.cfenceMml))}rewrite_(){let t=this.semantic,e=null;const n=this.introduceNewLayer_();for(uc(n,this.fenced.parent);!this.fencedElement(t);){const r=t.mathmlTree,o=this.specialCase_(t,r);if(o)t=o;else{uc(r,t);const e=[];for(let n,r=1;n=t.childNodes[r];r++)e.push(Xc(n));t=t.childNodes[0]}const i=G("dummy"),s=r.childNodes[0];B(r,i),B(n,r),B(r.childNodes[0],n),B(i,s),e||(e=r)}return Xc(this.ofence),Xc(this.cfence),this.cleanupParents_(),e||n}specialCase_(t,e){const n=W(e);let r,o=null;if(n===ur.MSUBSUP?(o=t.childNodes[0],r=Nl):n===ur.MMULTISCRIPTS&&(t.type===ot.SUPERSCRIPT||t.type===ot.SUBSCRIPT?r=Tl:t.type===ot.TENSOR&&(r=Il),o=r&&t.childNodes[0]&&t.childNodes[0].role===st.SUBSUP?t.childNodes[0]:t),!o)return null;const i=o.childNodes[0],s=bl.makeEmptyNode_(i.id);return o.childNodes[0]=s,e=new r(t).getMathml(),o.childNodes[0]=i,this.parentCleanup.push(e),o.childNodes[0]}introduceNewLayer_(){const t=this.fullFence(this.ofenceMml),e=this.fullFence(this.cfenceMml);let n=dc();if(B(this.fencedMml,n),this.fencedMmlNodes.forEach((t=>n.appendChild(t))),n.insertBefore(t,this.fencedMml),n.appendChild(e),!n.parentNode){const t=dc();for(;n.childNodes.length>0;)t.appendChild(n.childNodes[0]);n.appendChild(t),n=t}return n}fullFence(t){const e=this.fencedMml.parentNode;let n=t;for(;n.parentNode&&n.parentNode!==e;)n=n.parentNode;return n}cleanupParents_(){this.parentCleanup.forEach((function(t){const e=t.childNodes[1].getAttribute(ac.PARENT);t.childNodes[0].setAttribute(ac.PARENT,e)}))}}class Al extends gl{static test(t){return!!t.mathmlTree&&t.hasAnnotation("Emph","top")}constructor(t){super(t),this.mrows=[],this.mml=t.mathmlTree}getMathml(){if(this.recurseToTable(this.semantic),this.mrows.length){const t=dc();this.mml.parentNode.insertBefore(t,this.mml);for(const e of this.mrows)t.appendChild(e);t.appendChild(this.mml)}return this.mml}recurseToTable(t){var e,n;if(t.hasAnnotation("Emph","top")||t.hasAnnotation("Emph","fence")||!t.hasAnnotation("Emph","left")&&!t.hasAnnotation("Emph","right")){if(!t.mathmlTree||W(t.mathmlTree)===ur.MTABLE&&(null===(e=t.annotation.Emph)||void 0===e?void 0:e.length)&&"table"!==t.annotation.Emph[0]){const e=dc();uc(e,t),this.mrows.unshift(e)}else{if(W(t.mathmlTree)===ur.MTABLE&&(null===(n=t.annotation.Emph)||void 0===n?void 0:n.length)&&"table"===t.annotation.Emph[0])return void this.finalizeTable(t);uc(t.mathmlTree,t)}if(t.childNodes.forEach(this.recurseToTable.bind(this)),t.textContent||"punctuated"===t.type){const e=t.contentNodes.map((t=>{const e=ll(t);return e.hasAttribute("data-semantic-added")?this.mrows.unshift(e):this.recurseToTable(t),e}));dl(t,e)}else t.contentNodes.forEach(this.recurseToTable.bind(this))}else Xc(t)}finalizeTable(t){uc(t.mathmlTree,t),t.contentNodes.forEach((t=>{Xc(t)})),t.childNodes.forEach((t=>{Xc(t)}))}}class Rl extends gl{static test(t){if(!t.mathmlTree||!t.childNodes.length)return!1;const e=W(t.mathmlTree),n=t.type;return(n===ot.LIMUPPER||n===ot.LIMLOWER)&&(e===ur.MSUBSUP||e===ur.MUNDEROVER)||n===ot.LIMBOTH&&(e===ur.MSUB||e===ur.MUNDER||e===ur.MSUP||e===ur.MOVER)}static walkTree_(t){t&&Xc(t)}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){const t=this.semantic.childNodes;return this.semantic.type!==ot.LIMBOTH&&this.mml.childNodes.length>=3&&(this.mml=Yc([this.mml],this.semantic)),uc(this.mml,this.semantic),t[0].mathmlTree||(t[0].mathmlTree=this.semantic.mathmlTree),t.forEach(Rl.walkTree_),this.mml}}class Ol extends gl{static test(t){return!!t.mathmlTree&&t.type===ot.LINE}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){return this.semantic.contentNodes.length&&Xc(this.semantic.contentNodes[0]),this.semantic.childNodes.length&&Xc(this.semantic.childNodes[0]),uc(this.mml,this.semantic),this.mml}}class Cl extends gl{static test(t){return!!t.mathmlTree&&(t.type===ot.INFERENCE||t.type===ot.PREMISES)}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){return this.semantic.childNodes.length?(this.semantic.contentNodes.forEach((function(t){Xc(t),uc(t.mathmlTree,t)})),this.semantic.childNodes.forEach((function(t){Xc(t)})),uc(this.mml,this.semantic),this.mml.getAttribute("data-semantic-id")===this.mml.getAttribute("data-semantic-parent")&&this.mml.removeAttribute("data-semantic-parent"),this.mml):this.mml}}class yl extends gl{static test(t){return t.type===ot.MATRIX||t.type===ot.VECTOR||t.type===ot.CASES}constructor(t){super(t),this.inner=[],this.mml=t.mathmlTree}getMathml(){const t=ll(this.semantic.contentNodes[0]),e=this.semantic.contentNodes[1]?ll(this.semantic.contentNodes[1]):null;if(this.inner=this.semantic.childNodes.map(Xc),this.mml)if(W(this.mml)===ur.MFENCED){const n=this.mml.childNodes;this.mml.insertBefore(t,n[0]||null),e&&this.mml.appendChild(e),this.mml=ul(this.mml)}else{const n=[t,this.mml];e&&n.push(e),this.mml=Yc(n,this.semantic)}else this.mml=Yc([t].concat(this.inner,[e]),this.semantic);return uc(this.mml,this.semantic),this.mml}}class Ll extends gl{static test(t){return t.type===ot.PUNCTUATED&&(t.role===st.TEXT||t.contentNodes.every((t=>t.role===st.DUMMY)))}constructor(t){super(t),this.mml=t.mathmlTree}getMathml(){const t=[],e=ml(this.semantic,t);return this.mml=Yc(t,this.semantic),uc(this.mml,this.semantic),this.mml.removeAttribute(ac.CONTENT),cl(this.mml,e),this.mml}}function Ml(t,e){const n=K(t);return function(t,e,n){return N.getInstance().generate((()=>["Original MathML",pl(t)])),$c.clear(),Xc(e.root),n.structure&&t.setAttribute(ac.STRUCTURE,fc.fromStructure(t,e,n).toString()),N.getInstance().generate((()=>["Semantic Tree\n",pl(e),"Semantically enriched MathML\n",pl(t)])),t}(n,Hc(n,e),e)}jc.push({test:Rl.test,constr:t=>new Rl(t)},{test:bl.test,constr:t=>new bl(t)},{test:Nl.test,constr:t=>new Nl(t)},{test:Il.test,constr:t=>new Il(t)},{test:Tl.test,constr:t=>new Tl(t)},{test:Ol.test,constr:t=>new Ol(t)},{test:El.test,constr:t=>new El(t)},{test:Cl.test,constr:t=>new Cl(t)},{test:Al.test,constr:t=>new Al(t)},{test:yl.test,constr:t=>new yl(t)},{test:Ll.test,constr:t=>new Ll(t)});let _l=0;class vl{constructor(){this.counter=_l++,this.ATTR="sre-highlight-"+this.counter.toString(),this.color=null,this.mactionName="",this.currentHighlights=[]}highlight(t){this.currentHighlights.push(t.map((t=>{const e=this.highlightNode(t);return this.setHighlighted(t),e})))}highlightAll(t){const e=this.getMactionNodes(t);for(let t,n=0;t=e[n];n++)this.highlight([t])}unhighlight(){const t=this.currentHighlights.pop();t&&t.forEach((t=>{this.isHighlighted(t.node)&&(this.unhighlightNode(t),this.unsetHighlighted(t.node))}))}unhighlightAll(){for(;this.currentHighlights.length>0;)this.unhighlight()}setColor(t){this.color=t}colorString(){return this.color.rgba()}addEvents(t,e){const n=this.getMactionNodes(t);for(let t,r=0;t=n[r];r++)for(const[n,r]of Object.entries(e))t.addEventListener(n,r)}getMactionNodes(t){return Array.from(t.getElementsByClassName(this.mactionName))}isMactionNode(t){const e=t.className||t.getAttribute("class");return!!e&&!!e.match(new RegExp(this.mactionName))}isHighlighted(t){return t.hasAttribute(this.ATTR)}setHighlighted(t){t.setAttribute(this.ATTR,"true")}unsetHighlighted(t){t.removeAttribute(this.ATTR)}colorizeAll(t){w(t);v(`.//*[@${ac.ID}]`,t).forEach((t=>this.colorize(t)))}uncolorizeAll(t){v(`.//*[@${ac.ID}]`,t).forEach((t=>this.uncolorize(t)))}colorize(t){const e=hc("foreground");t.hasAttribute(e)&&(t.setAttribute(e+"-old",t.style.color),t.style.color=t.getAttribute(e))}uncolorize(t){const e=hc("foreground")+"-old";t.hasAttribute(e)&&(t.style.color=t.getAttribute(e))}}class wl extends vl{constructor(){super(),this.mactionName="mjx-maction"}highlightNode(t){const e={node:t,background:t.style.backgroundColor,foreground:t.style.color};if(!this.isHighlighted(t)){const e=this.colorString();t.style.backgroundColor=e.background,t.style.color=e.foreground}return e}unhighlightNode(t){t.node.style.backgroundColor=t.background,t.node.style.color=t.foreground}}const Dl={red:{red:255,green:0,blue:0},green:{red:0,green:255,blue:0},blue:{red:0,green:0,blue:255},yellow:{red:255,green:255,blue:0},cyan:{red:0,green:255,blue:255},magenta:{red:255,green:0,blue:255},white:{red:255,green:255,blue:255},black:{red:0,green:0,blue:0}};function Pl(t,e){const n=t||{color:e};let r=Object.prototype.hasOwnProperty.call(n,"color")?Dl[n.color]:n;return r||(r=Dl[e]),r.alpha=Object.prototype.hasOwnProperty.call(n,"alpha")?n.alpha:1,function(t){const e=t=>(t=Math.max(t,0),t=Math.min(255,t),Math.round(t));return t.red=e(t.red),t.green=e(t.green),t.blue=e(t.blue),t.alpha=Math.max(t.alpha,0),t.alpha=Math.min(1,t.alpha),t}(r)}class xl{static toHex(t){const e=t.toString(16);return 1===e.length?"0"+e:e}constructor(t,e){this.foreground=Pl(e,xl.DEFAULT_FOREGROUND_),this.background=Pl(t,xl.DEFAULT_BACKGROUND_)}rgba(){const t=function(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"};return{background:t(this.background),foreground:t(this.foreground)}}rgb(){const t=function(t){return"rgb("+t.red+","+t.green+","+t.blue+")"};return{background:t(this.background),alphaback:this.background.alpha.toString(),foreground:t(this.foreground),alphafore:this.foreground.alpha.toString()}}hex(){const t=function(t){return"#"+xl.toHex(t.red)+xl.toHex(t.green)+xl.toHex(t.blue)};return{background:t(this.background),alphaback:this.background.alpha.toString(),foreground:t(this.foreground),alphafore:this.foreground.alpha.toString()}}}xl.DEFAULT_BACKGROUND_="blue",xl.DEFAULT_FOREGROUND_="black";class Ul{constructor(){this.hue=10,this.sat=100,this.light=50,this.incr=50}generate(){return e=function(t,e,n){e=e>1?e/100:e,n=n>1?n/100:n;const r=(1-Math.abs(2*n-1))*e,o=r*(1-Math.abs(t/60%2-1)),i=n-r/2;let s=0,a=0,c=0;return 0<=t&&t<60?[s,a,c]=[r,o,0]:60<=t&&t<120?[s,a,c]=[o,r,0]:120<=t&&t<180?[s,a,c]=[0,r,o]:180<=t&&t<240?[s,a,c]=[0,o,r]:240<=t&&t<300?[s,a,c]=[o,0,r]:300<=t&&t<360&&([s,a,c]=[r,0,o]),{red:s+i,green:a+i,blue:c+i}}(this.hue,this.sat,this.light),"rgb("+(t={red:Math.round(255*e.red),green:Math.round(255*e.green),blue:Math.round(255*e.blue)}).red+","+t.green+","+t.blue+")";var t,e}increment(){this.hue=(this.hue+this.incr)%360}}class Fl extends vl{constructor(){super(),this.mactionName="mjx-svg-maction"}highlightNode(t){let e;if(this.isHighlighted(t))return e={node:t.previousSibling||t,background:t.style.backgroundColor,foreground:t.style.color},e;if("svg"===t.tagName){const e={node:t,background:t.style.backgroundColor,foreground:t.style.color};return t.style.backgroundColor=this.colorString().background,t.style.color=this.colorString().foreground,e}const n=V("http://www.w3.org/2000/svg","rect");let r;if("use"===t.nodeName){const e=V("http://www.w3.org/2000/svg","g");t.parentNode.insertBefore(e,t),e.appendChild(t),r=e.getBBox(),e.parentNode.replaceChild(t,e)}else r=t.getBBox();n.setAttribute("x",(r.x-40).toString()),n.setAttribute("y",(r.y-40).toString()),n.setAttribute("width",(r.width+80).toString()),n.setAttribute("height",(r.height+80).toString());const o=t.getAttribute("transform");return o&&n.setAttribute("transform",o),n.setAttribute("fill",this.colorString().background),n.setAttribute(this.ATTR,"true"),t.parentNode.insertBefore(n,t),e={node:n,foreground:t.getAttribute("fill")},t.setAttribute("fill",this.colorString().foreground),e}setHighlighted(t){"svg"===t.tagName&&super.setHighlighted(t)}unhighlightNode(t){if("background"in t)return t.node.style.backgroundColor=t.background,void(t.node.style.color=t.foreground);t.foreground?t.node.nextSibling.setAttribute("fill",t.foreground):t.node.nextSibling.removeAttribute("fill"),t.node.parentNode.removeChild(t.node)}isMactionNode(t){let e=t.className||t.getAttribute("class");return!!e&&(e=void 0!==e.baseVal?e.baseVal:e,!!e&&!!e.match(new RegExp(this.mactionName)))}}const kl={SVG:Fl,"SVG-V3":class extends Fl{constructor(){super(),this.mactionName="maction"}highlightNode(t){let e;if(this.isHighlighted(t))return e={node:t,background:this.colorString().background,foreground:this.colorString().foreground},e;if("svg"===t.tagName||"MJX-CONTAINER"===t.tagName)return e={node:t,background:t.style.backgroundColor,foreground:t.style.color},t.style.backgroundColor=this.colorString().background,t.style.color=this.colorString().foreground,e;const n=(null!==document&&void 0!==document?document:s).createElementNS("http://www.w3.org/2000/svg","rect");n.setAttribute("sre-highlighter-added","true");const r=t.getBBox();n.setAttribute("x",(r.x-40).toString()),n.setAttribute("y",(r.y-40).toString()),n.setAttribute("width",(r.width+80).toString()),n.setAttribute("height",(r.height+80).toString());const o=t.getAttribute("transform");if(o&&n.setAttribute("transform",o),n.setAttribute("fill",this.colorString().background),t.setAttribute(this.ATTR,"true"),t.parentNode.insertBefore(n,t),e={node:t,foreground:t.getAttribute("fill")},"rect"===t.nodeName){const e=new xl({alpha:0,color:"white"});t.setAttribute("fill",e.rgba().background)}else t.setAttribute("fill",this.colorString().foreground);return e}unhighlightNode(t){const e=t.node.previousSibling;if(e&&e.hasAttribute("sre-highlighter-added"))return t.foreground?t.node.setAttribute("fill",t.foreground):t.node.removeAttribute("fill"),void t.node.parentNode.removeChild(e);t.node.style.backgroundColor=t.background,t.node.style.color=t.foreground}isMactionNode(t){return t.getAttribute("data-mml-node")===this.mactionName}getMactionNodes(t){return Array.from(v(`.//*[@data-mml-node="${this.mactionName}"]`,t))}},NativeMML:class extends vl{constructor(){super(),this.mactionName="maction"}highlightNode(t){let e=t.getAttribute("style");return e+=";background-color: "+this.colorString().background,e+=";color: "+this.colorString().foreground,t.setAttribute("style",e),{node:t}}unhighlightNode(t){let e=t.node.getAttribute("style");e=e.replace(";background-color: "+this.colorString().background,""),e=e.replace(";color: "+this.colorString().foreground,""),t.node.setAttribute("style",e)}colorString(){return this.color.rgba()}getMactionNodes(t){return Array.from(t.getElementsByTagName(this.mactionName))}isMactionNode(t){return t.tagName===this.mactionName}},"HTML-CSS":class extends vl{constructor(){super(),this.mactionName="maction"}highlightNode(t){const e={node:t,foreground:t.style.color,position:t.style.position},n=this.color.rgb();t.style.color=n.foreground,t.style.position="relative";const r=t.bbox;if(r&&r.w){const o=.05,i=0,s=G("span"),a=parseFloat(t.style.paddingLeft||"0");s.style.backgroundColor=n.background,s.style.opacity=n.alphaback.toString(),s.style.display="inline-block",s.style.height=r.h+r.d+2*o+"em",s.style.verticalAlign=-r.d+"em",s.style.marginTop=s.style.marginBottom=-o+"em",s.style.width=r.w+2*i+"em",s.style.marginLeft=a-i+"em",s.style.marginRight=-r.w-i-a+"em",t.parentNode.insertBefore(s,t),e.box=s}return e}unhighlightNode(t){const e=t.node;e.style.color=t.foreground,e.style.position=t.position,t.box&&t.box.parentNode.removeChild(t.box)}},"MML-CSS":class extends wl{constructor(){super(),this.mactionName="maction"}getMactionNodes(t){return Array.from(t.getElementsByTagName(this.mactionName))}isMactionNode(t){return t.tagName===this.mactionName}},CommonHTML:wl,CHTML:class extends wl{constructor(){super()}isMactionNode(t){var e;return(null===(e=t.tagName)||void 0===e?void 0:e.toUpperCase())===this.mactionName.toUpperCase()}getMactionNodes(t){return Array.from(t.getElementsByTagName(this.mactionName))}}};function Bl(t){return t?t.split(/,/):[]}function Gl(t,e){return t.getAttribute(e)}function Vl(t){if(t.hasAttribute(ac.TYPE)&&!t.hasAttribute(ac.PARENT))return t;const e=$(t,ac.TYPE);for(let t,n=0;t=e[n];n++)if(!t.hasAttribute(ac.PARENT))return t;return t}function Hl(t,e){return t.getAttribute(ac.ID)===e?t:X(t,ac.ID,e)[0]}function jl(t,e){return t.getAttribute(ac.ID)===e?[t]:X(t,ac.ID,e)}class ql{static textContent(t,e,n){if(!n&&e.textContent)return void(t.textContent=e.textContent);const r=Bl(Gl(e,ac.OPERATOR));r.length>1&&(t.textContent=r[1])}static isPunctuated(t){return!fc.simpleCollapseStructure(t)&&t[1]&&fc.contentCollapseStructure(t[1])}constructor(t){this.mathml=t,this.factory=new Lr,this.nodeDict={},this.mmlRoot=Vl(t),this.streeRoot=this.assembleTree(this.mmlRoot),this.stree=Bc.fromNode(this.streeRoot,this.mathml),this.xml=this.stree.xml()}getTree(){return this.stree}assembleTree(t){const e=this.makeNode(t),n=Bl(Gl(t,ac.CHILDREN)),r=Bl(Gl(t,ac.CONTENT));if(0===r.length&&0===n.length)return ql.textContent(e,t),e;if(r.length>0){const t=Hl(this.mathml,r[0]);t&&ql.textContent(e,t,!0)}e.contentNodes=r.map((t=>this.setParent(t,e))),e.childNodes=n.map((t=>this.setParent(t,e)));const o=Gl(t,ac.COLLAPSED);return o?this.postProcess(e,o):e}makeNode(t){const e=Gl(t,ac.TYPE),n=Gl(t,ac.ROLE),r=Gl(t,ac.FONT),o=Gl(t,ac.ANNOTATION)||"",i=Gl(t,ac.ATTRIBUTES)||"",s=Gl(t,ac.ID),a=Gl(t,ac.EMBELLISHED),c=Gl(t,ac.FENCEPOINTER),l=this.createNode(parseInt(s,10));return l.type=e,l.role=n,l.font=r||ct.UNKNOWN,l.parseAnnotation(o),l.parseAttributes(i),c&&(l.fencePointer=c),a&&(l.embellished=a),l}makePunctuation(t){const e=this.createNode(t);return e.updateContent(rn.invisibleComma),e.role=st.DUMMY,e}makePunctuated(t,e,n){const r=this.createNode(e[0]);r.type=ot.PUNCTUATED,r.embellished=t.embellished,r.fencePointer=t.fencePointer,r.role=n;const o=e.splice(1,1)[0].slice(1);r.contentNodes=o.map(this.makePunctuation.bind(this)),this.collapsedChildren_(e)}makeEmpty(t,e,n){const r=this.createNode(e);r.type=ot.EMPTY,r.embellished=t.embellished,r.fencePointer=t.fencePointer,r.role=n}makeIndex(t,e,n){if(ql.isPunctuated(e))return this.makePunctuated(t,e,n),void(e=e[0]);fc.simpleCollapseStructure(e)&&!this.nodeDict[e.toString()]&&this.makeEmpty(t,e,n)}postProcess(t,e){const n=fc.fromString(e).array;if(t.type===st.SUBSUP){const e=this.createNode(n[1][0]);return e.type=ot.SUBSCRIPT,e.role=st.SUBSUP,t.type=ot.SUPERSCRIPT,e.embellished=t.embellished,e.fencePointer=t.fencePointer,this.makeIndex(t,n[1][2],st.RIGHTSUB),this.makeIndex(t,n[2],st.RIGHTSUPER),this.collapsedChildren_(n),t}if(t.type===ot.SUBSCRIPT)return this.makeIndex(t,n[2],st.RIGHTSUB),this.collapsedChildren_(n),t;if(t.type===ot.SUPERSCRIPT)return this.makeIndex(t,n[2],st.RIGHTSUPER),this.collapsedChildren_(n),t;if(t.type===ot.TENSOR)return this.makeIndex(t,n[2],st.LEFTSUB),this.makeIndex(t,n[3],st.LEFTSUPER),this.makeIndex(t,n[4],st.RIGHTSUB),this.makeIndex(t,n[5],st.RIGHTSUPER),this.collapsedChildren_(n),t;if(t.type===ot.PUNCTUATED){if(ql.isPunctuated(n)){const e=n.splice(1,1)[0].slice(1);t.contentNodes=e.map(this.makePunctuation.bind(this))}return t}if(t.type===st.UNDEROVER){const e=this.createNode(n[1][0]);return t.childNodes[1].role===st.OVERACCENT?(e.type=ot.OVERSCORE,t.type=ot.UNDERSCORE):(e.type=ot.UNDERSCORE,t.type=ot.OVERSCORE),e.role=st.UNDEROVER,e.embellished=t.embellished,e.fencePointer=t.fencePointer,this.collapsedChildren_(n),t}return t}createNode(t){const e=this.factory.makeNode(t);return this.nodeDict[t.toString()]=e,e}collapsedChildren_(t){const e=t=>{const n=this.nodeDict[t[0]];n.childNodes=[];for(let r=1,o=t.length;r<o;r++){const o=t[r];n.childNodes.push(fc.simpleCollapseStructure(o)?this.nodeDict[o]:e(o))}return n};e(t)}setParent(t,e){const n=Hl(this.mathml,t),r=this.assembleTree(n);return r.parent=e,r}}const $l=new a({AbsoluteValue:["Auto","AbsEnd","Cardinality","Determinant"],Bar:["Auto","Conjugate"],Caps:["Auto","SayCaps"],CombinationPermutation:["Auto","ChoosePermute"],Currency:["Auto","Position","Prefix"],Ellipses:["Auto","AndSoOn"],Enclosed:["Auto","EndEnclose"],Exponent:["Auto","AfterPower","Ordinal","OrdinalPower","Exponent"],Fraction:["Auto","EndFrac","FracOver","General","GeneralEndFrac","Ordinal","Over","OverEndFrac","Per"],Functions:["Auto","None","Reciprocal"],Inference:["Auto","Long"],ImpliedTimes:["Auto","MoreImpliedTimes","None"],Log:["Auto","LnAsNaturalLog"],Matrix:["Auto","Combinatoric","EndMatrix","EndVector","SilentColNum","SpeakColNum","Vector"],MultiLineLabel:["Auto","Case","Constraint","Equation","Line","None","Row","Step"],MultiLineOverview:["Auto","None"],MultiLinePausesBetweenColumns:["Auto","Long","Short"],MultsymbolDot:["Auto","Dot"],MultsymbolX:["Auto","By","Cross"],Paren:["Auto","CoordPoint","Interval","Silent","Speak","SpeakNestingLevel"],Prime:["Auto","Angle","Length"],Roots:["Auto","PosNegSqRoot","PosNegSqRootEnd","RootEnd"],SetMemberSymbol:["Auto","Belongs","Element","Member","In"],Sets:["Auto","SilentBracket","woAll"],TriangleSymbol:["Auto","Delta"],Trig:["Auto","ArcTrig","TrigInverse","Reciprocal"],VerticalLine:["Auto","Divides","Given","SuchThat"]}),Xl="Auto";function Yl(t){const e=t.split(":"),n={},r=$l.getProperties(),o=Object.keys(r);for(let t,i=0;t=e[i];i++){const e=t.split("_");if(-1===o.indexOf(e[0]))continue;const i=e[1];i&&i!==Xl&&-1!==r[e[0]].indexOf(i)&&(n[e[0]]=e[1])}return n}function Wl(t){const e=Object.keys(t),n=[];for(let r=0;r<e.length;r++)n.push(e[r]+"_"+t[e[r]]);return n.length?n.join(":"):c.DEFAULT_VALUE}class Kl extends c{static comparator(){return new zl(R.getInstance().dynamicCstr,a.createProp([c.DEFAULT_VALUES[r.LOCALE]],[c.DEFAULT_VALUES[r.MODALITY]],[c.DEFAULT_VALUES[r.DOMAIN]],[c.DEFAULT_VALUES[r.STYLE]]))}static getLocalePreferences(t){const e=t||function(t={}){for(const e of Nn.values())for(const[,n]of e.rules.entries())for(const{cstr:e}of n)t=Ln(e.getValues(),t);return t}(xa.getInstance().enumerate());return Kl.getLocalePreferences_(e)}static relevantPreferences(t){const e=Jl[t.type];if(!e)return"ImpliedTimes";const n=e[t.role]||e[""];return n?"string"==typeof n?n:function(t,e){for(const[n,r]of Object.entries(t))if(Zl(n,e))return r;return""}(n,t)||"ImpliedTimes":"ImpliedTimes"}static getLocalePreferences_(t){const e={};for(const n of Object.keys(t)){if(!t[n].speech||!t[n].speech.clearspeak)continue;const r=Object.keys(t[n].speech.clearspeak);if(r.length<3)continue;const o=e[n]={};for(const t in $l.getProperties()){const e=$l.getProperties()[t],n=[t+"_Auto"];if(e)for(const o of e)-1!==r.indexOf(t+"_"+o)&&n.push(t+"_"+o);o[t]=n}}return e}constructor(t,e){super(t),this.preference=e}equal(t){if(!super.equal(t))return!1;const e=Object.keys(this.preference),n=t.preference;if(e.length!==Object.keys(n).length)return!1;for(let t,r=0;t=e[r];r++)if(this.preference[t]!==n[t])return!1;return!0}}class zl extends u{constructor(t,e){super(t,e),this.preference=t instanceof Kl?t.preference:{}}match(t){if(!(t instanceof Kl))return super.match(t);if("default"===t.getComponents()[r.STYLE])return!0;const e=Object.keys(t.preference);for(let n,r=0;n=e[r];r++)if(this.preference[n]!==t.preference[n])return!1;return!0}compare(t,e){const n=super.compare(t,e);if(0!==n)return n;const r=t instanceof Kl,o=e instanceof Kl;if(!r&&o)return 1;if(r&&!o)return-1;if(!r&&!o)return 0;const i=Object.keys(t.preference).length,s=Object.keys(e.preference).length;return i>s?-1:i<s?1:0}}const Ql=[["AbsoluteValue",ot.FENCED,st.NEUTRAL],["AbsoluteValue",ot.FENCED,st.METRIC],["Bar",ot.OVERSCORE,""],["Caps",ot.IDENTIFIER,st.LATINLETTER,"category:Lu"],["CombinationPermutation",ot.APPL,st.UNKNOWN],["Currency",ot.IDENTIFIER,st.UNIT,"unit:currency"],["Currency",ot.INFIXOP,st.UNIT,"unit:currency"],["Enclosed",ot.ENCLOSE,""],["Ellipses",ot.PUNCTUATION,st.ELLIPSIS],["Exponent",ot.SUPERSCRIPT,""],["Fraction",ot.FRACTION,""],["Functions",ot.APPL,st.SIMPLEFUNC],["ImpliedTimes",ot.OPERATOR,st.IMPLICIT],["Log",ot.APPL,st.PREFIXFUNC,"appl:Logarithm"],["Log",ot.FUNCTION,st.PREFIXFUNC,"category:Logarithm"],["Matrix",ot.MATRIX,""],["Matrix",ot.VECTOR,""],["MultiLineLabel",ot.MULTILINE,st.LABEL],["MultiLinePausesBetweenColumns",ot.MULTILINE,st.TABLE],["MultiLineOverview",ot.MULTILINE,""],["MultiLineLabel",ot.TABLE,st.LABEL],["MultiLinePausesBetweenColumns",ot.TABLE,""],["MultiLineOverview",ot.TABLE,""],["MultiLineLabel",ot.CASES,st.LABEL],["MultiLinePausesBetweenColumns",ot.CASES,""],["MultiLineOverview",ot.CASES,""],["MultsymbolDot",ot.OPERATOR,st.MULTIPLICATION,"content:22C5"],["MultsymbolX",ot.OPERATOR,st.MULTIPLICATION,"content:00D7"],["Paren",ot.FENCED,st.LEFTRIGHT],["Prime",ot.PUNCTUATION,st.PRIME],["Roots",ot.ROOT,""],["Roots",ot.SQRT,""],["SetMemberSymbol",ot.OPERATOR,st.ELEMENT],["Sets",ot.FENCED,st.SETEXT],["TriangleSymbol",ot.IDENTIFIER,st.GREEKLETTER,"content:0394"],["Trig",ot.APPL,st.PREFIXFUNC,"appl:Trigonometric"],["Trig",ot.FUNCTION,st.PREFIXFUNC,"category:Trigonometric"],["VerticalLine",ot.PUNCTUATED,st.VBAR],["VerticalLine",ot.PUNCTUATION,st.VBAR],["Inference",ot.INFERENCE,""],["Inference",ot.PREMISES,""],["Inference",ot.RULELABEL,""],["Inference",ot.CONCLUSION,""]],Jl=function(){const t={};for(let e,n=0;e=Ql[n];n++){const n=e[0],r=e[3];let o=t[e[1]];if(o||(o={},t[e[1]]=o),!r){o[e[2]]=n;continue}let i=o[e[2]];i||(i={},o[e[2]]=i),i[r]=n}return t}();function Zl(t,e){const[n,r]=t.split(":");if(!n)return!1;const o=tu[n];return!!o&&o(e,r)}const tu={category:(t,e)=>yn(t.textContent)===e,content:(t,e)=>t.textContent===String.fromCodePoint(parseInt(e,16)),appl:(t,e)=>{const n=t.childNodes[0];return!!n&&yn(n.textContent)===e},unit:(t,e)=>yn(t.textContent+":unit")===e};function eu(t,e=!1){return xa.getInstance().evaluateNode(t,e)}function nu(t,e=!1){return wa(eu(t,e))}function ru(t){const e=function(t){return eu(Bc.fromNode(t).xml())}(t);return wa(e)}function ou(t,e,n){const r=X(n,"id",e.id.toString())[0],o=r?wa(eu(r)):ru(e);t.setAttribute(ac.SPEECH,o)}function iu(t,e,n){const r=ru(e);t.setAttribute(n,r)}function su(t,e){const n=au(e);n&&t.setAttribute(ac.PREFIX,n)}function au(t){const e=function(t){const e=Bc.fromRoot(t),n=v('.//*[@id="'+t.id+'"]',e.xml());let r=n[0];n.length>1&&(r=function(t,e){const n=e[0];if(!t.parent)return n;const r=[];for(;t;)r.push(t.id),t=t.parent;const o=function(t,e){for(;e.length&&e.shift().toString()===t.getAttribute("id")&&t.parentNode&&t.parentNode.parentNode;)t=t.parentNode.parentNode;return!e.length};for(let t,n=0;t=e[n];n++)if(o(t,r.slice()))return t;return n}(t,n)||r);return r}(t);return wa(cu(e))}function cu(t){return t?xa.getInstance().runInSetting({modality:"prefix",domain:"default",style:"default",strict:!0,speech:!0},(function(){return xa.getInstance().evaluateNode(t)})):[]}function lu(t,e,n){const r=Y(e,"maction");for(let e,o=0;e=r[o];o++){const r=X(t,"id",e.getAttribute("id"))[0];if(!r)continue;const o=e.childNodes[1],i=o.getAttribute(ac.ID);let s=Hl(t,i);if(s&&"dummy"!==s.getAttribute(ac.TYPE))continue;if(s=r.childNodes[0],s.getAttribute("sre-highlighter-added"))continue;const a=o.getAttribute(ac.PARENT);a&&s.setAttribute(ac.PARENT,a),s.setAttribute(ac.TYPE,"dummy"),s.setAttribute(ac.ID,i),s.setAttribute("role","treeitem"),s.setAttribute("aria-level",o.getAttribute("aria-level"));X(n,"id",i)[0].setAttribute("alternative",i)}}var uu;function hu(t){const e={};for(const[,n]of Object.entries(uu))e[n]=t.getAttribute(n);return e}function du(t,e){const n=Y(t,"maction"),r={};for(let t,o=0;t=n[o];o++){const n=parseInt(t.getAttribute("selection")),o=Array.from(t.childNodes),i=o.filter((t=>t.hasAttribute(uu.ID)))[0],s=o[n-1];if(!i||i===s)continue;const a=i.getAttribute(ac.ID);X(e,"id",a)[0].setAttribute("alternative",a),r[t.getAttribute("id")]=hu(i)}return r}function fu(t,e={}){const n=e.locale?{locale:e.locale}:{};return t?xa.getInstance().runInSetting(Object.assign(n,{modality:"summary",strict:!1,speech:!0}),(function(){return xa.getInstance().evaluateNode(t)})):[]}function pu(t){const e=[];return t.getAttribute("role")===st.MGLYPH&&e.push(new wn({text:"image",personality:{}})),t.hasAttribute("href")&&e.push(new wn({text:"link",personality:{}})),xa.getInstance().speechStructure.addNode(t,e,"postfix"),e}function mu(t){eu(t,!0);const e=xa.getInstance().speechStructure;return function(t){t.completeModality("speech",eu),t.completeModality("prefix",cu),t.completeModality("postfix",pu),t.completeModality("summary",fu)}(e),e.json(["none","ssml"])}function gu(t,e=p){var n;if("speech"!==t.modality)return t;return Kl.getLocalePreferences()[t.locale]?(t.domain="mathspeak"===t.domain?"clearspeak":"mathspeak",t.style=null!==(n=e[t.domain])&&void 0!==n?n:t.style,t):t}function Eu(t,e){const{modality:n,domain:r,style:o,locale:i}=e;if("speech"!==n)return o;if("mathspeak"===r){const t=["default","brief","sbrief"],e=t.indexOf(o);return-1===e?o:e>=t.length-1?t[0]:t[e+1]}if("clearspeak"===r){const e=Kl.getLocalePreferences()[i];if(!e)return"default";const n=Kl.relevantPreferences(t),r=function(t,e){return"default"===t?Xl:Yl(t)[e]||Xl}(o,n),s=e[n].map((function(t){return t.split("_")[1]})),a=s.indexOf(r);if(-1===a)return o;const c=function(t,e,n){if("default"===t)return e+"_"+n;const r=Yl(t);return r[e]=n,Wl(r)}(o,n,a>=s.length-1?s[0]:s[a+1]);return c}return o}function Nu(t){var e;const{domain:n,style:r,domain2style:o}=t,i={};if(!o)return Object.assign(i,p),i[n]=r,i;const s=o.split(",");for(const t of s){const[n,r]=t.split(/:(.*)/);i[n]=r||(null!==(e=p[n])&&void 0!==e?e:"default")}return i}function Su(t){const e=[];for(const[n,r]of Object.entries(t))e.push(`${n}:${r}`);return e.join(",")}R.getInstance().comparators.clearspeak=Kl.comparator,R.getInstance().parsers.clearspeak=new class extends l{constructor(){super([r.LOCALE,r.MODALITY,r.DOMAIN,r.STYLE])}parse(t){const e=super.parse(t);let n=e.getValue(r.STYLE);const o=e.getValue(r.LOCALE),i=e.getValue(r.MODALITY);let s={};return n!==c.DEFAULT_VALUE&&(s=this.fromPreference(n),n=this.toPreference(s)),new Kl({locale:o,modality:i,domain:"clearspeak",style:n},s)}fromPreference(t){return Yl(t)}toPreference(t){return Wl(t)}},function(t){t.ID="data-semantic-id",t.PARENT="data-semantic-parent",t.LEVEL="aria-level",t.POS="aria-posinset",t.ROLE="role"}(uu||(uu={}));class Tu{constructor(){this.modality=hc("speech"),this.rebuilt_=null,this.options_={}}getRebuilt(){return this.rebuilt_}setRebuilt(t){this.rebuilt_=t}computeRebuilt(t,e=!1){return this.rebuilt_&&!e||(this.rebuilt_=new ql(t)),this.rebuilt_}setOptions(t){this.options_=t||{},this.modality=hc(this.options_.modality||"speech")}setOption(t,e){const n=this.getOptions();n[t]=e,this.setOptions(n)}getOptions(){return this.options_}generateSpeech(t,e){return this.rebuilt_||(this.rebuilt_=new ql(e)),tc(this.options_),nu(this.getRebuilt().xml)}nextRules(){this.setOptions(gu(this.getOptions()))}nextStyle(t){this.setOption("style",Eu(this.getRebuilt().nodeDict[t],this.getOptions()))}getLevel(t){return Nt.MESSAGES.navigate.LEVEL+" "+t}getActionable(t){return t?t<0?Nt.MESSAGES.navigate.EXPANDABLE:Nt.MESSAGES.navigate.COLLAPSIBLE:""}}class Iu extends Tu{getSpeech(t,e){const n=this.generateSpeech(t,e);return t.setAttribute(this.modality,n),n}}class bu extends Tu{constructor(){super(...arguments),this.modality=hc("foreground"),this.contrast=new Ul}static visitStree_(t,e,n){if(t.childNodes.length){if(t.contentNodes.length&&("punctuated"===t.type&&t.contentNodes.forEach((t=>n[t.id]=!0)),"implicit"!==t.role&&e.push(t.contentNodes.map((t=>t.id)))),t.childNodes.length){if("implicit"===t.role){const r=[];let o=[];for(const e of t.childNodes){const t=[];bu.visitStree_(e,t,n),t.length<=2&&r.push(t.shift()),o=o.concat(t)}return e.push(r),void o.forEach((t=>e.push(t)))}t.childNodes.forEach((t=>bu.visitStree_(t,e,n)))}}else n[t.id]||e.push(t.id)}getSpeech(t,e){return Gl(t,this.modality)}generateSpeech(t,e){return this.getRebuilt()||this.setRebuilt(new ql(P(e))),this.colorLeaves_(t),Gl(t,this.modality)}colorLeaves_(t){const e=[];bu.visitStree_(this.getRebuilt().streeRoot,e,{});for(const n of e){const e=this.contrast.generate();let r=!1;r=Array.isArray(n)?n.map((n=>this.colorLeave_(t,n,e))).reduce(((t,e)=>t||e),!1):this.colorLeave_(t,n.toString(),e),r&&this.contrast.increment()}}colorLeave_(t,e,n){const r=Hl(t,e);return!!r&&(r.setAttribute(this.modality,n),!0)}}class Au extends Tu{getSpeech(t,e){return Gl(t,this.modality)}}class Ru extends Tu{getSpeech(t,e){return""}}class Ou extends Tu{getSpeech(t,e,n=null){this.getRebuilt()&&lu(t,e,this.getRebuilt().xml);const r=this.generateSpeech(t,e),o=this.getRebuilt().nodeDict;for(const[r,i]of Object.entries(o)){const o=Hl(e,r),s=Hl(t,r)||n&&Hl(n,r);o&&s&&(this.modality&&this.modality!==ac.SPEECH?iu(s,i,this.modality):ou(s,i,this.getRebuilt().xml),this.modality===ac.SPEECH&&su(s,i))}return r}}class Cu extends Ou{getSpeech(t,e){return super.getSpeech(t,e),Gl(t,this.modality)}}class yu extends Tu{getSpeech(t,e){tc(this.getOptions());const n=t.getAttribute(ac.ID);iu(t,this.getRebuilt().streeRoot.querySelectorAll((t=>t.id.toString()===n))[0],this.modality);return t.getAttribute(ac.SUMMARY)}}function Lu(t){return(Mu[t]||Mu.Direct)()}const Mu={Adhoc:()=>new Iu,Color:()=>new bu,Direct:()=>new Au,Dummy:()=>new Ru,Node:()=>new Cu,Summary:()=>new yu,Tree:()=>new Ou};class _u{static factory(t,e,n,r){const o=t=>Hl(r,t),i=n.nodeDict,s=o(t),a=e.map(o),c=e.map((function(t){return i[t]})),l=new _u(c,i[t]);return l.domNodes=a,l.domPrimary_=s,l.allNodes=_u.generateAllVisibleNodes_(e,a,i,r),l}static generateAllVisibleNodes_(t,e,n,r){let o=[];for(let i=0,s=t.length;i<s;i++){if(e[i]){const n=_u.getAllVisibleNodes([t[i]],r);n.length?o=o.concat(n):o.push(e[i]);continue}const s=n[t[i]];if(!s)continue;const a=s.childNodes.map((t=>t.id.toString())),c=_u.getAllVisibleNodes(a,r);o=o.concat(_u.generateAllVisibleNodes_(a,c,n,r))}return o}static getAllVisibleNodes(t,e){let n=[];for(const r of t)n=n.concat(jl(e,r));return n}constructor(t,e){this.nodes=t,this.primary=e,this.domNodes=[],this.domPrimary_=null,this.allNodes=[]}getSemanticPrimary(){return this.primary}getSemanticNodes(){return this.nodes}getNodes(){return this.allNodes}getDomNodes(){return this.domNodes}getDomPrimary(){return this.domPrimary_}toString(){return"Primary:"+this.domPrimary_+" Nodes:"+this.domNodes}clone(){const t=new _u(this.nodes,this.primary);return t.domNodes=this.domNodes,t.domPrimary_=this.domPrimary_,t.allNodes=this.allNodes,t}}var vu;!function(t){t.UP="up",t.DOWN="down",t.LEFT="left",t.RIGHT="right",t.REPEAT="repeat",t.DEPTH="depth",t.ENTER="enter",t.EXPAND="expand",t.HOME="home",t.SUMMARY="summary",t.DETAIL="detail",t.ROW="row",t.CELL="cell"}(vu||(vu={}));class wu{static resetState(t){delete wu.STATE[t]}static setState(t,e){wu.STATE[t]=e}static getState(t){return wu.STATE[t]}}wu.STATE={};class Du{constructor(t,e,n,r){this.node=t,this.generator=e,this.highlighter=n,this.modifier=!1,this.keyMapping=new Map([[Hs.UP,this.up.bind(this)],[Hs.DOWN,this.down.bind(this)],[Hs.RIGHT,this.right.bind(this)],[Hs.LEFT,this.left.bind(this)],[Hs.TAB,this.repeat.bind(this)],[Hs.DASH,this.expand.bind(this)],[Hs.SPACE,this.depth.bind(this)],[Hs.HOME,this.home.bind(this)],[Hs.X,this.summary.bind(this)],[Hs.Z,this.detail.bind(this)],[Hs.V,this.virtualize.bind(this)],[Hs.P,this.previous.bind(this)],[Hs.U,this.undo.bind(this)],[Hs.LESS,this.previousRules.bind(this)],[Hs.GREATER,this.nextRules.bind(this)]]),this.cursors=[],this.xml_=null,this.rebuilt_=null,this.focus_=null,this.active_=!1,this.node.id?this.id=this.node.id:this.node.hasAttribute(Du.SRE_ID_ATTR)?this.id=this.node.getAttribute(Du.SRE_ID_ATTR):(this.node.setAttribute(Du.SRE_ID_ATTR,Du.ID_COUNTER.toString()),this.id=Du.ID_COUNTER++),this.rootNode=Vl(t),this.rootId=this.rootNode.getAttribute(ac.ID),this.xmlString_=r,this.moved=vu.ENTER}getXml(){return this.xml_||(this.xml_=P(this.xmlString_)),this.xml_}getRebuilt(){return this.rebuilt_||this.rebuildStree(),this.rebuilt_}isActive(){return this.active_}activate(){this.isActive()||this.toggleActive_()}deactivate(){this.isActive()&&(wu.setState(this.id,this.primaryId()),this.toggleActive_())}getFocus(t=!1){return null===this.rootId&&this.getRebuilt(),this.focus_||(this.focus_=this.singletonFocus(this.rootId)),t&&this.updateFocus(),this.focus_}setFocus(t){this.focus_=t}getDepth(){return this.levels.depth()-1}isSpeech(){return this.generator.modality===ac.SPEECH}focusDomNodes(){return this.getFocus().getDomNodes()}focusSemanticNodes(){return this.getFocus().getSemanticNodes()}speech(){const t=this.focusDomNodes();if(!t.length)return"";const e=this.specialMove();if(null!==e)return e;switch(this.moved){case vu.DEPTH:return this.depth_();case vu.SUMMARY:return this.summary_();case vu.DETAIL:return this.detail_();default:{const e=[],n=this.focusSemanticNodes();for(let r=0,o=t.length;r<o;r++){const o=t[r],i=n[r];e.push(o?this.generator.getSpeech(o,this.getXml(),this.node):ru(i))}return this.mergePrefix_(e)}}}move(t){const e=this.keyMapping.get(t);if(!e)return null;const n=e();return!(!n||n===this.getFocus())&&(this.setFocus(n),this.moved===vu.HOME&&(this.levels=this.initLevels()),!0)}up(){return this.moved=vu.UP,this.getFocus()}down(){return this.moved=vu.DOWN,this.getFocus()}left(){return this.moved=vu.LEFT,this.getFocus()}right(){return this.moved=vu.RIGHT,this.getFocus()}repeat(){return this.moved=vu.REPEAT,this.getFocus().clone()}depth(){return this.moved=this.isSpeech()?vu.DEPTH:vu.REPEAT,this.getFocus().clone()}home(){this.moved=vu.HOME;return this.singletonFocus(this.rootId)}getBySemanticId(t){return Hl(this.node,t)}primaryId(){return this.getFocus().getSemanticPrimary().id.toString()}expand(){const t=this.getFocus().getDomPrimary(),e=this.actionable_(t);return e?(this.moved=vu.EXPAND,e.dispatchEvent(new Event("click")),this.getFocus().clone()):this.getFocus()}expandable(t){return!!this.actionable_(t)&&0===t.childNodes.length}collapsible(t){return!!this.actionable_(t)&&t.childNodes.length>0}restoreState(){if(!this.highlighter)return;const t=wu.getState(this.id);if(!t)return;let e=this.getRebuilt().nodeDict[t];const n=[];for(;e;)n.push(e.id),e=e.parent;for(n.pop();n.length>0;){this.down();const t=n.pop(),e=this.findFocusOnLevel(t);if(!e)break;this.setFocus(e)}this.moved=vu.ENTER}updateFocus(){this.setFocus(_u.factory(this.getFocus().getSemanticPrimary().id.toString(),this.getFocus().getSemanticNodes().map((t=>t.id.toString())),this.getRebuilt(),this.node))}rebuildStree(){this.rebuilt_=new ql(this.getXml()),this.rootId=this.rebuilt_.stree.root.id.toString(),this.generator.setRebuilt(this.rebuilt_),this.skeleton=fc.fromTree(this.rebuilt_.stree),this.skeleton.populate(),this.focus_=this.singletonFocus(this.rootId),this.levels=this.initLevels(),lu(this.node,this.getXml(),this.rebuilt_.xml)}previousLevel(){const t=this.getFocus().getDomPrimary();return t?Gl(t,ac.PARENT):this.getFocus().getSemanticPrimary().parent.id.toString()}nextLevel(){const t=this.getFocus().getDomPrimary();let e,n;if(t){e=Bl(Gl(t,ac.CHILDREN)),n=Bl(Gl(t,ac.CONTENT));const r=Gl(t,ac.TYPE),o=Gl(t,ac.ROLE);return this.combineContentChildren(r,o,n,e)}const r=t=>t.id.toString(),o=this.getRebuilt().nodeDict[this.primaryId()];return e=o.childNodes.map(r),n=o.contentNodes.map(r),0===e.length?[]:this.combineContentChildren(o.type,o.role,n,e)}singletonFocus(t){this.getRebuilt();const e=this.retrieveVisuals(t);return this.focusFromId(t,e)}retrieveVisuals(t){if(!this.skeleton)return[t];const e=parseInt(t,10),n=this.skeleton.subtreeNodes(e);if(!n.length)return[t];n.unshift(e);const r={},o=[];w(this.getXml());for(const t of n)r[t]||(o.push(t.toString()),r[t]=!0,this.subtreeIds(t,r));return o}subtreeIds(t,e){const n=v(`//*[@data-semantic-id="${t}"]`,this.getXml());v("*//@data-semantic-id",n[0]).forEach((t=>e[parseInt(t.textContent,10)]=!0))}focusFromId(t,e){return _u.factory(t,e,this.getRebuilt(),this.node)}summary(){return this.moved=this.isSpeech()?vu.SUMMARY:vu.REPEAT,this.getFocus().clone()}detail(){return this.moved=this.isSpeech()?vu.DETAIL:vu.REPEAT,this.getFocus().clone()}specialMove(){return null}virtualize(t){return this.cursors.push({focus:this.getFocus(),levels:this.levels,undo:t||!this.cursors.length}),this.levels=this.levels.clone(),this.getFocus().clone()}previous(){const t=this.cursors.pop();return t?(this.levels=t.levels,t.focus):this.getFocus()}undo(){let t;do{t=this.cursors.pop()}while(t&&!t.undo);return t?(this.levels=t.levels,t.focus):this.getFocus()}update(t){tc(t).then((()=>Lu("Tree").getSpeech(this.node,this.getXml())))}nextRules(){this.generator.nextRules();const t=this.generator.getOptions();return"speech"!==t.modality?this.getFocus():(this.update(t),this.moved=vu.REPEAT,this.getFocus().clone())}previousRules(){var t;this.generator.nextStyle(null===(t=this.getFocus().getSemanticPrimary())||void 0===t?void 0:t.id.toString());const e=this.generator.getOptions();return"speech"!==e.modality?this.getFocus():(this.update(e),this.moved=vu.REPEAT,this.getFocus().clone())}refocus(){let t,e=this.getFocus();for(;!e.getNodes().length;){t=this.levels.peek();const n=this.up();if(!n)break;this.setFocus(n),e=this.getFocus(!0)}this.levels.push(t),this.setFocus(e)}toggleActive_(){this.active_=!this.active_}mergePrefix_(t,e=[]){const n=this.isSpeech()?this.prefix_():"";n&&t.unshift(n);const r=this.isSpeech()?this.postfix_():"";return r&&t.push(r),Da(function(t){const e=t.map((t=>"string"==typeof t?Mn.stringEmpty(t):t)),n=va.get(R.getInstance().options.markup);return n?n.merge(e):t.join()}(e.concat(t)))}prefix_(){const t=this.getFocus().getDomNodes(),e=this.getFocus().getSemanticNodes();return t[0]?Gl(t[0],ac.PREFIX):au(e[0])}postfix_(){const t=this.getFocus().getDomNodes();return t[0]?Gl(t[0],ac.POSTFIX):""}depth_(){const t=Ot.getInstance().getParameter("depth");Ot.getInstance().setParameter("depth",!0);const e=this.getFocus().getDomPrimary(),n=this.expandable(e)?Nt.MESSAGES.navigate.EXPANDABLE:this.collapsible(e)?Nt.MESSAGES.navigate.COLLAPSIBLE:"",r=Nt.MESSAGES.navigate.LEVEL+" "+this.getDepth(),o=au(this.getFocus().getSemanticNodes()[0]),i=[new wn({text:r,personality:{}}),new wn({text:o,personality:{}}),new wn({text:n,personality:{}})];return Ot.getInstance().setParameter("depth",t),Da(wa(i))}actionable_(t){const e=null==t?void 0:t.parentNode;return e&&this.highlighter.isMactionNode(e)?e:null}summary_(){const t=this.getFocus().getSemanticPrimary().id.toString(),e=function(t,e={}){return wa(fu(t,e))}(this.getRebuilt().xml.getAttribute("id")===t?this.getRebuilt().xml:X(this.getRebuilt().xml,"id",t)[0]);return this.mergePrefix_([e])}detail_(){const t=this.getFocus().getSemanticPrimary().id.toString(),e=this.getRebuilt().xml.getAttribute("id")===t?this.getRebuilt().xml:X(this.getRebuilt().xml,"id",t)[0],n=e.getAttribute("alternative");e.removeAttribute("alternative");const r=nu(e),o=this.mergePrefix_([r]);return e.setAttribute("alternative",n),o}}Du.ID_COUNTER=0,Du.SRE_ID_ATTR="sre-explorer-id";class Pu extends Du{up(){return null}down(){return null}left(){return null}right(){return null}repeat(){return null}depth(){return null}home(){return null}getDepth(){return 0}initLevels(){return null}combineContentChildren(t,e,n,r){return[]}findFocusOnLevel(t){return null}}class xu{constructor(){this.level_=[]}push(t){this.level_.push(t)}pop(){return this.level_.pop()}peek(){return this.level_[this.level_.length-1]||null}indexOf(t){const e=this.peek();return e?e.indexOf(t):null}find(t){const e=this.peek();if(!e)return null;for(let n=0,r=e.length;n<r;n++)if(t(e[n]))return e[n];return null}get(t){const e=this.peek();return!e||t<0||t>=e.length?null:e[t]}depth(){return this.level_.length}clone(){const t=new xu;return t.level_=this.level_.slice(0),t}toString(){let t="";for(let e,n=0;e=this.level_[n];n++)t+="\n"+e.map((function(t){return t.toString()}));return t}}class Uu extends Du{constructor(t,e,n,r){super(t,e,n,r),this.node=t,this.generator=e,this.highlighter=n,this.levels=null,this.restoreState()}initLevels(){const t=new xu;return t.push([this.getFocus()]),t}up(){super.up();const t=this.previousLevel();if(!t)return null;this.levels.pop();return this.levels.find((function(e){return e.getSemanticNodes().some((function(e){return e.id.toString()===t}))}))}down(){super.down();const t=this.nextLevel();return 0===t.length?null:(this.levels.push(t),t[0])}combineContentChildren(t,e,n,r){switch(t){case ot.RELSEQ:case ot.INFIXOP:case ot.MULTIREL:return this.makePairList(r,n);case ot.PREFIXOP:return[this.focusFromId(r[0],n.concat(r))];case ot.POSTFIXOP:return[this.focusFromId(r[0],r.concat(n))];case ot.MATRIX:case ot.VECTOR:case ot.FENCED:return[this.focusFromId(r[0],[n[0],r[0],n[1]])];case ot.CASES:return[this.focusFromId(r[0],[n[0],r[0]])];case ot.PUNCTUATED:return e===st.TEXT?r.map(this.singletonFocus.bind(this)):r.length===n.length?n.map(this.singletonFocus.bind(this)):this.combinePunctuations(r,n,[],[]);case ot.APPL:return[this.focusFromId(r[0],[r[0],n[0]]),this.singletonFocus(r[1])];case ot.ROOT:return[this.singletonFocus(r[0]),this.singletonFocus(r[1])];default:return r.map(this.singletonFocus.bind(this))}}combinePunctuations(t,e,n,r){if(0===t.length)return r;const o=t.shift(),i=e.shift();return o===i?(n.push(i),this.combinePunctuations(t,e,n,r)):(e.unshift(i),n.push(o),t.length===e.length?(r.push(this.focusFromId(o,n.concat(e))),r):(r.push(this.focusFromId(o,n)),this.combinePunctuations(t,e,[],r)))}makePairList(t,e){if(0===t.length)return[];if(1===t.length)return[this.singletonFocus(t[0])];const n=[this.singletonFocus(t.shift())];for(let r=0,o=t.length;r<o;r++)n.push(this.focusFromId(t[r],[e[r],t[r]]));return n}left(){super.left();const t=this.levels.indexOf(this.getFocus());if(null===t)return null;const e=this.levels.get(t-1);return e||null}right(){super.right();const t=this.levels.indexOf(this.getFocus());if(null===t)return null;const e=this.levels.get(t+1);return e||null}findFocusOnLevel(t){return this.levels.find((e=>e.getSemanticPrimary().id===t))}}class Fu extends Du{constructor(t,e,n,r){super(t,e,n,r),this.node=t,this.generator=e,this.highlighter=n,this.levels=null,this.restoreState()}initLevels(){const t=new xu;return t.push([this.primaryId()]),t}up(){super.up();const t=this.previousLevel();return t?(this.levels.pop(),this.singletonFocus(t)):null}down(){super.down();const t=this.nextLevel();if(0===t.length)return null;const e=this.singletonFocus(t[0]);return e&&this.levels.push(t),e}combineContentChildren(t,e,n,r){switch(t){case ot.RELSEQ:case ot.INFIXOP:case ot.MULTIREL:return Zn(r,n);case ot.PREFIXOP:return n.concat(r);case ot.POSTFIXOP:return r.concat(n);case ot.MATRIX:case ot.VECTOR:case ot.FENCED:return r.unshift(n[0]),r.push(n[1]),r;case ot.CASES:return r.unshift(n[0]),r;case ot.PUNCTUATED:return e===st.TEXT?Zn(r,n):r;case ot.APPL:return[r[0],n[0],r[1]];case ot.ROOT:return[r[0],r[1]];default:return r}}left(){super.left();const t=this.levels.indexOf(this.primaryId());if(null===t)return null;const e=this.levels.get(t-1);return e?this.singletonFocus(e):null}right(){super.right();const t=this.levels.indexOf(this.primaryId());if(null===t)return null;const e=this.levels.get(t+1);return e?this.singletonFocus(e):null}findFocusOnLevel(t){return this.singletonFocus(t.toString())}focusDomNodes(){return[this.getFocus().getDomPrimary()]}focusSemanticNodes(){return[this.getFocus().getSemanticPrimary()]}}class ku extends Fu{constructor(t,e,n,r){super(t,e,n,r),this.node=t,this.generator=e,this.highlighter=n,this.firstJump=null,this.key_=null,this.row_=0,this.currentTable_=null,this.keyMapping.set(Hs.ZERO,this.jumpCell.bind(this)),this.keyMapping.set(Hs.ONE,this.jumpCell.bind(this)),this.keyMapping.set(Hs.TWO,this.jumpCell.bind(this)),this.keyMapping.set(Hs.THREE,this.jumpCell.bind(this)),this.keyMapping.set(Hs.FOUR,this.jumpCell.bind(this)),this.keyMapping.set(Hs.FIVE,this.jumpCell.bind(this)),this.keyMapping.set(Hs.SIX,this.jumpCell.bind(this)),this.keyMapping.set(Hs.SEVEN,this.jumpCell.bind(this)),this.keyMapping.set(Hs.EIGHT,this.jumpCell.bind(this)),this.keyMapping.set(Hs.NINE,this.jumpCell.bind(this))}move(t){this.key_=t;const e=super.move(t);return this.modifier=!1,e}up(){return this.moved=vu.UP,this.eligibleCell_()?this.verticalMove_(!1):super.up()}down(){return this.moved=vu.DOWN,this.eligibleCell_()?this.verticalMove_(!0):super.down()}jumpCell(){if(!this.isInTable_()||null===this.key_)return this.getFocus();if(this.moved===vu.ROW){this.moved=vu.CELL;const t=this.key_-Hs.ZERO;return this.isLegalJump_(this.row_,t)?this.jumpCell_(this.row_,t):this.getFocus()}const t=this.key_-Hs.ZERO;return t>this.currentTable_.childNodes.length?this.getFocus():(this.row_=t,this.moved=vu.ROW,this.getFocus().clone())}undo(){const t=super.undo();return t===this.firstJump&&(this.firstJump=null),t}eligibleCell_(){const t=this.getFocus().getSemanticPrimary();return this.modifier&&t.type===ot.CELL&&-1!==ku.ELIGIBLE_CELL_ROLES.indexOf(t.role)}verticalMove_(t){const e=this.previousLevel();if(!e)return null;const n=this.getFocus(),r=this.levels.indexOf(this.primaryId()),o=this.levels.pop(),i=this.levels.indexOf(e),s=this.levels.get(t?i+1:i-1);if(!s)return this.levels.push(o),null;this.setFocus(this.singletonFocus(s));const a=this.nextLevel();return a[r]?(this.levels.push(a),this.singletonFocus(a[r])):(this.setFocus(n),this.levels.push(o),null)}jumpCell_(t,e){this.firstJump?this.virtualize(!1):(this.firstJump=this.getFocus(),this.virtualize(!0));const n=this.currentTable_.id.toString();let r;do{r=this.levels.pop()}while(-1===r.indexOf(n));this.levels.push(r),this.setFocus(this.singletonFocus(n)),this.levels.push(this.nextLevel());const o=this.currentTable_.childNodes[t-1];return this.setFocus(this.singletonFocus(o.id.toString())),this.levels.push(this.nextLevel()),this.singletonFocus(o.childNodes[e-1].id.toString())}isLegalJump_(t,e){const n=X(this.getRebuilt().xml,"id",this.currentTable_.id.toString())[0];if(!n||n.hasAttribute("alternative"))return!1;const r=this.currentTable_.childNodes[t-1];if(!r)return!1;const o=X(n,"id",r.id.toString())[0];return!(!o||o.hasAttribute("alternative"))&&!(!r||!r.childNodes[e-1])}isInTable_(){let t=this.getFocus().getSemanticPrimary();for(;t;){if(-1!==ku.ELIGIBLE_TABLE_TYPES.indexOf(t.type))return this.currentTable_=t,!0;t=t.parent}return!1}}ku.ELIGIBLE_CELL_ROLES=[st.DETERMINANT,st.ROWVECTOR,st.BINOMIAL,st.SQUAREMATRIX,st.MULTILINE,st.MATRIX,st.VECTOR,st.CASES,st.TABLE],ku.ELIGIBLE_TABLE_TYPES=[ot.MULTILINE,ot.MATRIX,ot.VECTOR,ot.CASES,ot.TABLE];const Bu={dummy:(t,e,n,r)=>new Pu(t,e,n,r),semantic:(t,e,n,r)=>new Uu(t,e,n,r),syntax:(t,e,n,r)=>new Fu(t,e,n,r),table:(t,e,n,r)=>new ku(t,e,n,r)};class Gu{static stringify_(t){return t?t.toString():t}constructor(t,e){this.name=t,this.process=e.processor,this.postprocess=e.postprocessor||((t,e)=>t),this.processor=this.postprocess?function(t){return this.postprocess(this.process(t),t)}:this.process,this.print=e.print||Gu.stringify_,this.pprint=e.pprint||this.print}}Gu.LocalState={walker:null,speechGenerator:null,highlighter:null};class Vu extends Gu{static getKey_(t){return"string"==typeof t?Hs[t.toUpperCase()]:t}constructor(t,e){super(t,e),this.key=e.key||Vu.getKey_}}const Hu=new Map;function ju(t){Hu.set(t.name,t)}function qu(t){const e=Hu.get(t);if(!e)throw new A("Unknown processor "+t);return e}function $u(t,e){const n=qu(t);try{return n.processor(e)}catch(t){throw new A("Processing error for expression "+e)}}function Xu(t,e){const n=qu(t);try{const t=n.processor(e);return R.getInstance().options.pprint?n.pprint(t):n.print(t)}catch(t){throw console.log(t),new A("Processing error for expression "+e)}}ju(new Gu("semantic",{processor:function(t){return Vc(P(t),R.getInstance().options)},postprocessor:function(t,e){const n=R.getInstance().options.speech;if(n===d.NONE)return t;const r=K(t);let o=nu(r,!0);if(n===d.SHALLOW)return t.setAttribute("speech",Da(o)),t;const i=v(".//*[@id]",t),s=v(".//*[@id]",r);for(let t,e,n=0;t=i[n],e=s[n];n++)o=nu(e),t.setAttribute("speech",Da(o));return t},pprint:function(t){return j(t.toString())}})),ju(new Gu("speech",{processor:function(t){return Da(wa(eu(Vc(P(t),R.getInstance().options),!0)))},pprint:function(t){const e=t.toString();return va.get(R.getInstance().options.markup)instanceof sa?j(e):e}})),ju(new Gu("json",{processor:function(t){return Hc(P(t),R.getInstance().options).toJson()},postprocessor:function(t,e){const n=R.getInstance().options.speech;if(n===d.NONE)return t;const r=Vc(P(e),R.getInstance().options),o=nu(r);if(n===d.SHALLOW)return t.stree.speech=Da(o),t;const i=t=>{const e=nu(v(`.//*[@id=${t.id}]`,r)[0]);t.speech=Da(e),t.children&&t.children.forEach(i)};return i(t.stree),t},print:function(t){return JSON.stringify(t)},pprint:function(t){return JSON.stringify(t,null,2)}})),ju(new Gu("description",{processor:function(t){return eu(Vc(P(t),R.getInstance().options),!0)},print:function(t){return JSON.stringify(t)},pprint:function(t){return JSON.stringify(t,null,2)}})),ju(new Gu("enriched",{processor:function(t){return function(t,e){const n=P(t);try{return Ml(n,e)}catch(t){return console.error(t),n}}(t,R.getInstance().options)},postprocessor:function(t,e){const n=Vl(t);let r;switch(R.getInstance().options.speech){case d.NONE:break;case d.SHALLOW:r=Lu("Adhoc"),r.getSpeech(n,t);break;case d.DEEP:r=Lu("Tree"),r.getSpeech(t,t)}return t},pprint:function(t){return j(t.toString())}})),ju(new Gu("rebuild",{processor:function(t){return new ql(P(t)).stree.xml()},pprint:function(t){return j(t.toString())}})),ju(new Gu("walker",{processor:function(t){const e=Lu("Node");Gu.LocalState.speechGenerator=e,e.setOptions({modality:R.getInstance().options.modality,locale:R.getInstance().options.locale,domain:R.getInstance().options.domain,style:R.getInstance().options.style}),Gu.LocalState.highlighter=function(t,e,n){const r=new xl(t,e),o="NativeMML"===n.renderer&&"Safari"===n.browser?"MML-CSS":"SVG"===n.renderer&&"v3"===n.browser?"SVG-V3":n.renderer,i=new(kl[o]||kl.NativeMML);return i.setColor(r),i}({color:"black"},{color:"white"},{renderer:"NativeMML"});const n=$u("enriched",t),r=function(t,e){const n=qu(t);return R.getInstance().options.pprint?n.pprint(e):n.print(e)}("enriched",n);return Gu.LocalState.walker=function(t,e,n,r,o){return(Bu[t.toLowerCase()]||Bu.dummy)(e,n,r,o)}(R.getInstance().options.walker,n,e,Gu.LocalState.highlighter,r),Gu.LocalState.walker},print:function(t){return Gu.LocalState.walker.speech()}})),ju(new Vu("move",{processor:function(t){if(!Gu.LocalState.walker)return null;return!1===Gu.LocalState.walker.move(t)?function(t){const e=va.get(R.getInstance().options.markup);return e?e.error(t):""}(t):Gu.LocalState.walker.speech()}})),ju(new Gu("number",{processor:function(t){const e=parseInt(t,10);return isNaN(e)?"":Nt.NUMBERS.numberToWords(e)}})),ju(new Gu("ordinal",{processor:function(t){const e=parseInt(t,10);return isNaN(e)?"":Nt.NUMBERS.wordOrdinal(e)}})),ju(new Gu("numericOrdinal",{processor:function(t){const e=parseInt(t,10);return isNaN(e)?"":Nt.NUMBERS.numericOrdinal(e)}})),ju(new Gu("vulgar",{processor:function(t){const[e,n]=t.split("/").map((t=>parseInt(t,10)));return isNaN(e)||isNaN(n)?"":$u("speech",`<mfrac><mn>${e}</mn><mn>${n}</mn></mfrac>`)}})),ju(new Gu("latex",{processor:function(t){return"braille"===R.getInstance().options.modality&&"euro"===R.getInstance().options.locale||console.info("LaTeX input currently only works for Euro Braille output. Please use the latex-to-speech package from npm for general LaTeX input to SRE."),$u("speech",`<math data-latex="${t}"></math>`)}})),ju(new Gu("rebuildStree",{processor:function(t){return new ql(P(t))}})),ju(new Gu("speechStructure",{processor:function(t){const e=P(t),n=new ql(e).stree.xml();return du(e,n),mu(n)},print:function(t){return JSON.stringify(t)},pprint:function(t){return JSON.stringify(t,null,2)}}));var Yu=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function s(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))};const Wu=S.u.VERSION;function Ku(t){return Yu(this,void 0,void 0,(function*(){return tc(t)}))}function zu(){return function(){const t=R.getInstance().json();return t.json=m.f.jsonPath,t}()}function Qu(){R.getInstance().reset()}function Ju(){return Yu(this,void 0,void 0,(function*(){return Ku({}).then((()=>O.getall()))}))}const Zu=Ka;function th(t){return lh("speech",t)}function eh(t){return lh("semantic",t)}function nh(t){return lh("json",t)}function rh(t){return lh("description",t)}function oh(t){return lh("enriched",t)}function ih(t){return lh("number",t)}function sh(t){return lh("ordinal",t)}function ah(t){return lh("numericOrdinal",t)}function ch(t){return lh("vulgar",t)}function lh(t,e){return $u(t,e)}const uh={};function hh(t,e,n){switch(R.getInstance().mode){case o.ASYNC:return function(t,e,n){return Yu(this,void 0,void 0,(function*(){const r=yield m.f.fs.promises.readFile(e,{encoding:"utf8"}),o=Xu(t,r);if(n)try{m.f.fs.promises.writeFile(n,o)}catch(t){throw new A("Can not write to file: "+n)}return o}))}(t,e,n);case o.SYNC:return function(t,e,n){const r=function(t){let e;try{e=m.f.fs.readFileSync(t,{encoding:"utf8"})}catch(e){throw new A("Can not open file: "+t)}return e}(e),o=Xu(t,r);if(n)try{m.f.fs.writeFileSync(n,o)}catch(t){throw new A("Can not write to file: "+n)}return o}(t,e,n);default:throw new A(`Can process files in ${R.getInstance().mode} mode`)}}function dh(t){return Xu("walker",t)}function fh(t){return function(t,e){const n=qu(t),r=n instanceof Vu?n.key(e):e,o=n.processor(r);return R.getInstance().options.pprint?n.pprint(o):n.print(o)}("move",t)}function ph(t){const e=t||0;O.getall().then((()=>process.exit(e)))}function mh(t){return lh("speechStructure",t)}function gh(t,e){return Yu(this,void 0,void 0,(function*(){const n=P(t),r=new ql(n),o=Nu(e);return e.domain2style=Su(o),Ih(n,r.stree.xml(),e)}))}function Eh(t,e){return Yu(this,void 0,void 0,(function*(){const n=P(t),r=new ql(n),o=Nu(e);return(e=gu(e,o)).domain2style=Su(o),Ih(n,r.stree.xml(),e)}))}function Nh(t,e,n){return Yu(this,void 0,void 0,(function*(){const r=P(t),o=new ql(r),i=Nu(e);return e.style=Eu(o.nodeDict[n],e),i[e.domain]=e.style,e.domain2style=Su(i),Ih(r,o.stree.xml(),e)}))}function Sh(t){return Yu(this,void 0,void 0,(function*(){return Kl.getLocalePreferences()[t.locale]}))}function Th(t,e){return Yu(this,void 0,void 0,(function*(){var n;const r=P(t),o=new ql(r),i=null!==(n=o.stree.root.querySelectorAll((t=>t.id.toString()===e))[0])&&void 0!==n?n:o.stree.root;return Kl.relevantPreferences(i)}))}function Ih(t,e,n){return Yu(this,void 0,void 0,(function*(){var r;yield Ku(n),R.getInstance().options.automark=!0;const o={};o.options=n,o.mactions=du(t,e),o.speech=mu(e);const i=null===(r=e.childNodes[0])||void 0===r?void 0:r.getAttribute("id");return o.label=o.speech[i]["speech-none"],o.ssml=o.speech[i]["speech-ssml"],o.translations=Object.assign({},Nt.MESSAGES.navigate),"none"===n.braille||(yield Ku({modality:"braille",locale:n.braille,domain:"default",style:"default"}),o.braille=function(t){return eu(t,!0),xa.getInstance().speechStructure.json(["none"])}(e),o.braillelabel=o.braille[i]["braille-none"]),o}))}uh.toSpeech=function(t,e){return hh("speech",t,e)},uh.toSemantic=function(t,e){return hh("semantic",t,e)},uh.toJson=function(t,e){return hh("json",t,e)},uh.toDescription=function(t,e){return hh("description",t,e)},uh.toEnriched=function(t,e){return hh("enriched",t,e)};const bh=E;m.f.documentSupported||m.f.webworker?Ku({mode:o.HTTP}).then((()=>Ku({}))):Ku({mode:o.SYNC}).then((()=>Ku({mode:o.ASYNC})));const Ah=S.u},714:(t,e,n)=>{"use strict";n.d(e,{u:()=>r});class r{static ensureLocale(t,e){return r.LOCALES.get(t)?t:(console.error(`Locale ${t} does not exist! Using ${r.LOCALES.get(e)} instead.`),e)}}r.VERSION="5.0.0-beta.1",r.LOCALES=new Map([["af","Africaans"],["ca","Catalan"],["da","Danish"],["de","German"],["en","English"],["es","Spanish"],["euro","Euro"],["fr","French"],["hi","Hindi"],["it","Italian"],["ko","Korean"],["nb","Bokm\xe5l"],["nn","Nynorsk"],["sv","Swedish"],["nemeth","Nemeth"]]),r.mathjaxVersion="4.0.0",r.url="https://cdn.jsdelivr.net/npm/speech-rule-engine@"+r.VERSION+"/lib/mathmaps"},731:(t,e)=>{"use strict";function n(t){try{"function"!=typeof t&&(t=RegExp);var e=new t("\u{1d306}","u").exec("\u{1d306}");return!!e&&2===e[0].length}catch(t){}return!1}var r=n();function o(t){if("["!==t.source[0])throw new Error(t+" can not be used with chars");return t.source.slice(1,t.source.lastIndexOf("]"))}function i(t,e){if("["!==t.source[0])throw new Error("/"+t.source+"/ can not be used with chars_without");if(!e||"string"!=typeof e)throw new Error(JSON.stringify(e)+" is not a valid search");if(-1===t.source.indexOf(e))throw new Error('"'+e+'" is not is /'+t.source+"/");if("-"===e&&1!==t.source.indexOf(e))throw new Error('"'+e+'" is not at the first postion of /'+t.source+"/");return new RegExp(t.source.replace(e,""),r?"u":"")}function s(t){var e=this;return new RegExp(Array.prototype.slice.call(arguments).map((function(t){var n="string"==typeof t;if(n&&void 0===e&&"|"===t)throw new Error("use regg instead of reg to wrap expressions with `|`!");return n?t:t.source})).join(""),r?"mu":"m")}function a(t){if(0===arguments.length)throw new Error("no parameters provided");return s.apply(a,["(?:"].concat(Array.prototype.slice.call(arguments),[")"]))}var c=/[-\x09\x0A\x0D\x20-\x2C\x2E-\uD7FF\uE000-\uFFFD]/;r&&(c=s("[",o(c),"\\u{10000}-\\u{10FFFF}","]"));var l=/[\x20\x09\x0D\x0A]/,u=o(l),h=s(l,"+"),d=s(l,"*"),f=/[:_a-zA-Z\xC0-\xD6\xD8-\xF6\xF8-\u02FF\u0370-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/;r&&(f=s("[",o(f),"\\u{10000}-\\u{10FFFF}","]"));var p=s("[",o(f),o(/[-.0-9\xB7]/),o(/[\u0300-\u036F\u203F-\u2040]/),"]"),m=s(f,p,"*"),g=s(p,"+"),E=s("&",m,";"),N=a(/&#[0-9]+;|&#x[0-9a-fA-F]+;/),S=a(E,"|",N),T=s("%",m,";"),I=a(s('"',a(/[^%&"]/,"|",T,"|",S),"*",'"'),"|",s("'",a(/[^%&']/,"|",T,"|",S),"*","'")),b=a('"',a(/[^<&"]/,"|",S),"*",'"',"|","'",a(/[^<&']/,"|",S),"*","'"),A=s(i(f,":"),i(p,":"),"*"),R=s(A,a(":",A),"?"),O=s("^",R,"$"),C=s("(",R,")"),y=a(/"[^"]*"|'[^']*'/),L=s(/^<\?/,"(",m,")",a(h,"(",c,"*?)"),"?",/\?>/),M=/[\x20\x0D\x0Aa-zA-Z0-9-'()+,./:=?;!*#@$_%]/,_=a('"',M,'*"',"|","'",i(M,"'"),"*'"),v="\x3c!--",w=s(v,a(i(c,"-"),"|",s("-",i(c,"-"))),"*","--\x3e"),D="#PCDATA",P=a(s(/\(/,d,D,a(d,/\|/,d,R),"*",d,/\)\*/),"|",s(/\(/,d,D,d,/\)/)),x=a("EMPTY","|","ANY","|",P,"|",s(/\([^>]+\)/,/[?*+]?/)),U=s("<!ELEMENT",h,a(R,"|",T),h,a(x,"|",T),d,">"),F=s("NOTATION",h,/\(/,d,m,a(d,/\|/,d,m),"*",d,/\)/),k=s(/\(/,d,g,a(d,/\|/,d,g),"*",d,/\)/),B=a(F,"|",k),G=a(/CDATA|ID|IDREF|IDREFS|ENTITY|ENTITIES|NMTOKEN|NMTOKENS/,"|",B),V=a(/#REQUIRED|#IMPLIED/,"|",a(a("#FIXED",h),"?",b)),H=s("<!ATTLIST",h,m,a(h,m,h,G,h,V),"*",d,">"),j="about:legacy-compat",q=a('"'+j+'"',"|","'"+j+"'"),$="SYSTEM",X="PUBLIC",Y=a(a($,h,y),"|",a(X,h,_,h,y)),W=s("^",a(a($,h,"(?<SystemLiteralOnly>",y,")"),"|",a(X,h,"(?<PubidLiteral>",_,")",h,"(?<SystemLiteral>",y,")"))),K=a(h,"NDATA",h,m),z="<!ENTITY",Q=s(z,h,m,h,a(I,"|",a(Y,K,"?")),d,">"),J=a(I,"|",Y),Z=a(Q,"|",s(z,h,"%",h,m,h,J,d,">")),tt=s(X,h,_),et=s("<!NOTATION",h,m,h,a(Y,"|",tt),d,">"),nt=s(d,"=",d),rt=/1[.]\d+/,ot=s(h,"version",nt,a("'",rt,"'","|",'"',rt,'"')),it=/[A-Za-z][-A-Za-z0-9._]*/,st=s(/^<\?xml/,ot,a(h,"encoding",nt,a('"',it,'"',"|","'",it,"'")),"?",a(h,"standalone",nt,a("'",a("yes","|","no"),"'","|",'"',a("yes","|","no"),'"')),"?",d,/\?>/),at=s(c,"*?",/\]\]>/),ct=s(/<!\[CDATA\[/,at);e.chars=o,e.chars_without=i,e.detectUnicodeSupport=n,e.reg=s,e.regg=a,e.ABOUT_LEGACY_COMPAT=j,e.ABOUT_LEGACY_COMPAT_SystemLiteral=q,e.AttlistDecl=H,e.CDATA_START="<![CDATA[",e.CDATA_END="]]>",e.CDSect=ct,e.Char=c,e.Comment=w,e.COMMENT_START=v,e.COMMENT_END="--\x3e",e.DOCTYPE_DECL_START="<!DOCTYPE",e.elementdecl=U,e.EntityDecl=Z,e.EntityValue=I,e.ExternalID=Y,e.ExternalID_match=W,e.Name=m,e.NotationDecl=et,e.Reference=S,e.PEReference=T,e.PI=L,e.PUBLIC=X,e.PubidLiteral=_,e.QName=R,e.QName_exact=O,e.QName_group=C,e.S=h,e.SChar_s=u,e.S_OPT=d,e.SYSTEM=$,e.SystemLiteral=y,e.UNICODE_REPLACEMENT_CHARACTER="\ufffd",e.UNICODE_SUPPORT=r,e.XMLDecl=st},767:(t,e,n)=>{"use strict";function r(t,e){t.prototype=Object.create(Error.prototype,{constructor:{value:t},name:{value:t.name,enumerable:!0,writable:e}})}var o=n(870).freeze({Error:"Error",IndexSizeError:"IndexSizeError",DomstringSizeError:"DomstringSizeError",HierarchyRequestError:"HierarchyRequestError",WrongDocumentError:"WrongDocumentError",InvalidCharacterError:"InvalidCharacterError",NoDataAllowedError:"NoDataAllowedError",NoModificationAllowedError:"NoModificationAllowedError",NotFoundError:"NotFoundError",NotSupportedError:"NotSupportedError",InUseAttributeError:"InUseAttributeError",InvalidStateError:"InvalidStateError",SyntaxError:"SyntaxError",InvalidModificationError:"InvalidModificationError",NamespaceError:"NamespaceError",InvalidAccessError:"InvalidAccessError",ValidationError:"ValidationError",TypeMismatchError:"TypeMismatchError",SecurityError:"SecurityError",NetworkError:"NetworkError",AbortError:"AbortError",URLMismatchError:"URLMismatchError",QuotaExceededError:"QuotaExceededError",TimeoutError:"TimeoutError",InvalidNodeTypeError:"InvalidNodeTypeError",DataCloneError:"DataCloneError",EncodingError:"EncodingError",NotReadableError:"NotReadableError",UnknownError:"UnknownError",ConstraintError:"ConstraintError",DataError:"DataError",TransactionInactiveError:"TransactionInactiveError",ReadOnlyError:"ReadOnlyError",VersionError:"VersionError",OperationError:"OperationError",NotAllowedError:"NotAllowedError",OptOutError:"OptOutError"}),i=Object.keys(o);function s(t){return"number"==typeof t&&t>=1&&t<=25}function a(t,e){var n;s(t)?(this.name=i[t],this.message=e||""):(this.message=t,this.name="string"==typeof(n=e)&&n.substring(n.length-o.Error.length)===o.Error?e:o.Error),Error.captureStackTrace&&Error.captureStackTrace(this,a)}r(a,!0),Object.defineProperties(a.prototype,{code:{enumerable:!0,get:function(){var t=i.indexOf(this.name);return s(t)?t:0}}});for(var c={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25},l=Object.entries(c),u=0;u<l.length;u++){a[l[u][0]]=l[u][1]}function h(t,e){this.message=t,this.locator=e,Error.captureStackTrace&&Error.captureStackTrace(this,h)}r(h),e.DOMException=a,e.DOMExceptionName=o,e.ExceptionCode=c,e.ParseError=h},786:(t,e,n)=>{"use strict";var r=n(870),o=r.find,i=r.hasDefaultHTMLNamespace,s=r.hasOwn,a=r.isHTMLMimeType,c=r.isHTMLRawTextElement,l=r.isHTMLVoidElement,u=r.MIME_TYPE,h=r.NAMESPACE,d=Symbol(),f=n(767),p=f.DOMException,m=f.DOMExceptionName,g=n(731);function E(t){if(t!==d)throw new TypeError("Illegal constructor")}function N(t){return""!==t}function S(t,e){return s(t,e)||(t[e]=!0),t}function T(t){if(!t)return[];var e=function(t){return t?t.split(/[\t\n\f\r ]+/).filter(N):[]}(t);return Object.keys(e.reduce(S,{}))}function I(t){if(!g.QName_exact.test(t))throw new p(p.INVALID_CHARACTER_ERR,'invalid character in qualified name "'+t+'"')}function b(t,e){I(e),t=t||null;var n=null,o=e;if(e.indexOf(":")>=0){var i=e.split(":");n=i[0],o=i[1]}if(null!==n&&null===t)throw new p(p.NAMESPACE_ERR,"prefix is non-null and namespace is null");if("xml"===n&&t!==r.NAMESPACE.XML)throw new p(p.NAMESPACE_ERR,'prefix is "xml" and namespace is not the XML namespace');if(("xmlns"===n||"xmlns"===e)&&t!==r.NAMESPACE.XMLNS)throw new p(p.NAMESPACE_ERR,'either qualifiedName or prefix is "xmlns" and namespace is not the XMLNS namespace');if(t===r.NAMESPACE.XMLNS&&"xmlns"!==n&&"xmlns"!==e)throw new p(p.NAMESPACE_ERR,'namespace is the XMLNS namespace and neither qualifiedName nor prefix is "xmlns"');return[t,n,o]}function A(t,e){for(var n in t)s(t,n)&&(e[n]=t[n])}function R(t,e){var n=t.prototype;if(!(n instanceof e)){function r(){}r.prototype=e.prototype,A(n,r=new r),t.prototype=n=r}n.constructor!=t&&("function"!=typeof t&&console.error("unknown Class:"+t),n.constructor=t)}var O={},C=O.ELEMENT_NODE=1,y=O.ATTRIBUTE_NODE=2,L=O.TEXT_NODE=3,M=O.CDATA_SECTION_NODE=4,_=O.ENTITY_REFERENCE_NODE=5,v=O.ENTITY_NODE=6,w=O.PROCESSING_INSTRUCTION_NODE=7,D=O.COMMENT_NODE=8,P=O.DOCUMENT_NODE=9,x=O.DOCUMENT_TYPE_NODE=10,U=O.DOCUMENT_FRAGMENT_NODE=11,F=O.NOTATION_NODE=12,k=r.freeze({DOCUMENT_POSITION_DISCONNECTED:1,DOCUMENT_POSITION_PRECEDING:2,DOCUMENT_POSITION_FOLLOWING:4,DOCUMENT_POSITION_CONTAINS:8,DOCUMENT_POSITION_CONTAINED_BY:16,DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:32});function B(t,e){if(e.length<t.length)return B(e,t);var n=null;for(var r in t){if(t[r]!==e[r])return n;n=t[r]}return n}function G(t){return t.guid||(t.guid=Math.random()),t.guid}function V(){}function H(t,e){this._node=t,this._refresh=e,j(this)}function j(t){var e=t._node._inc||t._node.ownerDocument._inc;if(t._inc!==e){var n=t._refresh(t._node);if(_t(t,"length",n.length),!t.$$length||n.length<t.$$length)for(var r=n.length;r in t;r++)s(t,r)&&delete t[r];A(n,t),t._inc=e}}function q(){}function $(t,e){for(var n=0;n<t.length;){if(t[n]===e)return n;n++}}function X(t,e,n,r){if(r?e[$(e,r)]=n:(e[e.length]=n,e.length++),t){n.ownerElement=t;var o=t.ownerDocument;o&&(r&&Z(o,t,r),function(t,e,n){t&&t._inc++;var r=n.namespaceURI;r===h.XMLNS&&(e._nsMap[n.prefix?n.localName:""]=n.value)}(o,t,n))}}function Y(t,e,n){var r=$(e,n);if(r>=0){for(var o=e.length-1;r<=o;)e[r]=e[++r];if(e.length=o,t){var i=t.ownerDocument;i&&Z(i,t,n),n.ownerElement=null}}}function W(){}function K(t){E(t)}function z(t){return("<"==t?"&lt;":">"==t&&"&gt;")||"&"==t&&"&amp;"||'"'==t&&"&quot;"||"&#"+t.charCodeAt()+";"}function Q(t,e){if(e(t))return!0;if(t=t.firstChild)do{if(Q(t,e))return!0}while(t=t.nextSibling)}function J(t,e){E(t);var n=e||{};this.ownerDocument=this,this.contentType=n.contentType||u.XML_APPLICATION,this.type=a(this.contentType)?"html":"xml"}function Z(t,e,n,r){t&&t._inc++,n.namespaceURI===h.XMLNS&&delete e._nsMap[n.prefix?n.localName:""]}function tt(t,e,n){if(t&&t._inc){t._inc++;var r=e.childNodes;if(n&&!n.nextSibling)r[r.length++]=n;else{for(var o=e.firstChild,i=0;o;)r[i++]=o,o=o.nextSibling;r.length=i,delete r[r.length]}}}function et(t,e){if(t!==e.parentNode)throw new p(p.NOT_FOUND_ERR,"child's parent is not parent");var n=e.previousSibling,r=e.nextSibling;return n?n.nextSibling=r:t.firstChild=r,r?r.previousSibling=n:t.lastChild=n,tt(t.ownerDocument,t),e.parentNode=null,e.previousSibling=null,e.nextSibling=null,e}function nt(t){return t&&t.nodeType===K.DOCUMENT_TYPE_NODE}function rt(t){return t&&t.nodeType===K.ELEMENT_NODE}function ot(t){return t&&t.nodeType===K.TEXT_NODE}function it(t,e){var n=t.childNodes||[];if(o(n,rt)||nt(e))return!1;var r=o(n,nt);return!(e&&r&&n.indexOf(r)>n.indexOf(e))}function st(t,e){var n=t.childNodes||[];if(o(n,(function(t){return rt(t)&&t!==e})))return!1;var r=o(n,nt);return!(e&&r&&n.indexOf(r)>n.indexOf(e))}function at(t,e,n){if(!function(t){return t&&(t.nodeType===K.DOCUMENT_NODE||t.nodeType===K.DOCUMENT_FRAGMENT_NODE||t.nodeType===K.ELEMENT_NODE)}(t))throw new p(p.HIERARCHY_REQUEST_ERR,"Unexpected parent node type "+t.nodeType);if(n&&n.parentNode!==t)throw new p(p.NOT_FOUND_ERR,"child not in parent");if(!function(t){return t&&(t.nodeType===K.CDATA_SECTION_NODE||t.nodeType===K.COMMENT_NODE||t.nodeType===K.DOCUMENT_FRAGMENT_NODE||t.nodeType===K.DOCUMENT_TYPE_NODE||t.nodeType===K.ELEMENT_NODE||t.nodeType===K.PROCESSING_INSTRUCTION_NODE||t.nodeType===K.TEXT_NODE)}(e)||nt(e)&&t.nodeType!==K.DOCUMENT_NODE)throw new p(p.HIERARCHY_REQUEST_ERR,"Unexpected node type "+e.nodeType+" for parent node type "+t.nodeType)}function ct(t,e,n){var r=t.childNodes||[],i=e.childNodes||[];if(e.nodeType===K.DOCUMENT_FRAGMENT_NODE){var s=i.filter(rt);if(s.length>1||o(i,ot))throw new p(p.HIERARCHY_REQUEST_ERR,"More than one element or text in fragment");if(1===s.length&&!it(t,n))throw new p(p.HIERARCHY_REQUEST_ERR,"Element in fragment can not be inserted before doctype")}if(rt(e)&&!it(t,n))throw new p(p.HIERARCHY_REQUEST_ERR,"Only one element can be added and only after doctype");if(nt(e)){if(o(r,nt))throw new p(p.HIERARCHY_REQUEST_ERR,"Only one doctype is allowed");var a=o(r,rt);if(n&&r.indexOf(a)<r.indexOf(n))throw new p(p.HIERARCHY_REQUEST_ERR,"Doctype can only be inserted before an element");if(!n&&a)throw new p(p.HIERARCHY_REQUEST_ERR,"Doctype can not be appended since element is present")}}function lt(t,e,n){var r=t.childNodes||[],i=e.childNodes||[];if(e.nodeType===K.DOCUMENT_FRAGMENT_NODE){var s=i.filter(rt);if(s.length>1||o(i,ot))throw new p(p.HIERARCHY_REQUEST_ERR,"More than one element or text in fragment");if(1===s.length&&!st(t,n))throw new p(p.HIERARCHY_REQUEST_ERR,"Element in fragment can not be inserted before doctype")}if(rt(e)&&!st(t,n))throw new p(p.HIERARCHY_REQUEST_ERR,"Only one element can be added and only after doctype");if(nt(e)){function c(t){return nt(t)&&t!==n}if(o(r,c))throw new p(p.HIERARCHY_REQUEST_ERR,"Only one doctype is allowed");var a=o(r,rt);if(n&&r.indexOf(a)<r.indexOf(n))throw new p(p.HIERARCHY_REQUEST_ERR,"Doctype can only be inserted before an element")}}function ut(t,e,n,r){at(t,e,n),t.nodeType===K.DOCUMENT_NODE&&(r||ct)(t,e,n);var o=e.parentNode;if(o&&o.removeChild(e),e.nodeType===U){var i=e.firstChild;if(null==i)return e;var s=e.lastChild}else i=s=e;var a=n?n.previousSibling:t.lastChild;i.previousSibling=a,s.nextSibling=n,a?a.nextSibling=i:t.firstChild=i,null==n?t.lastChild=s:n.previousSibling=s;do{i.parentNode=t}while(i!==s&&(i=i.nextSibling));return tt(t.ownerDocument||t,t,e),e.nodeType==U&&(e.firstChild=e.lastChild=null),e}function ht(t){E(t),this._nsMap=Object.create(null)}function dt(t){E(t),this.namespaceURI=null,this.prefix=null,this.ownerElement=null}function ft(t){E(t)}function pt(t){E(t)}function mt(t){E(t)}function gt(t){E(t)}function Et(t){E(t)}function Nt(t){E(t)}function St(t){E(t)}function Tt(t){E(t)}function It(t){E(t)}function bt(t){E(t)}function At(){}function Rt(t){var e=[],n=this.nodeType===P&&this.documentElement||this,r=n.prefix,o=n.namespaceURI;if(o&&null==r&&null==(r=n.lookupPrefix(o)))var i=[{namespace:o,prefix:null}];return yt(this,e,t,i),e.join("")}function Ot(t,e,n){var r=t.prefix||"",o=t.namespaceURI;if(!o)return!1;if("xml"===r&&o===h.XML||o===h.XMLNS)return!1;for(var i=n.length;i--;){var s=n[i];if(s.prefix===r)return s.namespace!==o}return!0}function Ct(t,e,n){t.push(" ",e,'="',n.replace(/[<>&"\t\n\r]/g,z),'"')}function yt(t,e,n,r){r||(r=[]);var o="html"===(t.nodeType===P?t:t.ownerDocument).type;if(n){if(!(t=n(t)))return;if("string"==typeof t)return void e.push(t)}switch(t.nodeType){case C:var i=t.attributes,s=i.length,a=t.firstChild,u=t.tagName,d=u;if(!o&&!t.prefix&&t.namespaceURI){for(var f,p=0;p<i.length;p++)if("xmlns"===i.item(p).name){f=i.item(p).value;break}if(!f)for(var m=r.length-1;m>=0;m--){if(""===(E=r[m]).prefix&&E.namespace===t.namespaceURI){f=E.namespace;break}}if(f!==t.namespaceURI)for(m=r.length-1;m>=0;m--){var E;if((E=r[m]).namespace===t.namespaceURI){E.prefix&&(d=E.prefix+":"+u);break}}}e.push("<",d);for(var N=0;N<s;N++){"xmlns"==(S=i.item(N)).prefix?r.push({prefix:S.localName,namespace:S.value}):"xmlns"==S.nodeName&&r.push({prefix:"",namespace:S.value})}for(N=0;N<s;N++){var S,T,I;if(Ot(S=i.item(N),0,r))Ct(e,(T=S.prefix||"")?"xmlns:"+T:"xmlns",I=S.namespaceURI),r.push({prefix:T,namespace:I});yt(S,e,n,r)}if(u===d&&Ot(t,0,r))Ct(e,(T=t.prefix||"")?"xmlns:"+T:"xmlns",I=t.namespaceURI),r.push({prefix:T,namespace:I});var b=!a;if(b&&(o||t.namespaceURI===h.HTML)&&(b=l(u)),b)e.push("/>");else{if(e.push(">"),o&&c(u))for(;a;)a.data?e.push(a.data):yt(a,e,n,r.slice()),a=a.nextSibling;else for(;a;)yt(a,e,n,r.slice()),a=a.nextSibling;e.push("</",d,">")}return;case P:case U:for(a=t.firstChild;a;)yt(a,e,n,r.slice()),a=a.nextSibling;return;case y:return Ct(e,t.name,t.value);case L:return e.push(t.data.replace(/[<&>]/g,z));case M:return e.push(g.CDATA_START,t.data,g.CDATA_END);case D:return e.push(g.COMMENT_START,t.data,g.COMMENT_END);case x:var A=t.publicId,R=t.systemId;return e.push(g.DOCTYPE_DECL_START," ",t.name),A?(e.push(" ",g.PUBLIC," ",A),R&&"."!==R&&e.push(" ",R)):R&&"."!==R&&e.push(" ",g.SYSTEM," ",R),t.internalSubset&&e.push(" [",t.internalSubset,"]"),void e.push(">");case w:return e.push("<?",t.target," ",t.data,"?>");case _:return e.push("&",t.nodeName,";");default:e.push("??",t.nodeName)}}function Lt(t,e,n){var r;switch(e.nodeType){case C:(r=e.cloneNode(!1)).ownerDocument=t;case U:break;case y:n=!0}if(r||(r=e.cloneNode(!1)),r.ownerDocument=t,r.parentNode=null,n)for(var o=e.firstChild;o;)r.appendChild(Lt(t,o,n)),o=o.nextSibling;return r}function Mt(t,e,n){var r=new e.constructor(d);for(var o in e)if(s(e,o)){var i=e[o];"object"!=typeof i&&i!=r[o]&&(r[o]=i)}switch(e.childNodes&&(r.childNodes=new V),r.ownerDocument=t,r.nodeType){case C:var a=e.attributes,c=r.attributes=new q,l=a.length;c._ownerElement=r;for(var u=0;u<l;u++)r.setAttributeNode(Mt(t,a.item(u),!0));break;case y:n=!0}if(n)for(var h=e.firstChild;h;)r.appendChild(Mt(t,h,n)),h=h.nextSibling;return r}function _t(t,e,n){t[e]=n}V.prototype={length:0,item:function(t){return t>=0&&t<this.length?this[t]:null},toString:function(t){for(var e=[],n=0;n<this.length;n++)yt(this[n],e,t);return e.join("")},filter:function(t){return Array.prototype.filter.call(this,t)},indexOf:function(t){return Array.prototype.indexOf.call(this,t)}},V.prototype[Symbol.iterator]=function(){var t=this,e=0;return{next:function(){return e<t.length?{value:t[e++],done:!1}:{done:!0}},return:function(){return{done:!0}}}},H.prototype.item=function(t){return j(this),this[t]||null},R(H,V),q.prototype={length:0,item:V.prototype.item,getNamedItem:function(t){this._ownerElement&&this._ownerElement._isInHTMLDocumentAndNamespace()&&(t=t.toLowerCase());for(var e=0;e<this.length;){var n=this[e];if(n.nodeName===t)return n;e++}return null},setNamedItem:function(t){var e=t.ownerElement;if(e&&e!==this._ownerElement)throw new p(p.INUSE_ATTRIBUTE_ERR);var n=this.getNamedItemNS(t.namespaceURI,t.localName);return n===t?t:(X(this._ownerElement,this,t,n),n)},setNamedItemNS:function(t){return this.setNamedItem(t)},removeNamedItem:function(t){var e=this.getNamedItem(t);if(!e)throw new p(p.NOT_FOUND_ERR,t);return Y(this._ownerElement,this,e),e},removeNamedItemNS:function(t,e){var n=this.getNamedItemNS(t,e);if(!n)throw new p(p.NOT_FOUND_ERR,t?t+" : "+e:e);return Y(this._ownerElement,this,n),n},getNamedItemNS:function(t,e){t||(t=null);for(var n=0;n<this.length;){var r=this[n];if(r.localName===e&&r.namespaceURI===t)return r;n++}return null}},q.prototype[Symbol.iterator]=function(){var t=this,e=0;return{next:function(){return e<t.length?{value:t[e++],done:!1}:{done:!0}},return:function(){return{done:!0}}}},W.prototype={hasFeature:function(t,e){return!0},createDocument:function(t,e,n){var r=u.XML_APPLICATION;t===h.HTML?r=u.XML_XHTML_APPLICATION:t===h.SVG&&(r=u.XML_SVG_IMAGE);var o=new J(d,{contentType:r});if(o.implementation=this,o.childNodes=new V,o.doctype=n||null,n&&o.appendChild(n),e){var i=o.createElementNS(t,e);o.appendChild(i)}return o},createDocumentType:function(t,e,n,r){I(t);var o=new Et(d);return o.name=t,o.nodeName=t,o.publicId=e||"",o.systemId=n||"",o.internalSubset=r||"",o.childNodes=new V,o},createHTMLDocument:function(t){var e=new J(d,{contentType:u.HTML});if(e.implementation=this,e.childNodes=new V,!1!==t){e.doctype=this.createDocumentType("html"),e.doctype.ownerDocument=e,e.appendChild(e.doctype);var n=e.createElement("html");e.appendChild(n);var r=e.createElement("head");if(n.appendChild(r),"string"==typeof t){var o=e.createElement("title");o.appendChild(e.createTextNode(t)),r.appendChild(o)}n.appendChild(e.createElement("body"))}return e}},K.prototype={firstChild:null,lastChild:null,previousSibling:null,nextSibling:null,parentNode:null,get parentElement(){return this.parentNode&&this.parentNode.nodeType===this.ELEMENT_NODE?this.parentNode:null},childNodes:null,ownerDocument:null,nodeValue:null,namespaceURI:null,prefix:null,localName:null,baseURI:"about:blank",get isConnected(){var t=this.getRootNode();return t&&t.nodeType===t.DOCUMENT_NODE},contains:function(t){if(!t)return!1;var e=t;do{if(this===e)return!0;e=t.parentNode}while(e);return!1},getRootNode:function(t){var e=this;do{if(!e.parentNode)return e;e=e.parentNode}while(e)},isEqualNode:function(t){if(!t)return!1;if(this.nodeType!==t.nodeType)return!1;switch(this.nodeType){case this.DOCUMENT_TYPE_NODE:if(this.name!==t.name)return!1;if(this.publicId!==t.publicId)return!1;if(this.systemId!==t.systemId)return!1;break;case this.ELEMENT_NODE:if(this.namespaceURI!==t.namespaceURI)return!1;if(this.prefix!==t.prefix)return!1;if(this.localName!==t.localName)return!1;if(this.attributes.length!==t.attributes.length)return!1;for(var e=0;e<this.attributes.length;e++){var n=this.attributes.item(e);if(!n.isEqualNode(t.getAttributeNodeNS(n.namespaceURI,n.localName)))return!1}break;case this.ATTRIBUTE_NODE:if(this.namespaceURI!==t.namespaceURI)return!1;if(this.localName!==t.localName)return!1;if(this.value!==t.value)return!1;break;case this.PROCESSING_INSTRUCTION_NODE:if(this.target!==t.target||this.data!==t.data)return!1;break;case this.TEXT_NODE:case this.COMMENT_NODE:if(this.data!==t.data)return!1}if(this.childNodes.length!==t.childNodes.length)return!1;for(e=0;e<this.childNodes.length;e++)if(!this.childNodes[e].isEqualNode(t.childNodes[e]))return!1;return!0},isSameNode:function(t){return this===t},insertBefore:function(t,e){return ut(this,t,e)},replaceChild:function(t,e){ut(this,t,e,lt),e&&this.removeChild(e)},removeChild:function(t){return et(this,t)},appendChild:function(t){return this.insertBefore(t,null)},hasChildNodes:function(){return null!=this.firstChild},cloneNode:function(t){return Mt(this.ownerDocument||this,this,t)},normalize:function(){for(var t=this.firstChild;t;){var e=t.nextSibling;e&&e.nodeType==L&&t.nodeType==L?(this.removeChild(e),t.appendData(e.data)):(t.normalize(),t=e)}},isSupported:function(t,e){return this.ownerDocument.implementation.hasFeature(t,e)},lookupPrefix:function(t){for(var e=this;e;){var n=e._nsMap;if(n)for(var r in n)if(s(n,r)&&n[r]===t)return r;e=e.nodeType==y?e.ownerDocument:e.parentNode}return null},lookupNamespaceURI:function(t){for(var e=this;e;){var n=e._nsMap;if(n&&s(n,t))return n[t];e=e.nodeType==y?e.ownerDocument:e.parentNode}return null},isDefaultNamespace:function(t){return null==this.lookupPrefix(t)},compareDocumentPosition:function(t){if(this===t)return 0;var e=t,n=this,r=null,o=null;if(e instanceof dt&&(e=(r=e).ownerElement),n instanceof dt&&(n=(o=n).ownerElement,r&&e&&n===e))for(var i,s=0;i=n.attributes[s];s++){if(i===r)return k.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+k.DOCUMENT_POSITION_PRECEDING;if(i===o)return k.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+k.DOCUMENT_POSITION_FOLLOWING}if(!e||!n||n.ownerDocument!==e.ownerDocument)return k.DOCUMENT_POSITION_DISCONNECTED+k.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC+(G(n.ownerDocument)>G(e.ownerDocument)?k.DOCUMENT_POSITION_FOLLOWING:k.DOCUMENT_POSITION_PRECEDING);if(o&&e===n)return k.DOCUMENT_POSITION_CONTAINS+k.DOCUMENT_POSITION_PRECEDING;if(r&&e===n)return k.DOCUMENT_POSITION_CONTAINED_BY+k.DOCUMENT_POSITION_FOLLOWING;for(var a=[],c=e.parentNode;c;){if(!o&&c===n)return k.DOCUMENT_POSITION_CONTAINED_BY+k.DOCUMENT_POSITION_FOLLOWING;a.push(c),c=c.parentNode}a.reverse();for(var l=[],u=n.parentNode;u;){if(!r&&u===e)return k.DOCUMENT_POSITION_CONTAINS+k.DOCUMENT_POSITION_PRECEDING;l.push(u),u=u.parentNode}l.reverse();var h=B(a,l);for(var d in h.childNodes){var f=h.childNodes[d];if(f===n)return k.DOCUMENT_POSITION_FOLLOWING;if(f===e)return k.DOCUMENT_POSITION_PRECEDING;if(l.indexOf(f)>=0)return k.DOCUMENT_POSITION_FOLLOWING;if(a.indexOf(f)>=0)return k.DOCUMENT_POSITION_PRECEDING}return 0}},A(O,K),A(O,K.prototype),A(k,K),A(k,K.prototype),J.prototype={implementation:null,nodeName:"#document",nodeType:P,doctype:null,documentElement:null,_inc:1,insertBefore:function(t,e){if(t.nodeType===U){for(var n=t.firstChild;n;){var r=n.nextSibling;this.insertBefore(n,e),n=r}return t}return ut(this,t,e),t.ownerDocument=this,null===this.documentElement&&t.nodeType===C&&(this.documentElement=t),t},removeChild:function(t){var e=et(this,t);return e===this.documentElement&&(this.documentElement=null),e},replaceChild:function(t,e){ut(this,t,e,lt),t.ownerDocument=this,e&&this.removeChild(e),rt(t)&&(this.documentElement=t)},importNode:function(t,e){return Lt(this,t,e)},getElementById:function(t){var e=null;return Q(this.documentElement,(function(n){if(n.nodeType==C&&n.getAttribute("id")==t)return e=n,!0})),e},createElement:function(t){var e=new ht(d);return e.ownerDocument=this,"html"===this.type&&(t=t.toLowerCase()),i(this.contentType)&&(e.namespaceURI=h.HTML),e.nodeName=t,e.tagName=t,e.localName=t,e.childNodes=new V,(e.attributes=new q)._ownerElement=e,e},createDocumentFragment:function(){var t=new It(d);return t.ownerDocument=this,t.childNodes=new V,t},createTextNode:function(t){var e=new pt(d);return e.ownerDocument=this,e.childNodes=new V,e.appendData(t),e},createComment:function(t){var e=new mt(d);return e.ownerDocument=this,e.childNodes=new V,e.appendData(t),e},createCDATASection:function(t){var e=new gt(d);return e.ownerDocument=this,e.childNodes=new V,e.appendData(t),e},createProcessingInstruction:function(t,e){var n=new bt(d);return n.ownerDocument=this,n.childNodes=new V,n.nodeName=n.target=t,n.nodeValue=n.data=e,n},createAttribute:function(t){if(!g.QName_exact.test(t))throw new p(p.INVALID_CHARACTER_ERR,'invalid character in name "'+t+'"');return"html"===this.type&&(t=t.toLowerCase()),this._createAttribute(t)},_createAttribute:function(t){var e=new dt(d);return e.ownerDocument=this,e.childNodes=new V,e.name=t,e.nodeName=t,e.localName=t,e.specified=!0,e},createEntityReference:function(t){if(!g.Name.test(t))throw new p(p.INVALID_CHARACTER_ERR,'not a valid xml name "'+t+'"');if("html"===this.type)throw new p("document is an html document",m.NotSupportedError);var e=new Tt(d);return e.ownerDocument=this,e.childNodes=new V,e.nodeName=t,e},createElementNS:function(t,e){var n=b(t,e),r=new ht(d),o=r.attributes=new q;return r.childNodes=new V,r.ownerDocument=this,r.nodeName=e,r.tagName=e,r.namespaceURI=n[0],r.prefix=n[1],r.localName=n[2],o._ownerElement=r,r},createAttributeNS:function(t,e){var n=b(t,e),r=new dt(d);return r.ownerDocument=this,r.childNodes=new V,r.nodeName=e,r.name=e,r.specified=!0,r.namespaceURI=n[0],r.prefix=n[1],r.localName=n[2],r}},R(J,K),ht.prototype={nodeType:C,attributes:null,getQualifiedName:function(){return this.prefix?this.prefix+":"+this.localName:this.localName},_isInHTMLDocumentAndNamespace:function(){return"html"===this.ownerDocument.type&&this.namespaceURI===h.HTML},hasAttributes:function(){return!(!this.attributes||!this.attributes.length)},hasAttribute:function(t){return!!this.getAttributeNode(t)},getAttribute:function(t){var e=this.getAttributeNode(t);return e?e.value:null},getAttributeNode:function(t){return this._isInHTMLDocumentAndNamespace()&&(t=t.toLowerCase()),this.attributes.getNamedItem(t)},setAttribute:function(t,e){this._isInHTMLDocumentAndNamespace()&&(t=t.toLowerCase());var n=this.getAttributeNode(t);n?n.value=n.nodeValue=""+e:((n=this.ownerDocument._createAttribute(t)).value=n.nodeValue=""+e,this.setAttributeNode(n))},removeAttribute:function(t){var e=this.getAttributeNode(t);e&&this.removeAttributeNode(e)},setAttributeNode:function(t){return this.attributes.setNamedItem(t)},setAttributeNodeNS:function(t){return this.attributes.setNamedItemNS(t)},removeAttributeNode:function(t){return this.attributes.removeNamedItem(t.nodeName)},removeAttributeNS:function(t,e){var n=this.getAttributeNodeNS(t,e);n&&this.removeAttributeNode(n)},hasAttributeNS:function(t,e){return null!=this.getAttributeNodeNS(t,e)},getAttributeNS:function(t,e){var n=this.getAttributeNodeNS(t,e);return n?n.value:null},setAttributeNS:function(t,e,n){var r=b(t,e)[2],o=this.getAttributeNodeNS(t,r);o?o.value=o.nodeValue=""+n:((o=this.ownerDocument.createAttributeNS(t,e)).value=o.nodeValue=""+n,this.setAttributeNode(o))},getAttributeNodeNS:function(t,e){return this.attributes.getNamedItemNS(t,e)},getElementsByClassName:function(t){var e=T(t);return new H(this,(function(n){var r=[];return e.length>0&&Q(n,(function(o){if(o!==n&&o.nodeType===C){var i=o.getAttribute("class");if(i){var s=t===i;if(!s){var a=T(i);s=e.every((c=a,function(t){return c&&-1!==c.indexOf(t)}))}s&&r.push(o)}}var c})),r}))},getElementsByTagName:function(t){var e="html"===(this.nodeType===P?this:this.ownerDocument).type,n=t.toLowerCase();return new H(this,(function(r){var o=[];return Q(r,(function(i){i!==r&&i.nodeType===C&&(("*"===t||i.getQualifiedName()===(e&&i.namespaceURI===h.HTML?n:t))&&o.push(i))})),o}))},getElementsByTagNameNS:function(t,e){return new H(this,(function(n){var r=[];return Q(n,(function(o){o===n||o.nodeType!==C||"*"!==t&&o.namespaceURI!==t||"*"!==e&&o.localName!=e||r.push(o)})),r}))}},J.prototype.getElementsByClassName=ht.prototype.getElementsByClassName,J.prototype.getElementsByTagName=ht.prototype.getElementsByTagName,J.prototype.getElementsByTagNameNS=ht.prototype.getElementsByTagNameNS,R(ht,K),dt.prototype.nodeType=y,R(dt,K),ft.prototype={data:"",substringData:function(t,e){return this.data.substring(t,t+e)},appendData:function(t){t=this.data+t,this.nodeValue=this.data=t,this.length=t.length},insertData:function(t,e){this.replaceData(t,0,e)},deleteData:function(t,e){this.replaceData(t,e,"")},replaceData:function(t,e,n){n=this.data.substring(0,t)+n+this.data.substring(t+e),this.nodeValue=this.data=n,this.length=n.length}},R(ft,K),pt.prototype={nodeName:"#text",nodeType:L,splitText:function(t){var e=this.data,n=e.substring(t);e=e.substring(0,t),this.data=this.nodeValue=e,this.length=e.length;var r=this.ownerDocument.createTextNode(n);return this.parentNode&&this.parentNode.insertBefore(r,this.nextSibling),r}},R(pt,ft),mt.prototype={nodeName:"#comment",nodeType:D},R(mt,ft),gt.prototype={nodeName:"#cdata-section",nodeType:M},R(gt,pt),Et.prototype.nodeType=x,R(Et,K),Nt.prototype.nodeType=F,R(Nt,K),St.prototype.nodeType=v,R(St,K),Tt.prototype.nodeType=_,R(Tt,K),It.prototype.nodeName="#document-fragment",It.prototype.nodeType=U,R(It,K),bt.prototype.nodeType=w,R(bt,ft),At.prototype.serializeToString=function(t,e){return Rt.call(t,e)},K.prototype.toString=Rt;try{if(Object.defineProperty){function vt(t){switch(t.nodeType){case C:case U:var e=[];for(t=t.firstChild;t;)7!==t.nodeType&&8!==t.nodeType&&e.push(vt(t)),t=t.nextSibling;return e.join("");default:return t.nodeValue}}Object.defineProperty(H.prototype,"length",{get:function(){return j(this),this.$$length}}),Object.defineProperty(K.prototype,"textContent",{get:function(){return vt(this)},set:function(t){switch(this.nodeType){case C:case U:for(;this.firstChild;)this.removeChild(this.firstChild);(t||String(t))&&this.appendChild(this.ownerDocument.createTextNode(t));break;default:this.data=t,this.value=t,this.nodeValue=t}}}),_t=function(t,e,n){t["$$"+e]=n}}}catch(wt){}e._updateLiveList=j,e.Attr=dt,e.CDATASection=gt,e.CharacterData=ft,e.Comment=mt,e.Document=J,e.DocumentFragment=It,e.DocumentType=Et,e.DOMImplementation=W,e.Element=ht,e.Entity=St,e.EntityReference=Tt,e.LiveNodeList=H,e.NamedNodeMap=q,e.Node=K,e.NodeList=V,e.Notation=Nt,e.Text=pt,e.ProcessingInstruction=bt,e.XMLSerializer=At},802:(t,e,n)=>{"use strict";var r=n(870),o=n(731),i=n(767),s=r.isHTMLEscapableRawTextElement,a=r.isHTMLMimeType,c=r.isHTMLRawTextElement,l=r.hasOwn,u=r.NAMESPACE,h=i.ParseError,d=i.DOMException;function f(){}f.prototype={parse:function(t,e,n){var i=this.domBuilder;i.startDocument(),S(e,e=Object.create(null)),function(t,e,n,i,s){var c=a(i.mimeType);t.indexOf(o.UNICODE_REPLACEMENT_CHARACTER)>=0&&s.warning("Unicode replacement character detected, source encoding issues?");function u(t){if(t>65535){var e=55296+((t-=65536)>>10),n=56320+(1023&t);return String.fromCharCode(e,n)}return String.fromCharCode(t)}function f(t){var e=";"===t[t.length-1]?t:t+";";if(!c&&e!==t)return s.error("EntityRef: expecting ;"),t;var r=o.Reference.exec(e);if(!r||r[0].length!==e.length)return s.error("entity not matching Reference production: "+t),t;var i=e.slice(1,-1);return l(n,i)?n[i]:"#"===i.charAt(0)?u(parseInt(i.substring(1).replace("x","0x"))):(s.error("entity not found:"+t),t)}function S(e){if(e>_){var n=t.substring(_,e).replace(p,f);C&&y(_),i.characters(n,0,e-_),_=e}}var T=0,R=0,O=/\r\n?|\n|$/g,C=i.locator;function y(e,n){for(;e>=R&&(n=O.exec(t));)T=R,R=n.index+n[0].length,C.lineNumber++;C.columnNumber=e-T+1}var L=[{currentNSMap:e}],M=[],_=0;for(;;){try{var v=t.indexOf("<",_);if(v<0){if(!c&&M.length>0)return s.fatalError("unclosed xml tag(s): "+M.join(", "));if(!t.substring(_).match(/^\s*$/)){var w=i.doc,D=w.createTextNode(t.substring(_));if(w.documentElement)return s.error("Extra content at the end of the document");w.appendChild(D),i.currentElement=D}return}if(v>_){var P=t.substring(_,v);c||0!==M.length||(P=P.replace(new RegExp(o.S_OPT.source,"g"),""))&&s.error("Unexpected content outside root element: '"+P+"'"),S(v)}switch(t.charAt(v+1)){case"/":var x=t.indexOf(">",v+2),U=t.substring(v+2,x>0?x:void 0);if(!U)return s.fatalError("end tag name missing");var F=x>0&&o.reg("^",o.QName_group,o.S_OPT,"$").exec(U);if(!F)return s.fatalError('end tag name contains invalid characters: "'+U+'"');if(!i.currentElement&&!i.doc.documentElement)return;var k=M[M.length-1]||i.currentElement.tagName||i.doc.documentElement.tagName||"";if(k!==F[1]){var B=F[1].toLowerCase();if(!c||k.toLowerCase()!==B)return s.fatalError('Opening and ending tag mismatch: "'+k+'" != "'+U+'"')}var G=L.pop();M.pop();var V=G.localNSMap;if(i.endElement(G.uri,G.localName,k),V)for(var H in V)l(V,H)&&i.endPrefixMapping(H);x++;break;case"?":C&&y(v),x=b(t,v,i,s);break;case"!":C&&y(v),x=I(t,v,i,s,c);break;default:C&&y(v);var j=new A,q=L[L.length-1].currentNSMap,$=(x=g(t,v,j,q,f,s,c),j.length);if(j.closed||(c&&r.isHTMLVoidElement(j.tagName)?j.closed=!0:M.push(j.tagName)),C&&$){for(var X=m(C,{}),Y=0;Y<$;Y++){var W=j[Y];y(W.offset),W.locator=m(C,{})}i.locator=X,E(j,i,q)&&L.push(j),i.locator=C}else E(j,i,q)&&L.push(j);c&&!j.closed?x=N(t,x,j.tagName,f,i):x++}}catch(t){if(t instanceof h)throw t;if(t instanceof d)throw new h(t.name+": "+t.message,i.locator,t);s.error("element parse error: "+t),x=-1}x>_?_=x:S(Math.max(v,_)+1)}}(t,e,n,i,this.errorHandler),i.endDocument()}};var p=/&#?\w+;?/g;function m(t,e){return e.lineNumber=t.lineNumber,e.columnNumber=t.columnNumber,e}function g(t,e,n,r,o,i,s){function a(t,e,r){return l(n.attributeNames,t)?i.fatalError("Attribute "+t+" redefined"):!s&&e.indexOf("<")>=0?i.fatalError("Unescaped '<' not allowed in attributes values"):void n.addValue(t,e.replace(/[\t\n\r]/g," ").replace(p,o),r)}for(var c,u=++e,h=0;;){var d=t.charAt(u);switch(d){case"=":if(1===h)c=t.slice(e,u),h=3;else{if(2!==h)throw new Error("attribute equal must after attrName");h=3}break;case"'":case'"':if(3===h||1===h){if(1===h&&(i.warning('attribute value must after "="'),c=t.slice(e,u)),e=u+1,!((u=t.indexOf(d,e))>0))throw new Error("attribute value no end '"+d+"' match");a(c,f=t.slice(e,u),e-1),h=5}else{if(4!=h)throw new Error('attribute value must after "="');a(c,f=t.slice(e,u),e),i.warning('attribute "'+c+'" missed start quot('+d+")!!"),e=u+1,h=5}break;case"/":switch(h){case 0:n.setTagName(t.slice(e,u));case 5:case 6:case 7:h=7,n.closed=!0;case 4:case 1:break;case 2:n.closed=!0;break;default:throw new Error("attribute invalid close char('/')")}break;case"":return i.error("unexpected end of input"),0==h&&n.setTagName(t.slice(e,u)),u;case">":switch(h){case 0:n.setTagName(t.slice(e,u));case 5:case 6:case 7:break;case 4:case 1:"/"===(f=t.slice(e,u)).slice(-1)&&(n.closed=!0,f=f.slice(0,-1));case 2:2===h&&(f=c),4==h?(i.warning('attribute "'+f+'" missed quot(")!'),a(c,f,e)):(s||i.warning('attribute "'+f+'" missed value!! "'+f+'" instead!!'),a(f,f,e));break;case 3:if(!s)return i.fatalError("AttValue: ' or \" expected")}return u;case"\x80":d=" ";default:if(d<=" ")switch(h){case 0:n.setTagName(t.slice(e,u)),h=6;break;case 1:c=t.slice(e,u),h=2;break;case 4:var f=t.slice(e,u);i.warning('attribute "'+f+'" missed quot(")!!'),a(c,f,e);case 5:h=6}else switch(h){case 2:s||i.warning('attribute "'+c+'" missed value!! "'+c+'" instead2!!'),a(c,c,e),e=u,h=1;break;case 5:i.warning('attribute space is required"'+c+'"!!');case 6:h=1,e=u;break;case 3:h=4,e=u;break;case 7:throw new Error("elements closed character '/' and '>' must be connected to")}}u++}}function E(t,e,n){for(var r=t.tagName,o=null,i=t.length;i--;){var s=t[i],a=s.qName,c=s.value;if((p=a.indexOf(":"))>0)var h=s.prefix=a.slice(0,p),d=a.slice(p+1),f="xmlns"===h&&d;else d=a,h=null,f="xmlns"===a&&"";s.localName=d,!1!==f&&(null==o&&(o=Object.create(null),S(n,n=Object.create(null))),n[f]=o[f]=c,s.uri=u.XMLNS,e.startPrefixMapping(f,c))}for(i=t.length;i--;)(s=t[i]).prefix&&("xml"===s.prefix&&(s.uri=u.XML),"xmlns"!==s.prefix&&(s.uri=n[s.prefix]));var p;(p=r.indexOf(":"))>0?(h=t.prefix=r.slice(0,p),d=t.localName=r.slice(p+1)):(h=null,d=t.localName=r);var m=t.uri=n[h||""];if(e.startElement(m,d,r,t),!t.closed)return t.currentNSMap=n,t.localNSMap=o,!0;if(e.endElement(m,d,r),o)for(h in o)l(o,h)&&e.endPrefixMapping(h)}function N(t,e,n,r,o){var i=s(n);if(i||c(n)){var a=t.indexOf("</"+n+">",e),l=t.substring(e+1,a);return i&&(l=l.replace(p,r)),o.characters(l,0,l.length),a}return e+1}function S(t,e){for(var n in t)l(t,n)&&(e[n]=t[n])}function T(t,e){var n=e;function r(e){return e=e||0,t.charAt(n+e)}function i(t){n+=t=t||1}function s(){return t.substring(n)}return{char:r,getIndex:function(){return n},getMatch:function(t){var e=o.reg("^",t).exec(s());return e?(i(e[0].length),e[0]):null},getSource:function(){return t},skip:i,skipBlanks:function(){for(var e=0;n<t.length;){var o=r();if(" "!==o&&"\n"!==o&&"\t"!==o&&"\r"!==o)return e;e++,i()}return-1},substringFromIndex:s,substringStartsWith:function(e){return t.substring(n,n+e.length)===e},substringStartsWithCaseInsensitive:function(e){return t.substring(n,n+e.length).toUpperCase()===e.toUpperCase()}}}function I(t,e,n,r,i){var s=T(t,e);switch(i?s.char(2).toUpperCase():s.char(2)){case"-":var a=s.getMatch(o.Comment);return a?(n.comment(a,o.COMMENT_START.length,a.length-o.COMMENT_START.length-o.COMMENT_END.length),s.getIndex()):r.fatalError("comment is not well-formed at position "+s.getIndex());case"[":var c=s.getMatch(o.CDSect);return c?i||n.currentElement?(n.startCDATA(),n.characters(c,o.CDATA_START.length,c.length-o.CDATA_START.length-o.CDATA_END.length),n.endCDATA(),s.getIndex()):r.fatalError("CDATA outside of element"):r.fatalError("Invalid CDATA starting at position "+e);case"D":if(n.doc&&n.doc.documentElement)return r.fatalError("Doctype not allowed inside or after documentElement at position "+s.getIndex());if(i?!s.substringStartsWithCaseInsensitive(o.DOCTYPE_DECL_START):!s.substringStartsWith(o.DOCTYPE_DECL_START))return r.fatalError("Expected "+o.DOCTYPE_DECL_START+" at position "+s.getIndex());if(s.skip(o.DOCTYPE_DECL_START.length),s.skipBlanks()<1)return r.fatalError("Expected whitespace after "+o.DOCTYPE_DECL_START+" at position "+s.getIndex());var l={name:void 0,publicId:void 0,systemId:void 0,internalSubset:void 0};if(l.name=s.getMatch(o.Name),!l.name)return r.fatalError("doctype name missing or contains unexpected characters at position "+s.getIndex());if(i&&"html"!==l.name.toLowerCase()&&r.warning("Unexpected DOCTYPE in HTML document at position "+s.getIndex()),s.skipBlanks(),s.substringStartsWith(o.PUBLIC)||s.substringStartsWith(o.SYSTEM)){var u=o.ExternalID_match.exec(s.substringFromIndex());if(!u)return r.fatalError("doctype external id is not well-formed at position "+s.getIndex());void 0!==u.groups.SystemLiteralOnly?l.systemId=u.groups.SystemLiteralOnly:(l.systemId=u.groups.SystemLiteral,l.publicId=u.groups.PubidLiteral),s.skip(u[0].length)}else if(i&&s.substringStartsWithCaseInsensitive(o.SYSTEM)){if(s.skip(o.SYSTEM.length),s.skipBlanks()<1)return r.fatalError("Expected whitespace after "+o.SYSTEM+" at position "+s.getIndex());if(l.systemId=s.getMatch(o.ABOUT_LEGACY_COMPAT_SystemLiteral),!l.systemId)return r.fatalError("Expected "+o.ABOUT_LEGACY_COMPAT+" in single or double quotes after "+o.SYSTEM+" at position "+s.getIndex())}return i&&l.systemId&&!o.ABOUT_LEGACY_COMPAT_SystemLiteral.test(l.systemId)&&r.warning("Unexpected doctype.systemId in HTML document at position "+s.getIndex()),i||(s.skipBlanks(),l.internalSubset=function(t,e){function n(t,e){var n=o.PI.exec(t.substringFromIndex());return n?"xml"===n[1].toLowerCase()?e.fatalError("xml declaration is only allowed at the start of the document, but found at position "+t.getIndex()):(t.skip(n[0].length),n[0]):e.fatalError("processing instruction is not well-formed at position "+t.getIndex())}var r=t.getSource();if("["===t.char()){t.skip(1);for(var i=t.getIndex();t.getIndex()<r.length;){if(t.skipBlanks(),"]"===t.char()){var s=r.substring(i,t.getIndex());return t.skip(1),s}var a=null;if("<"===t.char()&&"!"===t.char(1))switch(t.char(2)){case"E":"L"===t.char(3)?a=t.getMatch(o.elementdecl):"N"===t.char(3)&&(a=t.getMatch(o.EntityDecl));break;case"A":a=t.getMatch(o.AttlistDecl);break;case"N":a=t.getMatch(o.NotationDecl);break;case"-":a=t.getMatch(o.Comment)}else if("<"===t.char()&&"?"===t.char(1))a=n(t,e);else{if("%"!==t.char())return e.fatalError("Error detected in Markup declaration");a=t.getMatch(o.PEReference)}if(!a)return e.fatalError("Error in internal subset at position "+t.getIndex())}return e.fatalError("doctype internal subset is not well-formed, missing ]")}}(s,r)),s.skipBlanks(),">"!==s.char()?r.fatalError("doctype not terminated with > at position "+s.getIndex()):(s.skip(1),n.startDTD(l.name,l.publicId,l.systemId,l.internalSubset),n.endDTD(),s.getIndex());default:return r.fatalError('Not well-formed XML starting with "<!" at position '+e)}}function b(t,e,n,r){var i=t.substring(e).match(o.PI);if(!i)return r.fatalError("Invalid processing instruction starting at position "+e);if("xml"===i[1].toLowerCase()){if(e>0)return r.fatalError("processing instruction at position "+e+" is an xml declaration which is only at the start of the document");if(!o.XMLDecl.test(t.substring(e)))return r.fatalError("xml declaration is not well-formed")}return n.processingInstruction(i[1],i[2]),e+i[0].length}function A(){this.attributeNames=Object.create(null)}A.prototype={setTagName:function(t){if(!o.QName_exact.test(t))throw new Error("invalid tagName:"+t);this.tagName=t},addValue:function(t,e,n){if(!o.QName_exact.test(t))throw new Error("invalid attribute:"+t);this.attributeNames[t]=this.length,this[this.length++]={qName:t,value:e,offset:n}},length:0,getLocalName:function(t){return this[t].localName},getLocator:function(t){return this[t].locator},getQName:function(t){return this[t].qName},getURI:function(t){return this[t].uri},getValue:function(t){return this[t].value}},e.XMLReader=f,e.parseUtils=T,e.parseDoctypeCommentOrCData=I},870:(t,e)=>{"use strict";function n(t,e){return void 0===e&&(e=Object),e&&"function"==typeof e.getOwnPropertyDescriptors&&(t=e.create(null,e.getOwnPropertyDescriptors(t))),e&&"function"==typeof e.freeze?e.freeze(t):t}function r(t,e){return Object.prototype.hasOwnProperty.call(t,e)}var o=n({allowfullscreen:!0,async:!0,autofocus:!0,autoplay:!0,checked:!0,controls:!0,default:!0,defer:!0,disabled:!0,formnovalidate:!0,hidden:!0,ismap:!0,itemscope:!0,loop:!0,multiple:!0,muted:!0,nomodule:!0,novalidate:!0,open:!0,playsinline:!0,readonly:!0,required:!0,reversed:!0,selected:!0});var i=n({area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});var s=n({script:!1,style:!1,textarea:!0,title:!0});function a(t){return t===c.HTML}var c=n({HTML:"text/html",XML_APPLICATION:"application/xml",XML_TEXT:"text/xml",XML_XHTML_APPLICATION:"application/xhtml+xml",XML_SVG_IMAGE:"image/svg+xml"}),l=Object.keys(c).map((function(t){return c[t]}));var u=n({HTML:"http://www.w3.org/1999/xhtml",SVG:"http://www.w3.org/2000/svg",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/"});e.assign=function(t,e){if(null===t||"object"!=typeof t)throw new TypeError("target is not an object");for(var n in e)r(e,n)&&(t[n]=e[n]);return t},e.find=function(t,e,n){if(void 0===n&&(n=Array.prototype),t&&"function"==typeof n.find)return n.find.call(t,e);for(var o=0;o<t.length;o++)if(r(t,o)){var i=t[o];if(e.call(void 0,i,o,t))return i}},e.freeze=n,e.HTML_BOOLEAN_ATTRIBUTES=o,e.HTML_RAW_TEXT_ELEMENTS=s,e.HTML_VOID_ELEMENTS=i,e.hasDefaultHTMLNamespace=function(t){return a(t)||t===c.XML_XHTML_APPLICATION},e.hasOwn=r,e.isHTMLBooleanAttribute=function(t){return r(o,t.toLowerCase())},e.isHTMLRawTextElement=function(t){var e=t.toLowerCase();return r(s,e)&&!s[e]},e.isHTMLEscapableRawTextElement=function(t){var e=t.toLowerCase();return r(s,e)&&s[e]},e.isHTMLMimeType=a,e.isHTMLVoidElement=function(t){return r(i,t.toLowerCase())},e.isValidMimeType=function(t){return l.indexOf(t)>-1},e.MIME_TYPE=c,e.NAMESPACE=u},999:(t,e,n)=>{(function(){"use strict";var e=this;function n(t){return"string"==typeof t}function r(t,e,n){return t.call.apply(t.bind,arguments)}function o(t,e,n){if(!t)throw Error();if(2<arguments.length){var r=Array.prototype.slice.call(arguments,2);return function(){var n=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(n,r),t.apply(e,n)}}return function(){return t.apply(e,arguments)}}function i(t,e,n){return(i=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?r:o).apply(null,arguments)}function s(t){var e=st;function n(){}n.prototype=e.prototype,t.G=e.prototype,t.prototype=new n,t.prototype.constructor=t,t.F=function(t,n,r){for(var o=Array(arguments.length-2),i=2;i<arguments.length;i++)o[i-2]=arguments[i];return e.prototype[n].apply(t,o)}}var a=String.prototype.trim?function(t){return t.trim()}:function(t){return t.replace(/^[\s\xa0]+|[\s\xa0]+$/g,"")};function c(t,e){return-1!=t.indexOf(e)}function l(t,e){return t<e?-1:t>e?1:0}var u,h=Array.prototype.indexOf?function(t,e,n){return Array.prototype.indexOf.call(t,e,n)}:function(t,e,r){if(r=null==r?0:0>r?Math.max(0,t.length+r):r,n(t))return n(e)&&1==e.length?t.indexOf(e,r):-1;for(;r<t.length;r++)if(r in t&&t[r]===e)return r;return-1},d=Array.prototype.forEach?function(t,e,n){Array.prototype.forEach.call(t,e,n)}:function(t,e,r){for(var o=t.length,i=n(t)?t.split(""):t,s=0;s<o;s++)s in i&&e.call(r,i[s],s,t)},f=Array.prototype.filter?function(t,e,n){return Array.prototype.filter.call(t,e,n)}:function(t,e,r){for(var o=t.length,i=[],s=0,a=n(t)?t.split(""):t,c=0;c<o;c++)if(c in a){var l=a[c];e.call(r,l,c,t)&&(i[s++]=l)}return i},p=Array.prototype.reduce?function(t,e,n,r){return r&&(e=i(e,r)),Array.prototype.reduce.call(t,e,n)}:function(t,e,n,r){var o=n;return d(t,(function(n,i){o=e.call(r,o,n,i,t)})),o},m=Array.prototype.some?function(t,e,n){return Array.prototype.some.call(t,e,n)}:function(t,e,r){for(var o=t.length,i=n(t)?t.split(""):t,s=0;s<o;s++)if(s in i&&e.call(r,i[s],s,t))return!0;return!1};t:{var g=e.navigator;if(g){var E=g.userAgent;if(E){u=E;break t}}u=""}var N,S,T=c(u,"Opera")||c(u,"OPR"),I=c(u,"Trident")||c(u,"MSIE"),b=c(u,"Edge"),A=c(u,"Gecko")&&!(c(u.toLowerCase(),"webkit")&&!c(u,"Edge"))&&!(c(u,"Trident")||c(u,"MSIE"))&&!c(u,"Edge"),R=c(u.toLowerCase(),"webkit")&&!c(u,"Edge");function O(){var t=e.document;return t?t.documentMode:void 0}t:{var C="",y=(S=u,A?/rv\:([^\);]+)(\)|;)/.exec(S):b?/Edge\/([\d\.]+)/.exec(S):I?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(S):R?/WebKit\/(\S+)/.exec(S):T?/(?:Version)[ \/]?(\S+)/.exec(S):void 0);if(y&&(C=y?y[1]:""),I){var L=O();if(null!=L&&L>parseFloat(C)){N=String(L);break t}}N=C}var M={};function _(t){if(!M[t]){for(var e=0,n=a(String(N)).split("."),r=a(String(t)).split("."),o=Math.max(n.length,r.length),i=0;0==e&&i<o;i++){var s=n[i]||"",c=r[i]||"",u=/(\d*)(\D*)/g,h=/(\d*)(\D*)/g;do{var d=u.exec(s)||["","",""],f=h.exec(c)||["","",""];if(0==d[0].length&&0==f[0].length)break;e=l(0==d[1].length?0:parseInt(d[1],10),0==f[1].length?0:parseInt(f[1],10))||l(0==d[2].length,0==f[2].length)||l(d[2],f[2])}while(0==e)}M[t]=0<=e}}var v=e.document,w=v&&I?O()||("CSS1Compat"==v.compatMode?parseInt(N,10):5):void 0,D=I&&!(9<=Number(w)),P=I&&!(8<=Number(w));function x(t,e,n,r){this.a=t,this.nodeName=n,this.nodeValue=r,this.nodeType=2,this.parentNode=this.ownerElement=e}function U(t,e){var n=P&&"href"==e.nodeName?t.getAttribute(e.nodeName,2):e.nodeValue;return new x(e,t,e.nodeName,n)}function F(t){var e=null;if(1==(n=t.nodeType)&&(e=null==(e=null==(e=t.textContent)||null==e?t.innerText:e)||null==e?"":e),"string"!=typeof e)if(D&&"title"==t.nodeName.toLowerCase()&&1==n)e=t.text;else if(9==n||1==n){t=9==n?t.documentElement:t.firstChild;var n=0,r=[];for(e="";t;){do{1!=t.nodeType&&(e+=t.nodeValue),D&&"title"==t.nodeName.toLowerCase()&&(e+=t.text),r[n++]=t}while(t=t.firstChild);for(;n&&!(t=r[--n].nextSibling););}}else e=t.nodeValue;return""+e}function k(t,e,n){if(null===e)return!0;try{if(!t.getAttribute)return!1}catch(t){return!1}return P&&"class"==e&&(e="className"),null==n?!!t.getAttribute(e):t.getAttribute(e,2)==n}function B(t,e,r,o,i){return(D?G:V).call(null,t,e,n(r)?r:null,n(o)?o:null,i||new z)}function G(t,e,n,r,o){if(t instanceof Pt||8==t.b||n&&null===t.b){var i=e.all;if(!i)return o;if("*"!=(t=$(t))&&!(i=e.getElementsByTagName(t)))return o;if(n){for(var s=[],a=0;e=i[a++];)k(e,n,r)&&s.push(e);i=s}for(a=0;e=i[a++];)"*"==t&&"!"==e.tagName||tt(o,e);return o}return q(t,e,n,r,o),o}function V(t,e,n,r,o){return e.getElementsByName&&r&&"name"==n&&!I?(e=e.getElementsByName(r),d(e,(function(e){t.a(e)&&tt(o,e)}))):e.getElementsByClassName&&r&&"class"==n?(e=e.getElementsByClassName(r),d(e,(function(e){e.className==r&&t.a(e)&&tt(o,e)}))):t instanceof Ot?q(t,e,n,r,o):e.getElementsByTagName&&(e=e.getElementsByTagName(t.f()),d(e,(function(t){k(t,n,r)&&tt(o,t)}))),o}function H(t,e,n,r,o){var i;if((t instanceof Pt||8==t.b||n&&null===t.b)&&(i=e.childNodes)){var s=$(t);return"*"==s||(i=f(i,(function(t){return t.tagName&&t.tagName.toLowerCase()==s})),i)?(n&&(i=f(i,(function(t){return k(t,n,r)}))),d(i,(function(t){"*"==s&&("!"==t.tagName||"*"==s&&1!=t.nodeType)||tt(o,t)})),o):o}return j(t,e,n,r,o)}function j(t,e,n,r,o){for(e=e.firstChild;e;e=e.nextSibling)k(e,n,r)&&t.a(e)&&tt(o,e);return o}function q(t,e,n,r,o){for(e=e.firstChild;e;e=e.nextSibling)k(e,n,r)&&t.a(e)&&tt(o,e),q(t,e,n,r,o)}function $(t){if(t instanceof Ot){if(8==t.b)return"!";if(null===t.b)return"*"}return t.f()}function X(t,e){if(!t||!e)return!1;if(t.contains&&1==e.nodeType)return t==e||t.contains(e);if(void 0!==t.compareDocumentPosition)return t==e||!!(16&t.compareDocumentPosition(e));for(;e&&t!=e;)e=e.parentNode;return e==t}function Y(t,n){if(t==n)return 0;if(t.compareDocumentPosition)return 2&t.compareDocumentPosition(n)?1:-1;if(I&&!(9<=Number(w))){if(9==t.nodeType)return-1;if(9==n.nodeType)return 1}if("sourceIndex"in t||t.parentNode&&"sourceIndex"in t.parentNode){var r=1==t.nodeType,o=1==n.nodeType;if(r&&o)return t.sourceIndex-n.sourceIndex;var i=t.parentNode,s=n.parentNode;return i==s?K(t,n):!r&&X(i,n)?-1*W(t,n):!o&&X(s,t)?W(n,t):(r?t.sourceIndex:i.sourceIndex)-(o?n.sourceIndex:s.sourceIndex)}return(r=(o=9==t.nodeType?t:t.ownerDocument||t.document).createRange()).selectNode(t),r.collapse(!0),(o=o.createRange()).selectNode(n),o.collapse(!0),r.compareBoundaryPoints(e.Range.START_TO_END,o)}function W(t,e){var n=t.parentNode;if(n==e)return-1;for(var r=e;r.parentNode!=n;)r=r.parentNode;return K(r,t)}function K(t,e){for(var n=e;n=n.previousSibling;)if(n==t)return-1;return 1}function z(){this.b=this.a=null,this.l=0}function Q(t){this.node=t,this.a=this.b=null}function J(t,e){if(!t.a)return e;if(!e.a)return t;for(var n=t.a,r=e.a,o=null,i=null,s=0;n&&r;){i=n.node;var a=r.node;i==a||i instanceof x&&a instanceof x&&i.a==a.a?(i=n,n=n.a,r=r.a):0<Y(n.node,r.node)?(i=r,r=r.a):(i=n,n=n.a),(i.b=o)?o.a=i:t.a=i,o=i,s++}for(i=n||r;i;)i.b=o,o=o.a=i,s++,i=i.a;return t.b=o,t.l=s,t}function Z(t,e){var n=new Q(e);n.a=t.a,t.b?t.a.b=n:t.a=t.b=n,t.a=n,t.l++}function tt(t,e){var n=new Q(e);n.b=t.b,t.a?t.b.a=n:t.a=t.b=n,t.b=n,t.l++}function et(t){return(t=t.a)?t.node:null}function nt(t){return(t=et(t))?F(t):""}function rt(t,e){return new ot(t,!!e)}function ot(t,e){this.f=t,this.b=(this.c=e)?t.b:t.a,this.a=null}function it(t){var e=t.b;if(null==e)return null;var n=t.a=e;return t.b=t.c?e.b:e.a,n.node}function st(t){this.i=t,this.b=this.g=!1,this.f=null}function at(t){return"\n  "+t.toString().split("\n").join("\n  ")}function ct(t,e){t.g=e}function lt(t,e){t.b=e}function ut(t,e){var n=t.a(e);return n instanceof z?+nt(n):+n}function ht(t,e){var n=t.a(e);return n instanceof z?nt(n):""+n}function dt(t,e){var n=t.a(e);return n instanceof z?!!n.l:!!n}function ft(t,e,n){st.call(this,t.i),this.c=t,this.h=e,this.o=n,this.g=e.g||n.g,this.b=e.b||n.b,this.c==Nt&&(n.b||n.g||4==n.i||0==n.i||!e.f?e.b||e.g||4==e.i||0==e.i||!n.f||(this.f={name:n.f.name,s:e}):this.f={name:e.f.name,s:n})}function pt(t,e,n,r,o){var i;if(e=e.a(r),n=n.a(r),e instanceof z&&n instanceof z){for(r=it(e=rt(e));r;r=it(e))for(i=it(o=rt(n));i;i=it(o))if(t(F(r),F(i)))return!0;return!1}if(e instanceof z||n instanceof z){e instanceof z?(o=e,r=n):(o=n,r=e);for(var s=typeof r,a=it(i=rt(o));a;a=it(i)){switch(s){case"number":a=+F(a);break;case"boolean":a=!!F(a);break;case"string":a=F(a);break;default:throw Error("Illegal primitive type for comparison.")}if(o==e&&t(a,r)||o==n&&t(r,a))return!0}return!1}return o?"boolean"==typeof e||"boolean"==typeof n?t(!!e,!!n):"number"==typeof e||"number"==typeof n?t(+e,+n):t(e,n):t(+e,+n)}function mt(t,e,n,r){this.a=t,this.w=e,this.i=n,this.m=r}!A&&!I||I&&9<=Number(w)||A&&_("1.9.1"),I&&_("9"),s(ft),ft.prototype.a=function(t){return this.c.m(this.h,this.o,t)},ft.prototype.toString=function(){return"Binary Expression: "+this.c+at(this.h)+at(this.o)},mt.prototype.toString=function(){return this.a};var gt={};function Et(t,e,n,r){if(gt.hasOwnProperty(t))throw Error("Binary operator already created: "+t);return t=new mt(t,e,n,r),gt[t.toString()]=t}Et("div",6,1,(function(t,e,n){return ut(t,n)/ut(e,n)})),Et("mod",6,1,(function(t,e,n){return ut(t,n)%ut(e,n)})),Et("*",6,1,(function(t,e,n){return ut(t,n)*ut(e,n)})),Et("+",5,1,(function(t,e,n){return ut(t,n)+ut(e,n)})),Et("-",5,1,(function(t,e,n){return ut(t,n)-ut(e,n)})),Et("<",4,2,(function(t,e,n){return pt((function(t,e){return t<e}),t,e,n)})),Et(">",4,2,(function(t,e,n){return pt((function(t,e){return t>e}),t,e,n)})),Et("<=",4,2,(function(t,e,n){return pt((function(t,e){return t<=e}),t,e,n)})),Et(">=",4,2,(function(t,e,n){return pt((function(t,e){return t>=e}),t,e,n)}));var Nt=Et("=",3,2,(function(t,e,n){return pt((function(t,e){return t==e}),t,e,n,!0)}));function St(t,e,n){this.a=t,this.b=e||1,this.f=n||1}function Tt(t,e){if(e.a.length&&4!=t.i)throw Error("Primary expression must evaluate to nodeset if filter has predicate(s).");st.call(this,t.i),this.c=t,this.h=e,this.g=t.g,this.b=t.b}function It(t,e){if(e.length<t.A)throw Error("Function "+t.j+" expects at least"+t.A+" arguments, "+e.length+" given");if(null!==t.v&&e.length>t.v)throw Error("Function "+t.j+" expects at most "+t.v+" arguments, "+e.length+" given");t.B&&d(e,(function(e,n){if(4!=e.i)throw Error("Argument "+n+" to function "+t.j+" is not of type Nodeset: "+e)})),st.call(this,t.i),this.h=t,this.c=e,ct(this,t.g||m(e,(function(t){return t.g}))),lt(this,t.D&&!e.length||t.C&&!!e.length||m(e,(function(t){return t.b})))}function bt(t,e,n,r,o,i,s,a,c){this.j=t,this.i=e,this.g=n,this.D=r,this.C=o,this.m=i,this.A=s,this.v=void 0!==a?a:s,this.B=!!c}Et("!=",3,2,(function(t,e,n){return pt((function(t,e){return t!=e}),t,e,n,!0)})),Et("and",2,2,(function(t,e,n){return dt(t,n)&&dt(e,n)})),Et("or",1,2,(function(t,e,n){return dt(t,n)||dt(e,n)})),s(Tt),Tt.prototype.a=function(t){return t=this.c.a(t),Vt(this.h,t)},Tt.prototype.toString=function(){return"Filter:"+at(this.c)+at(this.h)},s(It),It.prototype.a=function(t){return this.h.m.apply(null,function(t){return Array.prototype.concat.apply(Array.prototype,arguments)}(t,this.c))},It.prototype.toString=function(){var t="Function: "+this.h;if(this.c.length){var e=p(this.c,(function(t,e){return t+at(e)}),"Arguments:");t=t+at(e)}return t},bt.prototype.toString=function(){return this.j};var At={};function Rt(t,e,n,r,o,i,s,a){if(At.hasOwnProperty(t))throw Error("Function already created: "+t+".");At[t]=new bt(t,e,n,r,!1,o,i,s,a)}function Ot(t,e){switch(this.h=t,this.c=void 0!==e?e:null,this.b=null,t){case"comment":this.b=8;break;case"text":this.b=3;break;case"processing-instruction":this.b=7;break;case"node":break;default:throw Error("Unexpected argument")}}function Ct(t){return"comment"==t||"text"==t||"processing-instruction"==t||"node"==t}function yt(t){this.b=t,this.a=0}Rt("boolean",2,!1,!1,(function(t,e){return dt(e,t)}),1),Rt("ceiling",1,!1,!1,(function(t,e){return Math.ceil(ut(e,t))}),1),Rt("concat",3,!1,!1,(function(t,e){return p(function(t,e,n){return 2>=arguments.length?Array.prototype.slice.call(t,e):Array.prototype.slice.call(t,e,n)}(arguments,1),(function(e,n){return e+ht(n,t)}),"")}),2,null),Rt("contains",2,!1,!1,(function(t,e,n){return c(ht(e,t),ht(n,t))}),2),Rt("count",1,!1,!1,(function(t,e){return e.a(t).l}),1,1,!0),Rt("false",2,!1,!1,(function(){return!1}),0),Rt("floor",1,!1,!1,(function(t,e){return Math.floor(ut(e,t))}),1),Rt("id",4,!1,!1,(function(t,e){function r(t){if(D){var e=o.all[t];if(e){if(e.nodeType&&t==e.id)return e;if(e.length)return function(t,e){var r;t:{r=t.length;for(var o=n(t)?t.split(""):t,i=0;i<r;i++)if(i in o&&e.call(void 0,o[i],i,t)){r=i;break t}r=-1}return 0>r?null:n(t)?t.charAt(r):t[r]}(e,(function(e){return t==e.id}))}return null}return o.getElementById(t)}var o=9==(i=t.a).nodeType?i:i.ownerDocument,i=ht(e,t).split(/\s+/),s=[];d(i,(function(t){!(t=r(t))||0<=h(s,t)||s.push(t)})),s.sort(Y);var a=new z;return d(s,(function(t){tt(a,t)})),a}),1),Rt("lang",2,!1,!1,(function(){return!1}),1),Rt("last",1,!0,!1,(function(t){if(1!=arguments.length)throw Error("Function last expects ()");return t.f}),0),Rt("local-name",3,!1,!0,(function(t,e){var n=e?et(e.a(t)):t.a;return n?n.localName||n.nodeName.toLowerCase():""}),0,1,!0),Rt("name",3,!1,!0,(function(t,e){var n=e?et(e.a(t)):t.a;return n?n.nodeName.toLowerCase():""}),0,1,!0),Rt("namespace-uri",3,!0,!1,(function(){return""}),0,1,!0),Rt("normalize-space",3,!1,!0,(function(t,e){return(e?ht(e,t):F(t.a)).replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")}),0,1),Rt("not",2,!1,!1,(function(t,e){return!dt(e,t)}),1),Rt("number",1,!1,!0,(function(t,e){return e?ut(e,t):+F(t.a)}),0,1),Rt("position",1,!0,!1,(function(t){return t.b}),0),Rt("round",1,!1,!1,(function(t,e){return Math.round(ut(e,t))}),1),Rt("starts-with",2,!1,!1,(function(t,e,n){return e=ht(e,t),t=ht(n,t),0==e.lastIndexOf(t,0)}),2),Rt("string",3,!1,!0,(function(t,e){return e?ht(e,t):F(t.a)}),0,1),Rt("string-length",1,!1,!0,(function(t,e){return(e?ht(e,t):F(t.a)).length}),0,1),Rt("substring",3,!1,!1,(function(t,e,n,r){if(n=ut(n,t),isNaN(n)||1/0==n||-1/0==n)return"";if(r=r?ut(r,t):1/0,isNaN(r)||-1/0===r)return"";n=Math.round(n)-1;var o=Math.max(n,0);return t=ht(e,t),1/0==r?t.substring(o):t.substring(o,n+Math.round(r))}),2,3),Rt("substring-after",3,!1,!1,(function(t,e,n){return e=ht(e,t),t=ht(n,t),-1==(n=e.indexOf(t))?"":e.substring(n+t.length)}),2),Rt("substring-before",3,!1,!1,(function(t,e,n){return e=ht(e,t),t=ht(n,t),-1==(t=e.indexOf(t))?"":e.substring(0,t)}),2),Rt("sum",1,!1,!1,(function(t,e){for(var n=rt(e.a(t)),r=0,o=it(n);o;o=it(n))r+=+F(o);return r}),1,1,!0),Rt("translate",3,!1,!1,(function(t,e,n,r){e=ht(e,t),n=ht(n,t);var o=ht(r,t);for(t={},r=0;r<n.length;r++){var i=n.charAt(r);i in t||(t[i]=o.charAt(r))}for(n="",r=0;r<e.length;r++)n+=(i=e.charAt(r))in t?t[i]:i;return n}),3),Rt("true",2,!1,!1,(function(){return!0}),0),Ot.prototype.a=function(t){return null===this.b||this.b==t.nodeType},Ot.prototype.f=function(){return this.h},Ot.prototype.toString=function(){var t="Kind Test: "+this.h;return null===this.c||(t+=at(this.c)),t};var Lt=/\$?(?:(?![0-9-\.])(?:\*|[\w-\.]+):)?(?![0-9-\.])(?:\*|[\w-\.]+)|\/\/|\.\.|::|\d+(?:\.\d*)?|\.\d+|"[^"]*"|'[^']*'|[!<>]=|\s+|./g,Mt=/^\s/;function _t(t,e){return t.b[t.a+(e||0)]}function vt(t){return t.b[t.a++]}function wt(t){return t.b.length<=t.a}function Dt(t){st.call(this,3),this.c=t.substring(1,t.length-1)}function Pt(t,e){var n;this.j=t.toLowerCase(),n="*"==this.j?"*":"http://www.w3.org/1999/xhtml",this.c=e?e.toLowerCase():n}function xt(t,e){if(st.call(this,t.i),this.h=t,this.c=e,this.g=t.g,this.b=t.b,1==this.c.length){var n=this.c[0];n.u||n.c!=Xt||"*"!=(n=n.o).f()&&(this.f={name:n.f(),s:null})}}function Ut(){st.call(this,4)}function Ft(){st.call(this,4)}function kt(t){return"/"==t||"//"==t}function Bt(t){st.call(this,4),this.c=t,ct(this,m(this.c,(function(t){return t.g}))),lt(this,m(this.c,(function(t){return t.b})))}function Gt(t,e){this.a=t,this.b=!!e}function Vt(t,e,n){for(n=n||0;n<t.a.length;n++)for(var r,o=t.a[n],i=rt(e),s=e.l,a=0;r=it(i);a++){var c=t.b?s-a:a+1;if("number"==typeof(r=o.a(new St(r,c,s))))c=c==r;else if("string"==typeof r||"boolean"==typeof r)c=!!r;else{if(!(r instanceof z))throw Error("Predicate.evaluate returned an unexpected type.");c=0<r.l}if(!c){if(r=(c=i).f,!(u=c.a))throw Error("Next must be called at least once before remove.");var l=u.b,u=u.a;l?l.a=u:r.a=u,u?u.b=l:r.b=l,r.l--,c.a=null}}return e}function Ht(t,e,n,r){st.call(this,4),this.c=t,this.o=e,this.h=n||new Gt([]),this.u=!!r,e=0<(e=this.h).a.length?e.a[0].f:null,t.b&&e&&(t=e.name,t=D?t.toLowerCase():t,this.f={name:t,s:e.s});t:{for(t=this.h,e=0;e<t.a.length;e++)if((n=t.a[e]).g||1==n.i||0==n.i){t=!0;break t}t=!1}this.g=t}function jt(t,e,n,r){this.j=t,this.f=e,this.a=n,this.b=r}s(Dt),Dt.prototype.a=function(){return this.c},Dt.prototype.toString=function(){return"Literal: "+this.c},Pt.prototype.a=function(t){var e=t.nodeType;return(1==e||2==e)&&(e=void 0!==t.localName?t.localName:t.nodeName,("*"==this.j||this.j==e.toLowerCase())&&("*"==this.c||this.c==(t.namespaceURI?t.namespaceURI.toLowerCase():"http://www.w3.org/1999/xhtml")))},Pt.prototype.f=function(){return this.j},Pt.prototype.toString=function(){return"Name Test: "+("http://www.w3.org/1999/xhtml"==this.c?"":this.c+":")+this.j},s(xt),s(Ut),Ut.prototype.a=function(t){var e=new z;return 9==(t=t.a).nodeType?tt(e,t):tt(e,t.ownerDocument),e},Ut.prototype.toString=function(){return"Root Helper Expression"},s(Ft),Ft.prototype.a=function(t){var e=new z;return tt(e,t.a),e},Ft.prototype.toString=function(){return"Context Helper Expression"},xt.prototype.a=function(t){var e=this.h.a(t);if(!(e instanceof z))throw Error("Filter expression must evaluate to nodeset.");for(var n=0,r=(t=this.c).length;n<r&&e.l;n++){var o,i=t[n],s=rt(e,i.c.a);if(i.g||i.c!=Kt)if(i.g||i.c!=Qt)for(o=it(s),e=i.a(new St(o));null!=(o=it(s));)e=J(e,o=i.a(new St(o)));else o=it(s),e=i.a(new St(o));else{for(o=it(s);(e=it(s))&&(!o.contains||o.contains(e))&&8&e.compareDocumentPosition(o);o=e);e=i.a(new St(o))}}return e},xt.prototype.toString=function(){var t;if(t="Path Expression:"+at(this.h),this.c.length){var e=p(this.c,(function(t,e){return t+at(e)}),"Steps:");t+=at(e)}return t},s(Bt),Bt.prototype.a=function(t){var e=new z;return d(this.c,(function(n){if(!((n=n.a(t))instanceof z))throw Error("Path expression must evaluate to NodeSet.");e=J(e,n)})),e},Bt.prototype.toString=function(){return p(this.c,(function(t,e){return t+at(e)}),"Union Expression:")},Gt.prototype.toString=function(){return p(this.a,(function(t,e){return t+at(e)}),"Predicates:")},s(Ht),Ht.prototype.a=function(t){var e=t.a,n=null,r=null,o=null,i=0;if((n=this.f)&&(r=n.name,o=n.s?ht(n.s,t):null,i=1),this.u)if(this.g||this.c!=Yt)if(e=it(t=rt(new Ht(Wt,new Ot("node")).a(t))))for(n=this.m(e,r,o,i);null!=(e=it(t));)n=J(n,this.m(e,r,o,i));else n=new z;else n=B(this.o,e,r,o),n=Vt(this.h,n,i);else n=this.m(t.a,r,o,i);return n},Ht.prototype.m=function(t,e,n,r){return t=this.c.f(this.o,t,e,n),Vt(this.h,t,r)},Ht.prototype.toString=function(){var t;if(t="Step:"+at("Operator: "+(this.u?"//":"/")),this.c.j&&(t+=at("Axis: "+this.c)),t+=at(this.o),this.h.a.length){var e=p(this.h.a,(function(t,e){return t+at(e)}),"Predicates:");t+=at(e)}return t},jt.prototype.toString=function(){return this.j};var qt={};function $t(t,e,n,r){if(qt.hasOwnProperty(t))throw Error("Axis already created: "+t);return e=new jt(t,e,n,!!r),qt[t]=e}$t("ancestor",(function(t,e){for(var n=new z,r=e;r=r.parentNode;)t.a(r)&&Z(n,r);return n}),!0),$t("ancestor-or-self",(function(t,e){var n=new z,r=e;do{t.a(r)&&Z(n,r)}while(r=r.parentNode);return n}),!0);var Xt=$t("attribute",(function(t,e){var n=new z;if("style"==(i=t.f())&&D&&e.style)return tt(n,new x(e.style,e,"style",e.style.cssText)),n;var r=e.attributes;if(r)if(t instanceof Ot&&null===t.b||"*"==i)for(var o,i=0;o=r[i];i++)D?o.nodeValue&&tt(n,U(e,o)):tt(n,o);else(o=r.getNamedItem(i))&&(D?o.nodeValue&&tt(n,U(e,o)):tt(n,o));return n}),!1),Yt=$t("child",(function(t,e,r,o,i){return(D?H:j).call(null,t,e,n(r)?r:null,n(o)?o:null,i||new z)}),!1,!0);$t("descendant",B,!1,!0);var Wt=$t("descendant-or-self",(function(t,e,n,r){var o=new z;return k(e,n,r)&&t.a(e)&&tt(o,e),B(t,e,n,r,o)}),!1,!0),Kt=$t("following",(function(t,e,n,r){var o=new z;do{for(var i=e;i=i.nextSibling;)k(i,n,r)&&t.a(i)&&tt(o,i),o=B(t,i,n,r,o)}while(e=e.parentNode);return o}),!1,!0);$t("following-sibling",(function(t,e){for(var n=new z,r=e;r=r.nextSibling;)t.a(r)&&tt(n,r);return n}),!1),$t("namespace",(function(){return new z}),!1);var zt=$t("parent",(function(t,e){var n=new z;if(9==e.nodeType)return n;if(2==e.nodeType)return tt(n,e.ownerElement),n;var r=e.parentNode;return t.a(r)&&tt(n,r),n}),!1),Qt=$t("preceding",(function(t,e,n,r){var o=new z,i=[];do{i.unshift(e)}while(e=e.parentNode);for(var s=1,a=i.length;s<a;s++){var c=[];for(e=i[s];e=e.previousSibling;)c.unshift(e);for(var l=0,u=c.length;l<u;l++)k(e=c[l],n,r)&&t.a(e)&&tt(o,e),o=B(t,e,n,r,o)}return o}),!0,!0);$t("preceding-sibling",(function(t,e){for(var n=new z,r=e;r=r.previousSibling;)t.a(r)&&Z(n,r);return n}),!0);var Jt=$t("self",(function(t,e){var n=new z;return t.a(e)&&tt(n,e),n}),!1);function Zt(t){st.call(this,1),this.c=t,this.g=t.g,this.b=t.b}function te(t){st.call(this,1),this.c=t}function ee(t,e){this.a=t,this.b=e}function ne(t){for(var e,n=[];;){re(t,"Missing right hand side of binary expression."),e=ue(t);var r=vt(t.a);if(!r)break;var o=(r=gt[r]||null)&&r.w;if(!o){t.a.a--;break}for(;n.length&&o<=n[n.length-1].w;)e=new ft(n.pop(),n.pop(),e);n.push(e,r)}for(;n.length;)e=new ft(n.pop(),n.pop(),e);return e}function re(t,e){if(wt(t.a))throw Error(e)}function oe(t,e){var n=vt(t.a);if(n!=e)throw Error("Bad token, expected: "+e+" got: "+n)}function ie(t){if(")"!=(t=vt(t.a)))throw Error("Bad token: "+t)}function se(t){if(2>(t=vt(t.a)).length)throw Error("Unclosed literal string");return new Dt(t)}function ae(t){var e,n,r=[];if(kt(_t(t.a))){if(e=vt(t.a),n=_t(t.a),"/"==e&&(wt(t.a)||"."!=n&&".."!=n&&"@"!=n&&"*"!=n&&!/(?![0-9])[\w]/.test(n)))return new Ut;n=new Ut,re(t,"Missing next location step."),e=ce(t,e),r.push(e)}else{t:{switch(n=(e=_t(t.a)).charAt(0)){case"$":throw Error("Variable reference not allowed in HTML XPath");case"(":vt(t.a),e=ne(t),re(t,'unclosed "("'),oe(t,")");break;case'"':case"'":e=se(t);break;default:if(isNaN(+e)){if(Ct(e)||!/(?![0-9])[\w]/.test(n)||"("!=_t(t.a,1)){e=null;break t}for(e=vt(t.a),e=At[e]||null,vt(t.a),n=[];")"!=_t(t.a)&&(re(t,"Missing function argument list."),n.push(ne(t)),","==_t(t.a));)vt(t.a);re(t,"Unclosed function argument list."),ie(t),e=new It(e,n)}else e=new te(+vt(t.a))}"["==_t(t.a)&&(e=new Tt(e,n=new Gt(le(t))))}if(e){if(!kt(_t(t.a)))return e;n=e}else e=ce(t,"/"),n=new Ft,r.push(e)}for(;kt(_t(t.a));)e=vt(t.a),re(t,"Missing next location step."),e=ce(t,e),r.push(e);return new xt(n,r)}function ce(t,e){var n,r,o,i;if("/"!=e&&"//"!=e)throw Error('Step op should be "/" or "//"');if("."==_t(t.a))return r=new Ht(Jt,new Ot("node")),vt(t.a),r;if(".."==_t(t.a))return r=new Ht(zt,new Ot("node")),vt(t.a),r;if("@"==_t(t.a))i=Xt,vt(t.a),re(t,"Missing attribute name");else if("::"==_t(t.a,1)){if(!/(?![0-9])[\w]/.test(_t(t.a).charAt(0)))throw Error("Bad token: "+vt(t.a));if(n=vt(t.a),!(i=qt[n]||null))throw Error("No axis with name: "+n);vt(t.a),re(t,"Missing node name")}else i=Yt;if(n=_t(t.a),!/(?![0-9])[\w\*]/.test(n.charAt(0)))throw Error("Bad token: "+vt(t.a));if("("==_t(t.a,1)){if(!Ct(n))throw Error("Invalid node type: "+n);if(!Ct(n=vt(t.a)))throw Error("Invalid type name: "+n);oe(t,"("),re(t,"Bad nodetype");var s=null;'"'!=(o=_t(t.a).charAt(0))&&"'"!=o||(s=se(t)),re(t,"Bad nodetype"),ie(t),n=new Ot(n,s)}else if(-1==(o=(n=vt(t.a)).indexOf(":")))n=new Pt(n);else{var a;if("*"==(s=n.substring(0,o)))a="*";else if(!(a=t.b(s)))throw Error("Namespace prefix not declared: "+s);n=new Pt(n=n.substr(o+1),a)}return o=new Gt(le(t),i.a),r||new Ht(i,n,o,"//"==e)}function le(t){for(var e=[];"["==_t(t.a);){vt(t.a),re(t,"Missing predicate expression.");var n=ne(t);e.push(n),re(t,"Unclosed predicate expression."),oe(t,"]")}return e}function ue(t){if("-"==_t(t.a))return vt(t.a),new Zt(ue(t));var e=ae(t);if("|"!=_t(t.a))t=e;else{for(e=[e];"|"==vt(t.a);)re(t,"Missing next union location path."),e.push(ae(t));t.a.a--,t=new Bt(e)}return t}function he(t){switch(t.nodeType){case 1:return function(t,e){var n=Array.prototype.slice.call(arguments,1);return function(){var e=n.slice();return e.push.apply(e,arguments),t.apply(this,e)}}(fe,t);case 9:return he(t.documentElement);case 11:case 10:case 6:case 12:return de;default:return t.parentNode?he(t.parentNode):de}}function de(){return null}function fe(t,e){if(t.prefix==e)return t.namespaceURI||"http://www.w3.org/1999/xhtml";var n=t.getAttributeNode("xmlns:"+e);return n&&n.specified?n.value||null:t.parentNode&&9!=t.parentNode.nodeType?fe(t.parentNode,e):null}function pe(t,e){if(!t.length)throw Error("Empty XPath expression.");var n=function(t){t=t.match(Lt);for(var e=0;e<t.length;e++)Mt.test(t[e])&&t.splice(e,1);return new yt(t)}(t);if(wt(n))throw Error("Invalid XPath expression.");e?"function"==function(t){var e=typeof t;if("object"==e){if(!t)return"null";if(t instanceof Array)return"array";if(t instanceof Object)return e;var n=Object.prototype.toString.call(t);if("[object Window]"==n)return"object";if("[object Array]"==n||"number"==typeof t.length&&void 0!==t.splice&&void 0!==t.propertyIsEnumerable&&!t.propertyIsEnumerable("splice"))return"array";if("[object Function]"==n||void 0!==t.call&&void 0!==t.propertyIsEnumerable&&!t.propertyIsEnumerable("call"))return"function"}else if("function"==e&&void 0===t.call)return"object";return e}(e)||(e=i(e.lookupNamespaceURI,e)):e=function(){return null};var r=ne(new ee(n,e));if(!wt(n))throw Error("Bad token: "+vt(n));this.evaluate=function(t,e){return new me(r.a(new St(t)),e)}}function me(t,e){if(0==e)if(t instanceof z)e=4;else if("string"==typeof t)e=2;else if("number"==typeof t)e=1;else{if("boolean"!=typeof t)throw Error("Unexpected evaluation result.");e=3}if(2!=e&&1!=e&&3!=e&&!(t instanceof z))throw Error("value could not be converted to the specified type");var n;switch(this.resultType=e,e){case 2:this.stringValue=t instanceof z?nt(t):""+t;break;case 1:this.numberValue=t instanceof z?+nt(t):+t;break;case 3:this.booleanValue=t instanceof z?0<t.l:!!t;break;case 4:case 5:case 6:case 7:var r=rt(t);n=[];for(var o=it(r);o;o=it(r))n.push(o instanceof x?o.a:o);this.snapshotLength=t.l,this.invalidIteratorState=!1;break;case 8:case 9:r=et(t),this.singleNodeValue=r instanceof x?r.a:r;break;default:throw Error("Unknown XPathResult type.")}var i=0;this.iterateNext=function(){if(4!=e&&5!=e)throw Error("iterateNext called with wrong result type");return i>=n.length?null:n[i++]},this.snapshotItem=function(t){if(6!=e&&7!=e)throw Error("snapshotItem called with wrong result type");return t>=n.length||0>t?null:n[t]}}function ge(t){this.lookupNamespaceURI=he(t)}function Ee(t,n){var r=t||e,o=r.Document&&r.Document.prototype||r.document;o.evaluate&&!n||(r.XPathResult=me,o.evaluate=function(t,e,n,r){return new pe(t,n).evaluate(e,r)},o.createExpression=function(t,e){return new pe(t,e)},o.createNSResolver=function(t){return new ge(t)})}s(Zt),Zt.prototype.a=function(t){return-ut(this.c,t)},Zt.prototype.toString=function(){return"Unary Expression: -"+at(this.c)},s(te),te.prototype.a=function(){return this.c},te.prototype.toString=function(){return"Number: "+this.c},me.ANY_TYPE=0,me.NUMBER_TYPE=1,me.STRING_TYPE=2,me.BOOLEAN_TYPE=3,me.UNORDERED_NODE_ITERATOR_TYPE=4,me.ORDERED_NODE_ITERATOR_TYPE=5,me.UNORDERED_NODE_SNAPSHOT_TYPE=6,me.ORDERED_NODE_SNAPSHOT_TYPE=7,me.ANY_UNORDERED_NODE_TYPE=8,me.FIRST_ORDERED_NODE_TYPE=9;var Ne,Se=["wgxpath","install"],Te=e;Se[0]in Te||!Te.execScript||Te.execScript("var "+Se[0]);for(;Se.length&&(Ne=Se.shift());)Se.length||void 0===Ee?Te=Te[Ne]?Te[Ne]:Te[Ne]={}:Te[Ne]=Ee;t.exports.install=Ee,t.exports.XPathResultType={ANY_TYPE:0,NUMBER_TYPE:1,STRING_TYPE:2,BOOLEAN_TYPE:3,UNORDERED_NODE_ITERATOR_TYPE:4,ORDERED_NODE_ITERATOR_TYPE:5,UNORDERED_NODE_SNAPSHOT_TYPE:6,ORDERED_NODE_SNAPSHOT_TYPE:7,ANY_UNORDERED_NODE_TYPE:8,FIRST_ORDERED_NODE_TYPE:9}}).call(n.g)}},__webpack_module_cache__={},leafPrototypes,getProto;function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var n=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t](n,n.exports,__webpack_require__),n.exports}getProto=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,__webpack_require__.t=function(t,e){if(1&e&&(t=this(t)),8&e)return t;if("object"==typeof t&&t){if(4&e&&t.__esModule)return t;if(16&e&&"function"==typeof t.then)return t}var n=Object.create(null);__webpack_require__.r(n);var r={};leafPrototypes=leafPrototypes||[null,getProto({}),getProto([]),getProto(getProto)];for(var o=2&e&&t;"object"==typeof o&&!~leafPrototypes.indexOf(o);o=getProto(o))Object.getOwnPropertyNames(o).forEach((e=>r[e]=()=>t[e]));return r.default=()=>t,__webpack_require__.d(n,r),n},__webpack_require__.d=(t,e)=>{for(var n in e)__webpack_require__.o(e,n)&&!__webpack_require__.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),__webpack_require__.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),__webpack_require__.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var __webpack_exports__={};(()=>{"use strict";var t=function(t,e,n,r){return new(n||(n=Promise))((function(o,i){function s(t){try{c(r.next(t))}catch(t){i(t)}}function a(t){try{c(r.throw(t))}catch(t){i(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(s,a)}c((r=r.apply(t,e||[])).next())}))};t(void 0,void 0,void 0,(function*(){if("undefined"==typeof self){self=global.self=global,global.DedicatedWorkerGlobalScope=global.constructor;const{parentPort:t,workerData:e}=yield import("node:worker_threads");global.addEventListener=(e,n)=>{t.on(e,n)},global.postMessage=e=>{t.postMessage({data:e})},global.maps=e.maps,global.require||(yield import("./require.mjs")),global.getMap=t=>Promise.resolve(JSON.stringify(global.require(t)))}else global=self.global=self,global.getMap=t=>fetch(t).then((t=>t.json())).catch((t=>console.log(t)));global.SREfeature={custom:t=>global.getMap(`${global.maps}/${t}.json`)},yield(global.isLab?import("./sre-lab.js"):Promise.resolve().then(__webpack_require__.bind(__webpack_require__,581))).then((t=>global.SRE=t)),self.addEventListener("message",(function(t){t.data.debug&&console.log("Client  >>>  Worker:",t.data);const{cmd:o,data:i}=t.data;Object.hasOwn(e,o)?(n("Log",`running ${o}`),e[o](i).then((t=>r(o,{result:t}))).catch((t=>r(o,{error:t.message})))):r(o,{error:`Invalid worker command: ${o}`})}),!1);const e={import:t=>Array.isArray(t.imports)?Promise.all(t.imports.map((t=>__webpack_require__(238)(t)))):__webpack_require__(238)(t.imports),setup:t=>t?(SRE.setupEngine(t),SRE.engineReady()):Promise.resolve(),speech:t=>o(SRE.workerSpeech,t.mml,t.options),nextRules:t=>o(SRE.workerNextRules,t.mml,t.options),nextStyle:t=>o(SRE.workerNextStyle,t.mml,t.options,t.nodeId),localePreferences(e){return t(this,void 0,void 0,(function*(){const t=yield SRE.workerLocalePreferences(e.options);return t?JSON.stringify(t):t}))},relevantPreferences(e){return t(this,void 0,void 0,(function*(){var t;return null!==(t=yield SRE.workerRelevantPreferences(e.mml,e.id))&&void 0!==t?t:""}))}};function n(t,e){try{self.postMessage({cmd:t,data:e})}catch(t){console.log("Posting error in worker for ",{message:(n=t).message,stack:n.stack,fileName:n.fileName,lineNumber:n.lineNumber})}var n}function r(t,e){n("Log",`finished ${t}`),n("Finished",Object.assign(Object.assign({},e),{cmd:t,success:!e.error}))}function o(e,n,r,...o){return t(this,void 0,void 0,(function*(){var t;if(!n)return"";const i=null!==(t=yield e.call(null,n,r,...o))&&void 0!==t?t:{};return JSON.stringify(i)}))}yield SRE.engineReady(),n("Ready",{})}))})()})();