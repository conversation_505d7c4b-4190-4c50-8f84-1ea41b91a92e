{"nemeth/messages/alphabets.min": {"kind": "alphabets", "locale": "ne<PERSON>h", "messages": {"latinSmall": ["⠰⠁", "⠰⠃", "⠰⠉", "⠰⠙", "⠰⠑", "⠰⠋", "⠰⠛", "⠰⠓", "⠰⠊", "⠰⠚", "⠰⠅", "⠰⠇", "⠰⠍", "⠰⠝", "⠰⠕", "⠰⠏", "⠰⠟", "⠰⠗", "⠰⠎", "⠰⠞", "⠰⠥", "⠰⠧", "⠰⠺", "⠰⠭", "⠰⠽", "⠰⠵"], "latinCap": ["⠰⠠⠁", "⠰⠠⠃", "⠰⠠⠉", "⠰⠠⠙", "⠰⠠⠑", "⠰⠠⠋", "⠰⠠⠛", "⠰⠠⠓", "⠰⠠⠊", "⠰⠠⠚", "⠰⠠⠅", "⠰⠠⠇", "⠰⠠⠍", "⠰⠠⠝", "⠰⠠⠕", "⠰⠠⠏", "⠰⠠⠟", "⠰⠠⠗", "⠰⠠⠎", "⠰⠠⠞", "⠰⠠⠥", "⠰⠠⠧", "⠰⠠⠺", "⠰⠠⠭", "⠰⠠⠽", "⠰⠠⠵"], "greekSmall": ["⠨⠫", "⠨⠁", "⠨⠃", "⠨⠛", "⠨⠙", "⠨⠑", "⠨⠵", "⠨⠱", "⠨⠹", "⠨⠊", "⠨⠅", "⠨⠇", "⠨⠍", "⠨⠝", "⠨⠭", "⠨⠕", "⠨⠏", "⠨⠗", "⠨⠈⠎", "⠨⠎", "⠨⠞", "⠨⠥", "⠨⠈⠋", "⠨⠯", "⠨⠽", "⠨⠺", "⠈⠙", "⠨⠑", "⠨⠈⠹", "⠨⠅", "⠨⠋", "⠨⠗", "⠨⠏"], "greekCap": ["⠨⠠⠁", "⠨⠠⠃", "⠨⠠⠛", "⠨⠠⠙", "⠨⠠⠑", "⠨⠠⠵", "⠨⠠⠱", "⠨⠠⠹", "⠨⠠⠊", "⠨⠠⠅", "⠨⠠⠇", "⠨⠠⠍", "⠨⠠⠝", "⠨⠠⠭", "⠨⠠⠕", "⠨⠠⠏", "⠨⠠⠗", "⠨⠠⠹", "⠨⠠⠎", "⠨⠠⠞", "⠨⠠⠥", "⠨⠠⠋", "⠨⠠⠯", "⠨⠠⠽", "⠨⠠⠺"], "capPrefix": {"default": ""}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": "⠼"}, "languagePrefix": {"greek": "⠨", "english": "⠰", "german": "⠸", "hebrew": "⠠⠠", "number": "⠼"}}}, "nemeth/messages/messages.min": {"kind": "messages", "locale": "ne<PERSON>h", "messages": {"MS": {"FRACTION_REPEAT": "⠠", "FRACTION_START": "⠹", "FRAC_V": "⠹", "FRAC_B": "<PERSON><PERSON>", "FRAC_S": "<PERSON><PERSON>", "END": "⠠", "FRACTION_OVER": "⠌", "TWICE": "Twice", "NEST_FRAC": "Nest", "ENDFRAC": "⠼", "FRACTION_END": "⠼", "SUPER": "⠘", "SUB": "⠰", "SUP": "⠘", "SUPERSCRIPT": "⠘", "SUBSCRIPT": "⠰", "BASELINE": "⠐", "BASE": "⠐", "NESTED": "⠨", "NEST_ROOT": "Nest", "STARTROOT": "⠜", "ENDROOT": "⠻", "ROOTINDEX": "⠣", "ROOT": "⠨", "INDEX": "⠣", "UNDER": "⠩", "UNDERSCRIPT": "⠩", "OVER": "⠣", "OVERSCRIPT": "⠣"}, "MSroots": {}, "font": {"bold": "⠸", "bold-fraktur": ["⠸⠸", "german<PERSON><PERSON><PERSON>"], "bold-italic": "⠸⠨", "bold-script": "⠸⠈", "caligraphic": "⠈", "caligraphic-bold": "⠈⠸", "double-struck": "⠈", "double-struck-italic": "⠸⠨", "fraktur": ["⠸", "german<PERSON><PERSON><PERSON>"], "fullwidth": "", "italic": "⠨", "monospace": "", "normal": "", "oldstyle": "", "oldstyle-bold": "⠸", "script": "⠈", "sans-serif": "⠠⠨", "sans-serif-italic": "⠠⠨⠨", "sans-serif-bold": "⠠⠨⠸", "sans-serif-bold-italic": "⠠⠨⠸⠨", "unknown": ""}, "embellish": {"super": ["⠘", "german<PERSON><PERSON><PERSON>"], "sub": ["⠰", "german<PERSON><PERSON><PERSON>"], "circled": ["⠫⠉⠸⠫", "embellish<PERSON><PERSON><PERSON>"], "parenthesized": ["⠷", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "period": ["⠸⠲", "postfix<PERSON><PERSON><PERSON>"], "negative-circled": ["⠫⠸⠉⠸⠫", "embellish<PERSON><PERSON><PERSON>"], "double-circled": ["⠫⠉⠸⠫⠫⠉⠸⠫", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "circled-sans-serif": ["⠫⠉⠸⠫⠠⠨", "embellish<PERSON><PERSON><PERSON>"], "negative-circled-sans-serif": ["⠫⠸⠉⠸⠫⠠⠨", "embellish<PERSON><PERSON><PERSON>"], "comma": ["⠠", "postfix<PERSON><PERSON><PERSON>"], "squared": ["⠫⠲⠸⠫", "embellish<PERSON><PERSON><PERSON>"], "negative-squared": ["⠫⠸⠲⠸⠫", "embellish<PERSON><PERSON><PERSON>"]}, "role": {"addition": "addition", "multiplication": "multiplication", "subtraction": "subtraction", "division": "division", "equality": "equality", "inequality": "inequality", "element": "element", "arrow": "arrow", "determinant": "determinant", "rowvector": "row vector", "binomial": "binomial", "squarematrix": "square matrix", "multiline": "multiple lines", "matrix": "matrix", "vector": "vector", "cases": "case statement", "table": "table", "unknown": "unknown"}, "enclose": {"longdiv": "long division", "actuarial": "actuarial symbol", "radical": "square root", "box": "⠗", "roundedbox": "rounded box", "circle": "⠉", "left": "left vertical-line", "right": "right vertical-line", "top": "overbar", "bottom": "underbar", "updiagonalstrike": "crossout", "downdiagonalstrike": "crossout", "verticalstrike": "vertical strikeout", "horizontalstrike": "crossout", "madruwb": "Arabic factorial symbol", "updiagonalarrow": "diagonal arrow", "phasorangle": "⠪", "unknown": "long division"}, "navigate": {"COLLAPSIBLE": "collapsible", "EXPANDABLE": "expandable", "LEVEL": "Level"}, "regexp": {"TEXT": "a-zA-Z", "NUMBER": "((\\d{1,3})(?=(,| ))((,| )\\d{3})*(\\.\\d+)?)|^\\d*\\.\\d+|^\\d+", "DECIMAL_MARK": ".", "DIGIT_GROUP": ",", "JOINER_SUBSUPER": "", "JOINER_FRAC": "", "SPACE": "⠀", "HYPER": "3"}, "unitTimes": ""}}, "nemeth/messages/numbers.min": {"kind": "numbers", "locale": "ne<PERSON>h", "messages": {"ones": ["⠴", "⠂", "⠆", "⠒", "⠲", "⠢", "⠖", "⠶", "⠦", "⠔"], "vulgarSep": ""}}, "nemeth/si/prefixes.min": [{}], "nemeth/functions/algebra.min": [{"locale": "ne<PERSON>h"}], "nemeth/functions/elementary.min": [{"locale": "ne<PERSON>h"}], "nemeth/functions/hyperbolic.min": [{"locale": "ne<PERSON>h"}], "nemeth/functions/trigonometry.min": [{"locale": "ne<PERSON>h"}], "nemeth/symbols/currency_symbols.min": [{"locale": "ne<PERSON>h"}, {"key": "0024", "category": "Sc", "mappings": {"default": {"default": "⠈⠎"}}}, {"key": "1F4B2", "category": "Sc", "mappings": {"default": {"default": "⠈⠎"}}}, {"key": "FF04", "category": "Sc", "mappings": {"default": {"default": "⠈⠎"}}}, {"key": "FE69", "category": "Sc", "mappings": {"default": {"default": "⠈⠎"}}}, {"key": "00A3", "category": "Sc", "mappings": {"default": {"default": "⠈⠇"}}}, {"key": "FFE1", "category": "Sc", "mappings": {"default": {"default": "⠈⠇"}}}, {"key": "00A5", "category": "Sc", "mappings": {"default": {"default": "⠈⠽"}}}, {"key": "FFE5", "category": "Sc", "mappings": {"default": {"default": "⠈⠽"}}}, {"key": "20AC", "category": "Sc", "mappings": {"default": {"default": "⠈⠑"}}}, {"key": "20A3", "category": "Sc", "mappings": {"default": {"default": "⠈⠋"}}}, {"key": "20A6", "category": "Sc", "mappings": {"default": {"default": "⠈⠝"}}}, {"key": "FFE0", "category": "Sc", "mappings": {"default": {"default": "⠈⠉"}}}, {"key": "00A2", "category": "Sc", "mappings": {"default": {"default": "⠈⠉"}}}, {"category": "Sc", "mappings": {"default": {"default": "⠈⠫"}}, "key": "00A4"}, {"category": "Sc", "mappings": {"default": {"default": ""}}, "key": "FFE6"}], "nemeth/symbols/digits_rest.min": [{"locale": "ne<PERSON>h"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠲⠼"}}, "key": "00BC"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠆⠼"}}, "key": "00BD"}, {"category": "No", "mappings": {"default": {"default": "⠹⠒⠌⠲⠼"}}, "key": "00BE"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠶⠼"}}, "key": "2150"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠔⠼"}}, "key": "2151"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠂⠴⠼"}}, "key": "2152"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠒⠼"}}, "key": "2153"}, {"category": "No", "mappings": {"default": {"default": "⠹⠆⠌⠒⠼"}}, "key": "2154"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠢⠼"}}, "key": "2155"}, {"category": "No", "mappings": {"default": {"default": "⠹⠆⠌⠢⠼"}}, "key": "2156"}, {"category": "No", "mappings": {"default": {"default": "⠹⠒⠌⠢⠼"}}, "key": "2157"}, {"category": "No", "mappings": {"default": {"default": "⠹⠲⠌⠢⠼"}}, "key": "2158"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠖⠼"}}, "key": "2159"}, {"category": "No", "mappings": {"default": {"default": "⠹⠢⠌⠖⠼"}}, "key": "215A"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠦⠼"}}, "key": "215B"}, {"category": "No", "mappings": {"default": {"default": "⠹⠒⠌⠦⠼"}}, "key": "215C"}, {"category": "No", "mappings": {"default": {"default": "⠹⠢⠌⠦⠼"}}, "key": "215D"}, {"category": "No", "mappings": {"default": {"default": "⠹⠶⠌⠦⠼"}}, "key": "215E"}, {"category": "No", "mappings": {"default": {"default": "⠹⠂⠌⠼"}}, "key": "215F"}, {"category": "No", "mappings": {"default": {"default": "⠹⠴⠌⠒⠼"}}, "key": "2189"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠂⠴⠻⠻"}}, "key": "3248"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠆⠴⠻⠻"}}, "key": "3249"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠒⠴⠻⠻"}}, "key": "324A"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠲⠴⠻⠻"}}, "key": "324B"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠢⠴⠻⠻"}}, "key": "324C"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠖⠴⠻⠻"}}, "key": "324D"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠶⠴⠻⠻"}}, "key": "324E"}, {"category": "No", "mappings": {"default": {"default": "⠫⠸⠲⠸⠫⠫⠉⠸⠫⠼⠦⠴⠻⠻"}}, "key": "324F"}], "nemeth/symbols/greek-rest.min": [{"locale": "ne<PERSON>h"}], "nemeth/symbols/greek-scripts.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "key": "1D26", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D27", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D28", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D29", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D2A", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D5E", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D60", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D66", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D67", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D68", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D69", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D6A", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/greek-symbols.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "03D0"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "03D7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "03F6"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "1D7CA"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "1D7CB"}], "nemeth/symbols/hebrew_letters.min": [{"locale": "ne<PERSON>h"}, {"category": "Lo", "mappings": {"default": {"default": "⠠⠠⠁"}}, "key": "2135"}, {"category": "Lo", "mappings": {"default": {"default": "⠠⠠⠃"}}, "key": "2136"}, {"category": "Lo", "mappings": {"default": {"default": "⠠⠠⠉"}}, "key": "2137"}, {"category": "Lo", "mappings": {"default": {"default": "⠠⠠⠙"}}, "key": "2138"}], "nemeth/symbols/latin-lower-double-accent.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "key": "01D6", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01D8", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01DA", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01DC", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01DF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01E1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01ED", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01FB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "022B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "022D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0231", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E09", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E15", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E17", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E1D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E2F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E39", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E4D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E4F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E51", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E53", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E5D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E65", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E67", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E69", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E79", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E7B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EA5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EA7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EA9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EAB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EAD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EAF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EB1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EB3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EB5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EB7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EBF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EC1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EC3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EC5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EC7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ED1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ED3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ED5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ED7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ED9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EDB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EDD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EDF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EE1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EE3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EE9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EEB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EED", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EEF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EF1", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/latin-lower-phonetic.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "key": "00F8", "mappings": {"default": {"default": "⠈⠕"}}}, {"category": "Ll", "key": "0111", "mappings": {"default": {"default": "⠈⠫"}}}, {"category": "Ll", "key": "0127", "mappings": {"default": {"default": "⠈⠓"}}}, {"category": "Ll", "key": "0142", "mappings": {"default": {"default": "⠈⠇"}}}, {"category": "Ll", "key": "0167", "mappings": {"default": {"default": "⠈⠞"}}}, {"category": "Ll", "key": "0180", "mappings": {"default": {"default": "⠈⠃"}}}, {"category": "Ll", "key": "019B", "mappings": {"default": {"default": "⠈⠨⠇"}}}, {"category": "Ll", "key": "01B6", "mappings": {"default": {"default": "⠈⠵"}}}, {"category": "Ll", "key": "01BE", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01E5", "mappings": {"default": {"default": "⠈⠛"}}}, {"category": "Ll", "key": "01FF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "023C", "mappings": {"default": {"default": "⠈⠉"}}}, {"category": "Ll", "key": "0247", "mappings": {"default": {"default": "⠈⠑"}}}, {"category": "Ll", "key": "0249", "mappings": {"default": {"default": "⠈⠚"}}}, {"category": "Ll", "key": "024D", "mappings": {"default": {"default": "⠈⠗"}}}, {"category": "Ll", "key": "024F", "mappings": {"default": {"default": "⠈⠽"}}}, {"category": "Ll", "key": "025F", "mappings": {"default": {"default": "⠈⠚"}}}, {"category": "Ll", "key": "0268", "mappings": {"default": {"default": "⠈⠊"}}}, {"category": "Ll", "key": "0284", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A2", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D13", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D7C", "mappings": {"default": {"default": "⠈⠨⠊"}}}, {"category": "Ll", "key": "1D7D", "mappings": {"default": {"default": "⠈⠏"}}}, {"category": "Ll", "key": "1D7F", "mappings": {"default": {"default": "⠈⠨⠥"}}}, {"category": "Ll", "key": "1E9C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E9D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "018D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E9B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E9F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0138", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "017F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0183", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0185", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0188", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "018C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0192", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0195", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0199", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "019A", "mappings": {"default": {"default": "⠈⠇"}}}, {"category": "Ll", "key": "019E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01A1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01A3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01A5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01A8", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01AA", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01AB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01AD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01B0", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01B4", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01B9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01BA", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01BD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01BF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01C6", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01C9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01CC", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01E3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01EF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01F3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01DD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01FD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "021D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0221", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0223", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0225", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0234", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0235", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0236", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0238", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0239", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "023F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0240", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0242", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "024B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0250", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0251", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0252", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0253", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0254", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0255", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0256", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0257", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0258", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0259", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "025A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "025B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "025C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "025D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "025E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0260", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0261", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0263", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0264", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0265", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0266", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0267", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0269", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "026B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "026C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "026D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "026E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "026F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0270", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0271", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0272", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0273", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0275", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0277", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0278", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0279", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "027A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "027B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "027C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "027D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "027E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "027F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0282", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0283", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0285", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0286", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0287", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0288", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0289", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "028A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "028B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "028C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "028D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "028E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0290", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0291", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0292", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0293", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0295", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0296", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0297", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0298", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "029A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "029E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A0", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A4", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A6", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A8", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02A9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02AA", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02AB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02AC", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02AD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02AE", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "02AF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D02", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D08", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D09", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D11", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D12", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D14", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D16", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D17", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D1D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D1E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D1F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D24", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D25", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D6B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D6C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D6D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D6E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D6F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D70", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D71", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D72", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D73", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D74", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D75", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D76", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D77", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D79", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D7A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D80", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D81", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D82", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D83", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D84", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D85", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D86", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D87", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D88", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D89", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D8A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D8B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D8C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D8D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D8E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D8F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D90", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D91", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D92", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D93", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D94", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D95", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D96", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D97", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D98", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D99", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1D9A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0149", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "014B", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/latin-lower-single-accent.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "key": "00E0", "mappings": {"default": {"default": "⠈⠁"}}}, {"category": "Ll", "key": "00E1", "mappings": {"default": {"default": "⠈⠁"}}}, {"category": "Ll", "key": "00E2", "mappings": {"default": {"default": "⠈⠁"}}}, {"category": "Ll", "key": "00E3", "mappings": {"default": {"default": "⠈⠁"}}}, {"category": "Ll", "key": "00E4", "mappings": {"default": {"default": "⠈⠁"}}}, {"category": "Ll", "key": "00E5", "mappings": {"default": {"default": "⠈⠁"}}}, {"category": "Ll", "key": "00E7", "mappings": {"default": {"default": "⠈⠉"}}}, {"category": "Ll", "key": "00E8", "mappings": {"default": {"default": "⠈⠑"}}}, {"category": "Ll", "key": "00E9", "mappings": {"default": {"default": "⠈⠑"}}}, {"category": "Ll", "key": "00EA", "mappings": {"default": {"default": "⠈⠑"}}}, {"category": "Ll", "key": "00EB", "mappings": {"default": {"default": "⠈⠑"}}}, {"category": "Ll", "key": "00EC", "mappings": {"default": {"default": "⠈⠊"}}}, {"category": "Ll", "key": "00ED", "mappings": {"default": {"default": "⠈⠊"}}}, {"category": "Ll", "key": "00EE", "mappings": {"default": {"default": "⠈⠊"}}}, {"category": "Ll", "key": "00EF", "mappings": {"default": {"default": "⠈⠊"}}}, {"category": "Ll", "key": "00F1", "mappings": {"default": {"default": "⠈⠝"}}}, {"category": "Ll", "key": "00F2", "mappings": {"default": {"default": "⠈⠕"}}}, {"category": "Ll", "key": "00F3", "mappings": {"default": {"default": "⠈⠕"}}}, {"category": "Ll", "key": "00F4", "mappings": {"default": {"default": "⠈⠕"}}}, {"category": "Ll", "key": "00F5", "mappings": {"default": {"default": "⠈⠕"}}}, {"category": "Ll", "key": "00F6", "mappings": {"default": {"default": "⠈⠕"}}}, {"category": "Ll", "key": "00F9", "mappings": {"default": {"default": "⠈⠥"}}}, {"category": "Ll", "key": "00FA", "mappings": {"default": {"default": "⠈⠥"}}}, {"category": "Ll", "key": "00FB", "mappings": {"default": {"default": "⠈⠥"}}}, {"category": "Ll", "key": "00FC", "mappings": {"default": {"default": "⠈⠥"}}}, {"category": "Ll", "key": "00FD", "mappings": {"default": {"default": "⠈⠽"}}}, {"category": "Ll", "key": "00FF", "mappings": {"default": {"default": "⠈⠽"}}}, {"category": "Ll", "key": "0101", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0103", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0105", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0107", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0109", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "010B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "010D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "010F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0113", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0115", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0117", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0119", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "011B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "011D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "011F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0121", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0123", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0125", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0129", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "012B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "012D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "012F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0131", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0135", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0137", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "013A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "013C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "013E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0140", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0144", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0146", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0148", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "014D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "014F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0151", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0155", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0157", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0159", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "015B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "015D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "015F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0161", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0163", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0165", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0169", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "016B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "016D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "016F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0171", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0173", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0175", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0177", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "017A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "017C", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "017E", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01CE", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01D0", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01D2", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01D4", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01E7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01E9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01EB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01F0", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01F5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "01F9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0201", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0203", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0205", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0207", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0209", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "020B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "020D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "020F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0211", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0213", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0215", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0217", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0219", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "021B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "021F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0227", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0229", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "022F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0233", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "0237", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E01", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E03", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E05", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E07", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E0B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E0D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E0F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E11", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E13", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E19", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E1B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E1F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E21", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E23", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E25", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E27", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E29", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E2B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E2D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E31", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E33", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E35", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E37", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E3B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E3D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E3F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E41", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E43", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E45", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E47", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E49", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E4B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E55", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E57", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E59", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E5B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E5F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E61", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E63", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E6B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E6D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E6F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E71", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E73", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E75", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E77", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E7D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E7F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E81", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E83", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E85", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E87", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E89", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E8B", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E8D", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E8F", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E91", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E93", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E95", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E96", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E97", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E98", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E99", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1E9A", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EA1", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EA3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EB9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EBB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EBD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EC9", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ECB", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ECD", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1ECF", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EE5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EE7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EF3", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EF5", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EF7", "mappings": {"default": {"default": ""}}}, {"category": "Ll", "key": "1EF9", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/latin-rest.min": [{"locale": "ne<PERSON>h"}, {"category": "Mn", "key": "210E", "mappings": {"default": {"default": "⠈⠓"}}}, {"category": "Mn", "key": "0363", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "0364", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "0365", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "0366", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "0367", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "0368", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "0369", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "036A", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "036B", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "036C", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "036D", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "036E", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "036F", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D62", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D63", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D64", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "1D65", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DCA", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD3", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD4", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD5", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD6", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD7", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD8", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DD9", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DDA", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DDB", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DDC", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DDD", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DDE", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DDF", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE0", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE1", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE2", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE3", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE4", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE5", "mappings": {"default": {"default": ""}}}, {"category": "Mn", "key": "1DE6", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2071", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "207F", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2090", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2091", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2092", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2093", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2094", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2095", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2096", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2097", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2098", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2099", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "209A", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "209B", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "209C", "mappings": {"default": {"default": ""}}}, {"category": "Lm", "key": "2C7C", "mappings": {"default": {"default": ""}}}, {"category": "So", "key": "1F12A", "mappings": {"default": {"default": ""}}}, {"category": "So", "key": "1F12B", "mappings": {"default": {"default": ""}}}, {"category": "So", "key": "1F12C", "mappings": {"default": {"default": ""}}}, {"category": "So", "key": "1F18A", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/latin-upper-double-accent.min": [{"locale": "ne<PERSON>h"}, {"category": "<PERSON>", "key": "01D5", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01D7", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01D9", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01DB", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01DE", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01E0", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01EC", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01FA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "022A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "022C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0230", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E08", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E14", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E16", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E1C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E2E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E38", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E4C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E4E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E50", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E52", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E5C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E64", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E66", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E68", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E78", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E7A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EA4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EA6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EA8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EAA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EAC", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EAE", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EB0", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EB2", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EB4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EB6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EBE", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EC0", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EC2", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EC4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EC6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ED0", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ED2", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ED4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ED6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ED8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EDA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EDC", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EDE", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EE0", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EE2", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EE8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EEA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EEC", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EEE", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EF0", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/latin-upper-single-accent.min": [{"locale": "ne<PERSON>h"}, {"category": "<PERSON>", "key": "00C0", "mappings": {"default": {"default": "⠠⠈⠁"}}}, {"category": "<PERSON>", "key": "00C1", "mappings": {"default": {"default": "⠠⠈⠁"}}}, {"category": "<PERSON>", "key": "00C2", "mappings": {"default": {"default": "⠠⠈⠁"}}}, {"category": "<PERSON>", "key": "00C3", "mappings": {"default": {"default": "⠠⠈⠁"}}}, {"category": "<PERSON>", "key": "00C4", "mappings": {"default": {"default": "⠠⠈⠁"}}}, {"category": "<PERSON>", "key": "00C5", "mappings": {"default": {"default": "⠠⠈⠁"}}}, {"category": "<PERSON>", "key": "00C7", "mappings": {"default": {"default": "⠠⠈⠉"}}}, {"category": "<PERSON>", "key": "00C8", "mappings": {"default": {"default": "⠠⠈⠑"}}}, {"category": "<PERSON>", "key": "00C9", "mappings": {"default": {"default": "⠠⠈⠑"}}}, {"category": "<PERSON>", "key": "00CA", "mappings": {"default": {"default": "⠠⠈⠑"}}}, {"category": "<PERSON>", "key": "00CB", "mappings": {"default": {"default": "⠠⠈⠑"}}}, {"category": "<PERSON>", "key": "00CC", "mappings": {"default": {"default": "⠠⠈⠊"}}}, {"category": "<PERSON>", "key": "00CD", "mappings": {"default": {"default": "⠠⠈⠊"}}}, {"category": "<PERSON>", "key": "00CE", "mappings": {"default": {"default": "⠠⠈⠊"}}}, {"category": "<PERSON>", "key": "00CF", "mappings": {"default": {"default": "⠠⠈⠊"}}}, {"category": "<PERSON>", "key": "00D1", "mappings": {"default": {"default": "⠠⠈⠝"}}}, {"category": "<PERSON>", "key": "00D2", "mappings": {"default": {"default": "⠠⠈⠕"}}}, {"category": "<PERSON>", "key": "00D3", "mappings": {"default": {"default": "⠠⠈⠕"}}}, {"category": "<PERSON>", "key": "00D4", "mappings": {"default": {"default": "⠠⠈⠕"}}}, {"category": "<PERSON>", "key": "00D5", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "00D6", "mappings": {"default": {"default": "⠠⠈⠕"}}}, {"category": "<PERSON>", "key": "00D9", "mappings": {"default": {"default": "⠠⠈⠥"}}}, {"category": "<PERSON>", "key": "00DA", "mappings": {"default": {"default": "⠠⠈⠥"}}}, {"category": "<PERSON>", "key": "00DB", "mappings": {"default": {"default": "⠠⠈⠥"}}}, {"category": "<PERSON>", "key": "00DC", "mappings": {"default": {"default": "⠠⠈⠥"}}}, {"category": "<PERSON>", "key": "00DD", "mappings": {"default": {"default": "⠠⠈⠽"}}}, {"category": "<PERSON>", "key": "0100", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0102", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0104", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0106", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0108", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "010A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "010C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "010E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0112", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0114", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0116", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0118", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "011A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "011C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "011E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0120", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0122", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0124", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0128", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "012A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "012C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "012E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0130", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0134", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0136", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0139", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "013B", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "013D", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "013F", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0143", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0145", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0147", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "014C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "014E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0150", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0154", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0156", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0158", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "015A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "015C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "015E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0160", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0162", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0164", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0168", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "016A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "016C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "016E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0170", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0172", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0174", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0176", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0178", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0179", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "017B", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "017D", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01CD", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01CF", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01D1", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01D3", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01E6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01E8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01EA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01F4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "01F8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0200", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0202", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0204", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0206", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0208", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "020A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "020C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "020E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0210", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0212", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0214", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0216", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0218", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "021A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "021E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0226", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0228", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "022E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "0232", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E00", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E02", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E04", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E06", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E0A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E0C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E0E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E10", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E12", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E18", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E1A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E1E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E20", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E22", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E24", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E26", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E28", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E2A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E2C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E30", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E32", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E34", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E36", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E3A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E3C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E3E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E40", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E42", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E44", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E46", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E48", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E4A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E54", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E56", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E58", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E5A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E5E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E60", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E62", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E6A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E6C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E6E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E70", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E72", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E74", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E76", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E7C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E7E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E80", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E82", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E84", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E86", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E88", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E8A", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E8C", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E8E", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E90", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E92", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1E94", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EA0", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EA2", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EB8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EBA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EBC", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EC8", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ECA", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ECC", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1ECE", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EE4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EE6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EF2", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EF4", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EF6", "mappings": {"default": {"default": ""}}}, {"category": "<PERSON>", "key": "1EF8", "mappings": {"default": {"default": ""}}}], "nemeth/symbols/math_angles.min": [{"locale": "ne<PERSON>h"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22BE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "237C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "299B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "299C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "299D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "299E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "299F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29A9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29AA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29AB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29AC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29AD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29AE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29AF"}], "nemeth/symbols/math_arrows.min": [{"locale": "ne<PERSON>h"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠒⠒"}}, "key": "2190"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠣⠒⠒⠕"}}, "key": "2191"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠕", "uncontracted": "⠫⠒⠒⠕"}}, "key": "2192"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠩⠒⠒⠕"}}, "key": "2193"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠒⠒⠕"}}, "key": "2194"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠩⠪⠒⠒⠕"}}, "key": "2195"}, {"category": "So", "mappings": {"default": {"default": "⠫⠘⠪⠒⠒"}}, "key": "2196"}, {"category": "So", "mappings": {"default": {"default": "⠫⠘⠒⠒⠕"}}, "key": "2197"}, {"category": "So", "mappings": {"default": {"default": "⠫⠰⠒⠒⠕"}}, "key": "2198"}, {"category": "So", "mappings": {"default": {"default": "⠫⠰⠪⠒⠒"}}, "key": "2199"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠪⠒⠒⠻"}}, "key": "219A"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠒⠒⠕⠻"}}, "key": "219B"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠔⠒⠢"}}, "key": "219C"}, {"category": "So", "mappings": {"default": {"default": "⠫⠔⠒⠢⠕"}}, "key": "219D"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠪⠒⠒"}}, "key": "219E"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠒⠒⠕⠕"}}, "key": "219F"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠒⠒⠕⠕"}}, "key": "21A0"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠒⠒⠕⠕"}}, "key": "21A1"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒⠠⠽"}}, "key": "21A2"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠠⠯⠒⠒⠕"}}, "key": "21A3"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒⠳"}}, "key": "21A4"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠳⠒⠒⠕"}}, "key": "21A5"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠳⠒⠒⠕"}}, "key": "21A6"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠳⠒⠒⠕"}}, "key": "21A7"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠳⠒⠕"}}, "key": "21A8"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒⠠⠕"}}, "key": "21A9"}, {"category": "So", "mappings": {"default": {"default": "⠫⠠⠪⠒⠒⠕"}}, "key": "21AA"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒⠨⠡"}}, "key": "21AB"}, {"category": "So", "mappings": {"default": {"default": "⠫⠨⠡⠒⠒⠕"}}, "key": "21AC"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠔⠒⠢⠕"}}, "key": "21AD"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠪⠒⠒⠕"}}, "key": "21AE"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠔⠢⠔"}}, "key": "21AF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21B0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21B1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21B2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21B3"}, {"category": "So", "mappings": {"default": {"default": "⠫⠠⠳⠒⠒⠕"}}, "key": "21B4"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠠⠳⠒⠒⠕"}}, "key": "21B5"}, {"category": "So", "mappings": {"default": {"default": "⠫⠢⠔⠕"}}, "key": "21B6"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠢⠔"}}, "key": "21B7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21B8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21B9"}, {"category": "So", "mappings": {"default": {"default": "⠫⠢⠔⠕"}}, "key": "21BA"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠢⠔"}}, "key": "21BB"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠕⠫⠪⠒⠒"}}, "key": "21C4"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠒⠒⠕⠐⠫⠩⠒⠒⠕"}}, "key": "21C5"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒⠫⠒⠒⠕"}}, "key": "21C6"}, {"category": "So", "mappings": {"default": {"default": "⠫⠚⠒⠒⠫⠚⠒⠒"}}, "key": "21C7"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠒⠒⠕⠐⠫⠣⠒⠒⠕"}}, "key": "21C8"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠕⠫⠒⠒⠕"}}, "key": "21C9"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠒⠒⠕⠐⠫⠩⠒⠒⠕"}}, "key": "21CA"}, {"category": "So", "mappings": {"default": {"default": "⠳⠈⠫⠪⠪⠒⠒"}}, "key": "21CD"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠪⠪⠒⠒⠕⠕"}}, "key": "21CE"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠒⠒⠕⠕"}}, "key": "21CF"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠶⠶"}}, "key": "21D0"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠶⠶⠕"}}, "key": "21D1"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠶⠶⠕"}}, "key": "21D2"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠶⠶⠕"}}, "key": "21D3"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠪⠶⠶⠕"}}, "key": "21D4"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠪⠪⠶⠶⠕"}}, "key": "21D5"}, {"category": "So", "mappings": {"default": {"default": "⠫⠘⠪⠶⠶"}}, "key": "21D6"}, {"category": "So", "mappings": {"default": {"default": "⠫⠘⠶⠶⠕"}}, "key": "21D7"}, {"category": "So", "mappings": {"default": {"default": "⠫⠰⠶⠶⠕"}}, "key": "21D8"}, {"category": "So", "mappings": {"default": {"default": "⠫⠰⠪⠶⠶"}}, "key": "21D9"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠪⠪⠒⠒"}}, "key": "21DA"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠕⠕⠕"}}, "key": "21DB"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠢⠤⠔⠒⠢"}}, "key": "21DC"}, {"category": "So", "mappings": {"default": {"default": "⠫⠢⠤⠔⠒⠢⠕"}}, "key": "21DD"}, {"category": "So", "mappings": {"default": {"default": "⠳⠳⠈⠫⠣⠒⠒⠕⠻"}}, "key": "21DE"}, {"category": "So", "mappings": {"default": {"default": "⠳⠳⠈⠫⠩⠒⠒⠕⠻"}}, "key": "21DF"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒"}}, "key": "21E0"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠒⠒⠕"}}, "key": "21E1"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠕"}}, "key": "21E2"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠒⠒⠕"}}, "key": "21E3"}, {"category": "So", "mappings": {"default": {"default": "⠫⠳⠪⠒⠒"}}, "key": "21E4"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠕⠳"}}, "key": "21E5"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠪⠒⠒"}}, "key": "21E6"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠸⠒⠒⠕"}}, "key": "21E7"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠒⠒⠕"}}, "key": "21E8"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠸⠒⠒⠕"}}, "key": "21E9"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21EA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21EB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21EC"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21ED"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21EE"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21EF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21F0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21F1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "21F2"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠸⠪⠒⠒⠕"}}, "key": "21F3"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠒⠒⠕⠨⠡"}}, "key": "21F4"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠩⠒⠒⠕⠐⠫⠣⠒⠒⠕"}}, "key": "21F5"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠒⠒⠕⠫⠒⠒⠕⠫⠒⠒⠕"}}, "key": "21F6"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠪⠒⠒⠻"}}, "key": "21F7"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠒⠒⠕⠻"}}, "key": "21F8"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠈⠫⠪⠒⠒⠕"}}, "key": "21F9"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠳⠈⠫⠪⠒⠒⠻"}}, "key": "21FA"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠳⠈⠫⠒⠒⠕⠻"}}, "key": "21FB"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠳⠈⠫⠪⠒⠒⠕"}}, "key": "21FC"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠳⠒⠒"}}, "key": "21FD"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠒⠒⠳"}}, "key": "21FE"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠳⠒⠒⠳"}}, "key": "21FF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2301"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2303"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2304"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2324"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "238B"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠒⠒⠕"}}, "key": "2794"}, {"category": "So", "mappings": {"default": {"default": "⠫⠰⠸⠒⠒⠕"}}, "key": "2798"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠒⠒⠕"}}, "key": "2799"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "279A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "279B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "279C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "279D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "279E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "279F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27A9"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27AA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27AB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27AC"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27AD"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27AE"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27AF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B9"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27BA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27BB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27BC"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27BD"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27BE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27F0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27F1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27F2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27F3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27F4"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠒⠒⠒"}}, "key": "27F5"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠒⠒⠒⠕"}}, "key": "27F6"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠒⠒⠒⠕"}}, "key": "27F7"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠶⠶⠶"}}, "key": "27F8"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠶⠶⠶⠕"}}, "key": "27F9"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠶⠶⠶⠕"}}, "key": "27FA"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠒⠒⠒⠳"}}, "key": "27FB"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠳⠒⠒⠒⠕"}}, "key": "27FC"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠶⠶⠶⠳"}}, "key": "27FD"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠳⠶⠶⠶⠕"}}, "key": "27FE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27FF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2900"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2901"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2902"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2903"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2904"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2905"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠶⠶⠳"}}, "key": "2906"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠳⠶⠶⠕"}}, "key": "2907"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2908"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2909"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "290A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "290B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "290C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "290D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "290E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "290F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2910"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2911"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2912"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2913"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2914"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2915"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2916"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2917"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2918"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2919"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "291A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "291B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "291C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "291D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "291E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "291F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2920"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2921"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2922"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2923"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2924"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2925"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2926"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2927"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2928"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2929"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "292A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "292D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "292E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "292F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2930"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2931"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2932"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2933"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2934"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2935"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2936"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2937"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2938"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2939"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "293A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "293B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "293C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "293D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "293E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "293F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2940"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2941"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2942"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2943"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2944"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2945"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2946"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2947"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2948"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2949"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2970"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2971"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2972"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2973"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2974"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2975"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2976"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2977"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2978"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2979"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "297A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "297B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29B3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29B4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29BD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29EA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29EC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29ED"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A17"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B00"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B01"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B02"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B03"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B04"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B05"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B06"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B07"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B08"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B09"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B0A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B0B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B0C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B0D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B0E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B0F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B10"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B11"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B30"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B31"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B32"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B33"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B34"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B35"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B36"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B37"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B38"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B39"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B3A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B3B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B3C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B3D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B3E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B3F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B40"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B41"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B42"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B43"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B44"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B45"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B46"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B47"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B48"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B49"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B4A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B4B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2B4C"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠒⠒"}}, "key": "FFE9"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠣⠒⠒⠕"}}, "key": "FFEA"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠕"}}, "key": "FFEB"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠩⠒⠒⠕"}}, "key": "FFEC"}], "nemeth/symbols/math_characters.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "mappings": {"default": {"default": "⠈⠰⠇"}}, "key": "2113"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2118"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "213C"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "213D"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "213E"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "213F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2140"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "2145"}, {"category": "Ll", "mappings": {"default": {"default": "⠙"}}, "key": "2146"}, {"category": "Ll", "mappings": {"default": {"default": "⠑"}}, "key": "2147"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "2148"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "2149"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "1D6A4"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "1D6A5"}], "nemeth/symbols/math_delimiters.min": [{"locale": "ne<PERSON>h"}, {"category": "Ps", "mappings": {"default": {"default": "⠷"}}, "key": "0028"}, {"category": "Pe", "mappings": {"default": {"default": "⠾"}}, "key": "0029"}, {"category": "Ps", "mappings": {"default": {"default": "⠈⠷"}}, "key": "005B"}, {"category": "Pe", "mappings": {"default": {"default": "⠈⠾"}}, "key": "005D"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠷"}}, "key": "007B"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠾"}}, "key": "007D"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2045"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2046"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠘⠷"}}, "key": "2308"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠘⠾"}}, "key": "2309"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠰⠷"}}, "key": "230A"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠰⠾"}}, "key": "230B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "230C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "230D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "230E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "230F"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠷"}}, "key": "231C"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠾"}}, "key": "231D"}, {"category": "So", "mappings": {"default": {"default": "⠈⠰⠷"}}, "key": "231E"}, {"category": "So", "mappings": {"default": {"default": "⠈⠰⠾"}}, "key": "231F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2320"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2321"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠨⠷"}}, "key": "2329"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠨⠾"}}, "key": "232A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "239B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "239C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "239D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "239E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "239F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23A9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23AA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23AB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23AC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23AD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23AE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23AF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23B0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23B1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23B2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23B3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23B4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23B5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23B6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23B7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23B8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23B9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23DC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23DD"}, {"category": "Sm", "mappings": {"default": {"default": "⠣⠨⠷"}}, "key": "23DE"}, {"category": "Sm", "mappings": {"default": {"default": "⠣⠨⠾"}}, "key": "23DF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23E0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "23E1"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2768"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2769"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "276A"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "276B"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "276C"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "276D"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "276E"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "276F"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2770"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2771"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2772"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2773"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2774"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2775"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "27C5"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "27C6"}, {"category": "Ps", "mappings": {"default": {"default": "⠈⠸⠷"}}, "key": "27E6"}, {"category": "Pe", "mappings": {"default": {"default": "⠈⠸⠾"}}, "key": "27E7"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠨⠷"}}, "key": "27E8"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠨⠾"}}, "key": "27E9"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠨⠨⠷"}}, "key": "27EA"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠨⠨⠾"}}, "key": "27EB"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "27EC"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "27ED"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "27EE"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "27EF"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠸⠷"}}, "key": "2983"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠸⠾"}}, "key": "2984"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2985"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2986"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2987"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2988"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2989"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "298A"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "298B"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "298C"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "298D"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "298E"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "298F"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2990"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2991"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2992"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2993"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2994"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2995"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2996"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2997"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2998"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "29D8"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "29D9"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "29DA"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "29DB"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "29FC"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "29FD"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2E22"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2E23"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2E24"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2E25"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2E26"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2E27"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "2E28"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "2E29"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠨⠷"}}, "key": "3008"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠨⠾"}}, "key": "3009"}, {"category": "Ps", "mappings": {"default": {"default": "⠨⠨⠨⠷"}}, "key": "300A"}, {"category": "Pe", "mappings": {"default": {"default": "⠨⠨⠨⠾"}}, "key": "300B"}, {"category": "Ps", "mappings": {"default": {"default": "⠈⠘⠷"}}, "key": "300C"}, {"category": "Pe", "mappings": {"default": {"default": "⠈⠘⠾"}}, "key": "300D"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "300E"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "300F"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "3010"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "3011"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "3014"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "3015"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "3016"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "3017"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "3018"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "3019"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "301A"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "301B"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "301D"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "301E"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "301F"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FD3E"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FD3F"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE17"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE18"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE35"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE36"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE37"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE38"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE39"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE3A"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE3B"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE3C"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE3D"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE3E"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE3F"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE40"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE41"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE42"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE43"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE44"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE47"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE48"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE59"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE5A"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE5B"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE5C"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FE5D"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FE5E"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FF08"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FF09"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FF3B"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FF3D"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FF5B"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FF5D"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FF5F"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FF60"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "FF62"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "FF63"}], "nemeth/symbols/math_geometry.min": [{"locale": "ne<PERSON>h"}, {"category": "So", "mappings": {"default": {"default": "⠱"}}, "key": "2500"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2501"}, {"category": "So", "mappings": {"default": {"default": "⠳"}}, "key": "2502"}, {"category": "So", "mappings": {"default": {"default": "⠳"}}, "key": "2503"}, {"category": "So", "mappings": {"default": {"default": "⠒⠀⠒"}}, "key": "2504"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2505"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2506"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2507"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2508"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2509"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "250A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "250B"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠷"}}, "key": "250C"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠷"}}, "key": "250D"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠷"}}, "key": "250E"}, {"category": "So", "mappings": {"default": {"default": "⠸⠈⠘⠷"}}, "key": "250F"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠾"}}, "key": "2510"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠾"}}, "key": "2511"}, {"category": "So", "mappings": {"default": {"default": "⠈⠘⠾"}}, "key": "2512"}, {"category": "So", "mappings": {"default": {"default": "⠸⠈⠘⠾"}}, "key": "2513"}, {"category": "So", "mappings": {"default": {"default": "⠈⠳"}}, "key": "2514"}, {"category": "So", "mappings": {"default": {"default": "⠈⠳"}}, "key": "2515"}, {"category": "So", "mappings": {"default": {"default": "⠈⠳"}}, "key": "2516"}, {"category": "So", "mappings": {"default": {"default": "⠸⠈⠳"}}, "key": "2517"}, {"category": "So", "mappings": {"default": {"default": "⠈⠳"}}, "key": "2518"}, {"category": "So", "mappings": {"default": {"default": "⠈⠳"}}, "key": "2519"}, {"category": "So", "mappings": {"default": {"default": "⠈⠳"}}, "key": "251A"}, {"category": "So", "mappings": {"default": {"default": "⠸⠈⠳"}}, "key": "251B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "251C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "251D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "251E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "251F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2520"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2521"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2522"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2523"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2524"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2525"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2526"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2527"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2528"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2529"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "252A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "252B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "252C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "252D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "252E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "252F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2530"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2531"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2532"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2533"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2534"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2535"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2536"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2537"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2538"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2539"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "253A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "253B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "253C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "253D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "253E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "253F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2540"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2541"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2542"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2543"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2544"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2545"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2546"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2547"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2548"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2549"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "254A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "254B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "254C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "254D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "254E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "254F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2550"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2551"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2552"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2553"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2554"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2555"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2556"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2557"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2558"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2559"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "255A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "255B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "255C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "255D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "255E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "255F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2560"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2561"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2562"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2563"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2564"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2565"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2566"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2567"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2568"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2569"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "256A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "256B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "256C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "256D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "256E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "256F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2570"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2571"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2572"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2573"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2574"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2575"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2576"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2577"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2578"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2579"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "257A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "257B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "257C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "257D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "257E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "257F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2580"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2581"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2582"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2583"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2584"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2585"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2586"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2587"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2588"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2589"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "258A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "258B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "258C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "258D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "258E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "258F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2590"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2591"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2592"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2593"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2594"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2595"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2596"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2597"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2598"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2599"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "259A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "259B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "259C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "259D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "259E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "259F"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠲"}}, "key": "25A0"}, {"category": "So", "mappings": {"default": {"default": "⠫⠲"}}, "key": "25A1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25A9"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠲"}}, "key": "25AA"}, {"category": "So", "mappings": {"default": {"default": "⠫⠲"}}, "key": "25AB"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠗"}}, "key": "25AC"}, {"category": "So", "mappings": {"default": {"default": "⠫⠗"}}, "key": "25AD"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25AE"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25AF"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠛"}}, "key": "25B0"}, {"category": "So", "mappings": {"default": {"default": "⠫⠛"}}, "key": "25B1"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠞"}}, "key": "25B2"}, {"category": "So", "mappings": {"default": {"default": "⠫⠞"}}, "key": "25B3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25B4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25B5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25B6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "25B7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25B8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25B9"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25BA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25BB"}, {"category": "So", "mappings": {"default": {"default": "⠸⠨⠫"}}, "key": "25BC"}, {"category": "So", "mappings": {"default": {"default": "⠨⠫"}}, "key": "25BD"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25BE"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25BF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25C0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "25C1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25C2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25C3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25C4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25C5"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠙"}}, "key": "25C6"}, {"category": "So", "mappings": {"default": {"default": "⠫⠙"}}, "key": "25C7"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠙"}}, "key": "25C8"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠉"}}, "key": "25C9"}, {"category": "So", "mappings": {"default": {"default": "⠫⠙"}}, "key": "25CA"}, {"category": "So", "mappings": {"default": {"default": "⠫⠉"}}, "key": "25CB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25CC"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25CD"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25CE"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠉"}}, "key": "25CF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25D9"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25DA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25DB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25DC"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25DD"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25DE"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25DF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E7"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25E9"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25EA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25EB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25EC"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25ED"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25EE"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25EF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F2"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F5"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F6"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "25F7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "25F8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "25F9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "25FA"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠲"}}, "key": "25FB"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠸⠲"}}, "key": "25FC"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠲"}}, "key": "25FD"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠸⠲"}}, "key": "25FE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "25FF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B12"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B13"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B14"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B15"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B16"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B17"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B18"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B19"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B1A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B1B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B1C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B1D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B1E"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠢"}}, "key": "2B1F"}, {"category": "So", "mappings": {"default": {"default": "⠫⠢"}}, "key": "2B20"}, {"category": "So", "mappings": {"default": {"default": "⠫⠖"}}, "key": "2B21"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠖"}}, "key": "2B22"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠖"}}, "key": "2B23"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠉"}}, "key": "2B24"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠙"}}, "key": "2B25"}, {"category": "So", "mappings": {"default": {"default": "⠫⠙"}}, "key": "2B26"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠙"}}, "key": "2B27"}, {"category": "So", "mappings": {"default": {"default": "⠫⠙"}}, "key": "2B28"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠙"}}, "key": "2B29"}, {"category": "So", "mappings": {"default": {"default": "⠫⠸⠙"}}, "key": "2B2A"}, {"category": "So", "mappings": {"default": {"default": "⠫⠙"}}, "key": "2B2B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B2C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B2D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B2E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B2F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B50"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B51"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B52"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B53"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B54"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B55"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B56"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B57"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B58"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2B59"}], "nemeth/symbols/math_harpoons.min": [{"locale": "ne<PERSON>h"}, {"category": "So", "mappings": {"default": {"default": "⠫⠈⠪⠒⠒"}}, "key": "21BC"}, {"category": "So", "mappings": {"default": {"default": "⠫⠠⠪⠒⠒"}}, "key": "21BD"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠒⠒⠠⠕"}}, "key": "21BE"}, {"category": "So", "mappings": {"default": {"default": "⠫⠣⠒⠒⠈⠕"}}, "key": "21BF"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠈⠕"}}, "key": "21C0"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠠⠕"}}, "key": "21C1"}, {"category": "So", "mappings": {"default": {"default": "⠫⠪⠒⠒⠈⠕"}}, "key": "21C2"}, {"category": "So", "mappings": {"default": {"default": "⠫⠩⠒⠒⠠⠕"}}, "key": "21C3"}, {"category": "So", "mappings": {"default": {"default": "⠫⠈⠪⠒⠒⠫⠒⠒⠈⠕"}}, "key": "21CB"}, {"category": "So", "mappings": {"default": {"default": "⠫⠒⠒⠈⠕⠫⠈⠪⠒⠒"}}, "key": "21CC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "294A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "294B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "294C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "294D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "294E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "294F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2950"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2951"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2952"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2953"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2954"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2955"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2956"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2957"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2958"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2959"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "295A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "295B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "295C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "295D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "295E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "295F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2960"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2961"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2962"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2963"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2964"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2965"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2966"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2967"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2968"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2969"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "296A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "296B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "296C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "296D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "296E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "296F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "297C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "297D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "297E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "297F"}], "nemeth/symbols/math_non_characters.min": [{"locale": "ne<PERSON>h"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "210F"}, {"category": "So", "mappings": {"default": {"default": "⠳"}}, "key": "2114"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2116"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2117"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "211E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "211F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2120"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2121"}, {"category": "So", "mappings": {"default": {"default": "⠘⠞"}}, "key": "2122"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2123"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2125"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "2126"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2127"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "212A"}, {"category": "<PERSON>", "mappings": {"default": {"default": "⠈⠠⠁"}}, "key": "212B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "212E"}, {"category": "<PERSON>", "mappings": {"default": {"default": ""}}, "key": "2132"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "2139"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "213A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "213B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2141"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2142"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2143"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2144"}], "nemeth/symbols/math_symbols.min": [{"locale": "ne<PERSON>h"}, {"category": "Po", "mappings": {"default": {"default": "⠖"}}, "key": "0021"}, {"category": "Po", "mappings": {"default": {"default": "⠄⠄"}}, "key": "0022"}, {"category": "Po", "mappings": {"default": {"default": "⠨⠼"}}, "key": "0023"}, {"category": "Po", "mappings": {"default": {"default": "⠈⠴"}}, "key": "0025"}, {"category": "Po", "mappings": {"default": {"default": "⠸⠯"}}, "key": "0026"}, {"category": "Po", "mappings": {"default": {"default": "⠄"}}, "key": "0027"}, {"category": "Po", "mappings": {"default": {"default": "⠈⠼"}}, "key": "002A"}, {"category": "Sm", "mappings": {"default": {"default": "⠬"}}, "key": "002B"}, {"category": "Po", "mappings": {"default": {"default": "⠠", "defaultLibLouis": "⠠⠀"}}, "key": "002C"}, {"category": "Pd", "mappings": {"default": {"default": "⠤"}}, "key": "002D"}, {"category": "Po", "mappings": {"default": {"default": "⠨", "literary": "⠲"}}, "key": "002E"}, {"category": "Po", "mappings": {"default": {"default": "⠸⠌"}}, "key": "002F"}, {"category": "Po", "mappings": {"default": {"default": "⠐⠂", "literary": "⠒"}}, "key": "003A"}, {"category": "Po", "mappings": {"default": {"default": "⠆"}}, "key": "003B"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅"}}, "key": "003C"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠅"}}, "key": "003D"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂"}}, "key": "003E"}, {"category": "Po", "mappings": {"default": {"default": "⠿"}}, "key": "003F"}, {"category": "Po", "mappings": {"default": {"default": "⠈⠁"}}, "key": "0040"}, {"category": "Po", "mappings": {"default": {"default": "⠸⠡"}}, "key": "005C"}, {"category": "Sk", "mappings": {"default": {"default": "⠸⠣"}}, "key": "005E"}, {"category": "Pc", "mappings": {"default": {"default": "⠤"}}, "key": "005F"}, {"category": "Sk", "mappings": {"default": {"default": "⠈"}}, "key": "0060"}, {"category": "Sm", "mappings": {"default": {"default": "⠳"}}, "key": "007C"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱"}}, "key": "007E"}, {"category": "Po", "mappings": {"default": {"default": "⠖"}}, "key": "00A1"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "00A6"}, {"category": "Po", "mappings": {"default": {"default": "⠈⠠⠎"}}, "key": "00A7"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "00A8"}, {"category": "So", "mappings": {"default": {"default": "⠘⠉"}}, "key": "00A9"}, {"category": "Lo", "mappings": {"default": {"default": "⠸⠰⠁"}}, "key": "00AA"}, {"category": "Pi", "mappings": {"default": {"default": "⠐⠅⠈⠐⠅⠻"}}, "key": "00AB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "00AC"}, {"category": "So", "mappings": {"default": {"default": "⠘⠗"}}, "key": "00AE"}, {"category": "Sk", "mappings": {"default": {"default": "⠱"}}, "key": "00AF"}, {"category": "So", "mappings": {"default": {"default": "⠘⠨⠡"}}, "key": "00B0"}, {"category": "Sm", "mappings": {"default": {"default": "⠬⠤"}}, "key": "00B1"}, {"category": "Sk", "mappings": {"default": {"default": "⠈"}}, "key": "00B4"}, {"category": "Ll", "mappings": {"default": {"default": "⠨⠍"}}, "key": "00B5"}, {"category": "Po", "mappings": {"default": {"default": "⠈⠠⠏"}}, "key": "00B6"}, {"category": "Po", "mappings": {"default": {"default": "⠡"}}, "key": "00B7"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "00B8"}, {"category": "Lo", "mappings": {"default": {"default": ""}}, "key": "00BA"}, {"category": "Pf", "mappings": {"default": {"default": ""}}, "key": "00BB"}, {"category": "Po", "mappings": {"default": {"default": "⠦"}}, "key": "00BF"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠡"}}, "key": "00D7"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠌"}}, "key": "00F7"}, {"key": "02B9", "mappings": {"default": {"default": "⠄"}}, "category": "Lm"}, {"key": "02BA", "mappings": {"default": {"default": "⠄⠄"}}, "category": "Lm"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "02D8"}, {"category": "Sk", "mappings": {"default": {"default": "⠡"}}, "key": "02D9"}, {"category": "Sk", "mappings": {"default": {"default": "⠈"}}, "key": "02DA"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "02DB"}, {"category": "Sk", "mappings": {"default": {"default": "⠈⠱"}}, "key": "02DC"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "02DD"}, {"category": "Pd", "mappings": {"default": {"default": "⠤"}}, "key": "2010"}, {"category": "Pd", "mappings": {"default": {"default": "⠤"}}, "key": "2011"}, {"category": "Pd", "mappings": {"default": {"default": "⠤⠤"}}, "key": "2012"}, {"category": "Pd", "mappings": {"default": {"default": "⠤⠤"}}, "key": "2013"}, {"category": "Pd", "mappings": {"default": {"default": "⠤⠤⠤⠤"}}, "key": "2014"}, {"category": "Pd", "mappings": {"default": {"default": "⠤⠤⠤⠤"}}, "key": "2015"}, {"category": "Po", "mappings": {"default": {"default": "⠳⠳"}}, "key": "2016"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2017"}, {"category": "Pi", "mappings": {"default": {"default": "⠠⠦"}}, "key": "2018"}, {"category": "Pf", "mappings": {"default": {"default": "⠄"}}, "key": "2019"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "201A"}, {"category": "Pi", "mappings": {"default": {"default": ""}}, "key": "201B"}, {"category": "Pi", "mappings": {"default": {"default": "⠦"}}, "key": "201C"}, {"category": "Pf", "mappings": {"default": {"default": "⠴"}}, "key": "201D"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "201E"}, {"category": "Pi", "mappings": {"default": {"default": ""}}, "key": "201F"}, {"category": "Po", "mappings": {"default": {"default": "⠸⠻"}}, "key": "2020"}, {"category": "Po", "mappings": {"default": {"default": "⠸⠸⠻"}}, "key": "2021"}, {"category": "Po", "mappings": {"default": {"default": "⠔⠔"}}, "key": "2022"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2023"}, {"category": "Po", "mappings": {"default": {"default": "⠄"}}, "key": "2024"}, {"category": "Po", "mappings": {"default": {"default": "⠄⠄"}}, "key": "2025"}, {"category": "Po", "mappings": {"default": {"default": "⠄⠄⠄"}}, "key": "2026"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2027"}, {"category": "Po", "mappings": {"default": {"default": "⠈⠴⠴"}}, "key": "2030"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2031"}, {"category": "Po", "mappings": {"default": {"default": "⠄"}}, "key": "2032"}, {"category": "Po", "mappings": {"default": {"default": "⠄⠄"}}, "key": "2033"}, {"category": "Po", "mappings": {"default": {"default": "⠄⠄⠄"}}, "key": "2034"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2035"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2036"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2037"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2038"}, {"category": "Pi", "mappings": {"default": {"default": ""}}, "key": "2039"}, {"category": "Pf", "mappings": {"default": {"default": ""}}, "key": "203A"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "203B"}, {"category": "Po", "mappings": {"default": {"default": "⠖⠖"}}, "key": "203C"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "203D"}, {"category": "Po", "mappings": {"default": {"default": "⠱"}}, "key": "203E"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "203F"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "2040"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2041"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2042"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2043"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2044"}, {"category": "Po", "mappings": {"default": {"default": "⠹⠹"}}, "key": "2047"}, {"category": "Po", "mappings": {"default": {"default": "⠹⠖"}}, "key": "2048"}, {"category": "Po", "mappings": {"default": {"default": "⠖⠹"}}, "key": "2049"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "204B"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "204C"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "204D"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "204E"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "204F"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2050"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2051"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2052"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2053"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "2054"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2055"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2056"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2057"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2058"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "2059"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "205A"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "205B"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "205C"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "205D"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "205E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "207A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "207B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "207C"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "207D"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "207E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "208A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "208B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "208C"}, {"category": "Ps", "mappings": {"default": {"default": ""}}, "key": "208D"}, {"category": "Pe", "mappings": {"default": {"default": ""}}, "key": "208E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "214A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "214B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "214C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "214D"}, {"category": "Ll", "mappings": {"default": {"default": ""}}, "key": "214E"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠯"}}, "key": "2200"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2201"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠿"}}, "key": "2203"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠿"}}, "key": "2204"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠴"}}, "key": "2205"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2206"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠑"}}, "key": "2208"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠑"}}, "key": "2209"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠑"}}, "key": "220A"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠢"}}, "key": "220B"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠢"}}, "key": "220C"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠢"}}, "key": "220D"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠳"}}, "key": "220E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "220F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2210"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠠⠎"}}, "key": "2211"}, {"category": "Sm", "mappings": {"default": {"default": "⠤"}}, "key": "2212"}, {"category": "Sm", "mappings": {"default": {"default": "⠤⠬"}}, "key": "2213"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2214"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠌"}}, "key": "2215"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠡"}}, "key": "2216"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠼"}}, "key": "2217"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠡"}}, "key": "2218"}, {"category": "Sm", "mappings": {"default": {"default": "⠡"}}, "key": "2219"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "221A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "221B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "221C"}, {"category": "Sm", "mappings": {"default": {"default": "⠰⠆"}}, "key": "221D"}, {"category": "Sm", "mappings": {"default": {"default": "⠠⠿"}}, "key": "221E"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠨⠗⠻"}}, "key": "221F"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪"}}, "key": "2220"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠸⠫⠫⠁⠻"}}, "key": "2221"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠪⠸⠫⠫⠁⠻"}}, "key": "2222"}, {"category": "Sm", "mappings": {"default": {"default": "⠳"}}, "key": "2223"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠳"}}, "key": "2224"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠇"}}, "key": "2225"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠫⠇"}}, "key": "2226"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠩"}}, "key": "2227"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠬"}}, "key": "2228"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠩"}}, "key": "2229"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠬"}}, "key": "222A"}, {"category": "Sm", "mappings": {"default": {"default": "⠮"}}, "key": "222B"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠮"}}, "key": "222C"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠮⠮"}}, "key": "222D"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠈⠫⠉⠻"}}, "key": "222E"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠮⠈⠫⠉⠻"}}, "key": "222F"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠮⠮⠈⠫⠉⠻"}}, "key": "2230"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2231"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠈⠫⠪⠢⠔⠻"}}, "key": "2232"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠈⠫⠢⠔⠕⠻"}}, "key": "2233"}, {"category": "Sm", "mappings": {"default": {"default": "⠠⠡"}}, "key": "2234"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠌"}}, "key": "2235"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠂"}}, "key": "2236"}, {"category": "Sm", "mappings": {"default": {"default": "⠰⠆"}}, "key": "2237"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠤"}}, "key": "2238"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2239"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "223A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "223B"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱"}}, "key": "223C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "223D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "223E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "223F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2240"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠱"}}, "key": "2241"}, {"category": "Sm", "mappings": {"default": {"default": "⠱⠈⠱"}}, "key": "2242"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠱"}}, "key": "2243"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠱⠱"}}, "key": "2244"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠨⠅"}}, "key": "2245"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠌⠨⠅"}}, "key": "2246"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠱⠨⠅"}}, "key": "2247"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠈⠱"}}, "key": "2248"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠱⠈⠱"}}, "key": "2249"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠈⠱⠱"}}, "key": "224A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "224B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "224C"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠣⠠⠣"}}, "key": "224D"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠣⠠⠣"}}, "key": "224E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "224F"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠡⠻"}}, "key": "2250"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠩⠡⠣⠡⠻"}}, "key": "2251"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2252"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2253"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2254"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2255"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2256"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠨⠡⠻"}}, "key": "2257"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠫⠁⠻"}}, "key": "2258"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠸⠣⠻"}}, "key": "2259"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠸⠩⠻"}}, "key": "225A"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠫⠸⠎⠻"}}, "key": "225B"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠫⠞⠻"}}, "key": "225C"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠙⠑⠋⠻"}}, "key": "225D"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠍⠻"}}, "key": "225E"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠨⠅⠣⠸⠦⠻"}}, "key": "225F"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠅"}}, "key": "2260"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠇"}}, "key": "2261"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠸⠇"}}, "key": "2262"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2263"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠱"}}, "key": "2264"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠱"}}, "key": "2265"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠨⠅"}}, "key": "2266"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠨⠅"}}, "key": "2267"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠌⠨⠅"}}, "key": "2268"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠌⠨⠅"}}, "key": "2269"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠈⠐⠅⠻"}}, "key": "226A"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠈⠨⠂⠻"}}, "key": "226B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "226C"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠈⠣⠠⠣"}}, "key": "226D"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠐⠅"}}, "key": "226E"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠂"}}, "key": "226F"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠐⠅⠱"}}, "key": "2270"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠂⠱"}}, "key": "2271"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠈⠱"}}, "key": "2272"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠈⠱"}}, "key": "2273"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠐⠅⠈⠱"}}, "key": "2274"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠂⠈⠱"}}, "key": "2275"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠨⠂"}}, "key": "2276"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠐⠅"}}, "key": "2277"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠐⠅⠨⠂"}}, "key": "2278"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠂⠐⠅"}}, "key": "2279"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅"}}, "key": "227A"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂"}}, "key": "227B"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠱"}}, "key": "227C"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠱"}}, "key": "227D"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠈⠱"}}, "key": "227E"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠈⠱"}}, "key": "227F"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠐⠅"}}, "key": "2280"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠨⠂⠅"}}, "key": "2281"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠐⠅"}}, "key": "2282"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠨⠂"}}, "key": "2283"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠸⠐⠅"}}, "key": "2284"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠸⠨⠂"}}, "key": "2285"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠐⠅⠱"}}, "key": "2286"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠨⠂⠱"}}, "key": "2287"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠸⠐⠅⠱"}}, "key": "2288"}, {"category": "Sm", "mappings": {"default": {"default": "⠌⠸⠨⠂⠱"}}, "key": "2289"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠐⠅⠌⠨⠅"}}, "key": "228A"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠨⠂⠌⠨⠅"}}, "key": "228B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "228C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "228D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "228E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "228F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2290"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2291"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2292"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2293"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2294"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠬⠻"}}, "key": "2295"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠤⠻"}}, "key": "2296"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠈⠡⠻"}}, "key": "2297"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠸⠌⠻"}}, "key": "2298"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠡⠻"}}, "key": "2299"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠘⠨⠡⠻"}}, "key": "229A"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠈⠼⠻"}}, "key": "229B"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠨⠅⠻"}}, "key": "229C"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠤⠻"}}, "key": "229D"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠲⠸⠫⠬⠻"}}, "key": "229E"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠲⠸⠫⠤⠻"}}, "key": "229F"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠲⠸⠫⠈⠡⠻"}}, "key": "22A0"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠲⠸⠫⠡⠻"}}, "key": "22A1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A4"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠏"}}, "key": "22A5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22A9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22AA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22AB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22AC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22AD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22AE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22AF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22B9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22BA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22BB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22BC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22BD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22BF"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠩"}}, "key": "22C0"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠬"}}, "key": "22C1"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠩"}}, "key": "22C2"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠬"}}, "key": "22C3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22C4"}, {"category": "Sm", "mappings": {"default": {"default": "⠡"}}, "key": "22C5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22C6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22C7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22C8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22C9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22CA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22CB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22CC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22CD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22CE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22CF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22D9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22DA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22DB"}, {"category": "Sm", "mappings": {"default": {"default": "⠱⠐⠅"}}, "key": "22DC"}, {"category": "Sm", "mappings": {"default": {"default": "⠱⠨⠂"}}, "key": "22DD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22DE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22DF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22E9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22EA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22EB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22EC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22ED"}, {"category": "Sm", "mappings": {"default": {"default": "⠣⠄⠄⠄"}}, "key": "22EE"}, {"category": "Sm", "mappings": {"default": {"default": "⠄⠄⠄"}}, "key": "22EF"}, {"category": "Sm", "mappings": {"default": {"default": "⠘⠄⠄⠄"}}, "key": "22F0"}, {"category": "Sm", "mappings": {"default": {"default": "⠰⠄⠄⠄"}}, "key": "22F1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22F9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22FA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22FB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22FC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22FD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22FE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "22FF"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2300"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2302"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2305"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2306"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2307"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2310"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2311"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2312"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2313"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2314"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2795"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2796"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2797"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27B0"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "27BF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C1"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠏"}}, "key": "27C2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27C9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27CA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27CB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27CC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27CD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27CE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27CF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27D9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27DA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27DB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27DC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27DD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27DE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27DF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27E0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27E1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27E2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27E3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27E4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "27E5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "292B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "292C"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠳⠳"}}, "key": "2980"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2981"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2982"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2999"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "299A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29B0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29B1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29B2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29B5"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠳⠻"}}, "key": "29B6"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠫⠇⠻"}}, "key": "29B7"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠸⠡⠻"}}, "key": "29B8"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠫⠏⠻"}}, "key": "29B9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29BA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29BB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29BC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29BE"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠔⠔⠻"}}, "key": "29BF"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠐⠅⠻"}}, "key": "29C0"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠨⠂⠻"}}, "key": "29C1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29C9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29CA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29CB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29CC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29CD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29CE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29CF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29D7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29DC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29DD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29DE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29DF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29E9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29EB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29EE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29EF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29F9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29FA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29FB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29FE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "29FF"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠡⠻"}}, "key": "2A00"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠬⠻"}}, "key": "2A01"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠈⠡⠻"}}, "key": "2A02"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A03"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A04"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A05"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A06"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A07"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A08"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A09"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A0A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A0B"}, {"category": "Sm", "mappings": {"default": {"default": "⠮⠮⠮⠮"}}, "key": "2A0C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A0D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A0E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A0F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A10"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A11"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A12"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A13"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A14"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A15"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A16"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A18"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A19"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A1A"}, {"category": "Sm", "mappings": {"default": {"default": "⠣⠮"}}, "key": "2A1B"}, {"category": "Sm", "mappings": {"default": {"default": "⠩⠮"}}, "key": "2A1C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A1D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A1E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A1F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A20"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A21"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A22"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A23"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A24"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A25"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A26"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A27"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A28"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A29"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A2A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A2B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A2C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A2D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A2E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A2F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A30"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A31"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A32"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A33"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A34"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A35"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A36"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A37"}, {"category": "Sm", "mappings": {"default": {"default": "⠫⠉⠸⠫⠈⠌⠻"}}, "key": "2A38"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A39"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A3A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A3B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A3C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A3D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A3E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A3F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A40"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A41"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A42"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A43"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A44"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A45"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A46"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A47"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A48"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A49"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A4A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A4B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A4C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A4D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A4E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A4F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A50"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A51"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A52"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A53"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A54"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A55"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A56"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A57"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A58"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A59"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A5A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A5B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A5C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A5D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A5E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A5F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A60"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A61"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A62"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A63"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A64"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A65"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A66"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A67"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A68"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A69"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A6A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A6B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A6C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A6D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A6E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A6F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A70"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A71"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A72"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A73"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A74"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A75"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A76"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A77"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A78"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A79"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A7A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A7B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A7C"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠱"}}, "key": "2A7D"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠱"}}, "key": "2A7E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A7F"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A80"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A81"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A82"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A83"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A84"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A85"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A86"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A87"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A88"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A89"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A8A"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠨⠅⠐⠅"}}, "key": "2A8B"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠨⠅⠨⠂"}}, "key": "2A8C"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠈⠱⠱"}}, "key": "2A8D"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠈⠱⠱"}}, "key": "2A8E"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠈⠱⠨⠂"}}, "key": "2A8F"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠈⠱⠐⠅"}}, "key": "2A90"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠨⠂⠨⠅"}}, "key": "2A91"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠐⠅⠨⠅"}}, "key": "2A92"}, {"category": "Sm", "mappings": {"default": {"default": "⠐⠅⠱⠨⠂⠱"}}, "key": "2A93"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠱⠐⠅⠱"}}, "key": "2A94"}, {"category": "Sm", "mappings": {"default": {"default": "⠱⠐⠅"}}, "key": "2A95"}, {"category": "Sm", "mappings": {"default": {"default": "⠱⠨⠂"}}, "key": "2A96"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A97"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2A98"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠅⠐⠅"}}, "key": "2A99"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠅⠨⠂"}}, "key": "2A9A"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠅⠐⠅"}}, "key": "2A9B"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠅⠨⠂"}}, "key": "2A9C"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠐⠅"}}, "key": "2A9D"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠨⠂"}}, "key": "2A9E"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠐⠅⠨⠅"}}, "key": "2A9F"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱⠨⠂⠨⠅"}}, "key": "2AA0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AA9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AAA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AAB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AAC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AAD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AAE"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠱"}}, "key": "2AAF"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠱"}}, "key": "2AB0"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠌⠱"}}, "key": "2AB1"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠌⠱"}}, "key": "2AB2"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠨⠅"}}, "key": "2AB3"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠨⠅"}}, "key": "2AB4"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠌⠨⠅"}}, "key": "2AB5"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠌⠨⠅"}}, "key": "2AB6"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠈⠱⠈⠱"}}, "key": "2AB7"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠈⠱⠈⠱"}}, "key": "2AB8"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠌⠈⠱⠈⠱"}}, "key": "2AB9"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠌⠈⠱⠈⠱"}}, "key": "2ABA"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠐⠅⠈⠨⠐⠅⠻"}}, "key": "2ABB"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠨⠂⠈⠨⠨⠂⠻"}}, "key": "2ABC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ABD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ABE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ABF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC4"}, {"category": "Sm", "mappings": {"default": {"default": "⠸⠐⠅⠨⠅"}}, "key": "2AC5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AC9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ACA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ACB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ACC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ACD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ACE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ACF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AD9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ADA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ADB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ADC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ADD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ADE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2ADF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE3"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AE9"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AEA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AEB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AEC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AED"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AEE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AEF"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF0"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF1"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF2"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF3"}, {"category": "Sm", "mappings": {"default": {"default": "⠳⠳⠳"}}, "key": "2AF4"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF5"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF6"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF7"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF8"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AF9"}, {"category": "Sm", "mappings": {"default": {"default": "⠨⠂⠨⠅"}}, "key": "2AFA"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AFB"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AFC"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AFD"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AFE"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "2AFF"}, {"category": "Pd", "mappings": {"default": {"default": ""}}, "key": "301C"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE10"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE13"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE14"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE15"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE16"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE19"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE30"}, {"category": "Pd", "mappings": {"default": {"default": ""}}, "key": "FE31"}, {"category": "Pd", "mappings": {"default": {"default": ""}}, "key": "FE32"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "FE33"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "FE34"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE45"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE46"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE49"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE4A"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE4B"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE4C"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "FE4D"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "FE4E"}, {"category": "Pc", "mappings": {"default": {"default": ""}}, "key": "FE4F"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE50"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE52"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE54"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE55"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE56"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE57"}, {"category": "Pd", "mappings": {"default": {"default": ""}}, "key": "FE58"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE5F"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE60"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE61"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FE62"}, {"category": "Pd", "mappings": {"default": {"default": ""}}, "key": "FE63"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FE64"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FE65"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FE66"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE68"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE6A"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FE6B"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF01"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF02"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF03"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF05"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF06"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF07"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF0A"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FF0B"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF0C"}, {"category": "Pd", "mappings": {"default": {"default": ""}}, "key": "FF0D"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF0E"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF0F"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF1A"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF1B"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FF1C"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FF1D"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FF1E"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF1F"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF20"}, {"category": "Po", "mappings": {"default": {"default": ""}}, "key": "FF3C"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "FF3E"}, {"category": "Pc", "mappings": {"default": {"default": "⠱"}}, "key": "FF3F"}, {"category": "Sk", "mappings": {"default": {"default": ""}}, "key": "FF40"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FF5C"}, {"category": "Sm", "mappings": {"default": {"default": "⠈⠱"}}, "key": "FF5E"}, {"category": "Sm", "mappings": {"default": {"default": ""}}, "key": "FFE2"}, {"category": "Sk", "mappings": {"default": {"default": "⠱"}}, "key": "FFE3"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "FFE4"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "FFE8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "FFED"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "FFEE"}], "nemeth/symbols/math_whitespace.min": [{"locale": "ne<PERSON>h"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "0020"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "00A0"}, {"category": "Cf", "mappings": {"default": {"default": "⠤"}}, "key": "00AD"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2000"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2001"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2002"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2003"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2004"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2005"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2006"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2007"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2008"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "2009"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "200A"}, {"category": "Cf", "mappings": {"default": {"default": "⠀"}}, "key": "200B"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "200C"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "200D"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "200E"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "200F"}, {"category": "Zl", "mappings": {"default": {"default": ""}}, "key": "2028"}, {"category": "Zp", "mappings": {"default": {"default": ""}}, "key": "2029"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "202A"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "202B"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "202C"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "202D"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "202E"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "202F"}, {"category": "Zs", "mappings": {"default": {"default": "⠀"}}, "key": "205F"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "2060"}, {"category": "Cf", "mappings": {"default": {"default": "⠀"}}, "key": "2061"}, {"category": "Cf", "mappings": {"default": {"default": "⠈⠡"}}, "key": "2062"}, {"category": "Cf", "mappings": {"default": {"default": "⠠"}}, "key": "2063"}, {"category": "Cf", "mappings": {"default": {"default": "⠬"}}, "key": "2064"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "206A"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "206B"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "206E"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "206F"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "FEFF"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "FFF9"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "FFFA"}, {"category": "Cf", "mappings": {"default": {"default": ""}}, "key": "FFFB"}], "nemeth/symbols/other_stars.min": [{"locale": "ne<PERSON>h"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "23E8"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2605"}, {"category": "So", "mappings": {"default": {"default": "⠫⠎"}}, "key": "2606"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "26AA"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "26AB"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2705"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2713"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2714"}, {"category": "So", "mappings": {"default": {"default": "⠈⠡"}}, "key": "2715"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2716"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2717"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2718"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "271B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "271C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2720"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2721"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2722"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2723"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2724"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2725"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2726"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2727"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2728"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2729"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "272A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "272B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "272C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "272D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "272E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "272F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2730"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2731"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2732"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2733"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2734"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2735"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2736"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2739"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "273A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "273B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "273C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "273D"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "273E"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "273F"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2740"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2741"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2742"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2743"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2744"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2745"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2746"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2747"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2748"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "2749"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "274A"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "274B"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "274C"}, {"category": "So", "mappings": {"default": {"default": ""}}, "key": "274D"}], "nemeth/units/energy.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/length.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/memory.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/other.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/speed.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/temperature.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/time.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/volume.min": [{"locale": "ne<PERSON>h"}], "nemeth/units/weight.min": [{"locale": "ne<PERSON>h"}], "nemeth/rules/nemeth.min": {"locale": "ne<PERSON>h", "modality": "braille", "domain": "default", "rules": [["Precondition", "stree", "default", "self::stree", "CQFresetNesting"], ["Rule", "direct-speech", "default", "[t] @ext-speech (grammar:literal)", "self::*[@ext-speech]", "priority=Infinity"], ["Precondition", "unknown", "default", "self::unknown"], ["Precondition", "protected", "default", "self::*[@role=\"protected\"]"], ["Precondition", "omit-empty", "default", "self::empty"], ["Precondition", "blank-empty", "default", "self::empty", "count(../*)=1", "name(../..)=\"cell\" or name(../..)=\"line\""], ["Precondition", "font", "default", "self::*[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\"", "@font!=\"fullwidth\"", "@font!=\"monospace\""], ["Precondition", "font-identifier-short", "default", "self::identifier[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\"", "string-length(text())=1", "@font=\"normal\" or @font=\"fullwidth\" or @font=\"monospace\"", "\"\"=translate(text(), \"abcdefghijklmnopqrstuvwxyzαβγδεζηθικλμνξοπρςστυφχψωABCDEFGHIJKLMNOPQRSTUVWXYZΑΒΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")"], ["Precondition", "font-identifier-english", "default", "self::identifier[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\"", "string-length(text())=1", "\"\"=translate(text(), \"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\", \"\")"], ["Precondition", "multi-caps-english", "default", "self::identifier[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\"", "string-length(text())>1", "@font=\"normal\" or @font=\"fullwidth\" or @font=\"monospace\"", "\"\"=translate(text(), \"ABCDEFGHIJKLMNOPQRSTUVWXYZ\", \"\")"], ["Precondition", "font-identifier", "default", "self::identifier[@font]", "not(contains(@grammar, \"ignoreFont\"))", "@role!=\"unit\"", "string-length(text())=1", "@font=\"normal\" or @font=\"fullwidth\" or @font=\"monospace\""], ["Precondition", "omit-font", "default", "self::identifier[@font=\"italic\"]", "string-length(text())=1", "@role!=\"greekletter\"", "not(contains(@grammar, \"ignoreFont\"))", "\"\"!=translate(text(), \"αβγδεζηθικλμνξοπρςστυφχψωΓΔΕΖΗΘΙΚΛΜΝΞΟΠΡΣΣΤΥΦΧΨΩ\", \"\")"], ["Precondition", "number-indicator", "default", "self::number", "@font=\"normal\" or @font=\"fullwidth\" or @font=\"monospace\"", "contains(@annotation, \"nemeth:number\")", "not(ancestor::sqrt)", "not(ancestor::root)", "not(ancestor::fraction)", "@role!=\"othernumber\""], ["Precondition", "number", "default", "self::number"], ["Precondition", "number-font-indicator", "default", "self::number[contains(@grammar, \"ignoreFont\")]", "\"\" = translate(text(), \"0123456789.,\", \"\")"], ["Precondition", "mixed-number", "default", "self::number", "@role=\"mixed\""], ["Precondition", "number-with-chars", "default", "self::number", "\"\" != translate(text(), \"0123456789.,\", \"\")", "text() != translate(text(), \"0123456789.,\", \"\")"], ["Precondition", "number-baseline", "default", "self::number", "not(contains(@grammar, \"ignoreFont\"))", "preceding-sibling::identifier", "preceding-sibling::*[1][@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Precondition", "number-baseline-font", "default", "self::number", "@font", "not(contains(@grammar, \"ignoreFont\"))", "@font!=\"normal\"", "@font!=\"fullwidth\"", "@font!=\"monospace\"", "preceding-sibling::identifier", "preceding-sibling::*[@role=\"latinletter\" or @role=\"greekletter\" or @role=\"otherletter\"]", "parent::*/parent::infixop[@role=\"implicit\"]"], ["Precondition", "identifier", "default", "self::identifier", "@role=\"protected\""], ["Precondition", "english-letter", "default", "self::identifier", "@role=\"latinletter\"", "@font=\"italic\" or @font=\"normal\" or @font=\"fullwidth\" or @font=\"monospace\""], ["Precondition", "prefix", "default", "self::prefixop"], ["Precondition", "prefix-geometry", "default", "self::prefixop[@role=\"geometry\"]"], ["Precondition", "postfix", "default", "self::postfixop"], ["Precondition", "binary-operation", "default", "self::infixop"], ["Precondition", "element", "default", "self::infixop[contains(@role, \"element\")]"], ["Precondition", "implicit", "default", "self::infixop", "@role=\"implicit\""], ["<PERSON><PERSON>", "implicit", "self::infixop", "@role=\"leftsuper\" or @role=\"leftsub\" or @role=\"rightsuper\" or @role=\"rightsub\""], ["Precondition", "function-named", "default", "self::appl"], ["Precondition", "function-prefix", "default", "self::prefixop", "content/*[1][@role=\"infix function\"]"], ["Precondition", "function-infix", "default", "self::infixop", "@role=\"infix function\""], ["Precondition", "function-simple", "default", "self::appl", "children/*[1][@role=\"simple function\"]"], ["Precondition", "fences-open-close", "default", "self::fenced"], ["Precondition", "fences-neutral", "default", "self::fenced", "@role=\"neutral\""], ["Precondition", "fences-parallel", "default", "self::fenced[@role=\"metric\"]", "content/*[1][text()]=\"∥\""], ["Precondition", "text", "default", "self::text"], ["Precondition", "factorial-space", "default", "self::punctuation[@role=\"exclamation\"]", "name(preceding-sibling::*[1])!=\"text\""], ["Precondition", "factorial", "default", "self::punctuation[@role=\"exclamation\"]", "name(preceding-sibling::*[1])!=\"text\"", "not(following::*) or name(following::*)=\"punctuation\""], ["Precondition", "single-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=1"], ["Precondition", "double-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=2"], ["Precondition", "triple-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=3"], ["Precondition", "quadruple-prime", "default", "self::punctuated", "@role=\"prime\"", "count(children/*)=4"], ["Precondition", "hyperfraction", "default", "self::fraction", "CQFhyperFraction"], ["<PERSON><PERSON>", "hyperfraction", "self::fraction", "contains(@grammar, \"hyperfraction\")"], ["Precondition", "fraction", "default", "self::fraction"], ["Precondition", "bevelled-fraction", "default", "self::fraction", "contains(@annotation, \"general:bevelled\")"], ["Precondition", "sqrt", "default", "self::sqrt"], ["Precondition", "root", "default", "self::root"], ["Precondition", "arrow-underscore", "default", "self::underscore", "name(children/*[1])=\"relation\"", "children/*[1][@role=\"arrow\"]", "children/*[1][text()]=\"→\""], ["Precondition", "arrow-overscore", "default", "self::overscore", "name(children/*[1])=\"relation\"", "children/*[1][@role=\"arrow\"]", "children/*[1][text()]=\"→\""], ["Precondition", "limboth", "default", "self::limboth", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limlower", "default", "self::lim<PERSON>er", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limupper", "default", "self::limupper", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower", "self::underscore", "@role=\"limit function\"", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limlower", "self::underscore", "children/*[2][@role!=\"underaccent\"]", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["<PERSON><PERSON>", "limupper", "self::overscore", "children/*[2][@role!=\"overaccent\"]", "name(../..)=\"underscore\" or name(../..)=\"overscore\"", "following-sibling::*[@role!=\"underaccent\" and @role!=\"overaccent\"]"], ["Precondition", "limboth-end", "default", "self::limboth"], ["Precondition", "limlower-end", "default", "self::lim<PERSON>er"], ["Precondition", "limupper-end", "default", "self::limupper"], ["<PERSON><PERSON>", "limlower-end", "self::underscore", "@role=\"limit function\""], ["<PERSON><PERSON>", "limlower-end", "self::underscore"], ["<PERSON><PERSON>", "limupper-end", "self::overscore"], ["Precondition", "integral-index", "default", "self::integral"], ["Precondition", "integral", "default", "self::limboth", "@role=\"integral\""], ["Precondition", "bigop", "default", "self::bigop"], ["Precondition", "relseq", "default", "self::relseq"], ["Precondition", "mult<PERSON>", "default", "self::multirel"], ["Precondition", "subscript", "default", "self::subscript"], ["Precondition", "subscript-simple", "default", "self::subscript[@role!=\"unknown\"]", "name(./children/*[1])=\"identifier\" or name(./children/*[1])=\"function\"", "name(./children/*[2])=\"number\"", "./children/*[2][@role!=\"mixed\"]", "./children/*[2][@role!=\"othernumber\"]"], ["Precondition", "subscript-baseline", "default", "self::subscript", "@role!=\"subsup\"", "following::*", "@role!=\"prefix function\"", "name(following::*[1]/../..)!=\"relseq\"", "name(following::*[1]/../..)!=\"multirel\"", "name(following::*[1])!=\"punctuation\"", "not(name(following-sibling::subscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"subscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\"))", "not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "following::*", "not(following-sibling::*)", "name(following::*[1])!=\"punctuation\"", "ancestor::fenced|ancestor::root|ancestor::sqrt|ancestor::punctuated|ancestor::fraction", "not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "ancestor::fenced", "name(following::*[1])!=\"punctuation\""], ["<PERSON><PERSON>", "subscript-baseline", "self::subscript", "following::*", "not(following-sibling::*)", "name(following::*[1])!=\"punctuation\"", "@embellished"], ["Precondition", "subscript-empty-sup", "default", "self::subscript", "name(children/*[2])=\"infixop\"", "name(children/*[2][@role=\"implicit\"]/children/*[1])=\"superscript\"", "name(children/*[2]/children/*[1]/children/*[1])=\"empty\""], ["<PERSON><PERSON>", "subscript-empty-sup", "self::subscript", "name(children/*[2])=\"superscript\"", "name(children/*[2]/children/*[1])=\"empty\""], ["Precondition", "superscript", "default", "self::superscript"], ["Precondition", "superscript-baseline", "default", "self::superscript", "following::*", "@role!=\"prefix function\"", "name(following::*[1]/../..)!=\"relseq\"", "name(following::*[1]/../..)!=\"multirel\"", "name(following::*[1])!=\"punctuation\"", "not(name(following-sibling::superscript/children/*[1])=\"empty\" or (name(following-sibling::infixop[@role=\"implicit\"]/children/*[1])=\"superscript\" and name(following-sibling::*/children/*[1]/children/*[1])=\"empty\")) and not(following-sibling::*[@role=\"rightsuper\" or @role=\"rightsub\" or @role=\"leftsub\" or @role=\"leftsub\"])", "not(children/*[2][@role=\"prime\"])"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "name(following::*[1])!=\"punctuation\"", "ancestor::punctuated", "ancestor::*/following-sibling::* and not(ancestor::punctuated[@role=\"leftsuper\" or @role=\"rightsub\" or @role=\"rightsuper\" or @role=\"rightsub\"])"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "name(following::*[1])!=\"punctuation\"", "ancestor::fraction|ancestor::fenced|ancestor::root|ancestor::sqrt"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "name(following::*[1])!=\"punctuation\"", "name(following::*[1]/../..)!=\"relseq\"", "name(following::*[1]/../..)!=\"multirel\"", "not(@embellished)", "CGFbaselineConstraint"], ["<PERSON><PERSON>", "superscript-baseline", "self::superscript", "not(following-sibling::*)", "name(following::*[1])!=\"punctuation\"", "@embellished", "not(children/*[2][@role=\"prime\"])"], ["Precondition", "superscript-empty-sub", "default", "self::superscript", "name(children/*[2])=\"infixop\"", "name(children/*[2][@role=\"implicit\"]/children/*[1])=\"subscript\"", "name(children/*[2]/children/*[1]/children/*[1])=\"empty\""], ["<PERSON><PERSON>", "superscript-empty-sub", "self::superscript", "name(children/*[2])=\"subscript\"", "name(children/*[2]/children/*[1])=\"empty\""], ["Precondition", "prime", "default", "self::superscript", "children/*[2]", "children/*[2][@role=\"prime\"]"], ["Precondition", "prime-subscript", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)"], ["Precondition", "prime-subscript-baseline", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "following-sibling::*"], ["<PERSON><PERSON>", "prime-subscript-baseline", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "not(following-sibling::*)", "@embellished"], ["Precondition", "prime-subscript-simple", "default", "self::superscript", "children/*[2][@role=\"prime\"]", "name(children/*[1])=\"subscript\"", "name(children/*[1]/children/*[1])=\"identifier\"", "name(children/*[1]/children/*[2])=\"number\"", "children/*[1]/children/*[2][@role!=\"mixed\"]", "children/*[1]/children/*[2][@role!=\"othernumber\"]"], ["Precondition", "overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]"], ["Precondition", "overscore-modified", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "contains(@grammar, \"modified\")"], ["Precondition", "double-overscore", "default", "self::overscore", "children/*[2][@role=\"overaccent\"]", "name(children/*[1])=\"overscore\"", "children/*[1]/children/*[2][@role=\"overaccent\"]"], ["Precondition", "underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]"], ["Precondition", "underscore-modified", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]", "contains(@grammar, \"modified\")"], ["Precondition", "double-underscore", "default", "self::underscore", "children/*[2][@role=\"underaccent\"]", "name(children/*[1])=\"underscore\"", "children/*[1]/children/*[2][@role=\"underaccent\"]"], ["Precondition", "modifier-bar", "default", "self::operator[contains(@grammar, \"modifier\")]", "contains(@annotation, \"accent:bar\")"], ["Precondition", "matrix", "default", "self::matrix"], ["<PERSON><PERSON>", "matrix", "self::vector"], ["Rule", "binomial", "default", "[n] content/*[1]; [n] children/*[1]; [t] \"⠩\"; [n] children/*[2]; [n] content/*[2]", "self::vector[@role=\"binomial\"]"], ["Precondition", "matrix-row", "default", "self::row"], ["Precondition", "vector-line", "default", "self::line", "@role=\"vector\""], ["Precondition", "empty-row", "default", "self::row", "count(children/*)=0"], ["Precondition", "matrix-cell", "default", "self::cell"], ["Precondition", "empty-cell", "default", "self::cell", "count(children/*)=0"], ["Precondition", "empty-table-cell", "default", "self::cell", "count(children/*)=0", "parent::*/parent::*/parent::*/parent::table[@role=\"cayley\"]"], ["Precondition", "layout", "default", "self::table"], ["Precondition", "cayley", "default", "self::table[@role=\"cayley\"]"], ["Precondition", "cases", "default", "self::cases"], ["<PERSON><PERSON>", "layout", "self::multiline"], ["Precondition", "line", "default", "self::line"], ["Precondition", "empty-line", "default", "self::line", "count(children/*)=0", "not(content)"], ["Precondition", "row-with-label", "default", "self::row", "content"], ["<PERSON><PERSON>", "row-with-label", "self::line", "content"], ["Precondition", "cycle", "default", "self::matrix[@role=\"cycle\"]"], ["Precondition", "enclose", "default", "self::enclose"], ["Precondition", "overbar", "default", "self::enclose", "@role=\"top\""], ["Precondition", "bar-above", "default", "self::overscore", "contains(@role,\"letter\") or contains(@role,\"integer\")", "string-length(children/*[1][text()])=1", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "bar-below", "default", "self::underscore", "contains(@role,\"letter\") or contains(@role,\"integer\")", "string-length(children/*[1][text()])=1", "children/*[2][contains(@annotation, \"accent:bar\")]"], ["Precondition", "underbar", "default", "self::enclose", "@role=\"bottom\""], ["Precondition", "leftbar", "default", "self::enclose", "@role=\"left\""], ["Precondition", "rightbar", "default", "self::enclose", "@role=\"right\""], ["Precondition", "crossout", "default", "self::enclose", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel", "default", "self::overscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["<PERSON><PERSON>", "cancel", "self::underscore", "@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\""], ["Precondition", "cancel-reverse", "default", "self::overscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["<PERSON><PERSON>", "cancel-reverse", "self::underscore", "name(children/*[2])=\"enclose\"", "children/*[2][@role=\"updiagonalstrike\" or @role=\"downdiagonalstrike\" or @role=\"horizontalstrike\"]"], ["Precondition", "end-punct", "default", "self::punctuated", "@role=\"endpunct\""], ["Precondition", "start-punct", "default", "self::punctuated", "@role=\"startpunct\""], ["Rule", "punctuation", "default", "[n] text() (annotation:punctuation, engine:style=literary)", "self::punctuation", "@role!=\"prime\""], ["Precondition", "punctuation-point", "default", "self::punctuation", "@role=\"fullstop\"", "contains(@annotation, \"nemeth:number\")", "following::*"], ["Precondition", "punctuated", "default", "self::punctuated"], ["Precondition", "punctuation-comma", "default", "self::punctuation[@role=\"comma\" or @role=\"semicolon\"]", "parent::*/parent::punctuated", "following-sibling::*"], ["Precondition", "punctuation-colon-mapping", "default", "self::punctuation[@role=\"colon\"]", "following-sibling::relseq[@role=\"arrow\"]"], ["Precondition", "punctuation-colon-ratio", "default", "self::punctuation[@role=\"colon\"]", "preceding-sibling::*", "following-sibling::*", "name(preceding-sibling::*)=name(following-sibling::*)", "preceding-sibling::*[@role=following-sibling::*/@role]"], ["Precondition", "punctuation-ellipsis-cell", "default", "self::punctuation[@role=\"ellipsis\"]", "count(../*)=1", "name(../..)=\"cell\" or name(../..)=\"line\""], ["<PERSON><PERSON>", "punctuation-ellipsis-cell", "self::punctuation[@role=\"ellipsis\"]", "children/*"], ["Precondition", "punctuation-ellipses-both", "default", "self::punctuation[@role=\"ellipsis\"]", "not(contains(@grammar, \"noellipsis\"))", "parent::*/parent::punctuated", "following-sibling::*", "name(preceding-sibling::*[1])!=\"punctuation\"", "name(following-sibling::*[1])!=\"punctuation\""], ["Precondition", "punctuation-ellipses-left", "default", "self::punctuation[@role=\"ellipsis\"]", "not(contains(@grammar, \"noellipsis\"))", "preceding-sibling::*", "name(preceding-sibling::*[1])!=\"punctuation\""], ["Precondition", "punctuation-ellipses-right", "default", "self::punctuation[@role=\"ellipsis\"]", "not(contains(@grammar, \"noellipsis\"))", "following-sibling::*", "name(following-sibling::*[1])!=\"punctuation\""], ["Rule", "long-bar", "default", "[t] \"⠤⠤⠤⠤\"", "self::punctuation[@role=\"dash\"]", "children/*"], ["Precondition", "reference-sign", "default", "self::superscript", "name(children/*[1])=\"text\" or (name(children/*[1])=\"punctuated\" and children/*[1][@role=\"text\"])", "name(children/*[2])=\"operator\" or name(children/*[2])=\"punctuation\""], ["Precondition", "reference-number", "default", "self::superscript", "name(children/*[1])=\"text\" or (name(children/*[1])=\"punctuated\" and children/*[1][@role=\"text\"])", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]"], ["Rule", "space", "default", "[t] \"⠀\"", "self::text[@role=\"space\"]"], ["Generator", "CGFtensorRules"], ["Rule", "unit", "default", "[n] text() (engine:style=literary)", "self::identifier[@role=\"unit\"]"], ["Rule", "question-underscore", "default", "[t] \"⠿\"", "self::underscore[@role=\"question\"]"], ["Rule", "question-rel", "default", "[t] \"⠀⠿⠀\"", "self::punctuation[@role=\"question\"]", "following-sibling::infixop or preceding-sibling::infixop"], ["Rule", "relation", "default", "[m] CQFspaceoutNumber (separator:\"⠐\")", "self::relation", "string-length(text())>1"], ["Rule", "operator", "default", "[m] CQFspaceoutNumber (separator:\"⠐\")", "self::operator", "string-length(text())>1"]], "annotators": ["number", "depth"]}, "nemeth/rules/nemeth_actions.min": {"locale": "ne<PERSON>h", "modality": "braille", "domain": "default", "kind": "actions", "rules": [["Action", "stree", "[n] ./*[1]"], ["Action", "unknown", "[n] text()"], ["Action", "protected", "[n] text()"], ["Action", "omit-empty", "[p] (pause:100)"], ["Action", "blank-empty", "[t] \"⠀\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-identifier-short", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "font-identifier-english", "[t] @font (grammar:localFont); [t] \"⠰\"; [n] . (grammar:ignoreFont=@font)"], ["Action", "multi-caps-english", "[t] \"⠠⠠\"; [n] . (grammar:ignoreFont=\"⠠\");"], ["Action", "font-identifier", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "omit-font", "[n] . (grammar:ignoreFont=@font)"], ["Action", "number-indicator", "[t] \"⠼\" (layout:numberIndicator); [n] text() (pause:10)"], ["Action", "number", "[n] text() (layout:number)"], ["Action", "number-font-indicator", "[t] \"⠼\"; [n] text() (pause:10)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"⠸⠹\"; [n] children/*[2]/children/*[1]; [t] \"⠌\"; [n] children/*[2]/children/*[2]; [t] \"⠸⠼\""], ["Action", "number-with-chars", "[t] \"⠼\"; [m] CQFspaceoutNumber"], ["Action", "number-baseline", "[t] \"⠐\"; [n] text()"], ["Action", "number-baseline-font", "[t] \"⠐\"; [t] @font; [n] . (grammar:ignoreFont=@font)"], ["Action", "identifier", "[n] text()"], ["Action", "english-letter", "[n] text() (layout:english, grammar:ignoreEnglish)"], ["Action", "prefix", "[m] content/*; [n] children/*[1]"], ["Action", "prefix-geometry", "[n] content/*[1]; [t] \"⠀\"; [n] children/*[1]"], ["Action", "postfix", "[n] children/*[1]; [m] content/*"], ["Action", "binary-operation", "[m] children/* (sepFunc:CTFcontentIterator);"], ["Action", "element", "[m] children/* (sepFunc:CTFrelationIterator);"], ["Action", "implicit", "[m] children/* (sepFunc:CTFimplicitIterator);"], ["Action", "function-named", "[n] children/*[1]; [t] \"⠀\"; [n] children/*[2]"], ["Action", "function-prefix", "[n] content/*[1]; [t] \"⠀\"; [n] children/*[1]"], ["Action", "function-infix", "[n] children/*[1]; [n] content/*[1]; [t] \"⠀\"; [n] children/*[2]"], ["Action", "function-simple", "[n] children/*[1]; [n] children/*[2];"], ["Action", "fences-open-close", "[n] content/*[1]; [n] children/*[1]; [n] content/*[2]"], ["Action", "fences-neutral", "[n] content/*[1]; [n] children/*[1]; [n] content/*[2]"], ["Action", "fences-parallel", "[t] \"⠳⠳\"; [n] children/*[1]; [t] \"⠳⠳\""], ["Action", "text", "[n] text() (engine:style=literary)"], ["Action", "factorial-space", "[t] \"⠯⠀\""], ["Action", "factorial", "[t] \"⠯\""], ["Action", "single-prime", "[t] \"⠄\""], ["Action", "double-prime", "[t] \"⠄⠄\""], ["Action", "triple-prime", "[t] \"⠄⠄⠄\""], ["Action", "quadruple-prime", "[t] \"⠄⠄⠄⠄\""], ["Action", "hyperfraction", "[p] (layout:beginfraction); [t] CSFopenFraction; [n] children/*[1] (layout:numerator, grammar:hyperfraction); [t] CSFoverFraction; [n] children/*[2] (layout:denominator, grammar:hyperfraction); [t] CSFcloseFraction; [p] (layout:endfraction)"], ["Action", "fraction", "[t] CSFopenFraction; [n] children/*[1]; [t] CSFoverFraction; [n] children/*[2]; [t] CSFcloseFraction"], ["Action", "bevelled-fraction", "[t] CSFopenFraction; [n] children/*[1]; [t] CSFoverBevFraction; [n] children/*[2]; [t] CSFcloseFraction"], ["Action", "sqrt", "[t] CSFopenRadical; [n] children/*[1]; [t] CSFcloseRadical"], ["Action", "root", "[t] CSFindexRadical; [n] children/*[1];[t] \"⠜\"; [n] children/*[2]; [t] CSFcloseRadical"], ["Action", "arrow-underscore", "[t] \"⠐\"; [t] \"⠫⠒⠒⠕\"; [t] CSFunderscript; [n] children/*[2]; [t] \"⠻\""], ["Action", "arrow-overscore", "[t] \"⠐\"; [t] \"⠫⠒⠒⠕\"; [t] CSFoverscript; [n] children/*[2]; [t] \"⠻\""], ["Action", "limboth", "[t] \"⠐\"; [n] children/*[1]; [t] CSFunderscript; [n] children/*[2];[t] CSFoverscript; [n] children/*[3]"], ["Action", "limlower", "[t] \"⠐\"; [n] children/*[1]; [t] CSFunderscript; [n] children/*[2];"], ["Action", "limupper", "[t] \"⠐\"; [n] children/*[1]; [t] CSFoverscript; [n] children/*[2];"], ["Action", "limboth-end", "[t] \"⠐\"; [n] children/*[1]; [t] CSFunderscript; [n] children/*[2];[t] CSFoverscript; [n] children/*[3]; [t] \"⠻\""], ["Action", "limlower-end", "[t] \"⠐\"; [n] children/*[1]; [t] CSFunderscript; [n] children/*[2]; [t] \"⠻\""], ["Action", "limupper-end", "[t] \"⠐\"; [n] children/*[1]; [t] CSFoverscript; [n] children/*[2]; [t] \"⠻\""], ["Action", "integral-index", "[n] children/*[1]; [n] children/*[2]; [n] children/*[3];"], ["Action", "integral", "[n] children/*[1]; [t] \"⠰\"; [n] children/*[2];[t] \"⠘\"; [n] children/*[3]; [t] \"⠐\""], ["Action", "bigop", "[n] children/*[1]; [n] children/*[2];"], ["Action", "relseq", "[m] children/* (sepFunc:CTFrelationIterator)"], ["Action", "mult<PERSON>", "[m] children/* (sepFunc:CTFrelationIterator)"], ["Action", "subscript", "[n] children/*[1]; [t] CSFsubscript; [n] children/*[2]"], ["Action", "subscript-simple", "[n] children/*[1]; [n] children/*[2]"], ["Action", "subscript-baseline", "[n] children/*[1]; [t] CSFsubscript; [n] children/*[2]; [t] CSFbaseline"], ["Action", "subscript-empty-sup", "[n] children/*[1]; [n] children/*[2]"], ["Action", "superscript", "[n] children/*[1]; [t] CSFsuperscript; [n] children/*[2]"], ["Action", "superscript-baseline", "[n] children/*[1]; [t] CSFsuperscript; [n] children/*[2];[t] CSFbaseline"], ["Action", "superscript-empty-sub", "[n] children/*[1]; [n] children/*[2]"], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "prime-subscript", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscript; [n] children/*[1]/children/*[2]"], ["Action", "prime-subscript-baseline", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [t] CSFsubscript; [n] children/*[1]/children/*[2]; [t] CSFbaseline"], ["Action", "prime-subscript-simple", "[n] children/*[1]/children/*[1]; [n] children/*[2]; [n] children/*[1]/children/*[2]"], ["Action", "overscore", "[t] \"⠐\"; [n] children/*[1]; [t] \"⠣\"; [n] children/*[2] (grammar:modifier); [t] \"⠻\""], ["Action", "overscore-modified", "[n] children/*[1]; [t] \"⠣\"; [n] children/*[2] (grammar:modifier)"], ["Action", "double-overscore", "[t] \"⠐\"; [n] children/*[1] (grammar:modified); [t] \"⠣\"; [n] children/*[2] (grammar:modifier); [t] \"⠻\""], ["Action", "underscore", "[t] \"⠐\"; [n] children/*[1]; [t] \"⠩\"; [n] children/*[2] (grammar:modifier); [t] \"⠻\""], ["Action", "underscore-modified", "[n] children/*[1]; [t] \"⠩\"; [n] children/*[2] (grammar:modifier)"], ["Action", "double-underscore", "[t] \"⠐\"; [n] children/*[1] (grammar:modified); [t] \"⠩\"; [n] children/*[2] (grammar:modifier); [t] \"⠻\""], ["Action", "modifier-bar", "[t] \"⠱\""], ["Action", "matrix", "[m] children/* (separator:\"⠀\", join:\"\", layout:matrix)"], ["Action", "matrix-row", "[p] (layout:beginrow); [n] ../../content/*[1] (grammar:enlargeFence, layout:fence); [m] children/* (separator:\"⠀\"); [n] ../../content/*[2] (grammar:enlargeFence, layout:fence); [p] (layout:endrow);"], ["Action", "vector-line", "[p] (layout:beginrow); [n] ../../content/*[1] (grammar:enlargeFence, layout:fence); [n] children/*[1] (separator:\"⠀\", layout:cell); [n] ../../content/*[2] (grammar:enlargeFence, layout:fence); [p] (layout:endrow);"], ["Action", "empty-row", "[t] \"⠀\" (pause:300)"], ["Action", "matrix-cell", "[n] children/*[1] (layout:cell)"], ["Action", "empty-cell", "[t] \"⠀\" (pause:300)"], ["Action", "empty-table-cell", "[t] \"⠀\" (pause: 300, layout:cell)"], ["Action", "cayley", "[m] children/* (separator:\"⠀\", join:\"\", layout:cayley)"], ["Action", "layout", "[m] children/* (separator:\"⠀\", join:\"\", layout:table)"], ["Action", "cases", "[p] (layout:begincases); [m] children/* (separator:\"⠀\"); [t] \"⠐\"; [p] (layout:endcases)"], ["Action", "line", "[p] (layout:beginrow); [p] (layout:fence); [m] children/* (layout:cell); [p] (layout:fence); [p] (layout:endrow)"], ["Action", "empty-line", "[t] \"⠀\""], ["Action", "row-with-label", "[m] children/* (separator:\"⠀\"); [t] \"⠀⠀⠀\"; [m] content/*"], ["Action", "cycle", "[n] content/*[1]; [m] children/*[1]/children/* (separator:\"⠀\", join:\"\"); [n] content/*[2];"], ["Action", "enclose", "[t] \"⠫\"; [t] @role (grammar:localEnclose); [t] \"⠸⠫\"; [n] children/*[1]; [t] \"⠻\""], ["Action", "overbar", "[t] \"⠐\"; [n] children/*[1]; [t] \"⠣⠱⠻\""], ["Action", "bar-above", "[n] children/*[1]; [t] \"⠱\""], ["Action", "bar-below", "[n] children/*[1]; [t] \"⠩⠱\""], ["Action", "underbar", "[t] \"⠐\"; [n] children/*[1]; [t] \"⠩⠱⠻\""], ["Action", "leftbar", "[t] \"⠳\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"⠳\""], ["Action", "crossout", "[t] \"⠪\"; [n] children/*[1]; [t] \"⠻\""], ["Action", "cancel", "[t] \"⠪\"; [n] children/*[1]/children/*[1]; [t] \"⠪\"; [n] children/*[2]; [t] \"⠻\""], ["Action", "cancel-reverse", "[t] \"⠪\"; [n] children/*[2]/children/*[1]; [t] \"⠪\"; [n] children/*[1]; [t] \"⠻\""], ["Action", "end-punct", "[n] children/*[1]; [n] children/*[2] (engine:style=literary)"], ["Action", "start-punct", "[n] content/*[1]; [m] children/*[position()>1]"], ["Action", "punctuation-point", "[n] text() (annotation:punctuation); [t] \"⠐\""], ["Action", "punctuated", "[m] children/*"], ["Action", "punctuation-comma", "[n] text() (annotation:punctuation); [t] \"⠀\""], ["Action", "punctuation-colon-mapping", "[n] text() (annotation:punctuation); [t] \"⠀\""], ["Action", "punctuation-colon-ratio", "[t] \"⠀⠐⠂⠀\""], ["Action", "punctuation-ellipsis-cell", "[t] \"⠄⠄⠄\""], ["Action", "punctuation-ellipses-both", "[t] \"⠀\"; [n] . (grammar:?noellipsis); [t] \"⠀\";"], ["Action", "punctuation-ellipses-left", "[t] \"⠀\"; [n] . (grammar:?noellipsis);"], ["Action", "punctuation-ellipses-right", "[n] . (grammar:?noellipsis); [t] \"⠀\";"], ["Action", "reference-sign", "[n] children/*[1]; [n] children/*[2]"], ["Action", "reference-number", "[n] children/*[1]; [t] \"⠈⠻\"; [n] children/*[2]; [t] \"⠐\""]]}}