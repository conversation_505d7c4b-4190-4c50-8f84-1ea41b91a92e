{"it/messages/alphabets.min": {"kind": "alphabets", "locale": "it", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["nabla", "alfa", "beta", "gamma", "delta", "epsilon", "zeta greca", "eta", "theta", "iota", "kappa greca", "lambda", "mu", "nu", "xi", "omicron", "pi greco", "rho", "sigma final", "sigma", "tau", "upsilon", "phi", "chi", "psi", "omega", "derivata parziale", "epsilon", "theta", "kappa", "phi", "rho", "pi"], "greekCap": ["Alfa", "Beta", "Gamma", "Delta", "Epsilon", "Zeta greca", "Eta", "Theta", "Iota", "Kappa greca", "Lambda", "Mu", "<PERSON>u", "Xi", "Omicron", "Pi greca", "Rho", "Theta", "Sigma", "Tau", "Upsilon", "Phi", "<PERSON>", "Psi", "Omega"], "capPrefix": {"default": "<PERSON><PERSON><PERSON>"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "it/messages/messages.min": {"kind": "messages", "locale": "it", "messages": {"MS": {"START": "inizio", "FRAC_V": "frazione", "FRAC_B": "frazione", "FRAC_S": "frazione", "END": "fine", "FRAC_OVER": "fratto", "ONCE": "una volta", "TWICE": "due volte", "NEST_FRAC": "annidamento", "ENDFRAC": "fine frazione", "SUPER": "super", "SUB": "sub", "SUP": "sup", "SUPERSCRIPT": "apice", "SUBSCRIPT": "pedice", "BASELINE": "linea di base", "BASE": "base", "NESTED": "anni<PERSON><PERSON>", "NEST_ROOT": "annidamento", "STARTROOT": "inizio radice", "ENDROOT": "fine radice", "ROOTINDEX": "indice radice", "ROOT": "radice", "INDEX": "indice Radice", "UNDER": "sotto", "UNDERSCRIPT": "sottos<PERSON>ritto", "OVER": "sopra", "OVERSCRIPT": "sopra<PERSON><PERSON><PERSON><PERSON>", "ENDSCRIPTS": "fine script"}, "MSroots": {}, "font": {"bold": "grassetto", "bold-fraktur": "grassetto gotico", "bold-italic": "grassetto italico", "bold-script": "grassetto script", "caligraphic": "calligrafico", "caligraphic-bold": "grassetto calligrafico", "double-struck": "grassetto da lavagna", "double-struck-italic": "grassetto da lavagna italico", "fraktur": "gotico", "fullwidth": "fullwidth", "italic": "italico", "monospace": "monospazio", "normal": "normale", "oldstyle": "vecchio stile", "oldstyle-bold": "vecchio stile grassetto", "script": "script", "sans-serif": "senza grazie", "sans-serif-italic": "senza grazie italico", "sans-serif-bold": "senza grazie grassetto", "sans-serif-bold-italic": "senza grazie grassetto italico", "unknown": "scon<PERSON><PERSON><PERSON>"}, "embellish": {"super": ["apice", "prefixCombiner"], "sub": ["pedice", "prefixCombiner"], "circled": ["cerchiato", "italianPostfix"], "parenthesized": "tra <PERSON>esi", "period": "punto", "negative-circled": ["cerchiato in negativo", "italianPostfix"], "double-circled": "do<PERSON>io cerchiato", "circled-sans-serif": "cerchiato senza grazie", "negative-circled-sans-serif": "cerchiato in negativo senza grazie", "comma": "virgola", "squared": "dentro quadrato", "negative-squared": "dentro quadrato in negativo"}, "role": {"addition": "addizione", "multiplication": "moltiplicazione", "subtraction": "sottrazione", "division": "divisione", "equality": "uguaglianza", "inequality": "disuguaglianza", "element": "elemento", "arrow": "freccia", "determinant": "determinante", "rowvector": "vettore riga", "binomial": "binomiale", "squarematrix": "matrice quadrata", "set empty": "insieme vuoto", "set extended": "estensione di insieme", "set singleton": "sing<PERSON>tto", "set collection": "collezione", "label": "<PERSON><PERSON><PERSON>", "multiline": "linee multiple", "matrix": "matrice", "vector": "vettore", "cases": "comando switch", "table": "tavola", "unknown": "scon<PERSON><PERSON><PERSON>"}, "enclose": {"longdiv": "divisione lunga", "actuarial": "simbolo attuario", "radical": "radice quadrata", "box": "riquadro", "roundedbox": "riquadro arrotondato", "circle": "cerchio", "left": "linea verticale sinistra", "right": "linea verticale destra", "top": "barra sopra", "bottom": "barra sotto", "updiagonalstrike": "cancellatura", "downdiagonalstrike": "cancellatura", "verticalstrike": "cancellatura verticale", "horizontalstrike": "cancellatura", "madruwb": "simbolo fattoriale arabo", "updiagonalarrow": "freccia diagonale", "phasorangle": "angolo fasore", "unknown": "divisione lunga"}, "navigate": {"COLLAPSIBLE": "collassabile", "EXPANDABLE": "espandibile", "LEVEL": "livello"}, "regexp": {"TEXT": "a-zA-ZàèìòùéóÀ", "NUMBER": "((\\d{1,3})(?=(.| ))((.| )\\d{3})*(\\,\\d+)?)|^\\d*\\,\\d+|^\\d+", "DECIMAL_MARK": ",", "DIGIT_GROUP": "\\.", "JOINER_SUBSUPER": " ", "JOINER_FRAC": " "}, "unitTimes": ""}}, "it/messages/numbers.min": {"kind": "numbers", "locale": "it", "messages": {"zero": "zero", "ones": ["", "uno", "due", "tre", "quattro", "cinque", "sei", "sette", "otto", "nove", "dieci", "undici", "dodici", "tredici", "qua<PERSON><PERSON><PERSON>", "quindici", "sedici", "diciassette", "<PERSON><PERSON><PERSON>", "diciannove"], "tens": ["", "", "venti", "trenta", "quaranta", "cinquanta", "sessanta", "<PERSON><PERSON><PERSON>", "ottanta", "<PERSON>vanta"], "large": ["", "mille", "milione", "milia<PERSON>", "bilione", "biliardo", "trilione", "triliardo", "quadrilione", "quadriliardo", "quntilione", "quintil<PERSON><PERSON>"], "special": {"onesOrdinals": ["zero", "primo", "secondo", "terzo", "quarto", "quinto", "sesto", "setti<PERSON>", "ottavo", "nono", "decimo"]}, "vulgarSep": " ", "numSep": ""}}, "it/si/prefixes.min": [{"a": "atto", "c": "centi", "da": "deca", "d": "deci", "E": "exa", "f": "femto", "G": "giga", "h": "etto", "k": "chilo", "M": "mega", "m": "milli", "n": "nano", "P": "peta", "p": "pico", "T": "tera", "Y": "yotta", "y": "yocto", "z": "zepto", "Z": "zetta", "µ": "micro", "μ": "micro"}], "it/functions/algebra.min": [{"locale": "it"}, {"key": "deg", "mappings": {"default": {"default": "grado"}}}, {"key": "det", "mappings": {"default": {"default": "determinante"}}}, {"key": "dim", "mappings": {"default": {"default": "dimensione"}}}, {"key": "hom", "mappings": {"default": {"default": "omomorfismo"}}}, {"key": "ker", "mappings": {"default": {"default": "nucleo"}}}, {"key": "Tr", "mappings": {"default": {"default": "traccia"}}}], "it/functions/elementary.min": [{"locale": "it"}, {"key": "log", "mappings": {"default": {"default": "logaritmo"}}}, {"key": "ln", "mappings": {"default": {"default": "logaritmo naturale"}, "clearspeak": {"default": "l n", "Log_LnAsNaturalLog": "logaritmo naturale"}}}, {"key": "lg", "mappings": {"default": {"default": "logaritmo in basi 10"}}}, {"key": "exp", "mappings": {"default": {"default": "esponenziale"}}}, {"key": "gcd", "mappings": {"default": {"default": "massimo comun divisore"}}, "names": ["mcd", "MCD"]}, {"key": "lcm", "mappings": {"default": {"default": "minimo comune multiplo"}}, "names": ["mcm", "MCM"]}, {"key": "arg", "mappings": {"default": {"default": "argomento"}}}, {"key": "im", "mappings": {"default": {"default": "la parte immaginaria del numero complesso"}}}, {"key": "re", "mappings": {"default": {"default": "la parte reale del numero complesso"}}}, {"key": "inf", "mappings": {"default": {"default": "estremo inferiore"}}}, {"key": "lim", "mappings": {"default": {"default": "limite"}}}, {"key": "liminf", "mappings": {"default": {"default": "limite inferiore"}}}, {"key": "limsup", "mappings": {"default": {"default": "limite superiore"}}}, {"key": "max", "mappings": {"default": {"default": "massimo"}}}, {"key": "min", "mappings": {"default": {"default": "minimo"}}}, {"key": "sup", "mappings": {"default": {"default": "estremo superiore"}}}, {"key": "<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "colimiti"}}}, {"key": "proj<PERSON>", "mappings": {"default": {"default": "limite proiettivo"}}}, {"key": "mod", "mappings": {"default": {"default": "modulo"}}}, {"key": "Pr", "mappings": {"default": {"default": "probabilità"}}}], "it/functions/hyperbolic.min": [{"locale": "it"}, {"key": "cosh", "mappings": {"default": {"default": "coseno <PERSON>o"}}}, {"key": "coth", "mappings": {"default": {"default": "cotangente iperbolica"}}}, {"key": "csch", "mappings": {"default": {"default": "cosecante iperbolica"}}}, {"key": "sech", "mappings": {"default": {"default": "secante i<PERSON>a"}}}, {"key": "sinh", "mappings": {"default": {"default": "seno iper<PERSON>o"}}}, {"key": "tanh", "mappings": {"default": {"default": "tangente iperbolica"}}}, {"key": "arcosh", "mappings": {"default": {"default": "settore coseno i<PERSON>o"}}, "names": ["settcosh"]}, {"key": "arcoth", "mappings": {"default": {"default": "settore co-tangente iperbolica"}}, "names": ["settcoth"]}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "settore co-secante iper<PERSON>a"}}, "names": ["settcsch"]}, {"key": "arsech", "mappings": {"default": {"default": "settore secante iperbolica"}}, "names": ["settsech"]}, {"key": "a<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "settore seno iperbolico"}}, "names": ["set<PERSON><PERSON><PERSON>"]}, {"key": "artanh", "mappings": {"default": {"default": "settore tangente iperbolica"}}, "names": ["settanh"]}], "it/functions/trigonometry.min": [{"locale": "it"}, {"key": "cos", "mappings": {"default": {"default": "coseno"}}}, {"key": "cot", "mappings": {"default": {"default": "cotangente"}}}, {"key": "csc", "mappings": {"default": {"default": "cosecante"}}}, {"key": "sec", "mappings": {"default": {"default": "secante"}}}, {"key": "sin", "mappings": {"default": {"default": "seno"}}, "names": ["sen"]}, {"key": "tan", "mappings": {"default": {"default": "tangente"}}}, {"key": "arccos", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}, "names": ["acos"]}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "arcocotangente"}}, "names": ["acot"]}, {"key": "arccsc", "mappings": {"default": {"default": "arcocosecante"}}, "names": ["acsc"]}, {"key": "arcsec", "mappings": {"default": {"default": "arcosecante"}}, "names": ["asec"]}, {"key": "arcsin", "mappings": {"default": {"default": "arcoseno"}}, "names": ["<PERSON><PERSON>", "asin", "asen", "asn"]}, {"key": "arctan", "mappings": {"default": {"default": "arcotangente"}}, "names": ["atan"]}], "it/symbols/digits_rest.min": [{"locale": "it"}, {"key": "00B2", "mappings": {"default": {"default": "apice 2"}}}, {"key": "00B3", "mappings": {"default": {"default": "apice 3"}}}, {"key": "00BC", "mappings": {"default": {"default": "un quarto"}}}, {"key": "00BD", "mappings": {"default": {"default": "un mezzo"}}}, {"key": "00BE", "mappings": {"default": {"default": "tre quarti"}}}, {"key": "2150", "mappings": {"default": {"default": "un settimo"}}}, {"key": "2151", "mappings": {"default": {"default": "un nono"}}}, {"key": "2152", "mappings": {"default": {"default": "un decimo"}}}, {"key": "2153", "mappings": {"default": {"default": "un terzo"}}}, {"key": "2154", "mappings": {"default": {"default": "due terzi"}}}, {"key": "2155", "mappings": {"default": {"default": "un quinto"}}}, {"key": "2156", "mappings": {"default": {"default": "due quinti"}}}, {"key": "2157", "mappings": {"default": {"default": "tre quinti"}}}, {"key": "2158", "mappings": {"default": {"default": "quattro quinti"}}}, {"key": "2159", "mappings": {"default": {"default": "un sesto"}}}, {"key": "215A", "mappings": {"default": {"default": "cinque sesti"}}}, {"key": "215B", "mappings": {"default": {"default": "un ottavo"}}}, {"key": "215C", "mappings": {"default": {"default": "tre ottavi"}}}, {"key": "215D", "mappings": {"default": {"default": "cinque ottavi"}}}, {"key": "215E", "mappings": {"default": {"default": "sette ottavi"}}}, {"key": "215F", "mappings": {"default": {"default": "numeratore uno"}}}, {"key": "2189", "mappings": {"default": {"default": "zero un terzo"}}}, {"key": "3248", "mappings": {"default": {"default": "dieci cerchiato su quadrato nero"}}}, {"key": "3249", "mappings": {"default": {"default": "venti cerchiato su quadrato nero"}}}, {"key": "324A", "mappings": {"default": {"default": "trenta cerchiato su quadrato nero"}}}, {"key": "324B", "mappings": {"default": {"default": "quaranta cerchiato su quadrato nero"}}}, {"key": "324C", "mappings": {"default": {"default": "cinquanta cerchiato su quadrato nero"}}}, {"key": "324D", "mappings": {"default": {"default": "sessanta cerchiato su quadrato nero"}}}, {"key": "324E", "mappings": {"default": {"default": "settanta cerchiato su quadrato nero"}}}, {"key": "324F", "mappings": {"default": {"default": "ottanta cerchiato su quadrato nero"}}}], "it/symbols/greek-rest.min": [{"locale": "it"}, {"key": "0394", "mappings": {"clearspeak": {"default": "triangolo", "TriangleSymbol_Delta": "Delta maiuscola"}}}], "it/symbols/greek-scripts.min": [{"locale": "it"}, {"key": "1D26", "mappings": {"default": {"default": "Gamma maiuscola piccola"}}}, {"key": "1D27", "mappings": {"default": {"default": "<PERSON><PERSON> ma<PERSON>cola piccola"}}}, {"key": "1D28", "mappings": {"default": {"default": "Pi greca maiuscola piccola"}}}, {"key": "1D29", "mappings": {"default": {"default": "<PERSON><PERSON> ma<PERSON>cola piccola"}}}, {"key": "1D2A", "mappings": {"default": {"default": "Psi ma<PERSON>cola piccola"}}}, {"key": "1D5E", "mappings": {"default": {"default": "gamma apice"}}}, {"key": "1D60", "mappings": {"default": {"default": "phi apice"}}}, {"key": "1D66", "mappings": {"default": {"default": "beta apice"}}}, {"key": "1D67", "mappings": {"default": {"default": "gamma pedice"}}}, {"key": "1D68", "mappings": {"default": {"default": "rho pedice"}}}, {"key": "1D69", "mappings": {"default": {"default": "phi pedice"}}}, {"key": "1D6A", "mappings": {"default": {"default": "chi pedice"}}}], "it/symbols/greek-symbols.min": [{"locale": "it"}, {"key": "03D0", "mappings": {"default": {"default": "beta arricciata"}}}, {"key": "03D7", "mappings": {"default": {"default": "simbolo greco Kai"}}}, {"key": "03F6", "mappings": {"default": {"default": "epsilon dritto invertito"}}}, {"key": "1D7CA", "mappings": {"default": {"default": "Digamma ma<PERSON>cola in grassetto"}}}, {"key": "1D7CB", "mappings": {"default": {"default": "digamma in grassetto"}}}], "it/symbols/hebrew_letters.min": [{"locale": "it"}, {"key": "2135", "mappings": {"default": {"default": "alef"}}}, {"key": "2136", "mappings": {"default": {"default": "bet"}}}, {"key": "2137", "mappings": {"default": {"default": "gimel"}}}, {"key": "2138", "mappings": {"default": {"default": "dalet"}}}], "it/symbols/latin-lower-double-accent.min": [{"locale": "it"}, {"key": "01D6", "mappings": {"default": {"default": "u con diaeresi e macron"}}}, {"key": "01D8", "mappings": {"default": {"default": "u con diaeresi e acuto"}}}, {"key": "01DA", "mappings": {"default": {"default": "u con diaeresi e caronte"}}}, {"key": "01DC", "mappings": {"default": {"default": "u con diaeresi e tomba"}}}, {"key": "01DF", "mappings": {"default": {"default": "a con diaeresis e macron"}}}, {"key": "01E1", "mappings": {"default": {"default": "a con punto e macron"}}}, {"key": "01ED", "mappings": {"default": {"default": "o con Ogonek e macron"}}}, {"key": "01FB", "mappings": {"default": {"default": "a con anello sopra e accento acuto"}}}, {"key": "022B", "mappings": {"default": {"default": "o con diaeresi e macron"}}}, {"key": "022D", "mappings": {"default": {"default": "o con tilde e macron"}}}, {"key": "0231", "mappings": {"default": {"default": "o con punto sopra e macron"}}}, {"key": "1E09", "mappings": {"default": {"default": "c con cediglia e accento acuto"}}}, {"key": "1E15", "mappings": {"default": {"default": "e con barra sopra e accento grave"}}}, {"key": "1E17", "mappings": {"default": {"default": "e con barra sopra e accento acuto"}}}, {"key": "1E1D", "mappings": {"default": {"default": "e con cediglia e con segno di vocale corta"}}}, {"key": "1E2F", "mappings": {"default": {"default": "i con dieresi ed accento acuto"}}}, {"key": "1E39", "mappings": {"default": {"default": "l con punto sotto e con barra sopra"}}}, {"key": "1E4D", "mappings": {"default": {"default": "o con tilde ed accento acuto"}}}, {"key": "1E4F", "mappings": {"default": {"default": "o con tilde e dieresi"}}}, {"key": "1E51", "mappings": {"default": {"default": "o con barra sopra ed accento grave"}}}, {"key": "1E53", "mappings": {"default": {"default": "o con barra sopra ed accento acuto"}}}, {"key": "1E5D", "mappings": {"default": {"default": "r con punto sotto e con barra sopra"}}}, {"key": "1E65", "mappings": {"default": {"default": "s con accento acuto e punto sopra"}}}, {"key": "1E67", "mappings": {"default": {"default": "s con caron e punto sopra"}}}, {"key": "1E69", "mappings": {"default": {"default": "s con punto sotto e punto sopra"}}}, {"key": "1E79", "mappings": {"default": {"default": "u con tilde ed accento acuto"}}}, {"key": "1E7B", "mappings": {"default": {"default": "u con barra sopra e dieresi"}}}, {"key": "1EA5", "mappings": {"default": {"default": "a con accento circonflesso ed accento acuto"}}}, {"key": "1EA7", "mappings": {"default": {"default": "a con accento circonflesso ed accento grave"}}}, {"key": "1EA9", "mappings": {"default": {"default": "a con accento circonflesso e gancio sopra"}}}, {"key": "1EAB", "mappings": {"default": {"default": "a con accento circonflesso e tilde"}}}, {"key": "1EAD", "mappings": {"default": {"default": "a con accento circonflesso e punto sotto"}}}, {"key": "1EAF", "mappings": {"default": {"default": "a con segno di vocale corta ed accento acuto"}}}, {"key": "1EB1", "mappings": {"default": {"default": "a con segno di vocale corta ed accento grave"}}}, {"key": "1EB3", "mappings": {"default": {"default": "a con segno di vocale corta e gancio sopra"}}}, {"key": "1EB5", "mappings": {"default": {"default": "a con segno di vocale corta e tilde"}}}, {"key": "1EB7", "mappings": {"default": {"default": "a con segno di vocale corta e punto sotto"}}}, {"key": "1EBF", "mappings": {"default": {"default": "e con accento circonflesso ed acuto"}}}, {"key": "1EC1", "mappings": {"default": {"default": "e con accento circonflesso e grave"}}}, {"key": "1EC3", "mappings": {"default": {"default": "e con accento circonflesso e gancio sopra"}}}, {"key": "1EC5", "mappings": {"default": {"default": "e con accento circonflesso e tilde"}}}, {"key": "1EC7", "mappings": {"default": {"default": "e con accento circonflesso e punto sotto"}}}, {"key": "1ED1", "mappings": {"default": {"default": "o con accento circonflesso e accento acuto"}}}, {"key": "1ED3", "mappings": {"default": {"default": "o con accento circonflesso e accento grave"}}}, {"key": "1ED5", "mappings": {"default": {"default": "o con accento circonflesso e gancio sopra"}}}, {"key": "1ED7", "mappings": {"default": {"default": "o con accento circonflesso e tilde"}}}, {"key": "1ED9", "mappings": {"default": {"default": "o con accento circonflesso e punto sotto"}}}, {"key": "1EDB", "mappings": {"default": {"default": "o con corno e accento acuto"}}}, {"key": "1EDD", "mappings": {"default": {"default": "o con corno e accento grave"}}}, {"key": "1EDF", "mappings": {"default": {"default": "o con corno e gancio sopra"}}}, {"key": "1EE1", "mappings": {"default": {"default": "o con corno e tilde"}}}, {"key": "1EE3", "mappings": {"default": {"default": "o con corno e punto sotto"}}}, {"key": "1EE9", "mappings": {"default": {"default": "u con corno e accento acuto"}}}, {"key": "1EEB", "mappings": {"default": {"default": "u con corno e accento grave"}}}, {"key": "1EED", "mappings": {"default": {"default": "u con corno e gancio sopra"}}}, {"key": "1EEF", "mappings": {"default": {"default": "u con corno e tilde"}}}, {"key": "1EF1", "mappings": {"default": {"default": "u con corno e punto sotto"}}}], "it/symbols/latin-lower-phonetic.min": [{"locale": "it"}, {"key": "00F8", "mappings": {"default": {"default": "o con tratto diagonale"}}}, {"key": "0111", "mappings": {"default": {"default": "d con tratto diagonale"}}}, {"key": "0127", "mappings": {"default": {"default": "h con tratto diagonale"}}}, {"key": "0142", "mappings": {"default": {"default": "l con tratto diagonale"}}}, {"key": "0167", "mappings": {"default": {"default": "t con tratto diagonale"}}}, {"key": "0180", "mappings": {"default": {"default": "b con tratto"}}}, {"key": "019B", "mappings": {"default": {"default": "lambda con tratto diagonale"}}}, {"key": "01B6", "mappings": {"default": {"default": "latina piccola lettera Z con tratto"}}}, {"key": "01BE", "mappings": {"default": {"default": "lettera latina capovolta fermata glottale con tratto"}}}, {"key": "01E5", "mappings": {"default": {"default": "g con tratto"}}}, {"key": "01FF", "mappings": {"default": {"default": "o con tratto e accento acuto"}}}, {"key": "023C", "mappings": {"default": {"default": "c con tratto"}}}, {"key": "0247", "mappings": {"default": {"default": "e con tratto"}}}, {"key": "0249", "mappings": {"default": {"default": "j con tratto"}}}, {"key": "024D", "mappings": {"default": {"default": "r con tratto"}}}, {"key": "024F", "mappings": {"default": {"default": "y con tratto"}}}, {"key": "025F", "mappings": {"default": {"default": "j senza punto con tratto"}}}, {"key": "0268", "mappings": {"default": {"default": "i con tratto"}}}, {"key": "0284", "mappings": {"default": {"default": "lettera minuscola latina Dotless J con tratto e gancio"}}}, {"key": "02A1", "mappings": {"default": {"default": "lettera latina fermata glottale con tratto"}}}, {"key": "02A2", "mappings": {"default": {"default": "lettera latina invertita fermata glottale con tratto"}}}, {"key": "1D13", "mappings": {"default": {"default": "o di traverso con tratto"}}}, {"key": "1D7C", "mappings": {"default": {"default": "iota con tratto"}}}, {"key": "1D7D", "mappings": {"default": {"default": "p con tratto"}}}, {"key": "1D7F", "mappings": {"default": {"default": "upsilon con tratto"}}}, {"key": "1E9C", "mappings": {"default": {"default": "s lunga con tratto diagonale"}}}, {"key": "1E9D", "mappings": {"default": {"default": "s lunga con tratto alto"}}}, {"key": "018D", "mappings": {"default": {"default": "delta capovolta"}}}, {"key": "1E9B", "mappings": {"default": {"default": "s lunga con punto sopra"}}}, {"key": "1E9F", "mappings": {"default": {"default": "delta"}}}, {"key": "0138", "mappings": {"default": {"default": "kra"}}}, {"key": "017F", "mappings": {"default": {"default": "s lunga"}}}, {"key": "0183", "mappings": {"default": {"default": "b con tratto in alto"}}}, {"key": "0185", "mappings": {"default": {"default": "lettera tono sei"}}}, {"key": "0188", "mappings": {"default": {"default": "c con gancio"}}}, {"key": "018C", "mappings": {"default": {"default": "d con tratto in alto"}}}, {"key": "0192", "mappings": {"default": {"default": "f con uncino"}}}, {"key": "0195", "mappings": {"default": {"default": "lettera Hv"}}}, {"key": "0199", "mappings": {"default": {"default": "k con gancio"}}}, {"key": "019A", "mappings": {"default": {"default": "l con barra"}}}, {"key": "019E", "mappings": {"default": {"default": "n con lunga gamba destra"}}}, {"key": "01A1", "mappings": {"default": {"default": "o con corno"}}}, {"key": "01A3", "mappings": {"default": {"default": "lettera Oi"}}}, {"key": "01A5", "mappings": {"default": {"default": "latina piccola lettera P con gancio"}}}, {"key": "01A8", "mappings": {"default": {"default": "lettera tono due"}}}, {"key": "01AA", "mappings": {"default": {"default": "Esh Loop invertita"}}}, {"key": "01AB", "mappings": {"default": {"default": "t con gancio palatale"}}}, {"key": "01AD", "mappings": {"default": {"default": "t con gancio"}}}, {"key": "01B0", "mappings": {"default": {"default": "u con corno"}}}, {"key": "01B4", "mappings": {"default": {"default": "y con gancio"}}}, {"key": "01B9", "mappings": {"default": {"default": "ezh capovolta"}}}, {"key": "01BA", "mappings": {"default": {"default": "ezh con coda"}}}, {"key": "01BD", "mappings": {"default": {"default": "lettera tono cinque"}}}, {"key": "01BF", "mappings": {"default": {"default": "wynn"}}}, {"key": "01C6", "mappings": {"default": {"default": "dz con Car<PERSON>"}}}, {"key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"key": "01E3", "mappings": {"default": {"default": "ae con macron"}}}, {"key": "01EF", "mappings": {"default": {"default": "ezh con caron"}}}, {"key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"key": "021D", "mappings": {"default": {"default": "yogh"}}}, {"key": "026E", "mappings": {"default": {"default": "lezh"}}}, {"key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"key": "0293", "mappings": {"default": {"default": "ezh con ricciolo"}}}, {"key": "02A4", "mappings": {"default": {"default": "dezh digrafo"}}}, {"key": "01DD", "mappings": {"default": {"default": "e capovolta"}}}, {"key": "01FD", "mappings": {"default": {"default": "ae con accento acuto"}}}, {"key": "0221", "mappings": {"default": {"default": "d con arricciatura"}}}, {"key": "0223", "mappings": {"default": {"default": "ou"}}}, {"key": "0225", "mappings": {"default": {"default": "z con gancio"}}}, {"key": "0234", "mappings": {"default": {"default": "l con ricciolo"}}}, {"key": "0235", "mappings": {"default": {"default": "n con arricciatura"}}}, {"key": "0236", "mappings": {"default": {"default": "t con ricciolo"}}}, {"key": "0238", "mappings": {"default": {"default": "db digrafo"}}}, {"key": "0239", "mappings": {"default": {"default": "qp digrafo"}}}, {"key": "023F", "mappings": {"default": {"default": "s con svolazzo alto"}}}, {"key": "0240", "mappings": {"default": {"default": "z con svolazzo alto"}}}, {"key": "0242", "mappings": {"default": {"default": "fermata glottale"}}}, {"key": "024B", "mappings": {"default": {"default": "q con coda a uncino"}}}, {"key": "0250", "mappings": {"default": {"default": "a capovolta"}}}, {"key": "0251", "mappings": {"default": {"default": "alfa latina"}}}, {"key": "0252", "mappings": {"default": {"default": "alfa latina capovolta"}}}, {"key": "0253", "mappings": {"default": {"default": "b con gancio"}}}, {"key": "0254", "mappings": {"default": {"default": "o aperta"}}}, {"key": "0255", "mappings": {"default": {"default": "c con arricciatura"}}}, {"key": "0256", "mappings": {"default": {"default": "d con gancio retroflesso"}}}, {"key": "0257", "mappings": {"default": {"default": "d con gancio"}}}, {"key": "0258", "mappings": {"default": {"default": "e invertita"}}}, {"key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"key": "025A", "mappings": {"default": {"default": "schwa con gancio"}}}, {"key": "025B", "mappings": {"default": {"default": "e aperta"}}}, {"key": "025C", "mappings": {"default": {"default": "e aperta invertita"}}}, {"key": "025D", "mappings": {"default": {"default": "e aperta invertita con gancio"}}}, {"key": "025E", "mappings": {"default": {"default": "epsilon invertita chiusa"}}}, {"key": "0260", "mappings": {"default": {"default": "e con gancio"}}}, {"key": "0261", "mappings": {"default": {"default": "script g"}}}, {"key": "0263", "mappings": {"default": {"default": "gamma"}}}, {"key": "0264", "mappings": {"default": {"default": "baby gamma"}}}, {"key": "0265", "mappings": {"default": {"default": "h capovolta"}}}, {"key": "0266", "mappings": {"default": {"default": "h con gancio"}}}, {"key": "0267", "mappings": {"default": {"default": "heng con gancio"}}}, {"key": "0269", "mappings": {"default": {"default": "iota"}}}, {"key": "026B", "mappings": {"default": {"default": "l con tilde centrale"}}}, {"key": "026C", "mappings": {"default": {"default": "l con cintura"}}}, {"key": "026D", "mappings": {"default": {"default": "l con gancio retroflesso"}}}, {"key": "026F", "mappings": {"default": {"default": "m capovolta"}}}, {"key": "0270", "mappings": {"default": {"default": "m capovolta con gamba lunga"}}}, {"key": "0271", "mappings": {"default": {"default": "m con gancio"}}}, {"key": "0272", "mappings": {"default": {"default": "n con gancio sinistro"}}}, {"key": "0273", "mappings": {"default": {"default": "n con gancio retroflesso"}}}, {"key": "0275", "mappings": {"default": {"default": "o barrata"}}}, {"key": "0277", "mappings": {"default": {"default": "omega chiusa"}}}, {"key": "0278", "mappings": {"default": {"default": "phi"}}}, {"key": "0279", "mappings": {"default": {"default": "r capovolta"}}}, {"key": "027A", "mappings": {"default": {"default": "r con gamba lunga"}}}, {"key": "027B", "mappings": {"default": {"default": "r con gancio"}}}, {"key": "027C", "mappings": {"default": {"default": "r con gamba lunga"}}}, {"key": "027D", "mappings": {"default": {"default": "r con coda"}}}, {"key": "027E", "mappings": {"default": {"default": "r con amo da pesca"}}}, {"key": "027F", "mappings": {"default": {"default": "r capovolta con amo"}}}, {"key": "0282", "mappings": {"default": {"default": "s con gancio"}}}, {"key": "0283", "mappings": {"default": {"default": "esh"}}}, {"key": "0285", "mappings": {"default": {"default": "esc invertita"}}}, {"key": "0286", "mappings": {"default": {"default": "esh con ricciolo"}}}, {"key": "0287", "mappings": {"default": {"default": "t capovolta"}}}, {"key": "0288", "mappings": {"default": {"default": "t con gancio retroflesso"}}}, {"key": "0289", "mappings": {"default": {"default": "u barrata"}}}, {"key": "028A", "mappings": {"default": {"default": "upsilon"}}}, {"key": "028B", "mappings": {"default": {"default": "latina piccola lettera V con gancio"}}}, {"key": "028C", "mappings": {"default": {"default": "v capovolta"}}}, {"key": "028D", "mappings": {"default": {"default": "w capovolta"}}}, {"key": "028E", "mappings": {"default": {"default": "y capovolta"}}}, {"key": "0290", "mappings": {"default": {"default": "z con gancio retroflesso"}}}, {"key": "0291", "mappings": {"default": {"default": "z con arricciatura"}}}, {"key": "0295", "mappings": {"default": {"default": "stop glottale invertito"}}}, {"key": "0296", "mappings": {"default": {"default": "stop glottale invertito capovolto"}}}, {"key": "0297", "mappings": {"default": {"default": "c allungata"}}}, {"key": "0298", "mappings": {"default": {"default": "bullseye"}}}, {"key": "029A", "mappings": {"default": {"default": "epsilon chiuso"}}}, {"key": "029E", "mappings": {"default": {"default": "k capovolta"}}}, {"key": "02A0", "mappings": {"default": {"default": "q con gancio"}}}, {"key": "02A3", "mappings": {"default": {"default": "digrafo dz"}}}, {"key": "02A5", "mappings": {"default": {"default": "digrafo dz con arricciatura"}}}, {"key": "02A6", "mappings": {"default": {"default": "digrafo ts"}}}, {"key": "02A7", "mappings": {"default": {"default": "digrafo tesh"}}}, {"key": "02A8", "mappings": {"default": {"default": "digrafo tc con ricciolo"}}}, {"key": "02A9", "mappings": {"default": {"default": "digrafo feng"}}}, {"key": "02AA", "mappings": {"default": {"default": "digrafo ls"}}}, {"key": "02AB", "mappings": {"default": {"default": "digrafo lz"}}}, {"key": "02AC", "mappings": {"default": {"default": "percussivo bilabiale"}}}, {"key": "02AD", "mappings": {"default": {"default": "percussivo bidentale"}}}, {"key": "02AE", "mappings": {"default": {"default": "h capovolta con amo"}}}, {"key": "02AF", "mappings": {"default": {"default": "h capovolta con amo e coda"}}}, {"key": "1D02", "mappings": {"default": {"default": "ae capovolta"}}}, {"key": "1D08", "mappings": {"default": {"default": "e aperta invertita"}}}, {"key": "1D09", "mappings": {"default": {"default": "i capovolta"}}}, {"key": "1D11", "mappings": {"default": {"default": "o di traverso"}}}, {"key": "1D12", "mappings": {"default": {"default": "o aperta di traverso"}}}, {"key": "1D14", "mappings": {"default": {"default": "oe invertita"}}}, {"key": "1D16", "mappings": {"default": {"default": "metà alta di o"}}}, {"key": "1D17", "mappings": {"default": {"default": "<PERSON>à bassa di o"}}}, {"key": "1D1D", "mappings": {"default": {"default": "u di traverso"}}}, {"key": "1D1E", "mappings": {"default": {"default": "u con dieresi di traverso"}}}, {"key": "1D1F", "mappings": {"default": {"default": "m di traverso"}}}, {"key": "1D24", "mappings": {"default": {"default": "spirale laringeo"}}}, {"key": "1D25", "mappings": {"default": {"default": "ain"}}}, {"key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"key": "1D6C", "mappings": {"default": {"default": "b con tilde centrale"}}}, {"key": "1D6D", "mappings": {"default": {"default": "d con tilde centrale"}}}, {"key": "1D6E", "mappings": {"default": {"default": "f con tilde centrale"}}}, {"key": "1D6F", "mappings": {"default": {"default": "m con tilde centrale"}}}, {"key": "1D70", "mappings": {"default": {"default": "n con tilde centrale"}}}, {"key": "1D71", "mappings": {"default": {"default": "p con tilde centrale"}}}, {"key": "1D72", "mappings": {"default": {"default": "r con tilde centrale"}}}, {"key": "1D73", "mappings": {"default": {"default": "r con amo e tilde centrale"}}}, {"key": "1D74", "mappings": {"default": {"default": "s con tilde centrale"}}}, {"key": "1D75", "mappings": {"default": {"default": "t con tilde centrale"}}}, {"key": "1D76", "mappings": {"default": {"default": "z con tilde centrale"}}}, {"key": "1D77", "mappings": {"default": {"default": "g capovolta"}}}, {"key": "1D79", "mappings": {"default": {"default": "g insulare"}}}, {"key": "1D7A", "mappings": {"default": {"default": "th barrata"}}}, {"key": "1D80", "mappings": {"default": {"default": "b con gancio palatale"}}}, {"key": "1D81", "mappings": {"default": {"default": "d con gancio palatale"}}}, {"key": "1D82", "mappings": {"default": {"default": "f con gancio palatale"}}}, {"key": "1D83", "mappings": {"default": {"default": "g con gancio palatale"}}}, {"key": "1D84", "mappings": {"default": {"default": "k con gancio palatale"}}}, {"key": "1D85", "mappings": {"default": {"default": "l con gancio palatale"}}}, {"key": "1D86", "mappings": {"default": {"default": "m con gancio palatale"}}}, {"key": "1D87", "mappings": {"default": {"default": "n con gancio palatale"}}}, {"key": "1D88", "mappings": {"default": {"default": "p con gancio palatale"}}}, {"key": "1D89", "mappings": {"default": {"default": "r con gancio palatale"}}}, {"key": "1D8A", "mappings": {"default": {"default": "s con gancio palatale"}}}, {"key": "1D8B", "mappings": {"default": {"default": "esh con gancio palatale"}}}, {"key": "1D8C", "mappings": {"default": {"default": "v con gancio palatale"}}}, {"key": "1D8D", "mappings": {"default": {"default": "x con gancio palatale"}}}, {"key": "1D8E", "mappings": {"default": {"default": "z con gancio palatale"}}}, {"key": "1D8F", "mappings": {"default": {"default": "a con gancio retroflesso"}}}, {"key": "1D90", "mappings": {"default": {"default": "alfa con gancio retroflesso"}}}, {"key": "1D91", "mappings": {"default": {"default": "d con gancio e coda"}}}, {"key": "1D92", "mappings": {"default": {"default": "e con gancio retroflesso"}}}, {"key": "1D93", "mappings": {"default": {"default": "e aperta con gancio retroflesso"}}}, {"key": "1D94", "mappings": {"default": {"default": "e aperta con gancio retroflesso invertita"}}}, {"key": "1D95", "mappings": {"default": {"default": "schwa con gancio retroflesso"}}}, {"key": "1D96", "mappings": {"default": {"default": "I con gancio <PERSON>"}}}, {"key": "1D97", "mappings": {"default": {"default": "o aperta con gancio retroflesso"}}}, {"key": "1D98", "mappings": {"default": {"default": "esh with gancio retroflesso"}}}, {"key": "1D99", "mappings": {"default": {"default": "u con gancio retroflesso"}}}, {"key": "1D9A", "mappings": {"default": {"default": "ezh con gancio retroflesso"}}}, {"key": "0149", "mappings": {"default": {"default": "n preceduta da apostrofo"}}}, {"key": "014B", "mappings": {"default": {"default": "eng"}}}], "it/symbols/latin-lower-single-accent.min": [{"locale": "it"}, {"key": "00E0", "mappings": {"default": {"default": "a con accento grave"}}}, {"key": "00E1", "mappings": {"default": {"default": "a con accento acuto"}}}, {"key": "00E2", "mappings": {"default": {"default": "a con accento circonflesso"}}}, {"key": "00E3", "mappings": {"default": {"default": "a con tilde"}}}, {"key": "00E4", "mappings": {"default": {"default": "a con dieresi"}}}, {"key": "00E5", "mappings": {"default": {"default": "a con anello sopra"}}}, {"key": "00E7", "mappings": {"default": {"default": "c con cediglia"}}}, {"key": "00E8", "mappings": {"default": {"default": "e con accento grave"}}}, {"key": "00E9", "mappings": {"default": {"default": "e con accento acuto"}}}, {"key": "00EA", "mappings": {"default": {"default": "e con accento circonflesso"}}}, {"key": "00EB", "mappings": {"default": {"default": "e con dieresi"}}}, {"key": "00EC", "mappings": {"default": {"default": "i con accento grave"}}}, {"key": "00ED", "mappings": {"default": {"default": "i con accento acuto"}}}, {"key": "00EE", "mappings": {"default": {"default": "i con accento circonflesso"}}}, {"key": "00EF", "mappings": {"default": {"default": "i con dieresi"}}}, {"key": "00F1", "mappings": {"default": {"default": "n con tilde"}}}, {"key": "00F2", "mappings": {"default": {"default": "o con accento grave"}}}, {"key": "00F3", "mappings": {"default": {"default": "o con accento acuto"}}}, {"key": "00F4", "mappings": {"default": {"default": "o con accento circonflesso"}}}, {"key": "00F5", "mappings": {"default": {"default": "o con tilde"}}}, {"key": "00F6", "mappings": {"default": {"default": "o con dieresi"}}}, {"key": "00F9", "mappings": {"default": {"default": "u con accento grave"}}}, {"key": "00FA", "mappings": {"default": {"default": "u con accento acuto"}}}, {"key": "00FB", "mappings": {"default": {"default": "u con accento circonflesso"}}}, {"key": "00FC", "mappings": {"default": {"default": "u con dieresi"}}}, {"key": "00FD", "mappings": {"default": {"default": "y con accento acuto"}}}, {"key": "00FF", "mappings": {"default": {"default": "y con dieresi"}}}, {"key": "0101", "mappings": {"default": {"default": "a con barra sopra"}}}, {"key": "0103", "mappings": {"default": {"default": "a con segno di vocale corta"}}}, {"key": "0105", "mappings": {"default": {"default": "a con ogonek"}}}, {"key": "0107", "mappings": {"default": {"default": "c con accento acuto"}}}, {"key": "0109", "mappings": {"default": {"default": "c con accento circonflesso"}}}, {"key": "010B", "mappings": {"default": {"default": "c con un punto sopra"}}}, {"key": "010D", "mappings": {"default": {"default": "c con caron"}}}, {"key": "010F", "mappings": {"default": {"default": "d con caron"}}}, {"key": "0113", "mappings": {"default": {"default": "e con barra sopra"}}}, {"key": "0115", "mappings": {"default": {"default": "piccola lettera latina E con Breve"}}}, {"key": "0117", "mappings": {"default": {"default": "e con punto sopra"}}}, {"key": "0119", "mappings": {"default": {"default": "e con ogonek"}}}, {"key": "011B", "mappings": {"default": {"default": "e con caron"}}}, {"key": "011D", "mappings": {"default": {"default": "g con accento circonflesso"}}}, {"key": "011F", "mappings": {"default": {"default": "g con segno di vocale corta"}}}, {"key": "0121", "mappings": {"default": {"default": "g con punto sopra"}}}, {"key": "0123", "mappings": {"default": {"default": "latina piccola lettera G con Cedilla"}}}, {"key": "0125", "mappings": {"default": {"default": "h con accento circonflesso"}}}, {"key": "0129", "mappings": {"default": {"default": "i con tilde"}}}, {"key": "012B", "mappings": {"default": {"default": "i con barra sopra"}}}, {"key": "012D", "mappings": {"default": {"default": "piccola lettera latina I con breve"}}}, {"key": "012F", "mappings": {"default": {"default": "i con ogonek"}}}, {"key": "0131", "mappings": {"default": {"default": "i senza puntino"}}}, {"key": "0135", "mappings": {"default": {"default": "gei con accento circonflesso"}}}, {"key": "0137", "mappings": {"default": {"default": "k con cediglia"}}}, {"key": "013A", "mappings": {"default": {"default": "l con accento"}}}, {"key": "013C", "mappings": {"default": {"default": "l con cediglia"}}}, {"key": "013E", "mappings": {"default": {"default": "l con caron"}}}, {"key": "0140", "mappings": {"default": {"default": "l con punto nel mezzo"}}}, {"key": "0144", "mappings": {"default": {"default": "n con accento acuto"}}}, {"key": "0146", "mappings": {"default": {"default": "n con cediglia"}}}, {"key": "0148", "mappings": {"default": {"default": "n con caron"}}}, {"key": "014D", "mappings": {"default": {"default": "o con barra sopra"}}}, {"key": "014F", "mappings": {"default": {"default": "lettera minuscola latina O con Breve"}}}, {"key": "0151", "mappings": {"default": {"default": "o con doppio accento acuto"}}}, {"key": "0155", "mappings": {"default": {"default": "r con accento acuto"}}}, {"key": "0157", "mappings": {"default": {"default": "r con cediglia"}}}, {"key": "0159", "mappings": {"default": {"default": "r con caron"}}}, {"key": "015B", "mappings": {"default": {"default": "s con accento acuto"}}}, {"key": "015D", "mappings": {"default": {"default": "s con accento circonflesso"}}}, {"key": "015F", "mappings": {"default": {"default": "s con cediglia"}}}, {"key": "0161", "mappings": {"default": {"default": "s con caron"}}}, {"key": "0163", "mappings": {"default": {"default": "t con cediglia"}}}, {"key": "0165", "mappings": {"default": {"default": "t con caron"}}}, {"key": "0169", "mappings": {"default": {"default": "u con tilde"}}}, {"key": "016B", "mappings": {"default": {"default": "u con barra sopra"}}}, {"key": "016D", "mappings": {"default": {"default": "u con segno di vocale corta"}}}, {"key": "016F", "mappings": {"default": {"default": "u con anello sopra"}}}, {"key": "0171", "mappings": {"default": {"default": "u con doppio accento acuto"}}}, {"key": "0173", "mappings": {"default": {"default": "u con ogonek"}}}, {"key": "0175", "mappings": {"default": {"default": "w con accento circonflesso"}}}, {"key": "0177", "mappings": {"default": {"default": "y con accento circonflesso"}}}, {"key": "017A", "mappings": {"default": {"default": "z con accento acuto"}}}, {"key": "017C", "mappings": {"default": {"default": "z con punto sopra"}}}, {"key": "017E", "mappings": {"default": {"default": "z con caron"}}}, {"key": "01CE", "mappings": {"default": {"default": "a con caron"}}}, {"key": "01D0", "mappings": {"default": {"default": "i con caron"}}}, {"key": "01D2", "mappings": {"default": {"default": "o con caron"}}}, {"key": "01D4", "mappings": {"default": {"default": "u con caron"}}}, {"key": "01E7", "mappings": {"default": {"default": "g con caron"}}}, {"key": "01E9", "mappings": {"default": {"default": "k con caron"}}}, {"key": "01EB", "mappings": {"default": {"default": "o con uncino"}}}, {"key": "01F0", "mappings": {"default": {"default": "j con caron"}}}, {"key": "01F5", "mappings": {"default": {"default": "g con accento acuto"}}}, {"key": "01F9", "mappings": {"default": {"default": "n con accento grave"}}}, {"key": "0201", "mappings": {"default": {"default": "a con doppio accento grave"}}}, {"key": "0203", "mappings": {"default": {"default": "a con accento breve inverso"}}}, {"key": "0205", "mappings": {"default": {"default": "e con doppio accento grave"}}}, {"key": "0207", "mappings": {"default": {"default": "e con accento breve inverso"}}}, {"key": "0209", "mappings": {"default": {"default": "I con doppio accento grave"}}}, {"key": "020B", "mappings": {"default": {"default": "i con accento breve inverso"}}}, {"key": "020D", "mappings": {"default": {"default": "o con doppio accento grave"}}}, {"key": "020F", "mappings": {"default": {"default": "o con accento breve inverso"}}}, {"key": "0211", "mappings": {"default": {"default": "r con doppio accento grave"}}}, {"key": "0213", "mappings": {"default": {"default": "r con accento breve"}}}, {"key": "0215", "mappings": {"default": {"default": "u con doppio accento grave"}}}, {"key": "0217", "mappings": {"default": {"default": "u con accento breve inverso"}}}, {"key": "0219", "mappings": {"default": {"default": "s con virgola in basso"}}}, {"key": "021B", "mappings": {"default": {"default": "t con virgola in basso"}}}, {"key": "021F", "mappings": {"default": {"default": "h con caron"}}}, {"key": "0227", "mappings": {"default": {"default": "a con punto in alto"}}}, {"key": "0229", "mappings": {"default": {"default": "e con cediglia"}}}, {"key": "022F", "mappings": {"default": {"default": "o con punto in alto"}}}, {"key": "0233", "mappings": {"default": {"default": "y con macron"}}}, {"key": "0237", "mappings": {"default": {"default": "j senza punto"}}}, {"key": "1E01", "mappings": {"default": {"default": "a con anello sotto"}}}, {"key": "1E03", "mappings": {"default": {"default": "b con punto sopra"}}}, {"key": "1E05", "mappings": {"default": {"default": "b con punto sotto"}}}, {"key": "1E07", "mappings": {"default": {"default": "b con linea sotto"}}}, {"key": "1E0B", "mappings": {"default": {"default": "d con punto sopra"}}}, {"key": "1E0D", "mappings": {"default": {"default": "d con punto sotto"}}}, {"key": "1E0F", "mappings": {"default": {"default": "d con linea sotto"}}}, {"key": "1E11", "mappings": {"default": {"default": "d con cediglia"}}}, {"key": "1E13", "mappings": {"default": {"default": "d con accento circonflesso sotto"}}}, {"key": "1E19", "mappings": {"default": {"default": "e con accento circonflesso sotto"}}}, {"key": "1E1B", "mappings": {"default": {"default": "e con tilde sotto"}}}, {"key": "1E1F", "mappings": {"default": {"default": "f con punto sopra"}}}, {"key": "1E21", "mappings": {"default": {"default": "g con barra sopra"}}}, {"key": "1E23", "mappings": {"default": {"default": "h con punto sopra"}}}, {"key": "1E25", "mappings": {"default": {"default": "h con punto sotto"}}}, {"key": "1E27", "mappings": {"default": {"default": "h con dieresi"}}}, {"key": "1E29", "mappings": {"default": {"default": "h con cediglia"}}}, {"key": "1E2B", "mappings": {"default": {"default": "h con segno di vocale corta sotto"}}}, {"key": "1E2D", "mappings": {"default": {"default": "i con tilde sotto"}}}, {"key": "1E31", "mappings": {"default": {"default": "k con accento acuto"}}}, {"key": "1E33", "mappings": {"default": {"default": "k con punto sotto"}}}, {"key": "1E35", "mappings": {"default": {"default": "k con linea sotto"}}}, {"key": "1E37", "mappings": {"default": {"default": "l con punto sotto"}}}, {"key": "1E3B", "mappings": {"default": {"default": "l con linea sotto"}}}, {"key": "1E3D", "mappings": {"default": {"default": "l con accento circonflesso sotto"}}}, {"key": "1E3F", "mappings": {"default": {"default": "m con accento acuto"}}}, {"key": "1E41", "mappings": {"default": {"default": "m con punto sopra"}}}, {"key": "1E43", "mappings": {"default": {"default": "m con punto sotto"}}}, {"key": "1E45", "mappings": {"default": {"default": "n con punto sopra"}}}, {"key": "1E47", "mappings": {"default": {"default": "n con punto sotto"}}}, {"key": "1E49", "mappings": {"default": {"default": "n con linea sotto"}}}, {"key": "1E4B", "mappings": {"default": {"default": "n con accento circonflesso sotto"}}}, {"key": "1E55", "mappings": {"default": {"default": "p con accento acuto"}}}, {"key": "1E57", "mappings": {"default": {"default": "p con punto sopra"}}}, {"key": "1E59", "mappings": {"default": {"default": "r con punto sopra"}}}, {"key": "1E5B", "mappings": {"default": {"default": "r con punto sotto"}}}, {"key": "1E5F", "mappings": {"default": {"default": "r con linea sotto"}}}, {"key": "1E61", "mappings": {"default": {"default": "s con punto sopra"}}}, {"key": "1E63", "mappings": {"default": {"default": "s con punto sotto"}}}, {"key": "1E6B", "mappings": {"default": {"default": "t con punto sopra"}}}, {"key": "1E6D", "mappings": {"default": {"default": "t con punto sotto"}}}, {"key": "1E6F", "mappings": {"default": {"default": "t con linea sotto"}}}, {"key": "1E71", "mappings": {"default": {"default": "t con accento circonflesso sotto"}}}, {"key": "1E73", "mappings": {"default": {"default": "u con dieresi sotto"}}}, {"key": "1E75", "mappings": {"default": {"default": "u con tilde sotto"}}}, {"key": "1E77", "mappings": {"default": {"default": "u con accento circonflesso sotto"}}}, {"key": "1E7D", "mappings": {"default": {"default": "v con tilde"}}}, {"key": "1E7F", "mappings": {"default": {"default": "v con punto sotto"}}}, {"key": "1E81", "mappings": {"default": {"default": "w con accento grave"}}}, {"key": "1E83", "mappings": {"default": {"default": "w con accento acuto"}}}, {"key": "1E85", "mappings": {"default": {"default": "w con dieresi"}}}, {"key": "1E87", "mappings": {"default": {"default": "w con punto sopra"}}}, {"key": "1E89", "mappings": {"default": {"default": "w con punto sotto"}}}, {"key": "1E8B", "mappings": {"default": {"default": "x con punto sopra"}}}, {"key": "1E8D", "mappings": {"default": {"default": "x con dieresi"}}}, {"key": "1E8F", "mappings": {"default": {"default": "y con punto sopra"}}}, {"key": "1E91", "mappings": {"default": {"default": "z con accento circonflesso"}}}, {"key": "1E93", "mappings": {"default": {"default": "z con punto sotto"}}}, {"key": "1E95", "mappings": {"default": {"default": "z con linea sotto"}}}, {"key": "1E96", "mappings": {"default": {"default": "h con linea sotto"}}}, {"key": "1E97", "mappings": {"default": {"default": "t con dieresi"}}}, {"key": "1E98", "mappings": {"default": {"default": "w con anello sopra"}}}, {"key": "1E99", "mappings": {"default": {"default": "y con anello sopra"}}}, {"key": "1E9A", "mappings": {"default": {"default": "a con metà anello destro"}}}, {"key": "1EA1", "mappings": {"default": {"default": "a con punto sotto"}}}, {"key": "1EA3", "mappings": {"default": {"default": "a con gancio sopra"}}}, {"key": "1EB9", "mappings": {"default": {"default": "e con punto sotto"}}}, {"key": "1EBB", "mappings": {"default": {"default": "e con gancio sopra"}}}, {"key": "1EBD", "mappings": {"default": {"default": "e con tilde"}}}, {"key": "1EC9", "mappings": {"default": {"default": "i con gancio sopra"}}}, {"key": "1ECB", "mappings": {"default": {"default": "i con punto sotto"}}}, {"key": "1ECD", "mappings": {"default": {"default": "o con punto sotto"}}}, {"key": "1ECF", "mappings": {"default": {"default": "o con gancio sopra"}}}, {"key": "1EE5", "mappings": {"default": {"default": "u con punto sotto"}}}, {"key": "1EE7", "mappings": {"default": {"default": "u con gancio sopra"}}}, {"key": "1EF3", "mappings": {"default": {"default": "y con accento grave"}}}, {"key": "1EF5", "mappings": {"default": {"default": "y con punto sotto"}}}, {"key": "1EF7", "mappings": {"default": {"default": "y con gancio sopra"}}}, {"key": "1EF9", "mappings": {"default": {"default": "y con tilde"}}}], "it/symbols/latin-rest.min": [{"locale": "it"}, {"key": "210E", "mappings": {"default": {"physics": "costante di planck"}}}, {"key": "0363", "mappings": {"default": {"default": "combinando la piccola A"}}}, {"key": "0364", "mappings": {"default": {"default": "combinando la piccola E"}}}, {"key": "0365", "mappings": {"default": {"default": "combinando la piccola I"}}}, {"key": "0366", "mappings": {"default": {"default": "combinare la piccola O"}}}, {"key": "0367", "mappings": {"default": {"default": "combinazione di caratteri latini piccoli U"}}}, {"key": "0368", "mappings": {"default": {"default": "combinare la piccola C"}}}, {"key": "0369", "mappings": {"default": {"default": "combinare la lettera minuscola latina D"}}}, {"key": "036A", "mappings": {"default": {"default": "combinare la lettera minuscola latina H"}}}, {"key": "036B", "mappings": {"default": {"default": "combinare la lettera minuscola latina M"}}}, {"key": "036C", "mappings": {"default": {"default": "combinare la lettera minuscola latina R"}}}, {"key": "036D", "mappings": {"default": {"default": "combinando la piccola T"}}}, {"key": "036E", "mappings": {"default": {"default": "combinare la lettera minuscola latina V"}}}, {"key": "036F", "mappings": {"default": {"default": "combinare la piccola X"}}}, {"key": "1D62", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera I"}}}, {"key": "1D63", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera R"}}}, {"key": "1D64", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera U"}}}, {"key": "1D65", "mappings": {"default": {"default": "sottoscrizione latina V lettera minuscola"}}}, {"key": "1DCA", "mappings": {"default": {"default": "combinando la piccola R sotto"}}}, {"key": "1DD3", "mappings": {"default": {"default": "combinare la lettera minuscola latina appiattita Apri sopra"}}}, {"key": "1DD4", "mappings": {"default": {"default": "combina la piccola Ae"}}}, {"key": "1DD5", "mappings": {"default": {"default": "combinare la piccola Ao"}}}, {"key": "1DD6", "mappings": {"default": {"default": "combinare la lettera minuscola latina Av"}}}, {"key": "1DD7", "mappings": {"default": {"default": "combinando la piccola C Cedilla"}}}, {"key": "1DD8", "mappings": {"default": {"default": "combinando la lettera minuscola latina insulare D"}}}, {"key": "1DD9", "mappings": {"default": {"default": "combinazione di caratteri latini piccoli Eth"}}}, {"key": "1DDA", "mappings": {"default": {"default": "combinando la piccola G"}}}, {"key": "1DDB", "mappings": {"default": {"default": "combinando la Small Capital G"}}}, {"key": "1DDC", "mappings": {"default": {"default": "combinare la piccola K"}}}, {"key": "1DDD", "mappings": {"default": {"default": "combinare la piccola L"}}}, {"key": "1DDE", "mappings": {"default": {"default": "combinando la Small Capital L"}}}, {"key": "1DDF", "mappings": {"default": {"default": "combinare la Small Capital M"}}}, {"key": "1DE0", "mappings": {"default": {"default": "combinare la piccola N"}}}, {"key": "1DE1", "mappings": {"default": {"default": "combinare la Small Capital N"}}}, {"key": "1DE2", "mappings": {"default": {"default": "combinando la Small Capital R"}}}, {"key": "1DE3", "mappings": {"default": {"default": "combinazione di caratteri latini R Rotunda"}}}, {"key": "1DE4", "mappings": {"default": {"default": "combinare la piccola S"}}}, {"key": "1DE5", "mappings": {"default": {"default": "combinando la piccola lunga S"}}}, {"key": "1DE6", "mappings": {"default": {"default": "combinando la piccola Z"}}}, {"key": "2071", "mappings": {"default": {"default": "apice latina piccola lettera I"}}}, {"key": "207F", "mappings": {"default": {"default": "apice latina Piccola lettera N"}}}, {"key": "2090", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera A"}}}, {"key": "2091", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera E"}}}, {"key": "2092", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera O"}}}, {"key": "2093", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera X"}}}, {"key": "2094", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera Schwa"}}}, {"key": "2095", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera H"}}}, {"key": "2096", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera K"}}}, {"key": "2097", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera L"}}}, {"key": "2098", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera M"}}}, {"key": "2099", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera N"}}}, {"key": "209A", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera P"}}}, {"key": "209B", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera S"}}}, {"key": "209C", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera T"}}}, {"key": "2C7C", "mappings": {"default": {"default": "sottoscrizione latina Piccola lettera J"}}}, {"key": "1F12A", "mappings": {"default": {"default": "lettera maiuscola latina a caratteri cubitali con guscio di tartaruga s"}}}, {"key": "1F12B", "mappings": {"default": {"default": "registrazione su disco singolo"}}}, {"key": "1F12C", "mappings": {"default": {"default": "registrazione del disco"}}}, {"key": "1F18A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> ch<PERSON>o"}}}], "it/symbols/latin-upper-double-accent.min": [{"locale": "it"}, {"key": "01D5", "mappings": {"default": {"default": "Latin Capital Lettera U con Diaeresis e Macron"}}}, {"key": "01D7", "mappings": {"default": {"default": "Capitale latina Lettera U con diaeresi e acuta"}}}, {"key": "01D9", "mappings": {"default": {"default": "Latin Capital Lettera U con Diaeresis e Caron"}}}, {"key": "01DB", "mappings": {"default": {"default": "Capitale latina Lettera U con diaeresi e tomba"}}}, {"key": "01DE", "mappings": {"default": {"default": "Lettera maiuscola latina a con Diaeresis e Macron"}}}, {"key": "01E0", "mappings": {"default": {"default": "Lettera maiuscola latina a con Punto Sopra e Macron"}}}, {"key": "01EC", "mappings": {"default": {"default": "Lettera maiuscola latina O con Ogonek e Macron"}}}, {"key": "01FA", "mappings": {"default": {"default": "Lettera maiuscola latina a con anello sopra e acuto"}}}, {"key": "022A", "mappings": {"default": {"default": "Capitale latina Lettera O con Diaeresis e Macron"}}}, {"key": "022C", "mappings": {"default": {"default": "Capitale latina Lettera O con Tilde e Macron"}}}, {"key": "0230", "mappings": {"default": {"default": "Capitale latina Lettera O con Punto Sopra e Macron"}}}, {"key": "1E08", "mappings": {"default": {"default": "c maiuscola con cediglia and acute"}}}, {"key": "1E14", "mappings": {"default": {"default": "e maiuscola con barra sopra e accento grave"}}}, {"key": "1E16", "mappings": {"default": {"default": "e maiuscola con barra sopra e accento acuto"}}}, {"key": "1E1C", "mappings": {"default": {"default": "e maiuscola con cediglia e con segno di vocale corta"}}}, {"key": "1E2E", "mappings": {"default": {"default": "i maiuscola con dieresi ed accento acuto"}}}, {"key": "1E38", "mappings": {"default": {"default": "l maiuscola con punto sotto e con barra sopra"}}}, {"key": "1E4C", "mappings": {"default": {"default": "o maiuscola con tilde ed accento acuto"}}}, {"key": "1E4E", "mappings": {"default": {"default": "o maiuscola con tilde e dieresi"}}}, {"key": "1E50", "mappings": {"default": {"default": "o maiuscola con barra sopra ed accento grave"}}}, {"key": "1E52", "mappings": {"default": {"default": "o maiuscola con barra sopra ed accento acuto"}}}, {"key": "1E5C", "mappings": {"default": {"default": "r ma<PERSON><PERSON> con punto sotto e con barra sopra"}}}, {"key": "1E64", "mappings": {"default": {"default": "s maiuscola con accento acuto e punto sopra"}}}, {"key": "1E66", "mappings": {"default": {"default": "s maiuscola con caron e punto sopra"}}}, {"key": "1E68", "mappings": {"default": {"default": "s maiuscola con punto sotto e punto sopra"}}}, {"key": "1E78", "mappings": {"default": {"default": "u maiuscola con tilde ed accento acuto"}}}, {"key": "1E7A", "mappings": {"default": {"default": "u maiuscola con barra sopra e dieresi"}}}, {"key": "1EA4", "mappings": {"default": {"default": "a maiuscola con accento circonflesso ed accento acuto"}}}, {"key": "1EA6", "mappings": {"default": {"default": "a maiuscola con accento circonflesso ed accento grave"}}}, {"key": "1EA8", "mappings": {"default": {"default": "a maiuscola con accento circonflesso e gancio sopra"}}}, {"key": "1EAA", "mappings": {"default": {"default": "a maiuscola con accento circonflesso e tilde"}}}, {"key": "1EAC", "mappings": {"default": {"default": "a maiuscola con accento circonflesso e punto sotto"}}}, {"key": "1EAE", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta ed accento acuto"}}}, {"key": "1EB0", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta ed accento grave"}}}, {"key": "1EB2", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta e gancio sopra"}}}, {"key": "1EB4", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta e tilde"}}}, {"key": "1EB6", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta e punto sotto"}}}, {"key": "1EBE", "mappings": {"default": {"default": "e maiuscola con accento circonflesso ed acuto"}}}, {"key": "1EC0", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e grave"}}}, {"key": "1EC2", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e gancio sopra"}}}, {"key": "1EC4", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e tilde"}}}, {"key": "1EC6", "mappings": {"default": {"default": "e maiuscola con accento circonflesso e punto sotto"}}}, {"key": "1ED0", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e accento acuto"}}}, {"key": "1ED2", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e accento grave"}}}, {"key": "1ED4", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e gancio sopra"}}}, {"key": "1ED6", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e tilde"}}}, {"key": "1ED8", "mappings": {"default": {"default": "o maiuscola con accento circonflesso e punto sotto"}}}, {"key": "1EDA", "mappings": {"default": {"default": "o maiuscola con corno e accento acuto"}}}, {"key": "1EDC", "mappings": {"default": {"default": "o maiuscola con corno e accento grave"}}}, {"key": "1EDE", "mappings": {"default": {"default": "o maiuscola con corno e gancio sopra"}}}, {"key": "1EE0", "mappings": {"default": {"default": "o maiuscola con corno e tilde"}}}, {"key": "1EE2", "mappings": {"default": {"default": "o maiuscola con corno e punto sotto"}}}, {"key": "1EE8", "mappings": {"default": {"default": "u maiuscola con corno e accento acuto"}}}, {"key": "1EEA", "mappings": {"default": {"default": "u maiuscola con corno e accento grave"}}}, {"key": "1EEC", "mappings": {"default": {"default": "u maiuscola con corno e gancio sopra"}}}, {"key": "1EEE", "mappings": {"default": {"default": "u maiuscola con corno e tilde"}}}, {"key": "1EF0", "mappings": {"default": {"default": "u maiuscola con corno e punto sotto"}}}], "it/symbols/latin-upper-single-accent.min": [{"locale": "it"}, {"key": "00C0", "mappings": {"default": {"default": "a maiuscola con accento grave"}}}, {"key": "00C1", "mappings": {"default": {"default": "a maiuscola con accento acuto"}}}, {"key": "00C2", "mappings": {"default": {"default": "a maiuscola con accento circonflesso"}}}, {"key": "00C3", "mappings": {"default": {"default": "a maiuscola con tilde"}}}, {"key": "00C4", "mappings": {"default": {"default": "a maiuscola con dieresi"}}}, {"key": "00C5", "mappings": {"default": {"default": "a maiuscola con accento un anellino sopra"}}}, {"key": "00C7", "mappings": {"default": {"default": "c maiuscola con cediglia"}}}, {"key": "00C8", "mappings": {"default": {"default": "e maiuscola con accento grave"}}}, {"key": "00C9", "mappings": {"default": {"default": "e maiuscola con accento acuto"}}}, {"key": "00CA", "mappings": {"default": {"default": "e maiuscola con accento circonflesso"}}}, {"key": "00CB", "mappings": {"default": {"default": "e maiuscola con dieresi"}}}, {"key": "00CC", "mappings": {"default": {"default": "i maiuscola con accento grave"}}}, {"key": "00CD", "mappings": {"default": {"default": "i maiuscola con accento acuto"}}}, {"key": "00CE", "mappings": {"default": {"default": "i maiuscola con accento circonflesso"}}}, {"key": "00CF", "mappings": {"default": {"default": "i maiuscola con diaeresis"}}}, {"key": "00D1", "mappings": {"default": {"default": "n maiuscola con tilde"}}}, {"key": "00D2", "mappings": {"default": {"default": "o maiuscola con accento grave"}}}, {"key": "00D3", "mappings": {"default": {"default": "o maiuscola con accento acuto"}}}, {"key": "00D4", "mappings": {"default": {"default": "o maiuscola con accento circonflesso"}}}, {"key": "00D5", "mappings": {"default": {"default": "o maiuscola con tilde"}}}, {"key": "00D6", "mappings": {"default": {"default": "o maiuscola con dieresi"}}}, {"key": "00D9", "mappings": {"default": {"default": "u maiuscola con accento grave"}}}, {"key": "00DA", "mappings": {"default": {"default": "u maiuscola con accento acuto"}}}, {"key": "00DB", "mappings": {"default": {"default": "u maiuscola con accento circonflesso"}}}, {"key": "00DC", "mappings": {"default": {"default": "u maiuscola con dieresi"}}}, {"key": "00DD", "mappings": {"default": {"default": "y maiuscola con accento acuto"}}}, {"key": "0100", "mappings": {"default": {"default": "a maiuscolo con barra sopra"}}}, {"key": "0102", "mappings": {"default": {"default": "a maiuscola con segno di vocale corta"}}}, {"key": "0104", "mappings": {"default": {"default": "a maiuscola con ogonek"}}}, {"key": "0106", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con accento acuto"}}}, {"key": "0108", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con accento circonflesso"}}}, {"key": "010A", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con un punto sopra"}}}, {"key": "010C", "mappings": {"default": {"default": "c ma<PERSON><PERSON> con caron"}}}, {"key": "010E", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con caron"}}}, {"key": "0112", "mappings": {"default": {"default": "e maiuscola con barra sopra"}}}, {"key": "0114", "mappings": {"default": {"default": "capitale latina Lettera E con Breve"}}}, {"key": "0116", "mappings": {"default": {"default": "e maiuscola con punto sopra"}}}, {"key": "0118", "mappings": {"default": {"default": "e maiuscola con ogonek"}}}, {"key": "011A", "mappings": {"default": {"default": "e maiuscola con caron"}}}, {"key": "011C", "mappings": {"default": {"default": "g maiuscola con accento circonflesso"}}}, {"key": "011E", "mappings": {"default": {"default": "g maiuscola con segno di vocale corta"}}}, {"key": "0120", "mappings": {"default": {"default": "g maiuscola con punto sopra"}}}, {"key": "0122", "mappings": {"default": {"default": "g maiuscola con cediglia"}}}, {"key": "0124", "mappings": {"default": {"default": "h maiuscola con accento circonflesso"}}}, {"key": "0128", "mappings": {"default": {"default": "i maiuscola con tilde"}}}, {"key": "012A", "mappings": {"default": {"default": "i maiuscola con barra sopra"}}}, {"key": "012C", "mappings": {"default": {"default": "lettera maiuscola latina I con breve"}}}, {"key": "012E", "mappings": {"default": {"default": "i maiuscola con ogonek"}}}, {"key": "0130", "mappings": {"default": {"default": "i maiuscola con punto sopra"}}}, {"key": "0134", "mappings": {"default": {"default": "gei maiuscola con accento circonflesso"}}}, {"key": "0136", "mappings": {"default": {"default": "k maiuscola con cediglia"}}}, {"key": "0139", "mappings": {"default": {"default": "l maiuscola con accento acuto"}}}, {"key": "013B", "mappings": {"default": {"default": "l maiuscola con cediglia"}}}, {"key": "013D", "mappings": {"default": {"default": "l maiuscola con caron"}}}, {"key": "013F", "mappings": {"default": {"default": "l maiuscola con un punto nel mezzo"}}}, {"key": "0143", "mappings": {"default": {"default": "n maiuscola con accento acuto"}}}, {"key": "0145", "mappings": {"default": {"default": "n maiuscola con cediglia"}}}, {"key": "0147", "mappings": {"default": {"default": "n maiuscola con caron"}}}, {"key": "014C", "mappings": {"default": {"default": "o maiuscola con barra sopra"}}}, {"key": "014E", "mappings": {"default": {"default": "lettera maiuscola latina O con Breve"}}}, {"key": "0150", "mappings": {"default": {"default": "o maiuscola con doppio accento acuto"}}}, {"key": "0154", "mappings": {"default": {"default": "r maiuscola con accento acuto"}}}, {"key": "0156", "mappings": {"default": {"default": "r maiuscola con cediglia"}}}, {"key": "0158", "mappings": {"default": {"default": "r maiuscola con caron"}}}, {"key": "015A", "mappings": {"default": {"default": "s maiuscola con accento acuto"}}}, {"key": "015C", "mappings": {"default": {"default": "s maiuscola s con accento circonflesso"}}}, {"key": "015E", "mappings": {"default": {"default": "s maiuscola s con cediglia"}}}, {"key": "0160", "mappings": {"default": {"default": "s maiuscola con caron"}}}, {"key": "0162", "mappings": {"default": {"default": "t maiuscola con cediglia"}}}, {"key": "0164", "mappings": {"default": {"default": "t maiuscola con caron"}}}, {"key": "0168", "mappings": {"default": {"default": "u maiuscola con tilde"}}}, {"key": "016A", "mappings": {"default": {"default": "u maiuscola con barra sopra"}}}, {"key": "016C", "mappings": {"default": {"default": "u maiuscola con segno di vocale corta"}}}, {"key": "016E", "mappings": {"default": {"default": "u maiuscola con anello sopra"}}}, {"key": "0170", "mappings": {"default": {"default": "u maiuscola con doppio accento acuto"}}}, {"key": "0172", "mappings": {"default": {"default": "u maiuscola con ogonek"}}}, {"key": "0174", "mappings": {"default": {"default": "w maiuscola con accento circonflesso"}}}, {"key": "0176", "mappings": {"default": {"default": "y maiuscola con accento circonflesso"}}}, {"key": "0178", "mappings": {"default": {"default": "y maiuscola with diaeresis"}}}, {"key": "0179", "mappings": {"default": {"default": "z maiuscola con accento acuto"}}}, {"key": "017B", "mappings": {"default": {"default": "z maiuscola con punto sopra"}}}, {"key": "017D", "mappings": {"default": {"default": "z maiuscola con caron"}}}, {"key": "01CD", "mappings": {"default": {"default": "lettera maiuscola latina a con Caron"}}}, {"key": "01CF", "mappings": {"default": {"default": "lettera maiuscola latina I con Caron"}}}, {"key": "01D1", "mappings": {"default": {"default": "lettera maiuscola latina O con <PERSON>"}}}, {"key": "01D3", "mappings": {"default": {"default": "carattere latino:  lettera U con Caron"}}}, {"key": "01E6", "mappings": {"default": {"default": "lettera maiuscola latina <PERSON>"}}}, {"key": "01E8", "mappings": {"default": {"default": "carattere latino:  lettera K con <PERSON>"}}}, {"key": "01EA", "mappings": {"default": {"default": "lettera maiuscola latina <PERSON> con O<PERSON>ek"}}}, {"key": "01F4", "mappings": {"default": {"default": "latin Capital Letter G with Acute"}}}, {"key": "01F8", "mappings": {"default": {"default": "lettera maiuscola latina con tomba"}}}, {"key": "0200", "mappings": {"default": {"default": "lettera maiuscola latina a con doppia tomba"}}}, {"key": "0202", "mappings": {"default": {"default": "lettera maiuscola latina a con breve invertito"}}}, {"key": "0204", "mappings": {"default": {"default": "capitale latina Lettera E con doppia tomba"}}}, {"key": "0206", "mappings": {"default": {"default": "capitale latina Lettera E con breve invertito"}}}, {"key": "0208", "mappings": {"default": {"default": "capitale latina Lettera I con doppia tomba"}}}, {"key": "020A", "mappings": {"default": {"default": "capitale latina Lettera I con Inverted Breve"}}}, {"key": "020C", "mappings": {"default": {"default": "capitale latina Lettera O con doppia tomba"}}}, {"key": "020E", "mappings": {"default": {"default": "lettera maiuscola latina O con breve invertito"}}}, {"key": "0210", "mappings": {"default": {"default": "latin Capital Letter R with Double Grave"}}}, {"key": "0212", "mappings": {"default": {"default": "lettera maiuscola latina R con breve invertito"}}}, {"key": "0214", "mappings": {"default": {"default": "latin Capital Lettera U con Double Grave"}}}, {"key": "0216", "mappings": {"default": {"default": "lettera maiuscola latina U con breve invertito"}}}, {"key": "0218", "mappings": {"default": {"default": "S maiuscola con comma sotto"}}}, {"key": "021A", "mappings": {"default": {"default": "lettera maiuscola latina T con la virgola sotto"}}}, {"key": "021E", "mappings": {"default": {"default": "capitale latina Lettera H con Caron"}}}, {"key": "0226", "mappings": {"default": {"default": "lettera maiuscola latina a con Punto Sopra"}}}, {"key": "0228", "mappings": {"default": {"default": "capitale latina Lettera E con Cedilla"}}}, {"key": "022E", "mappings": {"default": {"default": "lettera maiuscola latina O con Punto Sopra"}}}, {"key": "0232", "mappings": {"default": {"default": "capitale latina Lettera Y con Macron"}}}, {"key": "1E00", "mappings": {"default": {"default": "a maiuscola con anello sotto"}}}, {"key": "1E02", "mappings": {"default": {"default": "b ma<PERSON><PERSON> con punto sopra"}}}, {"key": "1E04", "mappings": {"default": {"default": "b ma<PERSON><PERSON> con punto sotto"}}}, {"key": "1E06", "mappings": {"default": {"default": "b ma<PERSON><PERSON> con linea sotto"}}}, {"key": "1E0A", "mappings": {"default": {"default": "d ma<PERSON><PERSON><PERSON> punto sopra"}}}, {"key": "1E0C", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con punto sotto"}}}, {"key": "1E0E", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con linea sotto"}}}, {"key": "1E10", "mappings": {"default": {"default": "d maiuscola con cediglia"}}}, {"key": "1E12", "mappings": {"default": {"default": "d ma<PERSON><PERSON> con accento circonflesso sotto"}}}, {"key": "1E18", "mappings": {"default": {"default": "e maiuscola con accento circonflesso sotto"}}}, {"key": "1E1A", "mappings": {"default": {"default": "e maiuscola con tilde sotto"}}}, {"key": "1E1E", "mappings": {"default": {"default": "f maiuscola con punto sopra"}}}, {"key": "1E20", "mappings": {"default": {"default": "g maiuscola con barra sopra"}}}, {"key": "1E22", "mappings": {"default": {"default": "h maiuscola con punto sopra"}}}, {"key": "1E24", "mappings": {"default": {"default": "h maiuscola con punto sotto"}}}, {"key": "1E26", "mappings": {"default": {"default": "h maiuscola con dieresi"}}}, {"key": "1E28", "mappings": {"default": {"default": "h maiuscola con cediglia"}}}, {"key": "1E2A", "mappings": {"default": {"default": "h maiuscola con segno di vocale corta sotto"}}}, {"key": "1E2C", "mappings": {"default": {"default": "i maiuscola con tilde sotto"}}}, {"key": "1E30", "mappings": {"default": {"default": "k maiuscola con accento acuto"}}}, {"key": "1E32", "mappings": {"default": {"default": "k maiuscola con punto sotto"}}}, {"key": "1E34", "mappings": {"default": {"default": "k maiuscola con linea sotto"}}}, {"key": "1E36", "mappings": {"default": {"default": "l maiuscola con punto sotto"}}}, {"key": "1E3A", "mappings": {"default": {"default": "l maiuscola con linea sotto"}}}, {"key": "1E3C", "mappings": {"default": {"default": "l maiuscola con accento circonflesso sotto"}}}, {"key": "1E3E", "mappings": {"default": {"default": "m maiuscola con accento acuto"}}}, {"key": "1E40", "mappings": {"default": {"default": "m maiuscola con punto sopra"}}}, {"key": "1E42", "mappings": {"default": {"default": "m maiuscola con punto sotto"}}}, {"key": "1E44", "mappings": {"default": {"default": "n maiuscola con punto sopra"}}}, {"key": "1E46", "mappings": {"default": {"default": "n maiuscola con punto sotto"}}}, {"key": "1E48", "mappings": {"default": {"default": "n maiuscola con linea sotto"}}}, {"key": "1E4A", "mappings": {"default": {"default": "n maiuscola con accento circonflesso sotto"}}}, {"key": "1E54", "mappings": {"default": {"default": "p maiuscola con accento acuto"}}}, {"key": "1E56", "mappings": {"default": {"default": "p maiuscola con punto sopra"}}}, {"key": "1E58", "mappings": {"default": {"default": "r maiuscola con punto sopra"}}}, {"key": "1E5A", "mappings": {"default": {"default": "r ma<PERSON><PERSON> con punto sotto"}}}, {"key": "1E5E", "mappings": {"default": {"default": "r ma<PERSON><PERSON> con linea sotto"}}}, {"key": "1E60", "mappings": {"default": {"default": "s maiuscola con punto sopra"}}}, {"key": "1E62", "mappings": {"default": {"default": "s maiuscola con punto sotto"}}}, {"key": "1E6A", "mappings": {"default": {"default": "t maiuscola con punto sopra"}}}, {"key": "1E6C", "mappings": {"default": {"default": "t maius<PERSON> con punto sotto"}}}, {"key": "1E6E", "mappings": {"default": {"default": "t maiuscola con linea sotto"}}}, {"key": "1E70", "mappings": {"default": {"default": "t maiuscola con accento circonflesso sotto"}}}, {"key": "1E72", "mappings": {"default": {"default": "u maiuscola con dieresi sotto"}}}, {"key": "1E74", "mappings": {"default": {"default": "u maiuscola con tilde sotto"}}}, {"key": "1E76", "mappings": {"default": {"default": "u maiuscola con accento circonflesso sotto"}}}, {"key": "1E7C", "mappings": {"default": {"default": "v maiuscola con tilde"}}}, {"key": "1E7E", "mappings": {"default": {"default": "v maiuscola con punto sotto"}}}, {"key": "1E80", "mappings": {"default": {"default": "w maiuscola con accento grave"}}}, {"key": "1E82", "mappings": {"default": {"default": "w maiuscola con accento acuto"}}}, {"key": "1E84", "mappings": {"default": {"default": "w maiuscola con dieresi"}}}, {"key": "1E86", "mappings": {"default": {"default": "w maiuscola con punto sopra"}}}, {"key": "1E88", "mappings": {"default": {"default": "w maiuscola con punto sotto"}}}, {"key": "1E8A", "mappings": {"default": {"default": "x maiuscola con punto sopra"}}}, {"key": "1E8C", "mappings": {"default": {"default": "x maiuscola con dieresi"}}}, {"key": "1E8E", "mappings": {"default": {"default": "y maiuscola con punto sopra"}}}, {"key": "1E90", "mappings": {"default": {"default": "z maiuscola con accento circonflesso"}}}, {"key": "1E92", "mappings": {"default": {"default": "z maiuscola con punto sotto"}}}, {"key": "1E94", "mappings": {"default": {"default": "z maiuscola con linea sotto"}}}, {"key": "1EA0", "mappings": {"default": {"default": "a maiuscola con punto sotto"}}}, {"key": "1EA2", "mappings": {"default": {"default": "a maiuscola con gancio sopra"}}}, {"key": "1EB8", "mappings": {"default": {"default": "e maiuscola con punto sotto"}}}, {"key": "1EBA", "mappings": {"default": {"default": "e maiuscola con gancio sopra"}}}, {"key": "1EBC", "mappings": {"default": {"default": "e maiuscola con tilde"}}}, {"key": "1EC8", "mappings": {"default": {"default": "i maiuscola con gancio sopra"}}}, {"key": "1ECA", "mappings": {"default": {"default": "i maiuscola con punto sotto"}}}, {"key": "1ECC", "mappings": {"default": {"default": "o maiuscola con punto sotto"}}}, {"key": "1ECE", "mappings": {"default": {"default": "o maiuscola con gancio sopra"}}}, {"key": "1EE4", "mappings": {"default": {"default": "u maiuscola con punto sotto"}}}, {"key": "1EE6", "mappings": {"default": {"default": "u maiuscola con gancio sopra"}}}, {"key": "1EF2", "mappings": {"default": {"default": "y maiuscola con accento grave"}}}, {"key": "1EF4", "mappings": {"default": {"default": "y maiuscola con punto sotto"}}}, {"key": "1EF6", "mappings": {"default": {"default": "y maiuscola con gancio sopra"}}}, {"key": "1EF8", "mappings": {"default": {"default": "y maiuscola con tilde"}}}], "it/symbols/math_angles.min": [{"locale": "it"}, {"key": "22BE", "mappings": {"default": {"default": "angolo retto con arco"}}}, {"key": "237C", "mappings": {"default": {"default": "angolo retto con freccia a zigzag verso il basso"}}}, {"key": "27C0", "mappings": {"default": {"default": "angolo tridimensionale"}}}, {"key": "299B", "mappings": {"default": {"default": "angolo misurato aperto a sinistra"}}}, {"key": "299C", "mappings": {"default": {"default": "variante ad angolo retto con quadrato"}}}, {"key": "299D", "mappings": {"default": {"default": "angolo retto misurato con punto"}}}, {"key": "299E", "mappings": {"default": {"default": "angolo con S Inside"}}}, {"key": "299F", "mappings": {"default": {"default": "angolo acuto"}}}, {"key": "29A0", "mappings": {"default": {"default": "angolo sferico aperto a sinistra"}}}, {"key": "29A1", "mappings": {"default": {"default": "apertura dell'angolo sferico"}}}, {"key": "29A2", "mappings": {"default": {"default": "angolo girato"}}}, {"key": "29A3", "mappings": {"default": {"default": "angolo invertito"}}}, {"key": "29A4", "mappings": {"default": {"default": "angolo con barra inferiore"}}}, {"key": "29A5", "mappings": {"default": {"default": "angolo inverso con barra inferiore"}}}, {"key": "29A6", "mappings": {"default": {"default": "apertura obliqua"}}}, {"key": "29A7", "mappings": {"default": {"default": "angolo obliquo aperto"}}}, {"key": "29A8", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso l'alto e verso destra"}}}, {"key": "29A9", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso l'alto e verso sinistra"}}}, {"key": "29AA", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso il basso e verso destra"}}}, {"key": "29AB", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso il basso e verso sinistra"}}}, {"key": "29AC", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso destra e verso l'alto"}}}, {"key": "29AD", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso sinistra e verso l'alto"}}}, {"key": "29AE", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso destra e verso il basso"}}}, {"key": "29AF", "mappings": {"default": {"default": "angolo misurato con il braccio aperto che termina con la freccia rivolta verso sinistra e verso il basso"}}}], "it/symbols/math_arrows.min": [{"locale": "it"}, {"key": "2190", "mappings": {"default": {"default": "freccia verso sinistra"}}}, {"key": "2191", "mappings": {"default": {"default": "freccia verso l'alto"}}}, {"key": "2192", "mappings": {"default": {"default": "freccia verso destra"}}}, {"key": "2193", "mappings": {"default": {"default": "freccia verso il basso"}}}, {"key": "2194", "mappings": {"default": {"default": "freccia sinistra e destra"}}}, {"key": "2195", "mappings": {"default": {"default": "freccia alto e basso"}}}, {"key": "2196", "mappings": {"default": {"default": "freccia a nord ovest"}}}, {"key": "2197", "mappings": {"default": {"default": "freccia a nord est"}}}, {"key": "2198", "mappings": {"default": {"default": "freccia a sud est"}}}, {"key": "2199", "mappings": {"default": {"default": "freccia a sud ovest"}}}, {"key": "219A", "mappings": {"default": {"default": "freccia sinistra con tratto diagonale"}}}, {"key": "219B", "mappings": {"default": {"default": "freccia destra con tratto diagonale"}}}, {"key": "219C", "mappings": {"default": {"default": "freccia d'onda a sinistra"}}}, {"key": "219D", "mappings": {"default": {"default": "freccia d'onda verso destra"}}}, {"key": "219E", "mappings": {"default": {"default": "freccia sinistra con doppia testa"}}}, {"key": "219F", "mappings": {"default": {"default": "freccia su con doppia testa"}}}, {"key": "21A0", "mappings": {"default": {"default": "freccia destra con doppia testa"}}}, {"key": "21A1", "mappings": {"default": {"default": "freccia giù con doppia testa"}}}, {"key": "21A2", "mappings": {"default": {"default": "freccia sinistra con coda"}}}, {"key": "21A3", "mappings": {"default": {"default": "freccia destra con coda :  iniezione totale di notazione z"}}}, {"key": "21A4", "mappings": {"default": {"default": "freccia sinistra dalla barra"}}}, {"key": "21A5", "mappings": {"default": {"default": "freccia verso l'alto dalla barra"}}}, {"key": "21A6", "mappings": {"default": {"default": "freccia destra dalla barra :  Mappa della notazione z"}}}, {"key": "21A7", "mappings": {"default": {"default": "freccia verso il basso dalla barra :  simbolo di profondità"}}}, {"key": "21A8", "mappings": {"default": {"default": "freccia giù con base"}}}, {"key": "21A9", "mappings": {"default": {"default": "freccia a sinistra con gancio"}}}, {"key": "21AA", "mappings": {"default": {"default": "freccia verso destra con gancio"}}}, {"key": "21AB", "mappings": {"default": {"default": "freccia sinistra con anello"}}}, {"key": "21AC", "mappings": {"default": {"default": "freccia verso destra con anello"}}}, {"key": "21AD", "mappings": {"default": {"default": "freccia dell'onda sinistra destra"}}}, {"key": "21AE", "mappings": {"default": {"default": "freccia destra sinistra con tratto"}}}, {"key": "21AF", "mappings": {"default": {"default": "freccia zigzag giù"}}}, {"key": "21B0", "mappings": {"default": {"default": "freccia verso l'alto con punta verso sinistra"}}}, {"key": "21B1", "mappings": {"default": {"default": "freccia verso l'alto con punta verso destra"}}}, {"key": "21B2", "mappings": {"default": {"default": "freccia verso il basso con punta verso sinistra"}}}, {"key": "21B3", "mappings": {"default": {"default": "freccia verso il basso con punta verso destra"}}}, {"key": "21B4", "mappings": {"default": {"default": "freccia verso destra con angolo verso il basso :  line feed"}}}, {"key": "21B5", "mappings": {"default": {"default": "freccia verso il basso con angolo verso sinistra"}}}, {"key": "21B6", "mappings": {"default": {"default": "freccia semicircolare superiore in senso antiorario"}}}, {"key": "21B7", "mappings": {"default": {"default": "freccia a semicerchio superiore in senso orario"}}}, {"key": "21B8", "mappings": {"default": {"default": "freccia nord ovest alla barra lunga"}}}, {"key": "21B9", "mappings": {"default": {"default": "freccia verso sinistra per barrare la freccia verso destra"}}}, {"key": "21BA", "mappings": {"default": {"default": "freccia circolare aperta in senso antiorario"}}}, {"key": "21BB", "mappings": {"default": {"default": "freccia a cerchio aperto in senso orario"}}}, {"key": "21C4", "mappings": {"default": {"default": "freccia verso destra sopra freccia verso sinistra"}}}, {"key": "21C5", "mappings": {"default": {"default": "freccia verso l'alto a sinistra di freccia verso il basso"}}}, {"key": "21C6", "mappings": {"default": {"default": "freccia verso sinistra sopra freccia verso destra"}}}, {"key": "21C7", "mappings": {"default": {"default": "coppia di frecce verso sinistra"}}}, {"key": "21C8", "mappings": {"default": {"default": "coppia di frecce verso l'alto"}}}, {"key": "21C9", "mappings": {"default": {"default": "coppia di frecce verso destra"}}}, {"key": "21CA", "mappings": {"default": {"default": "coppia di frecce verso il basso"}}}, {"key": "21CD", "mappings": {"default": {"default": "freccia doppia verso sinistra con tratto diagonale"}}}, {"key": "21CE", "mappings": {"default": {"default": "freccia doppia sinistra destra con tratto diagonale"}}}, {"key": "21CF", "mappings": {"default": {"default": "freccia doppia verso destra con tratto diagonale"}}}, {"key": "21D0", "mappings": {"default": {"default": "freccia doppia sinistra"}}}, {"key": "21D1", "mappings": {"default": {"default": "freccia doppia verso l'alto"}}}, {"key": "21D2", "mappings": {"default": {"default": "freccia doppia verso destra"}}}, {"key": "21D3", "mappings": {"default": {"default": "freccia doppia verso il basso"}}}, {"key": "21D4", "mappings": {"default": {"default": "freccia doppia sinistra destra"}}}, {"key": "21D5", "mappings": {"default": {"default": "freccia doppia su e giù"}}}, {"key": "21D6", "mappings": {"default": {"default": "doppia freccia nord ovest"}}}, {"key": "21D7", "mappings": {"default": {"default": "doppia freccia nord est"}}}, {"key": "21D8", "mappings": {"default": {"default": "doppia freccia sud est"}}}, {"key": "21D9", "mappings": {"default": {"default": "doppia freccia sud ovest"}}}, {"key": "21DA", "mappings": {"default": {"default": "freccia triple verso sinistra"}}}, {"key": "21DB", "mappings": {"default": {"default": "freccia triple verso destra"}}}, {"key": "21DC", "mappings": {"default": {"default": "freccia ondulata verso sinistra"}}}, {"key": "21DD", "mappings": {"default": {"default": "freccia destra"}}}, {"key": "21DE", "mappings": {"default": {"default": "freccia verso l'alto con doppio tratto"}}}, {"key": "21DF", "mappings": {"default": {"default": "freccia verso il basso con doppio tratto"}}}, {"key": "21E0", "mappings": {"default": {"default": "freccia tratteggiata a sinistra"}}}, {"key": "21E1", "mappings": {"default": {"default": "freccia tratteggiata verso l'alto"}}}, {"key": "21E2", "mappings": {"default": {"default": "freccia tratteggiata a destra"}}}, {"key": "21E3", "mappings": {"default": {"default": "freccia tratteggiata verso il basso"}}}, {"key": "21E4", "mappings": {"default": {"default": "tab a sinistra"}}}, {"key": "21E5", "mappings": {"default": {"default": "freccia verso destra verso il basso"}}}, {"key": "21E6", "mappings": {"default": {"default": "freccia bianca verso sinistra"}}}, {"key": "21E7", "mappings": {"default": {"default": "freccia bianca verso l'alto"}}}, {"key": "21E8", "mappings": {"default": {"default": "freccia bianca rivolta verso destra"}}}, {"key": "21E9", "mappings": {"default": {"default": "freccia bianca verso il basso"}}}, {"key": "21EA", "mappings": {"default": {"default": "freccia verso l'alto bianca da Bar"}}}, {"key": "21EB", "mappings": {"default": {"default": "freccia verso l'alto bianca sul piedistallo"}}}, {"key": "21EC", "mappings": {"default": {"default": "freccia verso l'alto bianca sul piedistallo con barra orizzontale"}}}, {"key": "21ED", "mappings": {"default": {"default": "freccia verso l'alto bianca sul piedistallo con barra verticale"}}}, {"key": "21EE", "mappings": {"default": {"default": "freccia doppia bianca verso l'alto"}}}, {"key": "21EF", "mappings": {"default": {"default": "freccia verso l'alto bianca doppia sul piedistallo"}}}, {"key": "21F0", "mappings": {"default": {"default": "freccia bianca rivolta verso destra"}}}, {"key": "21F1", "mappings": {"default": {"default": "freccia nord ovest all'angolo"}}}, {"key": "21F2", "mappings": {"default": {"default": "freccia sud est all'angolo"}}}, {"key": "21F3", "mappings": {"default": {"default": "freccia bianca in su"}}}, {"key": "21F4", "mappings": {"default": {"default": "freccia destra con piccolo cerchio"}}}, {"key": "21F5", "mappings": {"default": {"default": "freccia verso il basso freccia verso il basso"}}}, {"key": "21F6", "mappings": {"default": {"default": "tre frecce a destra"}}}, {"key": "21F7", "mappings": {"default": {"default": "freccia a sinistra con tratto verticale"}}}, {"key": "21F8", "mappings": {"default": {"default": "freccia verso destra con tratto verticale"}}}, {"key": "21F9", "mappings": {"default": {"default": "freccia destra sinistra con tratto verticale"}}}, {"key": "21FA", "mappings": {"default": {"default": "freccia a sinistra con doppio tratto verticale"}}}, {"key": "21FB", "mappings": {"default": {"default": "freccia verso destra con doppio tratto verticale"}}}, {"key": "21FC", "mappings": {"default": {"default": "freccia destra sinistra con doppio tratto verticale"}}}, {"key": "21FD", "mappings": {"default": {"default": "freccia a testa aperta rivolta a sinistra"}}}, {"key": "21FE", "mappings": {"default": {"default": "freccia a testa aperta rivolta verso destra"}}}, {"key": "21FF", "mappings": {"default": {"default": "freccia a freccia aperta sinistra destra"}}}, {"key": "2301", "mappings": {"default": {"default": "freccia elettrica"}}}, {"key": "2303", "mappings": {"default": {"default": "freccia in su"}}}, {"key": "2304", "mappings": {"default": {"default": "freccia giù"}}}, {"key": "2324", "mappings": {"default": {"default": "freccia su tra due barre oriz<PERSON>i"}}}, {"key": "238B", "mappings": {"default": {"default": "cerchio spezzato con freccia nord ovest"}}}, {"key": "2794", "mappings": {"default": {"default": "freccia verso destra con ampia freccia larga"}}}, {"key": "2798", "mappings": {"default": {"default": "pesante freccia del sud est"}}}, {"key": "2799", "mappings": {"default": {"default": "freccia verso destra pesante"}}}, {"key": "279A", "mappings": {"default": {"default": "freccia nord est pesante"}}}, {"key": "279B", "mappings": {"default": {"default": "freccia verso destra del punto di disegno"}}}, {"key": "279C", "mappings": {"default": {"default": "freccia destra con punta arrotondata pesante"}}}, {"key": "279D", "mappings": {"default": {"default": "freccia verso destra con la freccia a triangolo"}}}, {"key": "279E", "mappings": {"default": {"default": "freccia verso destra con freccia triangolare pesante"}}}, {"key": "279F", "mappings": {"default": {"default": "freccia verso destra con freccia a triangolo tratteggiata"}}}, {"key": "27A0", "mappings": {"default": {"default": "freccia rivolta verso il basso a punta triangolare con tratteggio pesante"}}}, {"key": "27A1", "mappings": {"default": {"default": "freccia destra nera"}}}, {"key": "27A2", "mappings": {"default": {"default": "freccia rivolta verso destra a tre punte superiore tridimensionale"}}}, {"key": "27A3", "mappings": {"default": {"default": "freccia rivolta verso destra a tre punte inferiore"}}}, {"key": "27A4", "mappings": {"default": {"default": "freccia destra nera"}}}, {"key": "27A5", "mappings": {"default": {"default": "freccia nera pesante rivolta verso il basso e verso destra"}}}, {"key": "27A6", "mappings": {"default": {"default": "freccia nera curva verso l'alto e verso destra"}}}, {"key": "27A7", "mappings": {"default": {"default": "freccia nera tozzo verso destra"}}}, {"key": "27A8", "mappings": {"default": {"default": "freccia verso destra nera con punta concava pesante"}}}, {"key": "27A9", "mappings": {"default": {"default": "freccia destra rivolta verso destra bianca"}}}, {"key": "27AA", "mappings": {"default": {"default": "freccia destra rivolta verso sinistra bianca"}}}, {"key": "27AB", "mappings": {"default": {"default": "freccia rivolta verso il basso bianca inclinata all'indietro"}}}, {"key": "27AC", "mappings": {"default": {"default": "freccia rivolta verso destra bianca con ombra inclinata nella parte anteriore"}}}, {"key": "27AD", "mappings": {"default": {"default": "freccia destra bianca in basso a destra in ombra"}}}, {"key": "27AE", "mappings": {"default": {"default": "freccia in alto a destra in alto a destra bianca in ombra"}}}, {"key": "27AF", "mappings": {"default": {"default": "freccia rivolta verso destra, in basso a destra, con ombra bianca"}}}, {"key": "27B1", "mappings": {"default": {"default": "freccia in alto a destra in alto a destra in bianco"}}}, {"key": "27B2", "mappings": {"default": {"default": "freccia destra bianca con cerchio pesante"}}}, {"key": "27B3", "mappings": {"default": {"default": "freccia verso destra con piume bianche"}}}, {"key": "27B4", "mappings": {"default": {"default": "freccia sud orientale con piume nere"}}}, {"key": "27B5", "mappings": {"default": {"default": "freccia verso il basso con piume nere"}}}, {"key": "27B6", "mappings": {"default": {"default": "freccia nord orientale con piume nere"}}}, {"key": "27B7", "mappings": {"default": {"default": "freccia sud orientale con grandi piume nere"}}}, {"key": "27B8", "mappings": {"default": {"default": "freccia verso il basso con piume nere pesanti"}}}, {"key": "27B9", "mappings": {"default": {"default": "freccia nord est con piume nere pesanti"}}}, {"key": "27BA", "mappings": {"default": {"default": "freccia rivolta verso il basso a goccia a goccia"}}}, {"key": "27BB", "mappings": {"default": {"default": "freccia destra con punta a goccia pesante"}}}, {"key": "27BC", "mappings": {"default": {"default": "freccia verso destra con coda a cuneo"}}}, {"key": "27BD", "mappings": {"default": {"default": "freccia verso destra con coda a cuneo"}}}, {"key": "27BE", "mappings": {"default": {"default": "freccia verso destra delineata aperta"}}}, {"key": "27F0", "mappings": {"default": {"default": "freccia quadrupla verso l'alto"}}}, {"key": "27F1", "mappings": {"default": {"default": "freccia quadrupla verso il basso"}}}, {"key": "27F2", "mappings": {"default": {"default": "freccia Cerchio Gapped in senso antiorario"}}}, {"key": "27F3", "mappings": {"default": {"default": "freccia circolare con gapping in senso orario"}}}, {"key": "27F4", "mappings": {"default": {"default": "freccia destra con cerchiata più"}}}, {"key": "27F5", "mappings": {"default": {"default": "freccia sinistra verso sinistra"}}}, {"key": "27F6", "mappings": {"default": {"default": "freccia verso destra lunga"}}}, {"key": "27F7", "mappings": {"default": {"default": "freccia sinistra sinistra lunga"}}}, {"key": "27F8", "mappings": {"default": {"default": "doppia freccia a sinistra lungo"}}}, {"key": "27F9", "mappings": {"default": {"default": "doppia freccia lunga destra"}}}, {"key": "27FA", "mappings": {"default": {"default": "doppia freccia destra sinistra lunga"}}}, {"key": "27FB", "mappings": {"default": {"default": "freccia sinistra verso sinistra dalla barra"}}}, {"key": "27FC", "mappings": {"default": {"default": "freccia destra verso destra dalla barra"}}}, {"key": "27FD", "mappings": {"default": {"default": "freccia a sinistra lunga doppia da barra"}}}, {"key": "27FE", "mappings": {"default": {"default": "freccia destra lunga doppia da barra"}}}, {"key": "27FF", "mappings": {"default": {"default": "freccia ondulata lunga destra"}}}, {"key": "2900", "mappings": {"default": {"default": "freccia a due punte a destra con tratto verticale"}}}, {"key": "2901", "mappings": {"default": {"default": "freccia a due punte rivolta verso destra con doppio tratto verticale"}}}, {"key": "2902", "mappings": {"default": {"default": "freccia sinistra a sinistra con tratto verticale"}}}, {"key": "2903", "mappings": {"default": {"default": "doppia freccia a destra con tratto verticale"}}}, {"key": "2904", "mappings": {"default": {"default": "freccia destra sinistra doppia con tratto verticale"}}}, {"key": "2905", "mappings": {"default": {"default": "freccia a due punte destra dalla barra"}}}, {"key": "2906", "mappings": {"default": {"default": "freccia sinistra a sinistra di Bar"}}}, {"key": "2907", "mappings": {"default": {"default": "a destra doppia freccia da bar"}}}, {"key": "2908", "mappings": {"default": {"default": "freccia verso il basso con tratto orizzontale"}}}, {"key": "2909", "mappings": {"default": {"default": "freccia verso l'alto con tratto orizzontale"}}}, {"key": "290A", "mappings": {"default": {"default": "tripla freccia verso l'alto"}}}, {"key": "290B", "mappings": {"default": {"default": "freccia tripla verso il basso"}}}, {"key": "290C", "mappings": {"default": {"default": "freccia Double Dash sinistra"}}}, {"key": "290D", "mappings": {"default": {"default": "freccia Double Dash rivolta verso destra"}}}, {"key": "290E", "mappings": {"default": {"default": "freccia tripla a sinistra"}}}, {"key": "290F", "mappings": {"default": {"default": "freccia Triple Dash rivolta verso destra"}}}, {"key": "2910", "mappings": {"default": {"default": "freccia tripla a due punte a destra"}}}, {"key": "2911", "mappings": {"default": {"default": "freccia verso destra con stelo tratteggiato"}}}, {"key": "2912", "mappings": {"default": {"default": "freccia verso l'alto da barrare"}}}, {"key": "2913", "mappings": {"default": {"default": "freccia verso il basso verso il basso"}}}, {"key": "2914", "mappings": {"default": {"default": "freccia verso destra con coda con tratto verticale"}}}, {"key": "2915", "mappings": {"default": {"default": "freccia destra con coda con doppio tratto verticale"}}}, {"key": "2916", "mappings": {"default": {"default": "freccia a due punte destra con coda"}}}, {"key": "2917", "mappings": {"default": {"default": "freccia a due punte a destra con coda con tratto verticale"}}}, {"key": "2918", "mappings": {"default": {"default": "freccia a due punte destra con coda con doppio tratto verticale"}}}, {"key": "2919", "mappings": {"default": {"default": "freccia a sinistra"}}}, {"key": "291A", "mappings": {"default": {"default": "freccia a destra"}}}, {"key": "291B", "mappings": {"default": {"default": "freccia a sinistra a doppia freccia"}}}, {"key": "291C", "mappings": {"default": {"default": "doppia freccia a destra"}}}, {"key": "291D", "mappings": {"default": {"default": "freccia verso sinistra al diamante nero"}}}, {"key": "291E", "mappings": {"default": {"default": "freccia verso destra per Black Diamond"}}}, {"key": "291F", "mappings": {"default": {"default": "freccia sinistra da Bar a diamante nero"}}}, {"key": "2920", "mappings": {"default": {"default": "freccia verso destra da Bar a Black Diamond"}}}, {"key": "2921", "mappings": {"default": {"default": "freccia nord occidentale e sud orientale"}}}, {"key": "2922", "mappings": {"default": {"default": "freccia nord est e sud ovest"}}}, {"key": "2923", "mappings": {"default": {"default": "freccia nord ovest con gancio"}}}, {"key": "2924", "mappings": {"default": {"default": "freccia nord est con gancio"}}}, {"key": "2925", "mappings": {"default": {"default": "freccia sud orientale con gancio"}}}, {"key": "2926", "mappings": {"default": {"default": "freccia sud ovest con gancio"}}}, {"key": "2927", "mappings": {"default": {"default": "freccia nord occidentale e freccia nord orientale"}}}, {"key": "2928", "mappings": {"default": {"default": "freccia nord orientale e freccia sud orientale"}}}, {"key": "2929", "mappings": {"default": {"default": "freccia sud orientale e freccia sud occidentale"}}}, {"key": "292A", "mappings": {"default": {"default": "freccia sud occidentale e freccia nord occidentale"}}}, {"key": "292D", "mappings": {"default": {"default": "freccia sud orientale che attraversa la freccia nord est"}}}, {"key": "292E", "mappings": {"default": {"default": "freccia nord orientale che attraversa la freccia sud orientale"}}}, {"key": "292F", "mappings": {"default": {"default": "caduta diagonale che attraversa la freccia di nord est"}}}, {"key": "2930", "mappings": {"default": {"default": "aumento diagonale che attraversa la freccia sud orientale"}}}, {"key": "2931", "mappings": {"default": {"default": "freccia nord orientale che attraversa la freccia nord occidentale"}}}, {"key": "2932", "mappings": {"default": {"default": "freccia nord ovest freccia nord est"}}}, {"key": "2933", "mappings": {"default": {"default": "freccia d'onda che punta direttamente a destra"}}}, {"key": "2934", "mappings": {"default": {"default": "freccia che punta verso destra e poi verso l'alto"}}}, {"key": "2935", "mappings": {"default": {"default": "freccia che punta verso destra e poi curva verso il basso"}}}, {"key": "2936", "mappings": {"default": {"default": "freccia che punta verso il basso e poi curva verso sinistra"}}}, {"key": "2937", "mappings": {"default": {"default": "freccia che punta verso il basso e poi curva verso destra"}}}, {"key": "2938", "mappings": {"default": {"default": "freccia destra dell'arco destro"}}}, {"key": "2939", "mappings": {"default": {"default": "freccia sinistra dell'arco sinistro"}}}, {"key": "293A", "mappings": {"default": {"default": "freccia in senso antiorario superiore"}}}, {"key": "293B", "mappings": {"default": {"default": "freccia in basso. freccia in senso antiorario"}}}, {"key": "293C", "mappings": {"default": {"default": "freccia in senso orario con meno"}}}, {"key": "293D", "mappings": {"default": {"default": "freccia in senso antiorario superiore con più"}}}, {"key": "293E", "mappings": {"default": {"default": "freccia semicircolare in senso orario in basso a destra"}}}, {"key": "293F", "mappings": {"default": {"default": "freccia sinistra in senso antiorario semicircolare in basso a sinistra"}}}, {"key": "2940", "mappings": {"default": {"default": "freccia circolare chiusa in senso antiorario"}}}, {"key": "2941", "mappings": {"default": {"default": "freccia circolare chiusa in senso orario"}}}, {"key": "2942", "mappings": {"default": {"default": "freccia destra sopra freccia corta sinistra"}}}, {"key": "2943", "mappings": {"default": {"default": "freccia sinistra verso destra freccia verso destra corta"}}}, {"key": "2944", "mappings": {"default": {"default": "freccia verso destra corta sopra la freccia verso sinistra"}}}, {"key": "2945", "mappings": {"default": {"default": "freccia verso destra con più sotto"}}}, {"key": "2946", "mappings": {"default": {"default": "freccia a sinistra con più sotto"}}}, {"key": "2947", "mappings": {"default": {"default": "freccia verso destra attraverso X"}}}, {"key": "2948", "mappings": {"default": {"default": "freccia destra sinistra attraverso il piccolo cerchio"}}}, {"key": "2949", "mappings": {"default": {"default": "freccia a due punte verso l'alto del piccolo cerchio"}}}, {"key": "2970", "mappings": {"default": {"default": "round implies"}}}, {"key": "2971", "mappings": {"default": {"default": "uguale segno sopra la freccia verso destra"}}}, {"key": "2972", "mappings": {"default": {"default": "operatore di tilde sopra la freccia verso destra"}}}, {"key": "2973", "mappings": {"default": {"default": "freccia verso sinistra sopra l'operatore Tilde"}}}, {"key": "2974", "mappings": {"default": {"default": "freccia verso destra sopra l'operatore Tilde"}}}, {"key": "2975", "mappings": {"default": {"default": "freccia a destra sopra a uguale circa a"}}}, {"key": "2976", "mappings": {"default": {"default": "freccia inferiore a sinistra"}}}, {"key": "2977", "mappings": {"default": {"default": "freccia sinistra verso il basso"}}}, {"key": "2978", "mappings": {"default": {"default": "freccia maggiore a quella sopra a destra"}}}, {"key": "2979", "mappings": {"default": {"default": "sottoinsieme sopra la freccia verso destra"}}}, {"key": "297A", "mappings": {"default": {"default": "freccia verso sinistra attraverso il sottoinsieme"}}}, {"key": "297B", "mappings": {"default": {"default": "superset sopra la freccia sinistra"}}}, {"key": "29B3", "mappings": {"default": {"default": "insieme vuoto con la freccia destra qui sopra"}}}, {"key": "29B4", "mappings": {"default": {"default": "set vuoto con freccia sinistra qui sopra"}}}, {"key": "29BD", "mappings": {"default": {"default": "freccia su cerchio"}}}, {"key": "29EA", "mappings": {"default": {"default": "black Diamond con freccia giù"}}}, {"key": "29EC", "mappings": {"default": {"default": "cerchio bianco con freccia giù"}}}, {"key": "29ED", "mappings": {"default": {"default": "cerchio nero con freccia giù"}}}, {"key": "2A17", "mappings": {"default": {"default": "integrale con freccia sinistra con gancio"}}}, {"key": "2B00", "mappings": {"default": {"default": "freccia bianca nord orientale"}}}, {"key": "2B01", "mappings": {"default": {"default": "freccia bianca del nord ovest"}}}, {"key": "2B02", "mappings": {"default": {"default": "freccia bianca sud orientale"}}}, {"key": "2B03", "mappings": {"default": {"default": "freccia bianca sud occidentale"}}}, {"key": "2B04", "mappings": {"default": {"default": "freccia bianca sinistra destra"}}}, {"key": "2B05", "mappings": {"default": {"default": "freccia nera verso sinistra"}}}, {"key": "2B06", "mappings": {"default": {"default": "freccia nera verso l'alto"}}}, {"key": "2B07", "mappings": {"default": {"default": "freccia nera verso il basso"}}}, {"key": "2B08", "mappings": {"default": {"default": "freccia nera nord orientale"}}}, {"key": "2B09", "mappings": {"default": {"default": "freccia nera del nord ovest"}}}, {"key": "2B0A", "mappings": {"default": {"default": "freccia nera a sud est"}}}, {"key": "2B0B", "mappings": {"default": {"default": "freccia nera sud occidentale"}}}, {"key": "2B0C", "mappings": {"default": {"default": "freccia nera sinistra destra"}}}, {"key": "2B0D", "mappings": {"default": {"default": "freccia nera in su"}}}, {"key": "2B0E", "mappings": {"default": {"default": "freccia verso destra con punta verso il basso"}}}, {"key": "2B0F", "mappings": {"default": {"default": "freccia verso destra con la punta verso l'alto"}}}, {"key": "2B10", "mappings": {"default": {"default": "freccia a sinistra con punta verso il basso"}}}, {"key": "2B11", "mappings": {"default": {"default": "freccia verso sinistra con punta verso l'alto"}}}, {"key": "2B30", "mappings": {"default": {"default": "freccia sinistra con piccolo cerchio"}}}, {"key": "2B31", "mappings": {"default": {"default": "tre frecce a sinistra"}}}, {"key": "2B32", "mappings": {"default": {"default": "freccia sinistra con cerchiata più"}}}, {"key": "2B33", "mappings": {"default": {"default": "freccia ondulata lunga sinistra"}}}, {"key": "2B34", "mappings": {"default": {"default": "freccia a due punte a sinistra con tratto verticale"}}}, {"key": "2B35", "mappings": {"default": {"default": "freccia a due punte a sinistra con doppio tratto verticale"}}}, {"key": "2B36", "mappings": {"default": {"default": "freccia a due punte a sinistra da barra"}}}, {"key": "2B37", "mappings": {"default": {"default": "freccia a triplo tratteggio a sinistra a due punte"}}}, {"key": "2B38", "mappings": {"default": {"default": "freccia a sinistra con stelo tratteggiato"}}}, {"key": "2B39", "mappings": {"default": {"default": "freccia sinistra con coda con tratto verticale"}}}, {"key": "2B3A", "mappings": {"default": {"default": "freccia sinistra con coda con doppio tratto verticale"}}}, {"key": "2B3B", "mappings": {"default": {"default": "freccia a due punte a sinistra con coda"}}}, {"key": "2B3C", "mappings": {"default": {"default": "freccia a due punte a sinistra con coda con tratto verticale"}}}, {"key": "2B3D", "mappings": {"default": {"default": "freccia a due punte a sinistra con coda con doppio tratto verticale"}}}, {"key": "2B3E", "mappings": {"default": {"default": "freccia verso sinistra attraverso X"}}}, {"key": "2B3F", "mappings": {"default": {"default": "freccia d'onda che punta direttamente a sinistra"}}}, {"key": "2B40", "mappings": {"default": {"default": "segno uguale sopra freccia sinistra"}}}, {"key": "2B41", "mappings": {"default": {"default": "operatore Tilde inverso sopra la freccia sinistra"}}}, {"key": "2B42", "mappings": {"default": {"default": "freccia sinistra verso sinistra al contrario quasi uguale a"}}}, {"key": "2B43", "mappings": {"default": {"default": "freccia verso destra per maggiore di"}}}, {"key": "2B44", "mappings": {"default": {"default": "freccia verso destra attraverso il Superset"}}}, {"key": "2B45", "mappings": {"default": {"default": "freccia quadrupla a sinistra"}}}, {"key": "2B46", "mappings": {"default": {"default": "freccia quadrupla rivolta verso destra"}}}, {"key": "2B47", "mappings": {"default": {"default": "tilde inverso sopra freccia verso destra"}}}, {"key": "2B48", "mappings": {"default": {"default": "freccia verso destra sopra il retromarcia quasi uguale a"}}}, {"key": "2B49", "mappings": {"default": {"default": "operatore Tilde sopra freccia sinistra"}}}, {"key": "2B4A", "mappings": {"default": {"default": "freccia sinistra verso sinistra quasi uguale a"}}}, {"key": "2B4B", "mappings": {"default": {"default": "freccia a sinistra sopra l'operatore Tilde inverso"}}}, {"key": "2B4C", "mappings": {"default": {"default": "freccia verso destra sopra l'operatore Tilde inverso"}}}, {"key": "FFE9", "mappings": {"default": {"default": "freccia a sinistra a metà campo"}}}, {"key": "FFEA", "mappings": {"default": {"default": "freccia verso l'alto a metà larghezza"}}}, {"key": "FFEB", "mappings": {"default": {"default": "freccia verso la metà a destra"}}}, {"key": "FFEC", "mappings": {"default": {"default": "freccia verso il basso a metà larghezza"}}}], "it/symbols/math_characters.min": [{"locale": "it"}, {"key": "2113", "mappings": {"default": {"default": "script l minuscola"}}}, {"key": "2118", "mappings": {"default": {"default": "script P <PERSON>"}}}, {"key": "213C", "mappings": {"default": {"default": "pi a grassetto da lavagna"}}}, {"key": "213D", "mappings": {"default": {"default": "gamma a grassetto da lavagna"}}}, {"key": "213E", "mappings": {"default": {"default": "gamma del capitale a grassetto da lavagna"}}}, {"key": "213F", "mappings": {"default": {"default": "<PERSON> maius<PERSON>lo a grassetto da lavagna"}}}, {"key": "2140", "mappings": {"default": {"default": "sommatoria a grassetto da lavagna"}}}, {"key": "2145", "mappings": {"default": {"default": "d ma<PERSON><PERSON> italico a grassetto da lavagna"}}}, {"key": "2146", "mappings": {"default": {"default": "d italico a grassetto da lavagna"}}}, {"key": "2147", "mappings": {"default": {"default": "e italico a grassetto da lavagna"}}}, {"key": "2148", "mappings": {"default": {"default": "i italico a grassetto da lavagna"}}}, {"key": "2149", "mappings": {"default": {"default": "j italico a grassetto da lavagna"}}}, {"key": "1D6A4", "mappings": {"default": {"default": "i senza punto"}}}, {"key": "1D6A5", "mappings": {"default": {"default": "j senza punto"}}}], "it/symbols/math_delimiters.min": [{"locale": "it"}, {"key": "0028", "mappings": {"default": {"default": "parentesi tonda aperta"}}}, {"key": "0029", "mappings": {"default": {"default": "parentesi tonda chiusa"}}}, {"key": "005B", "mappings": {"default": {"default": "parentesi quadra aperta"}}}, {"key": "005D", "mappings": {"default": {"default": "parentesi quadra chiusa"}}}, {"key": "007B", "mappings": {"default": {"default": "parentesi graffa aperta"}}}, {"key": "007D", "mappings": {"default": {"default": "parentesi graffa chiusa"}}}, {"key": "2045", "mappings": {"default": {"default": "parentesi quadrata sinistra con penna"}}}, {"key": "2046", "mappings": {"default": {"default": "parentesi quadrata destra con penna"}}}, {"key": "2308", "mappings": {"default": {"default": "parentesi sinistra di arrotondamento per eccesso"}}}, {"key": "2309", "mappings": {"default": {"default": "parentesi destra di arrotondamento per eccesso"}}}, {"key": "230A", "mappings": {"default": {"default": "parentesi sinistra di arrotondamento per difetto"}}}, {"key": "230B", "mappings": {"default": {"default": "parentesi destra di arrotondamento per difetto"}}}, {"key": "230C", "mappings": {"default": {"default": "crop in basso a destra"}}}, {"key": "230D", "mappings": {"default": {"default": "crop in basso a sinistra"}}}, {"key": "230E", "mappings": {"default": {"default": "crop in alto a destra"}}}, {"key": "230F", "mappings": {"default": {"default": "crop in alto a sinistra"}}}, {"key": "231C", "mappings": {"default": {"default": "angolo in alto a sinistra"}}}, {"key": "231D", "mappings": {"default": {"default": "angolo in alto a destra"}}}, {"key": "231E", "mappings": {"default": {"default": "angolo in basso a sinistra"}}}, {"key": "231F", "mappings": {"default": {"default": "angolo in basso a destra"}}}, {"key": "2320", "mappings": {"default": {"default": "mezzo integrale alto"}}}, {"key": "2321", "mappings": {"default": {"default": "mezzo intgrale basso"}}}, {"key": "2329", "mappings": {"default": {"default": "parentesi angolare sinistra"}}}, {"key": "232A", "mappings": {"default": {"default": "parentesi angolare a destra"}}}, {"key": "239B", "mappings": {"default": {"default": "parentesi a gancio superiore sinistro"}}}, {"key": "239C", "mappings": {"default": {"default": "estensione di parentesi sinistra"}}}, {"key": "239D", "mappings": {"default": {"default": "parentesi a gancio inferiore sinistro"}}}, {"key": "239E", "mappings": {"default": {"default": "parentesi a gancio superiore destro"}}}, {"key": "239F", "mappings": {"default": {"default": "estensione parentesi destra"}}}, {"key": "23A0", "mappings": {"default": {"default": "parentesi a gancio inferiore destro"}}}, {"key": "23A1", "mappings": {"default": {"default": "angolo superiore di parentesi quadra sinistra"}}}, {"key": "23A2", "mappings": {"default": {"default": "estensione per parentesi quadra sinistra"}}}, {"key": "23A3", "mappings": {"default": {"default": "angolo inferiore di parentesi quadra sinistra"}}}, {"key": "23A4", "mappings": {"default": {"default": "angolo superiore di parentesi quadra destra"}}}, {"key": "23A5", "mappings": {"default": {"default": "estensione per parentesi quadra destra"}}}, {"key": "23A6", "mappings": {"default": {"default": "angolo inferiore di parentesi quadra destra"}}}, {"key": "23A7", "mappings": {"default": {"default": "gancio superiore di parentesi sinistra"}}}, {"key": "23A8", "mappings": {"default": {"default": "parte centrale della parentesi graffa sinistra"}}}, {"key": "23A9", "mappings": {"default": {"default": "gancio inferiore di parentesi sinistra"}}}, {"key": "23AA", "mappings": {"default": {"default": "estensione di parentesi graffa"}}}, {"key": "23AB", "mappings": {"default": {"default": "gancio superiore di parentesi destra"}}}, {"key": "23AC", "mappings": {"default": {"default": "parte centrale della parentesi graffa destra"}}}, {"key": "23AD", "mappings": {"default": {"default": "gancio inferiore di parentesi destra"}}}, {"key": "23AE", "mappings": {"default": {"default": "estensione integrale"}}}, {"key": "23AF", "mappings": {"default": {"default": "estensione della linea orizzontale"}}}, {"key": "23B0", "mappings": {"default": {"default": "sezione di parentesi graffa superiore sinistra e destra inferiore"}}}, {"key": "23B1", "mappings": {"default": {"default": "sezione di parentesi graffa superiore destra e sinistra inferiore"}}}, {"key": "23B2", "mappings": {"default": {"default": "sommatoria superiore"}}}, {"key": "23B3", "mappings": {"default": {"default": "sommatoria inferiore"}}}, {"key": "23B4", "mappings": {"default": {"default": "parentesi quadra superiore"}}}, {"key": "23B5", "mappings": {"default": {"default": "parentesi quadra inferiore"}}}, {"key": "23B6", "mappings": {"default": {"default": "parentesi quadra inferiore su parentesi quadrata superiore"}}}, {"key": "23B7", "mappings": {"default": {"default": "simbolo radicale in basso"}}}, {"key": "23B8", "mappings": {"default": {"default": "riga verticale sinistra"}}}, {"key": "23B9", "mappings": {"default": {"default": "riga verticale destra"}}}, {"key": "23DC", "mappings": {"default": {"default": "parentesi superiore"}}}, {"key": "23DD", "mappings": {"default": {"default": "parentesi inferiore"}}}, {"key": "23DE", "mappings": {"default": {"default": "parentesi graffa superiore"}}}, {"key": "23DF", "mappings": {"default": {"default": "parentesi graffa inferiore"}}}, {"key": "23E0", "mappings": {"default": {"default": "parentesi a conchiglia superiore"}}}, {"key": "23E1", "mappings": {"default": {"default": "parentesi a conchiglia inferiore"}}}, {"key": "2768", "mappings": {"default": {"default": "ornamento di parentesi tonda sinistra medio"}}}, {"key": "2769", "mappings": {"default": {"default": "ornamento di parentesi tonda destra medio"}}}, {"key": "276A", "mappings": {"default": {"default": "ornamento di parentesi sinistra media appiattita"}}}, {"key": "276B", "mappings": {"default": {"default": "ornamento di parentesi destra media appiattita"}}}, {"key": "276C", "mappings": {"default": {"default": "ornamento di parentesi angolare sinistra media"}}}, {"key": "276D", "mappings": {"default": {"default": "ornamento di parentesi angolare destra media"}}}, {"key": "276E", "mappings": {"default": {"default": "ornamento di parentesi angolare sinistra grossa"}}}, {"key": "276F", "mappings": {"default": {"default": "ornamento di parentesi angolare destra grossa"}}}, {"key": "2770", "mappings": {"default": {"default": "ornamento di parentesi angolare sinistra grosso"}}}, {"key": "2771", "mappings": {"default": {"default": "ornamento di parentesi angolare destra grosso"}}}, {"key": "2772", "mappings": {"default": {"default": "ornamento di parentesi a conchiglia sinistro sottile"}}}, {"key": "2773", "mappings": {"default": {"default": "ornamento di parentesi a conchiglia destro sottile"}}}, {"key": "2774", "mappings": {"default": {"default": "ornamento di parentesi graffa sinistra medio"}}}, {"key": "2775", "mappings": {"default": {"default": "ornamento di parentesi graffa destra medio"}}}, {"key": "27C5", "mappings": {"default": {"default": "delimitatore bag sinistro a forma di S"}}}, {"key": "27C6", "mappings": {"default": {"default": "delimitatore bag destro a forma di S"}}}, {"key": "27E6", "mappings": {"default": {"default": "parentesi quadra sinistra bianca"}}}, {"key": "27E7", "mappings": {"default": {"default": "parentesi quadra destra bianca"}}}, {"key": "27E8", "mappings": {"default": {"default": "parentesi angolata sinistra"}}}, {"key": "27E9", "mappings": {"default": {"default": "parentesi angolata destra"}}}, {"key": "27EA", "mappings": {"default": {"default": "parentesi angolata sinistra doppia"}}}, {"key": "27EB", "mappings": {"default": {"default": "parentesi angolata destra doppia"}}}, {"key": "27EC", "mappings": {"default": {"default": "parentesi a conchiglia sinistra bianca"}}}, {"key": "27ED", "mappings": {"default": {"default": "parentesi a conchiglia destra bianca"}}}, {"key": "27EE", "mappings": {"default": {"default": "parentesi sinistra appiattita"}}}, {"key": "27EF", "mappings": {"default": {"default": "parentesi destra appiattita"}}}, {"key": "2983", "mappings": {"default": {"default": "parentesi graffa bianca sinistra"}}}, {"key": "2984", "mappings": {"default": {"default": "parentesi graffa bianca destra"}}}, {"key": "2985", "mappings": {"default": {"default": "parentesi tonda bianca sinistra"}}}, {"key": "2986", "mappings": {"default": {"default": "parentesi tonda bianca destra"}}}, {"key": "2987", "mappings": {"default": {"default": "parentesi immagine sinistra notazione Z"}}}, {"key": "2988", "mappings": {"default": {"default": "parentesi immagine destra di notazione Z"}}}, {"key": "2989", "mappings": {"default": {"default": "parentesi di rilegatura sinistra con notazione Z"}}}, {"key": "298A", "mappings": {"default": {"default": "parentesi di rilegatura destra a notazione Z"}}}, {"key": "298B", "mappings": {"default": {"default": "parentesi quadrata sinistra con sottobarra"}}}, {"key": "298C", "mappings": {"default": {"default": "parentesi quadra destra con sottobarra"}}}, {"key": "298D", "mappings": {"default": {"default": "parentesi quadrata sinistra con segno di spunta nell'angolo superiore"}}}, {"key": "298E", "mappings": {"default": {"default": "parentesi quadrata destra con segno di spunta nell'angolo inferiore"}}}, {"key": "298F", "mappings": {"default": {"default": "parentesi quadrata sinistra con segno di spunta nell'angolo inferiore"}}}, {"key": "2990", "mappings": {"default": {"default": "parentesi quadrata destra con segno di spunta nell'angolo superiore"}}}, {"key": "2991", "mappings": {"default": {"default": "parentesi angolata sinistra con punto"}}}, {"key": "2992", "mappings": {"default": {"default": "parentesi angolata destra con punto"}}}, {"key": "2993", "mappings": {"default": {"default": "parentesi sinistra ad arco con segno di minore"}}}, {"key": "2994", "mappings": {"default": {"default": "parentesi destra ad arco con segno di maggiore"}}}, {"key": "2995", "mappings": {"default": {"default": "parentesi sinistra a doppio arco con segno di minore"}}}, {"key": "2996", "mappings": {"default": {"default": "parentesi destra a doppio arco con segno di maggiore"}}}, {"key": "2997", "mappings": {"default": {"default": "parentesi a conchiglia nera sinistra"}}}, {"key": "2998", "mappings": {"default": {"default": "parentesi a conchiglia nera destra"}}}, {"key": "29D8", "mappings": {"default": {"default": "delimitatore a zig-zag sinistro"}}}, {"key": "29D9", "mappings": {"default": {"default": "delimitatore a zig-zag destro"}}}, {"key": "29DA", "mappings": {"default": {"default": "delimitatore doppio a zig-zag sinistro"}}}, {"key": "29DB", "mappings": {"default": {"default": "delimitatore doppio a zig-zag destro"}}}, {"key": "29FC", "mappings": {"default": {"default": "parentesi angolare curva a sinistra"}}}, {"key": "29FD", "mappings": {"default": {"default": "parentesi angolare curva a destra"}}}, {"key": "2E22", "mappings": {"default": {"default": "mezza parentesi superiore sinistra"}}}, {"key": "2E23", "mappings": {"default": {"default": "mezza parentesi superiore destra"}}}, {"key": "2E24", "mappings": {"default": {"default": "mezza parentesi inferiore sinistra"}}}, {"key": "2E25", "mappings": {"default": {"default": "mezza parentesi inferiore destra"}}}, {"key": "2E26", "mappings": {"default": {"default": "parentesi a U sdraiata sinistra"}}}, {"key": "2E27", "mappings": {"default": {"default": "parentesi a U sdraiata destra"}}}, {"key": "2E28", "mappings": {"default": {"default": "doppia parentesi aperta"}}}, {"key": "2E29", "mappings": {"default": {"default": "doppia parentesi chiusa"}}}, {"key": "3008", "mappings": {"default": {"default": "parentesi angolata sinistra"}}}, {"key": "3009", "mappings": {"default": {"default": "parentesi angolata destra "}}}, {"key": "300A", "mappings": {"default": {"default": "parentesi a doppio angolo sinistra"}}}, {"key": "300B", "mappings": {"default": {"default": "parentesi a doppio angolo destra"}}}, {"key": "300C", "mappings": {"default": {"default": "parentesi angolo superiore sinistro"}}}, {"key": "300D", "mappings": {"default": {"default": "parentesi angolo superiore destro"}}}, {"key": "300E", "mappings": {"default": {"default": "parentesi angolo superiore sinistro bianca"}}}, {"key": "300F", "mappings": {"default": {"default": "parentesi angolo superiore destro bianca"}}}, {"key": "3010", "mappings": {"default": {"default": "parentesi lenticolare nera sinistra"}}}, {"key": "3011", "mappings": {"default": {"default": "parentesi lenticolare nera destra"}}}, {"key": "3014", "mappings": {"default": {"default": "parentesi sinistra a conchiglia"}}}, {"key": "3015", "mappings": {"default": {"default": "parentesi destra a conchiglia"}}}, {"key": "3016", "mappings": {"default": {"default": "parentesi lenticolare bianca sinistra"}}}, {"key": "3017", "mappings": {"default": {"default": "parentesi lenticolare bianca destra"}}}, {"key": "3018", "mappings": {"default": {"default": "parentesi sinistra a conchiglia bianca"}}}, {"key": "3019", "mappings": {"default": {"default": "parentesi destra a conchiglia bianca "}}}, {"key": "301A", "mappings": {"default": {"default": "parentesi quadrata sinistra bianca"}}}, {"key": "301B", "mappings": {"default": {"default": "parentesi quadrata destra bianca"}}}, {"key": "301D", "mappings": {"default": {"default": "virgolette doppie a due punte invertite"}}}, {"key": "301E", "mappings": {"default": {"default": "doppie virgolette"}}}, {"key": "301F", "mappings": {"default": {"default": "do<PERSON><PERSON> virgolette basse"}}}, {"key": "FD3E", "mappings": {"default": {"default": "parentesi sinistra ornata"}}}, {"key": "FD3F", "mappings": {"default": {"default": "parentesi destra ornata"}}}, {"key": "FE17", "mappings": {"default": {"default": "parentesi lenticolare verticale sopra bianca"}}}, {"key": "FE18", "mappings": {"default": {"default": "parentesi lenticolare verticale sotto bianca"}}}, {"key": "FE35", "mappings": {"default": {"default": "parentesi sopra"}}}, {"key": "FE36", "mappings": {"default": {"default": "<PERSON><PERSON> sotto"}}}, {"key": "FE37", "mappings": {"default": {"default": "parentesi graffa sopra"}}}, {"key": "FE38", "mappings": {"default": {"default": "parentesi graffa sotto"}}}, {"key": "FE39", "mappings": {"default": {"default": "parentesi a conchiglia sopra"}}}, {"key": "FE3A", "mappings": {"default": {"default": "parentesi a conchiglia sotto"}}}, {"key": "FE3B", "mappings": {"default": {"default": "parentesi lenticolare verticale sopra nera"}}}, {"key": "FE3C", "mappings": {"default": {"default": "parentesi lenticolare verticale sotto nera"}}}, {"key": "FE3D", "mappings": {"default": {"default": "parentesi a doppio angolo sopra"}}}, {"key": "FE3E", "mappings": {"default": {"default": "parentesi a doppio angolo sotto"}}}, {"key": "FE3F", "mappings": {"default": {"default": "parentesi angolata sopra"}}}, {"key": "FE40", "mappings": {"default": {"default": "parentesi angolata sotto"}}}, {"key": "FE41", "mappings": {"default": {"default": "parentesi verticale ad angolo retto destro"}}}, {"key": "FE42", "mappings": {"default": {"default": "parentesi verticale ad angolo retto sinistro"}}}, {"key": "FE43", "mappings": {"default": {"default": "parentesi verticale ad angolo retto destro bianca"}}}, {"key": "FE44", "mappings": {"default": {"default": "parentesi verticale ad angolo retto sinistro bianca"}}}, {"key": "FE47", "mappings": {"default": {"default": "parentesi quadra sopra"}}}, {"key": "FE48", "mappings": {"default": {"default": "parentesi quadra sotto"}}}, {"key": "FE59", "mappings": {"default": {"default": "parentesi sinistra piccola"}}}, {"key": "FE5A", "mappings": {"default": {"default": "parentesi destra piccola"}}}, {"key": "FE5B", "mappings": {"default": {"default": "parentesi graffa sinistra piccola"}}}, {"key": "FE5C", "mappings": {"default": {"default": "parentesi graffa destra piccola"}}}, {"key": "FE5D", "mappings": {"default": {"default": "parentesi a conchiglia sinistra piccola"}}}, {"key": "FE5E", "mappings": {"default": {"default": "parentesi a conchiglia destra piccola"}}}, {"key": "FF08", "mappings": {"default": {"default": "parentesi sinistra a larghezza intera"}}}, {"key": "FF09", "mappings": {"default": {"default": "parentesi destra a larghezza intera"}}}, {"key": "FF3B", "mappings": {"default": {"default": "parentesi quadrata sinistra a larghezza intera"}}}, {"key": "FF3D", "mappings": {"default": {"default": "parentesi quadrata destra a larghezza intera"}}}, {"key": "FF5B", "mappings": {"default": {"default": "parentesi graffa sinistra a larghezza intera"}}}, {"key": "FF5D", "mappings": {"default": {"default": "parentesi graffa destra a larghezza intera"}}}, {"key": "FF5F", "mappings": {"default": {"default": "parentesi bianca sinistra a larghezza intera"}}}, {"key": "FF60", "mappings": {"default": {"default": "parentesi bianca destra a larghezza intera"}}}, {"key": "FF62", "mappings": {"default": {"default": "parentesi angolo superiore sinistro a mezza larghezza"}}}, {"key": "FF63", "mappings": {"default": {"default": "parentesi angolo superiore destro a mezza larghezza"}}}], "it/symbols/math_geometry.min": [{"locale": "it"}, {"key": "2500", "mappings": {"default": {"default": "box Drawings Light Horizontal"}}}, {"key": "2501", "mappings": {"default": {"default": "box Drawings Heavy Horizontal"}}}, {"key": "2502", "mappings": {"default": {"default": "cassetto verticale leggero"}}}, {"key": "2503", "mappings": {"default": {"default": "box Drawings Heavy Vertical"}}}, {"key": "2504", "mappings": {"default": {"default": "box Drawings Light Triple Dash Horizontal"}}}, {"key": "2505", "mappings": {"default": {"default": "box Drawings Heavy Triple Dash Horizontal"}}}, {"key": "2506", "mappings": {"default": {"default": "box Drawings Light Triple Dash Vertical"}}}, {"key": "2507", "mappings": {"default": {"default": "box Drawings Heavy Triple Dash Vertical"}}}, {"key": "2508", "mappings": {"default": {"default": "box Drawings Light Quadruple Dash Horizontal"}}}, {"key": "2509", "mappings": {"default": {"default": "disegni a riquadri Quadrato pesante Dash orizzontale"}}}, {"key": "250A", "mappings": {"default": {"default": "box Drawings Light Quadruple Dash Vertical"}}}, {"key": "250B", "mappings": {"default": {"default": "cassetto verticale pesante quadruplo Dash"}}}, {"key": "250C", "mappings": {"default": {"default": "disegni della scatola Light Down e Right"}}}, {"key": "250D", "mappings": {"default": {"default": "disegni della scatola Giù luce e destra pesante"}}}, {"key": "250E", "mappings": {"default": {"default": "box Disegna Giù Luce Pesante e Giusta"}}}, {"key": "250F", "mappings": {"default": {"default": "disegni scatola pesante in basso e a destra"}}}, {"key": "2510", "mappings": {"default": {"default": "disegni della scatola Light Down and Left"}}}, {"key": "2511", "mappings": {"default": {"default": "disegni scatola verso il basso luce e sinistra pesante"}}}, {"key": "2512", "mappings": {"default": {"default": "scatola Disegna Giù Luce Pesante e Sinistra"}}}, {"key": "2513", "mappings": {"default": {"default": "disegni scatola pesante in basso a sinistra"}}}, {"key": "2514", "mappings": {"default": {"default": "disegni della scatola Light Up and Right"}}}, {"key": "2515", "mappings": {"default": {"default": "box Draw Up Light and Right Heavy"}}}, {"key": "2516", "mappings": {"default": {"default": "box Draw Up Heavy e Right Light"}}}, {"key": "2517", "mappings": {"default": {"default": "disegni scatola pesante e destra"}}}, {"key": "2518", "mappings": {"default": {"default": "i disegni della scatola si illuminano a sinistra"}}}, {"key": "2519", "mappings": {"default": {"default": "box Disegna Light and Left Heavy"}}}, {"key": "251A", "mappings": {"default": {"default": "scatola Disegna Luce Pesante e Sinistra"}}}, {"key": "251B", "mappings": {"default": {"default": "disegni scatola pesante su e sinistra"}}}, {"key": "251C", "mappings": {"default": {"default": "box Drawings Light Vertical e Right"}}}, {"key": "251D", "mappings": {"default": {"default": "scatola di disegni di luce verticale e destra pesante"}}}, {"key": "251E", "mappings": {"default": {"default": "box Draw Up Heavy e Right Down Light"}}}, {"key": "251F", "mappings": {"default": {"default": "disegni scatola verso il basso Luce pesante e destra"}}}, {"key": "2520", "mappings": {"default": {"default": "scatola di disegni verticali pesanti e giusta luce"}}}, {"key": "2521", "mappings": {"default": {"default": "disegni della scatola Giù luce e destra sopra pesante"}}}, {"key": "2522", "mappings": {"default": {"default": "scatola in alto e in basso"}}}, {"key": "2523", "mappings": {"default": {"default": "box Drawings Heavy Vertical e Right"}}}, {"key": "2524", "mappings": {"default": {"default": "box Drawings Light Vertical and Left"}}}, {"key": "2525", "mappings": {"default": {"default": "scatola di disegni di luce verticale e sinistra pesante"}}}, {"key": "2526", "mappings": {"default": {"default": "box Draw Up Heavy e Left Down Light"}}}, {"key": "2527", "mappings": {"default": {"default": "disegni scatola verso il basso Luce pesante e sinistra"}}}, {"key": "2528", "mappings": {"default": {"default": "box Disegni verticali pesanti e luce sinistra"}}}, {"key": "2529", "mappings": {"default": {"default": "disegni della scatola Giù luce e sinistra su pesante"}}}, {"key": "252A", "mappings": {"default": {"default": "box Disegna Su e Giù <PERSON>"}}}, {"key": "252B", "mappings": {"default": {"default": "box Drawings Heavy Vertical e Left"}}}, {"key": "252C", "mappings": {"default": {"default": "disegni della scatola Light Down e Horizontal"}}}, {"key": "252D", "mappings": {"default": {"default": "box Drawings Left Heavy e Right Down Light"}}}, {"key": "252E", "mappings": {"default": {"default": "box Drawings Right Heavy e Left Down Light"}}}, {"key": "252F", "mappings": {"default": {"default": "disegni della scatola Giù leggero e orizzontale pesante"}}}, {"key": "2530", "mappings": {"default": {"default": "scatola Disegna Giù Luce Pesante e Orizzontale"}}}, {"key": "2531", "mappings": {"default": {"default": "disegni scatola destra e sinistra in basso pesante"}}}, {"key": "2532", "mappings": {"default": {"default": "disegni casella sinistra e destra verso il basso pesante"}}}, {"key": "2533", "mappings": {"default": {"default": "disegni scatola pesante in basso e orizzontale"}}}, {"key": "2534", "mappings": {"default": {"default": "disegni scatola illuminati e orizzontali"}}}, {"key": "2535", "mappings": {"default": {"default": "box Drawings Left Heavy e Right Up Light"}}}, {"key": "2536", "mappings": {"default": {"default": "box Drawings Right Heavy e Left Up Light"}}}, {"key": "2537", "mappings": {"default": {"default": "box Draw Up Light e Horizontal Heavy"}}}, {"key": "2538", "mappings": {"default": {"default": "scatola Disegna Luce Pesante e Orizzontale"}}}, {"key": "2539", "mappings": {"default": {"default": "scatola di disegni a destra e sinistra su pesante"}}}, {"key": "253A", "mappings": {"default": {"default": "box Drawings Left Light e Right Up Heavy"}}}, {"key": "253B", "mappings": {"default": {"default": "disegni scatola pesante e orizzontale"}}}, {"key": "253C", "mappings": {"default": {"default": "box Drawings Light verticale e orizzontale"}}}, {"key": "253D", "mappings": {"default": {"default": "scatola di disegni sinistra Luce verticale pesante e destra"}}}, {"key": "253E", "mappings": {"default": {"default": "cassetto a destra Luce verticale pesante e sinistra"}}}, {"key": "253F", "mappings": {"default": {"default": "scatola di disegni verticali leggeri e orizzontali pesanti"}}}, {"key": "2540", "mappings": {"default": {"default": "box Disegna la luce orizzontale pesante e discendente"}}}, {"key": "2541", "mappings": {"default": {"default": "box Drawings Down Heavy e Up Horizontal Light"}}}, {"key": "2542", "mappings": {"default": {"default": "scatola di disegni verticali pesanti e luce orizzontale"}}}, {"key": "2543", "mappings": {"default": {"default": "box Drawings Left Up Heavy e Right Down Light"}}}, {"key": "2544", "mappings": {"default": {"default": "disegni di riquadri in alto a destra e in basso a sinistra"}}}, {"key": "2545", "mappings": {"default": {"default": "disegni casella sinistra verso il basso Luce pesante e destra"}}}, {"key": "2546", "mappings": {"default": {"default": "box Drawings Right Down Heavy e Left Up Light"}}}, {"key": "2547", "mappings": {"default": {"default": "scatola di disegni in basso e in alto orizzontale pesante"}}}, {"key": "2548", "mappings": {"default": {"default": "scatola Disegna Su e Giù Pesante Orizzontale"}}}, {"key": "2549", "mappings": {"default": {"default": "<PERSON><PERSON>tto Disegni Luce Destra e Pesante Verticale Sinistra"}}}, {"key": "254A", "mappings": {"default": {"default": "cassetto a sinistra e destra verticale pesante"}}}, {"key": "254B", "mappings": {"default": {"default": "box Drawings Heavy Vertical e Horizontal"}}}, {"key": "254C", "mappings": {"default": {"default": "box Drawings Light Double Dash Horizontal"}}}, {"key": "254D", "mappings": {"default": {"default": "box Drawings Heavy Double Dash Horizontal"}}}, {"key": "254E", "mappings": {"default": {"default": "box Drawings Light Double Dash Vertical"}}}, {"key": "254F", "mappings": {"default": {"default": "box Drawings Heavy Double Dash Vertical"}}}, {"key": "2550", "mappings": {"default": {"default": "box Drawings Double Horizontal"}}}, {"key": "2551", "mappings": {"default": {"default": "box Drawings Double Vertical"}}}, {"key": "2552", "mappings": {"default": {"default": "box Disegni Giù Doppio Singolo e Destro"}}}, {"key": "2553", "mappings": {"default": {"default": "box Disegni Giù Doppio e Destro Singolo"}}}, {"key": "2554", "mappings": {"default": {"default": "disegni della casella Double Down e Right"}}}, {"key": "2555", "mappings": {"default": {"default": "disegni della casella in basso a destra ea sinistra"}}}, {"key": "2556", "mappings": {"default": {"default": "disegni casella giù doppio e singolo sinistro"}}}, {"key": "2557", "mappings": {"default": {"default": "box Disegni Double Down and Left"}}}, {"key": "2558", "mappings": {"default": {"default": "riquadro Disegni singoli e doppi"}}}, {"key": "2559", "mappings": {"default": {"default": "box Drawings Up Double and Right Single"}}}, {"key": "255A", "mappings": {"default": {"default": "box Drawings Double Up e Right"}}}, {"key": "255B", "mappings": {"default": {"default": "riquadro Disegni singoli e doppio sinistro"}}}, {"key": "255C", "mappings": {"default": {"default": "box Drawings Up Double and Left Single"}}}, {"key": "255D", "mappings": {"default": {"default": "box Disegni Double Up e Left"}}}, {"key": "255E", "mappings": {"default": {"default": "box Drawings Vertical Single and Right Double"}}}, {"key": "255F", "mappings": {"default": {"default": "box Drawings Vertical Double and Right Single"}}}, {"key": "2560", "mappings": {"default": {"default": "box Disegni Doppio verticale e destro"}}}, {"key": "2561", "mappings": {"default": {"default": "box Disegni singoli verticali e doppio sinistro"}}}, {"key": "2562", "mappings": {"default": {"default": "box Disegni Verticale doppio e singolo sinistro"}}}, {"key": "2563", "mappings": {"default": {"default": "box Disegni Doppio verticale e sinistro"}}}, {"key": "2564", "mappings": {"default": {"default": "box Disegni Giù Singolo e Doppio orizzontale"}}}, {"key": "2565", "mappings": {"default": {"default": "box Disegni Giù Do<PERSON>io e Singolo orizzontale"}}}, {"key": "2566", "mappings": {"default": {"default": "box Disegni Double Down e Horizontal"}}}, {"key": "2567", "mappings": {"default": {"default": "box Drawings Up Single and Horizontal Double"}}}, {"key": "2568", "mappings": {"default": {"default": "box Drawings Up Double and Horizontal Single"}}}, {"key": "2569", "mappings": {"default": {"default": "box Drawings Double Up e Horizontal"}}}, {"key": "256A", "mappings": {"default": {"default": "box Disegni verticali singolo e orizzontale doppio"}}}, {"key": "256B", "mappings": {"default": {"default": "box Drawings Vertical Double and Horizontal Single"}}}, {"key": "256C", "mappings": {"default": {"default": "box Disegni doppio verticale e orizzontale"}}}, {"key": "256D", "mappings": {"default": {"default": "box Drawings Light Arc Down e Right"}}}, {"key": "256E", "mappings": {"default": {"default": "box Drawings Light Arc Down and Left"}}}, {"key": "256F", "mappings": {"default": {"default": "box Drawings Light Arc Up and Left"}}}, {"key": "2570", "mappings": {"default": {"default": "box Drawings Light Arc Up e Right"}}}, {"key": "2571", "mappings": {"default": {"default": "disegni della scatola Luce diagonale in alto a destra in basso a sinistra"}}}, {"key": "2572", "mappings": {"default": {"default": "box Disegni Luce diagonale Superiore sinistra a destra inferiore"}}}, {"key": "2573", "mappings": {"default": {"default": "croce di disegni a croce diagonale"}}}, {"key": "2574", "mappings": {"default": {"default": "box Drawings Light Left"}}}, {"key": "2575", "mappings": {"default": {"default": "box Drawings Light Up"}}}, {"key": "2576", "mappings": {"default": {"default": "box Drawings Light Right"}}}, {"key": "2577", "mappings": {"default": {"default": "box Drawings Light Down"}}}, {"key": "2578", "mappings": {"default": {"default": "box Drawings Heavy Left"}}}, {"key": "2579", "mappings": {"default": {"default": "box Drawings Heavy Up"}}}, {"key": "257A", "mappings": {"default": {"default": "box Drawings Heavy Right"}}}, {"key": "257B", "mappings": {"default": {"default": "box Drawings Heavy Down"}}}, {"key": "257C", "mappings": {"default": {"default": "disegni scatola sinistra sinistra e destra pesante"}}}, {"key": "257D", "mappings": {"default": {"default": "disegni scatola illuminati e pesanti"}}}, {"key": "257E", "mappings": {"default": {"default": "box Drawings Heavy Left e Light Right"}}}, {"key": "257F", "mappings": {"default": {"default": "box Drawings Heavy Up e Light Down"}}}, {"key": "2580", "mappings": {"default": {"default": "mezzo blocco superiore"}}}, {"key": "2581", "mappings": {"default": {"default": "lower One Eighth Block"}}}, {"key": "2582", "mappings": {"default": {"default": "lower One Quarter Block"}}}, {"key": "2583", "mappings": {"default": {"default": "lower Three Eighths Block"}}}, {"key": "2584", "mappings": {"default": {"default": "lower Half Block"}}}, {"key": "2585", "mappings": {"default": {"default": "lower Five Eighths Block"}}}, {"key": "2586", "mappings": {"default": {"default": "lower Three Quarters Block"}}}, {"key": "2587", "mappings": {"default": {"default": "lower Seven Eighths Block"}}}, {"key": "2588", "mappings": {"default": {"default": "full Block"}}}, {"key": "2589", "mappings": {"default": {"default": "left Seven Eighths Block"}}}, {"key": "258A", "mappings": {"default": {"default": "left Three Quarters Block"}}}, {"key": "258B", "mappings": {"default": {"default": "left Five Eighths Block"}}}, {"key": "258C", "mappings": {"default": {"default": "left Half Block"}}}, {"key": "258D", "mappings": {"default": {"default": "left Three Eighths Block"}}}, {"key": "258E", "mappings": {"default": {"default": "left One Quarter Block"}}}, {"key": "258F", "mappings": {"default": {"default": "a sinistra un ottavo blocco"}}}, {"key": "2590", "mappings": {"default": {"default": "right Half Block"}}}, {"key": "2591", "mappings": {"default": {"default": "ombra leggera"}}}, {"key": "2592", "mappings": {"default": {"default": "ombra media"}}}, {"key": "2593", "mappings": {"default": {"default": "ombra scura"}}}, {"key": "2594", "mappings": {"default": {"default": "upper One Eighth Block"}}}, {"key": "2595", "mappings": {"default": {"default": "un ottavo blocco destro"}}}, {"key": "2596", "mappings": {"default": {"default": "quadrante in basso a sinistra"}}}, {"key": "2597", "mappings": {"default": {"default": "quadrante in basso a destra"}}}, {"key": "2598", "mappings": {"default": {"default": "quadrante in alto a sinistra"}}}, {"key": "2599", "mappings": {"default": {"default": "quadrante in alto a sinistra e in basso a sinistra e in basso a destra"}}}, {"key": "259A", "mappings": {"default": {"default": "quadrante in alto a sinistra e in basso a destra"}}}, {"key": "259B", "mappings": {"default": {"default": "quadrante in alto a sinistra e in alto a destra e in basso a sinistra"}}}, {"key": "259C", "mappings": {"default": {"default": "quadrante in alto a sinistra e in alto a destra e in basso a destra"}}}, {"key": "259D", "mappings": {"default": {"default": "quadrante in alto a destra"}}}, {"key": "259E", "mappings": {"default": {"default": "quadrante in alto a destra e in basso a sinistra"}}}, {"key": "259F", "mappings": {"default": {"default": "quadrante in alto a destra e in basso a sinistra e in basso a destra"}}}, {"key": "25A0", "mappings": {"default": {"default": "quadrato nero"}}}, {"key": "25A1", "mappings": {"default": {"default": "quadrato bianco"}}}, {"key": "25A2", "mappings": {"default": {"default": "quadrato bianco con angoli arrotondati"}}}, {"key": "25A3", "mappings": {"default": {"default": "quadrato bianco contenente quadratino nero"}}}, {"key": "25A4", "mappings": {"default": {"default": "quadrato con riempimento orizzontale"}}}, {"key": "25A5", "mappings": {"default": {"default": "quadrato con riempimento verticale"}}}, {"key": "25A6", "mappings": {"default": {"default": "quadrato con riempimento tratteggio ortogonale"}}}, {"key": "25A7", "mappings": {"default": {"default": "quadrato con riempimento sinistro in alto a destra inferiore"}}}, {"key": "25A8", "mappings": {"default": {"default": "quadrato con parte superiore destra per riempire in basso a sinistra"}}}, {"key": "25A9", "mappings": {"default": {"default": "quadrato con riempimento diagonale a campitura incrociata"}}}, {"key": "25AA", "mappings": {"default": {"default": "quadrato nero"}}}, {"key": "25AB", "mappings": {"default": {"default": "quadrato bianco"}}}, {"key": "25AC", "mappings": {"default": {"default": "retta<PERSON>lo nero"}}}, {"key": "25AD", "mappings": {"default": {"default": "rettangolo bianco"}}}, {"key": "25AE", "mappings": {"default": {"default": "rettangolo verticale nero"}}}, {"key": "25AF", "mappings": {"default": {"default": "rettangolo verticale bianco"}}}, {"key": "25B0", "mappings": {"default": {"default": "parallelogramma nero"}}}, {"key": "25B1", "mappings": {"default": {"default": "parallelogramma bianco"}}}, {"key": "25B2", "mappings": {"default": {"default": "triangolo up-pointing nero"}}}, {"key": "25B3", "mappings": {"default": {"default": "triangolo up-pointing bianco"}}}, {"key": "25B4", "mappings": {"default": {"default": "triangolo piccolo a punta nera"}}}, {"key": "25B5", "mappings": {"default": {"default": "piccolo triangolo bianco che punta in alto"}}}, {"key": "25B6", "mappings": {"default": {"default": "triangolo nero a destra"}}}, {"key": "25B7", "mappings": {"default": {"default": "triangolo bianco a destra"}}}, {"key": "25B8", "mappings": {"default": {"default": "triangolo nero a punta destra"}}}, {"key": "25B9", "mappings": {"default": {"default": "triangolo piccolo a destra bianco"}}}, {"key": "25BA", "mappings": {"default": {"default": "puntatore a punta destra nera"}}}, {"key": "25BB", "mappings": {"default": {"default": "puntatore a destra bianco"}}}, {"key": "25BC", "mappings": {"default": {"default": "triangolo verso il basso nero"}}}, {"key": "25BD", "mappings": {"default": {"default": "triangolo verso il basso bianco"}}}, {"key": "25BE", "mappings": {"default": {"default": "triangolo piccolo a discesa nera"}}}, {"key": "25BF", "mappings": {"default": {"default": "triangolo piccolo a discesa bianca"}}}, {"key": "25C0", "mappings": {"default": {"default": "triangolo nero a sinistra"}}}, {"key": "25C1", "mappings": {"default": {"default": "triangolo bianco a sinistra"}}}, {"key": "25C2", "mappings": {"default": {"default": "triangolo nero a punta sinistra"}}}, {"key": "25C3", "mappings": {"default": {"default": "triangolo piccolo a sinistra bianco"}}}, {"key": "25C4", "mappings": {"default": {"default": "puntatore a punta sinistra nero"}}}, {"key": "25C5", "mappings": {"default": {"default": "puntatore a sinistra bianco"}}}, {"key": "25C6", "mappings": {"default": {"default": "diamante nero"}}}, {"key": "25C7", "mappings": {"default": {"default": "diamante bianco"}}}, {"key": "25C8", "mappings": {"default": {"default": "diamante bianco contenente un piccolo diamante nero"}}}, {"key": "25C9", "mappings": {"default": {"default": "fisheye"}}}, {"key": "25CA", "mappings": {"default": {"default": "losanga"}}}, {"key": "25CB", "mappings": {"default": {"default": "cerchio bianco"}}}, {"key": "25CC", "mappings": {"default": {"default": "cer<PERSON>o <PERSON>"}}}, {"key": "25CD", "mappings": {"default": {"default": "cerchio con riempimento verticale"}}}, {"key": "25CE", "mappings": {"default": {"default": "bullseye"}}}, {"key": "25CF", "mappings": {"default": {"default": "cerchio nero"}}}, {"key": "25D0", "mappings": {"default": {"default": "cerchia con metà sinistra nera"}}}, {"key": "25D1", "mappings": {"default": {"default": "cerchia con metà nero a destra"}}}, {"key": "25D2", "mappings": {"default": {"default": "cerchia con metà nero inferiore"}}}, {"key": "25D3", "mappings": {"default": {"default": "cerchia con metà nero superiore"}}}, {"key": "25D4", "mappings": {"default": {"default": "cerchio con quadrante superiore destro nero"}}}, {"key": "25D5", "mappings": {"default": {"default": "cerchia con quadrante in bianco e nero"}}}, {"key": "25D6", "mappings": {"default": {"default": "sinistra Half Black Circle"}}}, {"key": "25D7", "mappings": {"default": {"default": "right Half Black Circle"}}}, {"key": "25D8", "mappings": {"default": {"default": "bullet Inverse"}}}, {"key": "25D9", "mappings": {"default": {"default": "cerchio bianco inverso"}}}, {"key": "25DA", "mappings": {"default": {"default": "cerchio bianco superiore a metà superiore"}}}, {"key": "25DB", "mappings": {"default": {"default": "cerchio bianco inferiore a metà inferiore"}}}, {"key": "25DC", "mappings": {"default": {"default": "arco circolare del quadrante in alto a sinistra"}}}, {"key": "25DD", "mappings": {"default": {"default": "arco circolare del quadrante superiore destro"}}}, {"key": "25DE", "mappings": {"default": {"default": "arco circolare quadrante inferiore destro"}}}, {"key": "25DF", "mappings": {"default": {"default": "arco circolare del quadrante inferiore sinistro"}}}, {"key": "25E0", "mappings": {"default": {"default": "mezza Cerchio Superiore"}}}, {"key": "25E1", "mappings": {"default": {"default": "semicerchio inferiore"}}}, {"key": "25E2", "mappings": {"default": {"default": "triangolo nero in basso a destra"}}}, {"key": "25E3", "mappings": {"default": {"default": "triangolo nero in basso a sinistra"}}}, {"key": "25E4", "mappings": {"default": {"default": "triangolo nero in alto a sinistra"}}}, {"key": "25E5", "mappings": {"default": {"default": "triangolo nero superiore destro"}}}, {"key": "25E6", "mappings": {"default": {"default": "white Bullet"}}}, {"key": "25E7", "mappings": {"default": {"default": "quadrato con mezzo nero a sinistra"}}}, {"key": "25E8", "mappings": {"default": {"default": "quadrato con mezzo mezzo nero"}}}, {"key": "25E9", "mappings": {"default": {"default": "quadrato con diagonale mezza sinistra superiore nera"}}}, {"key": "25EA", "mappings": {"default": {"default": "quadrato con semitono diagonale inferiore destro"}}}, {"key": "25EB", "mappings": {"default": {"default": "white Square con Vertical Bisecting Line"}}}, {"key": "25EC", "mappings": {"default": {"default": "triangolo up-pointing bianco con punto"}}}, {"key": "25ED", "mappings": {"default": {"default": "triangolo verso l'alto con metà sinistra nero"}}}, {"key": "25EE", "mappings": {"default": {"default": "triangolo verso l'alto con il mezzo nero a destra"}}}, {"key": "25EF", "mappings": {"default": {"default": "grande cerchio"}}}, {"key": "25F0", "mappings": {"default": {"default": "quadrato bianco con quadrante in alto a sinistra"}}}, {"key": "25F1", "mappings": {"default": {"default": "quadrato bianco con quadrante inferiore sinistro"}}}, {"key": "25F2", "mappings": {"default": {"default": "quadrato bianco con quadrante inferiore destro"}}}, {"key": "25F3", "mappings": {"default": {"default": "quadrato bianco con quadrante in alto a destra"}}}, {"key": "25F4", "mappings": {"default": {"default": "cerchio bianco con quadrante in alto a sinistra"}}}, {"key": "25F5", "mappings": {"default": {"default": "cerchio bianco con quadrante in basso a sinistra"}}}, {"key": "25F6", "mappings": {"default": {"default": "cerchio bianco con quadrante inferiore destro"}}}, {"key": "25F7", "mappings": {"default": {"default": "cerchio bianco con quadrante in alto a destra"}}}, {"key": "25F8", "mappings": {"default": {"default": "triangolo in alto a sinistra"}}}, {"key": "25F9", "mappings": {"default": {"default": "triangolo in alto a destra"}}}, {"key": "25FA", "mappings": {"default": {"default": "triangolo in basso a sinistra"}}}, {"key": "25FB", "mappings": {"default": {"default": "quadrato medio bianco"}}}, {"key": "25FC", "mappings": {"default": {"default": "quadrato nero medio"}}}, {"key": "25FD", "mappings": {"default": {"default": "quadrato piccolo medio bianco"}}}, {"key": "25FE", "mappings": {"default": {"default": "quadrato nero medio piccolo"}}}, {"key": "25FF", "mappings": {"default": {"default": "triangolo in basso a destra"}}}, {"key": "2B12", "mappings": {"default": {"default": "quadrato con Top Half Black"}}}, {"key": "2B13", "mappings": {"default": {"default": "quadrato con fondo nero mezzo"}}}, {"key": "2B14", "mappings": {"default": {"default": "quadrato con mezza diagonale superiore destra nera"}}}, {"key": "2B15", "mappings": {"default": {"default": "quadrato con semitono diagonale inferiore sinistro"}}}, {"key": "2B16", "mappings": {"default": {"default": "diamante con mezzo nero a sinistra"}}}, {"key": "2B17", "mappings": {"default": {"default": "diamante con mezzo mezzo nero"}}}, {"key": "2B18", "mappings": {"default": {"default": "diaman<PERSON> con Top Half Black"}}}, {"key": "2B19", "mappings": {"default": {"default": "diamante con fondo mezzo nero"}}}, {"key": "2B1A", "mappings": {"default": {"default": "piazza punt<PERSON>ta"}}}, {"key": "2B1B", "mappings": {"default": {"default": "grande quadrato nero"}}}, {"key": "2B1C", "mappings": {"default": {"default": "grande quadrato bianco"}}}, {"key": "2B1D", "mappings": {"default": {"default": "quadrato nero molto piccolo"}}}, {"key": "2B1E", "mappings": {"default": {"default": "quadrato bianco molto piccolo"}}}, {"key": "2B1F", "mappings": {"default": {"default": "pentagono nero"}}}, {"key": "2B20", "mappings": {"default": {"default": "pentagono bianco"}}}, {"key": "2B21", "mappings": {"default": {"default": "esagono bianco"}}}, {"key": "2B22", "mappings": {"default": {"default": "esagono nero"}}}, {"key": "2B23", "mappings": {"default": {"default": "esagono nero orizzontale"}}}, {"key": "2B24", "mappings": {"default": {"default": "grande cerchio nero"}}}, {"key": "2B25", "mappings": {"default": {"default": "diamante medio nero"}}}, {"key": "2B26", "mappings": {"default": {"default": "diamante medio bianco"}}}, {"key": "2B27", "mappings": {"default": {"default": "black Medium Losanga"}}}, {"key": "2B28", "mappings": {"default": {"default": "losanga medio bianco"}}}, {"key": "2B29", "mappings": {"default": {"default": "piccolo diamante nero"}}}, {"key": "2B2A", "mappings": {"default": {"default": "piccola losanga nera"}}}, {"key": "2B2B", "mappings": {"default": {"default": "piccola losanga bianca"}}}, {"key": "2B2C", "mappings": {"default": {"default": "el<PERSON><PERSON> oriz<PERSON> nera"}}}, {"key": "2B2D", "mappings": {"default": {"default": "ellisse orizzontale bianca"}}}, {"key": "2B2E", "mappings": {"default": {"default": "ellisse verticale nera"}}}, {"key": "2B2F", "mappings": {"default": {"default": "ellisse verticale bianca"}}}, {"key": "2B50", "mappings": {"default": {"default": "stella media bianca"}}}, {"key": "2B51", "mappings": {"default": {"default": "stella piccola nera"}}}, {"key": "2B52", "mappings": {"default": {"default": "piccola stella bianca"}}}, {"key": "2B53", "mappings": {"default": {"default": "pentagono nero a destra"}}}, {"key": "2B54", "mappings": {"default": {"default": "pentagono bianco a destra"}}}, {"key": "2B55", "mappings": {"default": {"default": "grande cerchio pesante"}}}, {"key": "2B56", "mappings": {"default": {"default": "ovale pesante con interno ovale"}}}, {"key": "2B57", "mappings": {"default": {"default": "heavy Circle con Circle Inside"}}}, {"key": "2B58", "mappings": {"default": {"default": "heavy Circle"}}}, {"key": "2B59", "mappings": {"default": {"default": "heavy Circle Saltire"}}}], "it/symbols/math_harpoons.min": [{"locale": "it"}, {"key": "21BC", "mappings": {"default": {"default": "arpione sinistro sopra"}}}, {"key": "21BD", "mappings": {"default": {"default": "arpione sinistro in basso"}}}, {"key": "21BE", "mappings": {"default": {"default": "arpione su a destra"}}}, {"key": "21BF", "mappings": {"default": {"default": "arpione su a sinistra"}}}, {"key": "21C0", "mappings": {"default": {"default": "arpione destro sopra"}}}, {"key": "21C1", "mappings": {"default": {"default": "arpione destro in basso"}}}, {"key": "21C2", "mappings": {"default": {"default": "arpione giù a destra"}}}, {"key": "21C3", "mappings": {"default": {"default": "arpione giù a sinistra"}}}, {"key": "21CB", "mappings": {"default": {"default": "arpione sinistro sopra arpione destro"}}}, {"key": "21CC", "mappings": {"default": {"default": "arpione destro sopra arpione sinistro"}}}, {"key": "294A", "mappings": {"default": {"default": "sinistra Barb Up Right Barb Down Harpoon"}}}, {"key": "294B", "mappings": {"default": {"default": "a sinistra Barb giù a destra Barb Up Harpoon"}}}, {"key": "294C", "mappings": {"default": {"default": "up Barb Right Down Barb Left Harpoon"}}}, {"key": "294D", "mappings": {"default": {"default": "up Barb Left Down Barb Right Harpoon"}}}, {"key": "294E", "mappings": {"default": {"default": "a sinistra Barb Up Right Barb Up Harpoon"}}}, {"key": "294F", "mappings": {"default": {"default": "arpione su e giù verso destra"}}}, {"key": "2950", "mappings": {"default": {"default": "sinistra Barb Down Right Barb Down Harpoon"}}}, {"key": "2951", "mappings": {"default": {"default": "arpione su e giù verso sinistra"}}}, {"key": "2952", "mappings": {"default": {"default": "arpione a sinistra con Barb Up To Bar"}}}, {"key": "2953", "mappings": {"default": {"default": "giusto arpione con Barb Up To Bar"}}}, {"key": "2954", "mappings": {"default": {"default": "upward Harpoon con Barb Right To Bar"}}}, {"key": "2955", "mappings": {"default": {"default": "downward Harpoon con Barb Right To Bar"}}}, {"key": "2956", "mappings": {"default": {"default": "arpione a sinistra con Barb Down To Bar"}}}, {"key": "2957", "mappings": {"default": {"default": "giusto arpione con Barb giù al bar"}}}, {"key": "2958", "mappings": {"default": {"default": "upward Harpoon con Barb Left To Bar"}}}, {"key": "2959", "mappings": {"default": {"default": "downward Harpoon con Barb Left To Bar"}}}, {"key": "295A", "mappings": {"default": {"default": "arpione a sinistra con Barb Up from Bar"}}}, {"key": "295B", "mappings": {"default": {"default": "g<PERSON><PERSON> con Barb Up from Bar"}}}, {"key": "295C", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Right from Bar"}}}, {"key": "295D", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Right from Bar"}}}, {"key": "295E", "mappings": {"default": {"default": "arpione a sinistra con Barb giù dal bar"}}}, {"key": "295F", "mappings": {"default": {"default": "g<PERSON><PERSON> con <PERSON><PERSON> g<PERSON><PERSON>"}}}, {"key": "2960", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Left from Bar"}}}, {"key": "2961", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Left from Bar"}}}, {"key": "2962", "mappings": {"default": {"default": "arpione a sinistra con Barb in alto a sinistra Arpione con Barb giù"}}}, {"key": "2963", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Left Beside Upward Harpoon with <PERSON><PERSON> Right"}}}, {"key": "2964", "mappings": {"default": {"default": "a destra Arpione con Barb Up Above Rightward con Barb Down"}}}, {"key": "2965", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Left Beside Downward Harpoon with <PERSON><PERSON> Right"}}}, {"key": "2966", "mappings": {"default": {"default": "arpione a sinistra con Barb Up Above Rightward con Barb Up"}}}, {"key": "2967", "mappings": {"default": {"default": "arpione a sinistra con Barb giù in alto a destra Arpione con Barb giù"}}}, {"key": "2968", "mappings": {"default": {"default": "a destra Arpione con Barb Up in alto a sinistra Harpoon con Barb Up"}}}, {"key": "2969", "mappings": {"default": {"default": "a destra Arpione con Barb giù in alto a sinistra Arpione con Barb giù"}}}, {"key": "296A", "mappings": {"default": {"default": "arpione a sinistra con Barb Up Above Long Dash"}}}, {"key": "296B", "mappings": {"default": {"default": "arpione a sinistra con Barb giù sotto Long Dash"}}}, {"key": "296C", "mappings": {"default": {"default": "giusto arpione con Barb Up Above Long Dash"}}}, {"key": "296D", "mappings": {"default": {"default": "a destra Arpione con Barb giù sotto Long Dash"}}}, {"key": "296E", "mappings": {"default": {"default": "upward Harpoon with <PERSON><PERSON> Left Beside Downward Harpoon with <PERSON><PERSON> Right"}}}, {"key": "296F", "mappings": {"default": {"default": "downward Harpoon with <PERSON><PERSON> Left Beside Upward Harpoon with <PERSON><PERSON> Right"}}}, {"key": "297C", "mappings": {"default": {"default": "coda di pesce sinistra"}}}, {"key": "297D", "mappings": {"default": {"default": "coda di pesce giusta"}}}, {"key": "297E", "mappings": {"default": {"default": "coda di pesce"}}}, {"key": "297F", "mappings": {"default": {"default": "giù coda di pesce"}}}], "it/symbols/math_non_characters.min": [{"locale": "it"}, {"key": "210F", "mappings": {"default": {"default": "h tagliata", "physics": "planck Constant Over Two Pi"}}}, {"key": "2114", "mappings": {"default": {"default": "l B Bar Symbol"}}}, {"key": "2116", "mappings": {"default": {"default": "simbolo di numero"}}}, {"key": "2117", "mappings": {"default": {"default": "copyright della registrazione del suono"}}}, {"key": "211E", "mappings": {"default": {"default": "presa di prescrizione"}}}, {"key": "211F", "mappings": {"default": {"default": "risposta"}}}, {"key": "2120", "mappings": {"default": {"default": "segno di servizio"}}}, {"key": "2121", "mappings": {"default": {"default": "segno del telefono"}}}, {"key": "2122", "mappings": {"default": {"default": "segno di treid mark"}}}, {"key": "2123", "mappings": {"default": {"default": "versicle"}}}, {"key": "2125", "mappings": {"default": {"default": "segno di oncia"}}}, {"key": "2126", "mappings": {"default": {"default": "simbolo dell'ohm"}}}, {"key": "2127", "mappings": {"default": {"default": "ohm invertito"}}}, {"key": "212A", "mappings": {"default": {"default": "se<PERSON>"}}}, {"key": "212B", "mappings": {"default": {"default": "angstrom"}}}, {"key": "212E", "mappings": {"default": {"default": "simbolo stimato"}}}, {"key": "2132", "mappings": {"default": {"default": "turned f maiuscola"}}}, {"key": "2139", "mappings": {"default": {"default": "fonte di informazione"}}}, {"key": "213A", "mappings": {"default": {"default": "capitale ruotato Q"}}}, {"key": "213B", "mappings": {"default": {"default": "segno di facsimile"}}}, {"key": "2141", "mappings": {"default": {"default": "trasformato Sans-Serif Capitale G"}}}, {"key": "2142", "mappings": {"default": {"default": "trasformato Sans-Serif Capital L"}}}, {"key": "2143", "mappings": {"default": {"default": "capitale Sans-Serif inversa L"}}}, {"key": "2144", "mappings": {"default": {"default": "svolta Sans-Serif Capital Y"}}}], "it/symbols/math_symbols.min": [{"locale": "it"}, {"key": "0021", "mappings": {"default": {"default": "punto esclamativo"}}}, {"key": "0022", "mappings": {"default": {"default": "vir<PERSON><PERSON>"}}}, {"key": "0023", "mappings": {"default": {"default": "cancelletto"}}}, {"key": "0024", "mappings": {"default": {"default": "dollaro"}}}, {"key": "0025", "mappings": {"default": {"default": "percento"}}}, {"key": "0026", "mappings": {"default": {"default": "e commerciale"}}}, {"key": "0027", "mappings": {"default": {"default": "apostrofo"}}}, {"key": "002A", "mappings": {"default": {"default": "asterisco"}}}, {"key": "002B", "mappings": {"default": {"default": "più"}}}, {"key": "002C", "mappings": {"default": {"default": "virgola"}}}, {"key": "002D", "mappings": {"default": {"default": "meno"}}}, {"key": "002E", "mappings": {"default": {"default": "punto"}}}, {"key": "002F", "mappings": {"default": {"default": "diviso"}}}, {"key": "003A", "mappings": {"default": {"default": "due punti"}}}, {"key": "003B", "mappings": {"default": {"default": "punto e virgola"}}}, {"key": "003C", "mappings": {"default": {"default": "minore di"}}}, {"key": "003D", "mappings": {"default": {"default": "uguale a"}}}, {"key": "003E", "mappings": {"default": {"default": "maggiore di"}}}, {"key": "003F", "mappings": {"default": {"default": "punto interrogativo"}}}, {"key": "0040", "mappings": {"default": {"default": "simbolo at"}}}, {"key": "005C", "mappings": {"default": {"default": "back slash"}}}, {"key": "005E", "mappings": {"default": {"default": "accento circonflesso"}}}, {"key": "005F", "mappings": {"default": {"default": "barra sotto"}}}, {"key": "0060", "mappings": {"default": {"default": "accento grave"}}}, {"key": "007C", "mappings": {"default": {"default": "barra verticale"}}}, {"key": "007E", "mappings": {"default": {"default": "tilde"}}}, {"key": "00A1", "mappings": {"default": {"default": "punto esclamativo rovesciato"}}}, {"key": "00A2", "mappings": {"default": {"default": "cent"}}}, {"key": "00A3", "mappings": {"default": {"default": "simbolo sterlina"}}}, {"key": "00A4", "mappings": {"default": {"default": "segno di valuta"}}}, {"key": "00A5", "mappings": {"default": {"default": "simbolo yen"}}}, {"key": "00A6", "mappings": {"default": {"default": "barra verticale interrotta"}}}, {"key": "00A7", "mappings": {"default": {"default": "simbolo di riferimento a sezione"}}}, {"key": "00A8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "00A9", "mappings": {"default": {"default": "copyright"}}}, {"key": "00AA", "mappings": {"default": {"default": "indicatore di ordinale femminile"}}}, {"key": "00AB", "mappings": {"default": {"default": "simbolo di citazione a doppio angolo verso sinistra"}}}, {"key": "00AC", "mappings": {"default": {"default": "simbolo not"}}}, {"key": "00AE", "mappings": {"default": {"default": "simbolo di marchio registrato"}}}, {"key": "00AF", "mappings": {"default": {"default": "barra sopra"}}}, {"key": "00B0", "mappings": {"default": {"default": "gradi"}}}, {"key": "00B1", "mappings": {"default": {"default": "più o meno"}}}, {"key": "00B4", "mappings": {"default": {"default": "accento acuto"}}}, {"key": "00B5", "mappings": {"default": {"default": "segno di micro"}}}, {"key": "00B6", "mappings": {"default": {"default": "segno di paragrafo"}}}, {"key": "00B7", "mappings": {"default": {"default": "puntino nel mezzo"}}}, {"key": "00B8", "mappings": {"default": {"default": "cediglia"}}}, {"key": "00BA", "mappings": {"default": {"default": "indicatore di ordinale maschile"}}}, {"key": "00BB", "mappings": {"default": {"default": "simbolo di citazione a doppio angolo verso destra"}}}, {"key": "00BF", "mappings": {"default": {"default": "punto di domanda rovesciato"}}}, {"key": "00D7", "mappings": {"default": {"default": "per", "alternative": "moltiplica<PERSON>"}}}, {"key": "00F7", "mappings": {"default": {"default": "diviso"}}}, {"key": "02B9", "mappings": {"default": {"default": "primo"}}}, {"key": "02BA", "mappings": {"default": {"default": "doppio primo"}}}, {"key": "02D8", "mappings": {"default": {"default": "breve"}}}, {"key": "02D9", "mappings": {"default": {"default": "punto sopra"}}}, {"key": "02DA", "mappings": {"default": {"default": "anello sopra"}}}, {"key": "02DB", "mappings": {"default": {"default": "ogonek"}}}, {"key": "02DC", "mappings": {"default": {"default": "tilde"}}}, {"key": "02DD", "mappings": {"default": {"default": "doppio accento acuto"}}}, {"key": "2010", "mappings": {"default": {"default": "meno"}}}, {"key": "2011", "mappings": {"default": {"default": "lineetta senza interruzioni"}}}, {"key": "2012", "mappings": {"default": {"default": "lineetta cifra"}}}, {"key": "2013", "mappings": {"default": {"default": "lineetta enne"}}}, {"key": "2014", "mappings": {"default": {"default": "lineetta emme"}}}, {"key": "2015", "mappings": {"default": {"default": "lineetta di citazione"}}}, {"key": "2016", "mappings": {"default": {"default": "doppia barra verticale"}}}, {"key": "2017", "mappings": {"default": {"default": "doppia linea orizzontale bassa"}}}, {"key": "2018", "mappings": {"default": {"default": "virgol<PERSON> sinistra singola"}}}, {"key": "2019", "mappings": {"default": {"default": "virgoletta destra singola"}}}, {"key": "201A", "mappings": {"default": {"default": "segno di citazione singolo basso a destra"}}}, {"key": "201B", "mappings": {"default": {"default": "virgolette singola invertita a sinistra"}}}, {"key": "201C", "mappings": {"default": {"default": "segno di citazione doppio a sinistra"}}}, {"key": "201D", "mappings": {"default": {"default": "segno di citazione doppio a destra"}}}, {"key": "201E", "mappings": {"default": {"default": "doppio segno di citazione basso a destra"}}}, {"key": "201F", "mappings": {"default": {"default": "virgolette doppie invertite a sinistra"}}}, {"key": "2020", "mappings": {"default": {"default": "obelisco"}}}, {"key": "2021", "mappings": {"default": {"default": "doppio obelisco"}}}, {"key": "2022", "mappings": {"default": {"default": "punto elenco"}}}, {"key": "2023", "mappings": {"default": {"default": "punto elenco triangolare"}}}, {"key": "2024", "mappings": {"default": {"default": "punto"}}}, {"key": "2025", "mappings": {"default": {"default": "due punti in orizzontale"}}}, {"key": "2026", "mappings": {"default": {"default": "punti di sospensione"}}}, {"key": "2027", "mappings": {"default": {"default": "punto di sillabazione"}}}, {"key": "2030", "mappings": {"default": {"default": "segno per mille"}}}, {"key": "2031", "mappings": {"default": {"default": "segno per diecimila"}}}, {"key": "2032", "mappings": {"default": {"default": "primo"}}}, {"key": "2033", "mappings": {"default": {"default": "doppio primo"}}}, {"key": "2034", "mappings": {"default": {"default": "triplo primo"}}}, {"key": "2035", "mappings": {"default": {"default": "primo inverso"}}}, {"key": "2036", "mappings": {"default": {"default": "doppio primo inverso"}}}, {"key": "2037", "mappings": {"default": {"default": "triple Prime invertito"}}}, {"key": "2038", "mappings": {"default": {"default": "segno di omissione"}}}, {"key": "2039", "mappings": {"default": {"default": "simbolo di citazione ad angolo singolo verso sinistra"}}}, {"key": "203A", "mappings": {"default": {"default": "simbolo di citazione ad angolo singolo verso destra"}}}, {"key": "203B", "mappings": {"default": {"default": "segno di riferimento"}}}, {"key": "203C", "mappings": {"default": {"default": "doppio punto esclamativo"}}}, {"key": "203D", "mappings": {"default": {"default": "punto esclarrogativo"}}}, {"key": "203E", "mappings": {"default": {"default": "barra superiore"}}}, {"key": "203F", "mappings": {"default": {"default": "legatura invertita"}}}, {"key": "2040", "mappings": {"default": {"default": "carattere di legatura"}}}, {"key": "2041", "mappings": {"default": {"default": "punto di inserimento del caret"}}}, {"key": "2042", "mappings": {"default": {"default": "asterismo"}}}, {"key": "2043", "mappings": {"default": {"default": "carattere elenco a trattino"}}}, {"key": "2044", "mappings": {"default": {"default": "barra di frazione"}}}, {"key": "2047", "mappings": {"default": {"default": "doppio punto interrogativo"}}}, {"key": "2048", "mappings": {"default": {"default": "punto interrogativo ed esclamativo"}}}, {"key": "2049", "mappings": {"default": {"default": "punto esclamativo e interrogativo"}}}, {"key": "204B", "mappings": {"default": {"default": "segno Pilcrow invertito"}}}, {"key": "204C", "mappings": {"default": {"default": "proiettile nero verso sinistra"}}}, {"key": "204D", "mappings": {"default": {"default": "proiettile nero verso destra"}}}, {"key": "204E", "mappings": {"default": {"default": "as<PERSON><PERSON> basso"}}}, {"key": "204F", "mappings": {"default": {"default": "punto e virgola invertito"}}}, {"key": "2050", "mappings": {"default": {"default": "avvicinamento"}}}, {"key": "2051", "mappings": {"default": {"default": "due asterischi allineati verticalmente"}}}, {"key": "2052", "mappings": {"default": {"default": "segno meno commerciale"}}}, {"key": "2053", "mappings": {"default": {"default": "lineetta ondulata"}}}, {"key": "2054", "mappings": {"default": {"default": "carattere di legatura invertito"}}}, {"key": "2055", "mappings": {"default": {"default": "punteggiatura a fiori"}}}, {"key": "2056", "mappings": {"default": {"default": "punteggiatura a tre punti"}}}, {"key": "2057", "mappings": {"default": {"default": "quattro volte primo"}}}, {"key": "2058", "mappings": {"default": {"default": "punteggiatura a quattro punti"}}}, {"key": "2059", "mappings": {"default": {"default": "punteggiatura a cinque punti"}}}, {"key": "205A", "mappings": {"default": {"default": "punteggiatura a due punti"}}}, {"key": "205B", "mappings": {"default": {"default": "segno di quattro punti"}}}, {"key": "205C", "mappings": {"default": {"default": "croce punteggia<PERSON>"}}}, {"key": "205D", "mappings": {"default": {"default": "tre punti verticali"}}}, {"key": "205E", "mappings": {"default": {"default": "quattro punti verticali"}}}, {"key": "207A", "mappings": {"default": {"default": "apice più"}}}, {"key": "207B", "mappings": {"default": {"default": "apice meno"}}}, {"key": "207C", "mappings": {"default": {"default": "apice meno"}}}, {"key": "207D", "mappings": {"default": {"default": "apice parentesi aperta"}}}, {"key": "207E", "mappings": {"default": {"default": "apice parentesi chiusa"}}}, {"key": "208A", "mappings": {"default": {"default": "pedice più"}}}, {"key": "208B", "mappings": {"default": {"default": "pedice meno"}}}, {"key": "208C", "mappings": {"default": {"default": "pedice uguale"}}}, {"key": "208D", "mappings": {"default": {"default": "pedice parentesi aperta"}}}, {"key": "208E", "mappings": {"default": {"default": "pedice parentesi chiusa"}}}, {"key": "214A", "mappings": {"default": {"default": "linea di proprietà"}}}, {"key": "214B", "mappings": {"default": {"default": "e commerciale capovolta"}}}, {"key": "214C", "mappings": {"default": {"default": "per"}}}, {"key": "214D", "mappings": {"default": {"default": "aktieselskab"}}}, {"key": "214E", "mappings": {"default": {"default": "F maiuscola piccola capovolta"}}}, {"key": "2200", "mappings": {"default": {"default": "per ogni"}}}, {"key": "2201", "mappings": {"default": {"default": "complemento"}}}, {"key": "2203", "mappings": {"default": {"default": "esiste"}}}, {"key": "2204", "mappings": {"default": {"default": "non esiste"}}}, {"key": "2205", "mappings": {"default": {"default": "insieme vuoto"}}}, {"key": "2206", "mappings": {"default": {"default": "incremento"}}}, {"key": "2208", "mappings": {"default": {"default": "appartenente a"}}}, {"key": "2209", "mappings": {"default": {"default": "non appartenente a"}}}, {"key": "220A", "mappings": {"default": {"default": "appartenente a"}}}, {"key": "220B", "mappings": {"default": {"default": "contiene come membro"}}}, {"key": "220C", "mappings": {"default": {"default": "non contiene come membro"}}}, {"key": "220D", "mappings": {"default": {"default": "contiene come membro piccolo"}}}, {"key": "220E", "mappings": {"default": {"default": "fine dimostrazione"}}}, {"key": "220F", "mappings": {"default": {"default": "produttoria"}}}, {"key": "2210", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>o"}}}, {"key": "2211", "mappings": {"default": {"default": "sommatoria"}}}, {"key": "2212", "mappings": {"default": {"default": "meno"}}}, {"key": "2213", "mappings": {"default": {"default": "meno più"}}}, {"key": "2214", "mappings": {"default": {"default": "punto più"}}}, {"key": "2215", "mappings": {"default": {"default": "barra di divisione"}}}, {"key": "2216", "mappings": {"default": {"default": "insieme meno"}}}, {"key": "2217", "mappings": {"default": {"default": "asterisco"}}}, {"key": "2218", "mappings": {"default": {"default": "composizione"}}}, {"key": "2219", "mappings": {"default": {"default": "operatore punto"}}}, {"key": "221A", "mappings": {"default": {"default": "radice quadrata"}}}, {"key": "221B", "mappings": {"default": {"default": "radice cubica"}}}, {"key": "221C", "mappings": {"default": {"default": "radice quarta"}}}, {"key": "221D", "mappings": {"default": {"default": "proporzionale a"}}}, {"key": "221E", "mappings": {"default": {"default": "infinito"}}}, {"key": "221F", "mappings": {"default": {"default": "angolo retto"}}}, {"key": "2220", "mappings": {"default": {"default": "angolo"}}}, {"key": "2221", "mappings": {"default": {"default": "angolo misurato"}}}, {"key": "2222", "mappings": {"default": {"default": "angolo sferico"}}}, {"key": "2223", "mappings": {"default": {"default": "divide"}}}, {"key": "2224", "mappings": {"default": {"default": "non divide"}}}, {"key": "2225", "mappings": {"default": {"default": "parallelo a"}}}, {"key": "2226", "mappings": {"default": {"default": "non parallelo a"}}}, {"key": "2227", "mappings": {"default": {"default": "e"}}}, {"key": "2228", "mappings": {"default": {"default": "o"}}}, {"key": "2229", "mappings": {"default": {"default": "intersezione"}}}, {"key": "222A", "mappings": {"default": {"default": "unione"}}}, {"key": "222B", "mappings": {"default": {"default": "integrale"}}}, {"key": "222C", "mappings": {"default": {"default": "integrale doppio"}}}, {"key": "222D", "mappings": {"default": {"default": "integrale triplo"}}}, {"key": "222E", "mappings": {"default": {"default": "integrale di contorno"}}}, {"key": "222F", "mappings": {"default": {"default": "integrale di superficie"}}}, {"key": "2230", "mappings": {"default": {"default": "integrale di volume"}}}, {"key": "2231", "mappings": {"default": {"default": "integrale in senso orario"}}}, {"key": "2232", "mappings": {"default": {"default": "integrale di contorno in senso orario"}}}, {"key": "2233", "mappings": {"default": {"default": "integrale di contorno in senso anti-orario"}}}, {"key": "2234", "mappings": {"default": {"default": "quindi"}}}, {"key": "2235", "mappings": {"default": {"default": "poiché"}}}, {"key": "2236", "mappings": {"default": {"default": "rapporto"}}}, {"key": "2237", "mappings": {"default": {"default": "proporzione"}}}, {"key": "2238", "mappings": {"default": {"default": "punto meno"}}}, {"key": "2239", "mappings": {"default": {"default": "eccede"}}}, {"key": "223A", "mappings": {"default": {"default": "proporzione geometrica"}}}, {"key": "223B", "mappings": {"default": {"default": "omotetico"}}}, {"key": "223C", "mappings": {"default": {"default": "tilde"}}}, {"key": "223D", "mappings": {"default": {"default": "tilde invertita"}}}, {"key": "223E", "mappings": {"default": {"default": "s invertita"}}}, {"key": "223F", "mappings": {"default": {"default": "onda seno"}}}, {"key": "2240", "mappings": {"default": {"default": "prodotto corona"}}}, {"key": "2241", "mappings": {"default": {"default": "non tilde"}}}, {"key": "2242", "mappings": {"default": {"default": "meno tilde"}}}, {"key": "2243", "mappings": {"default": {"default": "asintoticamente uguale a"}}}, {"key": "2244", "mappings": {"default": {"default": "non asintoticamente uguale a"}}}, {"key": "2245", "mappings": {"default": {"default": "approssimativamente uguale a"}}}, {"key": "2246", "mappings": {"default": {"default": "approssimativamente ma non effettivamente uguale a"}}}, {"key": "2247", "mappings": {"default": {"default": "né approssimativamente né effettivamente uguale a"}}}, {"key": "2248", "mappings": {"default": {"default": "circa uguale a"}}}, {"key": "2249", "mappings": {"default": {"default": "non circa uguale a"}}}, {"key": "224A", "mappings": {"default": {"default": "uguale circa o uguale a"}}}, {"key": "224B", "mappings": {"default": {"default": "tripla tilde"}}}, {"key": "224C", "mappings": {"default": {"default": "tutto uguale a"}}}, {"key": "224D", "mappings": {"default": {"default": "equivalente a"}}}, {"key": "224E", "mappings": {"default": {"default": "geometricamente equivalente a"}}}, {"key": "224F", "mappings": {"default": {"default": "differenza fra"}}}, {"key": "2250", "mappings": {"default": {"default": "si avvicina al limite"}}}, {"key": "2251", "mappings": {"default": {"default": "geometricamente uguale a"}}}, {"key": "2252", "mappings": {"default": {"default": "approssimatamente uguale a o immagine di"}}}, {"key": "2253", "mappings": {"default": {"default": "immagine di o approssimatamente uguale a"}}}, {"key": "2254", "mappings": {"default": {"default": "due punti uguale"}}}, {"key": "2255", "mappings": {"default": {"default": "uguale due punti"}}}, {"key": "2256", "mappings": {"default": {"default": "anello in uguale a"}}}, {"key": "2257", "mappings": {"default": {"default": "anello uguale a"}}}, {"key": "2258", "mappings": {"default": {"default": "corrisponde a"}}}, {"key": "2259", "mappings": {"default": {"default": "stima"}}}, {"key": "225A", "mappings": {"default": {"default": "equiangolare a"}}}, {"key": "225B", "mappings": {"default": {"default": "star uguale a"}}}, {"key": "225C", "mappings": {"default": {"default": "delta uguale a"}}}, {"key": "225D", "mappings": {"default": {"default": "per definizione uguale a"}}}, {"key": "225E", "mappings": {"default": {"default": "mi<PERSON><PERSON> da"}}}, {"key": "225F", "mappings": {"default": {"default": "uguale con punto di domanda"}}}, {"key": "2260", "mappings": {"default": {"default": "diverso da"}}}, {"key": "2261", "mappings": {"default": {"default": "identico a"}}}, {"key": "2262", "mappings": {"default": {"default": "non identico a"}}}, {"key": "2263", "mappings": {"default": {"default": "strettamente equivalente a"}}}, {"key": "2264", "mappings": {"default": {"default": "minore o uguale a"}}}, {"key": "2265", "mappings": {"default": {"default": "maggiore o uguale a"}}}, {"key": "2266", "mappings": {"default": {"default": "minore di sopra uguale a"}}}, {"key": "2267", "mappings": {"default": {"default": "maggiore di sopra uguale a"}}}, {"key": "2268", "mappings": {"default": {"default": "minore di ma non uguale a"}}}, {"key": "2269", "mappings": {"default": {"default": "maggiore di ma non uguale a"}}}, {"key": "226A", "mappings": {"default": {"default": "molto minore di"}}}, {"key": "226B", "mappings": {"default": {"default": "molto maggiore di"}}}, {"key": "226C", "mappings": {"default": {"default": "fra"}}}, {"key": "226D", "mappings": {"default": {"default": "non e' equivalente a"}}}, {"key": "226E", "mappings": {"default": {"default": "non minore di"}}}, {"key": "226F", "mappings": {"default": {"default": "non maggiore di"}}}, {"key": "2270", "mappings": {"default": {"default": "né minore di né uguale a"}}}, {"key": "2271", "mappings": {"default": {"default": "né maggiore di o uguale a"}}}, {"key": "2272", "mappings": {"default": {"default": "minore o uguale a"}}}, {"key": "2273", "mappings": {"default": {"default": "maggiore o uguale a"}}}, {"key": "2274", "mappings": {"default": {"default": "né minore di né equivalente a"}}}, {"key": "2275", "mappings": {"default": {"default": "né maggiore di né equivalente a"}}}, {"key": "2276", "mappings": {"default": {"default": "minore o maggiore di"}}}, {"key": "2277", "mappings": {"default": {"default": "maggiore o minore di"}}}, {"key": "2278", "mappings": {"default": {"default": "né minore di né maggiore di"}}}, {"key": "2279", "mappings": {"default": {"default": "né maggiore di né minore di"}}}, {"key": "227A", "mappings": {"default": {"default": "precede"}}}, {"key": "227B", "mappings": {"default": {"default": "segue"}}}, {"key": "227C", "mappings": {"default": {"default": "precede o equivale a"}}}, {"key": "227D", "mappings": {"default": {"default": "segue o equivale a"}}}, {"key": "227E", "mappings": {"default": {"default": "precede o equivale a"}}}, {"key": "227F", "mappings": {"default": {"default": "segue o equivale a"}}}, {"key": "2280", "mappings": {"default": {"default": "non precede"}}}, {"key": "2281", "mappings": {"default": {"default": "non segue"}}}, {"key": "2282", "mappings": {"default": {"default": "sottoinsieme di"}}}, {"key": "2283", "mappings": {"default": {"default": "superinsieme di"}}}, {"key": "2284", "mappings": {"default": {"default": "non un sottoinsieme di"}}}, {"key": "2285", "mappings": {"default": {"default": "non un super insieme di"}}}, {"key": "2286", "mappings": {"default": {"default": "sottoinsieme di o uguale a"}}}, {"key": "2287", "mappings": {"default": {"default": "superinsieme di o uguale a"}}}, {"key": "2288", "mappings": {"default": {"default": "né un sottoinsieme di né uguale a"}}}, {"key": "2289", "mappings": {"default": {"default": "né un super insieme di né uguale a"}}}, {"key": "228A", "mappings": {"default": {"default": "sottoinsieme di o non uguale a"}}}, {"key": "228B", "mappings": {"default": {"default": "super insieme di o non uguale a"}}}, {"key": "228C", "mappings": {"default": {"default": "multi insieme"}}}, {"key": "228D", "mappings": {"default": {"default": "moltiplicazione multi insieme"}}}, {"key": "228E", "mappings": {"default": {"default": "unione multi insieme"}}}, {"key": "228F", "mappings": {"default": {"default": "immagine quadrata di"}}}, {"key": "2290", "mappings": {"default": {"default": "quadrato originale di"}}}, {"key": "2291", "mappings": {"default": {"default": "immagine quadrata di o uguale a"}}}, {"key": "2292", "mappings": {"default": {"default": "quadrato originale di o uguale a"}}}, {"key": "2293", "mappings": {"default": {"default": "cappuccio quadrato"}}}, {"key": "2294", "mappings": {"default": {"default": "coppa quadrata"}}}, {"key": "2295", "mappings": {"default": {"default": "più cer<PERSON>to"}}}, {"key": "2296", "mappings": {"default": {"default": "meno cerchiato"}}}, {"key": "2297", "mappings": {"default": {"default": "moltiplicazione cerchiata"}}}, {"key": "2298", "mappings": {"default": {"default": "divisione cerchiata"}}}, {"key": "2299", "mappings": {"default": {"default": "punto cerchiato"}}}, {"key": "229A", "mappings": {"default": {"default": "operatore ad anello cerchiato"}}}, {"key": "229B", "mappings": {"default": {"default": "operatore di asterisco cerchiato"}}}, {"key": "229C", "mappings": {"default": {"default": "uguale cerchiato"}}}, {"key": "229D", "mappings": {"default": {"default": "lineetta cerchiata"}}}, {"key": "229E", "mappings": {"default": {"default": "più squadrato"}}}, {"key": "229F", "mappings": {"default": {"default": "meno squadrato"}}}, {"key": "22A0", "mappings": {"default": {"default": "per squadrato"}}}, {"key": "22A1", "mappings": {"default": {"default": "operatore punto squadrato"}}}, {"key": "22A2", "mappings": {"default": {"default": "<PERSON>ello"}}}, {"key": "22A3", "mappings": {"default": {"default": "tornello inverso"}}}, {"key": "22A4", "mappings": {"default": {"default": "tornello verso il basso"}}}, {"key": "22A5", "mappings": {"default": {"default": "perpendicolare"}}}, {"key": "22A6", "mappings": {"default": {"default": "asserzione"}}}, {"key": "22A7", "mappings": {"default": {"default": "modella"}}}, {"key": "22A8", "mappings": {"default": {"default": "vero"}}}, {"key": "22A9", "mappings": {"default": {"default": "forza"}}}, {"key": "22AA", "mappings": {"default": {"default": "tornello verticale a tripla barra"}}}, {"key": "22AB", "mappings": {"default": {"default": "tornello verticale a doppia barra"}}}, {"key": "22AC", "mappings": {"default": {"default": "non prova"}}}, {"key": "22AD", "mappings": {"default": {"default": "non vero"}}}, {"key": "22AE", "mappings": {"default": {"default": "non forza"}}}, {"key": "22AF", "mappings": {"default": {"default": "tornello verticale a doppia barra negata"}}}, {"key": "22B0", "mappings": {"default": {"default": "precede sotto la relazione di"}}}, {"key": "22B1", "mappings": {"default": {"default": "segue sotto la relazione di"}}}, {"key": "22B2", "mappings": {"default": {"default": "sottogruppo normale di"}}}, {"key": "22B3", "mappings": {"default": {"default": "contiene come sottogruppo normale"}}}, {"key": "22B4", "mappings": {"default": {"default": "sottogruppo normale di o uguale a"}}}, {"key": "22B5", "mappings": {"default": {"default": "contiene come sottogruppo normale o uguale a"}}}, {"key": "22B6", "mappings": {"default": {"default": "originale di"}}}, {"key": "22B7", "mappings": {"default": {"default": "immagine di"}}}, {"key": "22B8", "mappings": {"default": {"default": "multimappa"}}}, {"key": "22B9", "mappings": {"default": {"default": "matrice coniugale ermitica"}}}, {"key": "22BA", "mappings": {"default": {"default": "intercalare"}}}, {"key": "22BB", "mappings": {"default": {"default": "xor"}}}, {"key": "22BC", "mappings": {"default": {"default": "nand"}}}, {"key": "22BD", "mappings": {"default": {"default": "nor"}}}, {"key": "22BF", "mappings": {"default": {"default": "triangolo rettangolo"}}}, {"key": "22C0", "mappings": {"default": {"default": "and"}}}, {"key": "22C1", "mappings": {"default": {"default": "or"}}}, {"key": "22C2", "mappings": {"default": {"default": "intersezione"}}}, {"key": "22C3", "mappings": {"default": {"default": "unione"}}}, {"key": "22C4", "mappings": {"default": {"default": "operatore diamante"}}}, {"key": "22C5", "mappings": {"default": {"default": "punto"}}}, {"key": "22C6", "mappings": {"default": {"default": "operatore star"}}}, {"key": "22C7", "mappings": {"default": {"default": "divisione con segno moltiplicazione"}}}, {"key": "22C8", "mappings": {"default": {"default": "cravatta a farfalla"}}}, {"key": "22C9", "mappings": {"default": {"default": "prodotto semidiretto fattore sinistro normale"}}}, {"key": "22CA", "mappings": {"default": {"default": "prodotto semidiretto fattore destro normale"}}}, {"key": "22CB", "mappings": {"default": {"default": "prodotto semidiretto sinistro"}}}, {"key": "22CC", "mappings": {"default": {"default": "prodotto semidiretto destro"}}}, {"key": "22CD", "mappings": {"default": {"default": "tilde invertita uguale"}}}, {"key": "22CE", "mappings": {"default": {"default": "or logico riccio"}}}, {"key": "22CF", "mappings": {"default": {"default": "e logico riccio"}}}, {"key": "22D0", "mappings": {"default": {"default": "do<PERSON>io sotto<PERSON>me"}}}, {"key": "22D1", "mappings": {"default": {"default": "doppio superinsieme"}}}, {"key": "22D2", "mappings": {"default": {"default": "doppia intersezione"}}}, {"key": "22D3", "mappings": {"default": {"default": "doppia unione"}}}, {"key": "22D4", "mappings": {"default": {"default": "intersezione corretta"}}}, {"key": "22D5", "mappings": {"default": {"default": "uguale e parallelo a"}}}, {"key": "22D6", "mappings": {"default": {"default": "minore di con punto"}}}, {"key": "22D7", "mappings": {"default": {"default": "maggiore di con punto"}}}, {"key": "22D8", "mappings": {"default": {"default": "molto minore di"}}}, {"key": "22D9", "mappings": {"default": {"default": "molto maggiore di"}}}, {"key": "22DA", "mappings": {"default": {"default": "minore di o uguale a o maggiore di"}}}, {"key": "22DB", "mappings": {"default": {"default": "maggiore di o uguale a o minore di"}}}, {"key": "22DC", "mappings": {"default": {"default": "uguale o minore di"}}}, {"key": "22DD", "mappings": {"default": {"default": "uguale o maggiore di"}}}, {"key": "22DE", "mappings": {"default": {"default": "uguale a o precede"}}}, {"key": "22DF", "mappings": {"default": {"default": "uguale a o segue"}}}, {"key": "22E0", "mappings": {"default": {"default": "non precede né eguaglia"}}}, {"key": "22E1", "mappings": {"default": {"default": "non segue né eguaglia"}}}, {"key": "22E2", "mappings": {"default": {"default": "non è immagine quadrata di o uguale a"}}}, {"key": "22E3", "mappings": {"default": {"default": "non è originale quadrato di o uguale a"}}}, {"key": "22E4", "mappings": {"default": {"default": "immagine quadrata di o diverso da"}}}, {"key": "22E5", "mappings": {"default": {"default": "originale quadrato uguale o diverso da"}}}, {"key": "22E6", "mappings": {"default": {"default": "minore di ma non equivalente a"}}}, {"key": "22E7", "mappings": {"default": {"default": "maggiore di ma non equivalente a"}}}, {"key": "22E8", "mappings": {"default": {"default": "precede ma non equivale a"}}}, {"key": "22E9", "mappings": {"default": {"default": "segue ma non equivale a"}}}, {"key": "22EA", "mappings": {"default": {"default": "non sottogruppo normale"}}}, {"key": "22EB", "mappings": {"default": {"default": "non contiene come sottogruppo normale"}}}, {"key": "22EC", "mappings": {"default": {"default": "non sottogruppo normale o uguale a"}}}, {"key": "22ED", "mappings": {"default": {"default": "non contiene come sottogruppo normale o uguale"}}}, {"key": "22EE", "mappings": {"default": {"default": "puntini in verticale"}}}, {"key": "22EF", "mappings": {"default": {"default": "puntini in orizzontale"}}}, {"key": "22F0", "mappings": {"default": {"default": "puntini in diagonale verso alto destra"}}}, {"key": "22F1", "mappings": {"default": {"default": "puntini in diagonale verso basso destra"}}}, {"key": "22F2", "mappings": {"default": {"default": "elemento di con tratto orizzontale lungo"}}}, {"key": "22F3", "mappings": {"default": {"default": "elemento con barra verticale all'estremità del tratto orizzontale"}}}, {"key": "22F4", "mappings": {"default": {"default": "piccolo elemento di con barra verticale all'estremità del tratto orizzontale"}}}, {"key": "22F5", "mappings": {"default": {"default": "elemento di con punto sopra"}}}, {"key": "22F6", "mappings": {"default": {"default": "elemento di con barra sopra"}}}, {"key": "22F7", "mappings": {"default": {"default": "piccolo elemento di con barra sopra"}}}, {"key": "22F8", "mappings": {"default": {"default": "elemento di con barra sotto"}}}, {"key": "22F9", "mappings": {"default": {"default": "elemento di con due tratti oriz<PERSON>i"}}}, {"key": "22FA", "mappings": {"default": {"default": "contiene un tratto orizzontale lungo"}}}, {"key": "22FB", "mappings": {"default": {"default": "contiene con barra verticale alla fine del trato orizzontale"}}}, {"key": "22FC", "mappings": {"default": {"default": "piccoli contenitori con barra verticale all'estremità del tratto orizzontale"}}}, {"key": "22FD", "mappings": {"default": {"default": "contiene con barra sopra"}}}, {"key": "22FE", "mappings": {"default": {"default": "contiene piccolo con barra sopra"}}}, {"key": "22FF", "mappings": {"default": {"default": "appartiene a con notazione z"}}}, {"key": "2300", "mappings": {"default": {"default": "diametro"}}}, {"key": "2302", "mappings": {"default": {"default": "casa"}}}, {"key": "2305", "mappings": {"default": {"default": "proiettiva"}}}, {"key": "2306", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2307", "mappings": {"default": {"default": "linea ondulata"}}}, {"key": "2310", "mappings": {"default": {"default": "segno not"}}}, {"key": "2311", "mappings": {"default": {"default": "losanga quadrata"}}}, {"key": "2312", "mappings": {"default": {"default": "arco"}}}, {"key": "2313", "mappings": {"default": {"default": "segmento"}}}, {"key": "2314", "mappings": {"default": {"default": "settore"}}}, {"key": "2795", "mappings": {"default": {"default": "segno più grasetto"}}}, {"key": "2796", "mappings": {"default": {"default": "segno meno grassetto"}}}, {"key": "2797", "mappings": {"default": {"default": "segno di divisione grassetto"}}}, {"key": "27B0", "mappings": {"default": {"default": "ciclo riccio"}}}, {"key": "27BF", "mappings": {"default": {"default": "ciclo riccio do<PERSON>io"}}}, {"key": "27C1", "mappings": {"default": {"default": "triangolo bianco contenente un piccolo triangolo bianco"}}}, {"key": "27C2", "mappings": {"default": {"default": "perpendicolare "}}}, {"key": "27C3", "mappings": {"default": {"default": "sottoinsieme aperto"}}}, {"key": "27C4", "mappings": {"default": {"default": "superinseime aperto"}}}, {"key": "27C7", "mappings": {"default": {"default": "o con punto"}}}, {"key": "27C8", "mappings": {"default": {"default": "barra diagonale prima di sottoinsieme"}}}, {"key": "27C9", "mappings": {"default": {"default": "superinsieme prima di barra diagonale"}}}, {"key": "27CA", "mappings": {"default": {"default": "barra verticale con tratto orizzontale"}}}, {"key": "27CB", "mappings": {"default": {"default": "diagonale ascendente"}}}, {"key": "27CC", "mappings": {"default": {"default": "divisione lunga"}}}, {"key": "27CD", "mappings": {"default": {"default": "diagonale cadente"}}}, {"key": "27CE", "mappings": {"default": {"default": "and squadrato"}}}, {"key": "27CF", "mappings": {"default": {"default": "or squadrato"}}}, {"key": "27D0", "mappings": {"default": {"default": "diamante bianco con punto centrale"}}}, {"key": "27D1", "mappings": {"default": {"default": "and con punto"}}}, {"key": "27D2", "mappings": {"default": {"default": "elemento di apertura verso l'alto"}}}, {"key": "27D3", "mappings": {"default": {"default": "angolo in basso a destra con punto"}}}, {"key": "27D4", "mappings": {"default": {"default": "angolo superiore sinistro con punto"}}}, {"key": "27D5", "mappings": {"default": {"default": "join esterno sinistro"}}}, {"key": "27D6", "mappings": {"default": {"default": "join esterno destro"}}}, {"key": "27D7", "mappings": {"default": {"default": "join esterno pieno"}}}, {"key": "27D8", "mappings": {"default": {"default": "tornello grande"}}}, {"key": "27D9", "mappings": {"default": {"default": "tornello grande verso il basso"}}}, {"key": "27DA", "mappings": {"default": {"default": "doppio tornello a destra e a sinistra"}}}, {"key": "27DB", "mappings": {"default": {"default": "tornello sinistro e destro"}}}, {"key": "27DC", "mappings": {"default": {"default": "multimappa sinistra"}}}, {"key": "27DD", "mappings": {"default": {"default": "tornello lungo destro"}}}, {"key": "27DE", "mappings": {"default": {"default": "tornello lungo sinistro"}}}, {"key": "27DF", "mappings": {"default": {"default": "tornello in altro con cerchio sopra"}}}, {"key": "27E0", "mappings": {"default": {"default": "losanga divisa da linea orizzonatel"}}}, {"key": "27E1", "mappings": {"default": {"default": "diamante concavo bianco"}}}, {"key": "27E2", "mappings": {"default": {"default": "diamante bianco a punta concava con segno di spunta a sinistra"}}}, {"key": "27E3", "mappings": {"default": {"default": "diamante bianco a punta concava con segno di spunta a destra"}}}, {"key": "27E4", "mappings": {"default": {"default": "quadrato bianco con segno di spunta a sinistra"}}}, {"key": "27E5", "mappings": {"default": {"default": "quadrato bianco con segno di spunta a destra"}}}, {"key": "292B", "mappings": {"default": {"default": "diagonale crescente incrociante diagonale cadente"}}}, {"key": "292C", "mappings": {"default": {"default": "diagonale cadente incrociante diagonale crescente"}}}, {"key": "2980", "mappings": {"default": {"default": "delimitatore a tripla barra verticale"}}}, {"key": "2981", "mappings": {"default": {"default": "punto a notatione Z "}}}, {"key": "2982", "mappings": {"default": {"default": "due punti a notazione Z"}}}, {"key": "2999", "mappings": {"default": {"default": "delimitatore punteggiato"}}}, {"key": "299A", "mappings": {"default": {"default": "linea verticale a zig-zag"}}}, {"key": "29B0", "mappings": {"default": {"default": "set vuoto invertito"}}}, {"key": "29B1", "mappings": {"default": {"default": "set vuoto con barra"}}}, {"key": "29B2", "mappings": {"default": {"default": "set vuoto con piccolo cerchio sopra"}}}, {"key": "29B5", "mappings": {"default": {"default": "cerchio con barra orizzontale"}}}, {"key": "29B6", "mappings": {"default": {"default": "barra verticale cerchiata"}}}, {"key": "29B7", "mappings": {"default": {"default": "parallelo cerchiato"}}}, {"key": "29B8", "mappings": {"default": {"default": "solido inverso cerchiato"}}}, {"key": "29B9", "mappings": {"default": {"default": "perpendicolare cerchiato"}}}, {"key": "29BA", "mappings": {"default": {"default": "cerchio diviso da barra orizzontale e metà superiore diviso da barra verticale"}}}, {"key": "29BB", "mappings": {"default": {"default": "cerchio con X sovrapposta"}}}, {"key": "29BC", "mappings": {"default": {"default": "segno di divisione ruotato in senso antiorario"}}}, {"key": "29BE", "mappings": {"default": {"default": "punto elenco vuoto cerchiato"}}}, {"key": "29BF", "mappings": {"default": {"default": "punto elenco cer<PERSON>to"}}}, {"key": "29C0", "mappings": {"default": {"default": "minore di cerchiato"}}}, {"key": "29C1", "mappings": {"default": {"default": "maggiore di cerchiato"}}}, {"key": "29C2", "mappings": {"default": {"default": "cerchio con piccolo cerchio a destra"}}}, {"key": "29C3", "mappings": {"default": {"default": "cerchio con due tratti or<PERSON>i a destra"}}}, {"key": "29C4", "mappings": {"default": {"default": "barra diagonale crescente squadrata"}}}, {"key": "29C5", "mappings": {"default": {"default": "barra diagonale discendente squadrata"}}}, {"key": "29C6", "mappings": {"default": {"default": "asterisco squadrato"}}}, {"key": "29C7", "mappings": {"default": {"default": "piccolo cerchio squadrato"}}}, {"key": "29C8", "mappings": {"default": {"default": "quadrato squadrato"}}}, {"key": "29C9", "mappings": {"default": {"default": "due quadrati uniti"}}}, {"key": "29CA", "mappings": {"default": {"default": "triangolo con punto sopra"}}}, {"key": "29CB", "mappings": {"default": {"default": "triangolo con barra sotto"}}}, {"key": "29CC", "mappings": {"default": {"default": "s in triangolo"}}}, {"key": "29CD", "mappings": {"default": {"default": "triangolo con Ser<PERSON><PERSON> in basso"}}}, {"key": "29CE", "mappings": {"default": {"default": "triangolo destro sopra triangolo sinistro"}}}, {"key": "29CF", "mappings": {"default": {"default": "triangolo sinistro accanto a barra verticale"}}}, {"key": "29D0", "mappings": {"default": {"default": "barra verticale accanto a triangolo destro"}}}, {"key": "29D1", "mappings": {"default": {"default": "farfalla con metà sinistra nera"}}}, {"key": "29D2", "mappings": {"default": {"default": "farfalla con il giusto mezzo nero"}}}, {"key": "29D3", "mappings": {"default": {"default": "farfalla nera"}}}, {"key": "29D4", "mappings": {"default": {"default": "moltiplicazione con metà sinistra nera"}}}, {"key": "29D5", "mappings": {"default": {"default": "moltiplicazione con metà destra nera"}}}, {"key": "29D6", "mappings": {"default": {"default": "cless<PERSON><PERSON> bianca"}}}, {"key": "29D7", "mappings": {"default": {"default": "cless<PERSON>ra nera"}}}, {"key": "29DC", "mappings": {"default": {"default": "infinito incompleto"}}}, {"key": "29DD", "mappings": {"default": {"default": "legatura sopra infinito"}}}, {"key": "29DE", "mappings": {"default": {"default": "infinito negato con barra verticale"}}}, {"key": "29DF", "mappings": {"default": {"default": "multimappa a doppia estremità"}}}, {"key": "29E0", "mappings": {"default": {"default": "quadrato con contorno sagomato"}}}, {"key": "29E1", "mappings": {"default": {"default": "aumenta come"}}}, {"key": "29E2", "mappings": {"default": {"default": "pro<PERSON><PERSON>"}}}, {"key": "29E3", "mappings": {"default": {"default": "segno uguale e parallelo inclinato"}}}, {"key": "29E4", "mappings": {"default": {"default": "segno uguale e inclinato parallelo con tilde sopra"}}}, {"key": "29E5", "mappings": {"default": {"default": "parallelo identico a e inclinato"}}}, {"key": "29E6", "mappings": {"default": {"default": "equivalente tautologico"}}}, {"key": "29E7", "mappings": {"default": {"default": "termodinamico"}}}, {"key": "29E8", "mappings": {"default": {"default": "triangolo verso il basso con metà sinistra nera"}}}, {"key": "29E9", "mappings": {"default": {"default": "triangolo verso il basso con metà destra nera"}}}, {"key": "29EB", "mappings": {"default": {"default": "losanga nera"}}}, {"key": "29EE", "mappings": {"default": {"default": "quadrato bianco con barre errori"}}}, {"key": "29EF", "mappings": {"default": {"default": "quadrato nero con barre errori"}}}, {"key": "29F0", "mappings": {"default": {"default": "diamante bianco con barre errore"}}}, {"key": "29F1", "mappings": {"default": {"default": "diamante nero con barre errori"}}}, {"key": "29F2", "mappings": {"default": {"default": "cerchio bianco con barre errori"}}}, {"key": "29F3", "mappings": {"default": {"default": "cerchio nero con barre errori"}}}, {"key": "29F4", "mappings": {"default": {"default": "regola ritardata"}}}, {"key": "29F5", "mappings": {"default": {"default": "slash inverso"}}}, {"key": "29F6", "mappings": {"default": {"default": "slash con barra sopra"}}}, {"key": "29F7", "mappings": {"default": {"default": "solido inverso con tratto orizzontale"}}}, {"key": "29F8", "mappings": {"default": {"default": "slash grande"}}}, {"key": "29F9", "mappings": {"default": {"default": "backslash grande"}}}, {"key": "29FA", "mappings": {"default": {"default": "più doppio"}}}, {"key": "29FB", "mappings": {"default": {"default": "pi<PERSON> triplo"}}}, {"key": "29FE", "mappings": {"default": {"default": "minuscolo"}}}, {"key": "29FF", "mappings": {"default": {"default": "mini"}}}, {"key": "2A00", "mappings": {"default": {"default": "punto cerchiato"}}}, {"key": "2A01", "mappings": {"default": {"default": "più cer<PERSON>to"}}}, {"key": "2A02", "mappings": {"default": {"default": "per cerchiato"}}}, {"key": "2A03", "mappings": {"default": {"default": "unione con punto"}}}, {"key": "2A04", "mappings": {"default": {"default": "unione con più"}}}, {"key": "2A05", "mappings": {"default": {"default": "intersezione quadrata"}}}, {"key": "2A06", "mappings": {"default": {"default": "unione quadrata"}}}, {"key": "2A07", "mappings": {"default": {"default": "due e logici"}}}, {"key": "2A08", "mappings": {"default": {"default": "due o logici"}}}, {"key": "2A09", "mappings": {"default": {"default": "per"}}}, {"key": "2A0A", "mappings": {"default": {"default": "sommatoria modulo due"}}}, {"key": "2A0B", "mappings": {"default": {"default": "sommatoria con integrale"}}}, {"key": "2A0C", "mappings": {"default": {"default": "operatore integrale quadruplo"}}}, {"key": "2A0D", "mappings": {"default": {"default": "integrale parte finita"}}}, {"key": "2A0E", "mappings": {"default": {"default": "integrale con doppio trattino"}}}, {"key": "2A0F", "mappings": {"default": {"default": "media integrale con barra"}}}, {"key": "2A10", "mappings": {"default": {"default": "funzione di circolazione"}}}, {"key": "2A11", "mappings": {"default": {"default": "integrazione antioraria"}}}, {"key": "2A12", "mappings": {"default": {"default": "integrazione di linea con percorso rettangolare attorno al polo"}}}, {"key": "2A13", "mappings": {"default": {"default": "integrazione di linea con percorso semicircolare attorno al polo"}}}, {"key": "2A14", "mappings": {"default": {"default": "integrazione della linea non compresa il polo"}}}, {"key": "2A15", "mappings": {"default": {"default": "integrale attorno a un punto"}}}, {"key": "2A16", "mappings": {"default": {"default": "integrale quaternion"}}}, {"key": "2A18", "mappings": {"default": {"default": "integrale con segno per"}}}, {"key": "2A19", "mappings": {"default": {"default": "integrale con intersezione"}}}, {"key": "2A1A", "mappings": {"default": {"default": "integrale con unione"}}}, {"key": "2A1B", "mappings": {"default": {"default": "integrale con barra sopra"}}}, {"key": "2A1C", "mappings": {"default": {"default": "integrale con barra sotto"}}}, {"key": "2A1D", "mappings": {"default": {"default": "unione"}}}, {"key": "2A1E", "mappings": {"default": {"default": "triangolo sinistro grande"}}}, {"key": "2A1F", "mappings": {"default": {"default": "composizione di schema a notazione Z"}}}, {"key": "2A20", "mappings": {"default": {"default": "piping di schema a notazione Z"}}}, {"key": "2A21", "mappings": {"default": {"default": "proiezione a schema di notazione Z"}}}, {"key": "2A22", "mappings": {"default": {"default": "più con cerchio sopra"}}}, {"key": "2A23", "mappings": {"default": {"default": "più con cappuccio sopra"}}}, {"key": "2A24", "mappings": {"default": {"default": "tilde con più sotto"}}}, {"key": "2A25", "mappings": {"default": {"default": "più con punto sotto"}}}, {"key": "2A26", "mappings": {"default": {"default": "tilde con più sopra"}}}, {"key": "2A27", "mappings": {"default": {"default": "segno più con pedice due"}}}, {"key": "2A28", "mappings": {"default": {"default": "segno più con triangolo nero"}}}, {"key": "2A29", "mappings": {"default": {"default": "segno meno con virgola sopra"}}}, {"key": "2A2A", "mappings": {"default": {"default": "segno meno con punto sotto"}}}, {"key": "2A2B", "mappings": {"default": {"default": "segno meno con punti cadenti"}}}, {"key": "2A2C", "mappings": {"default": {"default": "segno meno con punti crescenti"}}}, {"key": "2A2D", "mappings": {"default": {"default": "segno più nel semicerchio sinistro"}}}, {"key": "2A2E", "mappings": {"default": {"default": "segno più nel semicerchio destro"}}}, {"key": "2A2F", "mappings": {"default": {"default": "prodotto vettoriale o incrociato"}}}, {"key": "2A30", "mappings": {"default": {"default": "segno di moltiplicazione con punto sopra"}}}, {"key": "2A31", "mappings": {"default": {"default": "segno di moltiplicazione con sottobarra"}}}, {"key": "2A32", "mappings": {"default": {"default": "prodotto semidiretto con fondo chiuso"}}}, {"key": "2A33", "mappings": {"default": {"default": "prodotto smash "}}}, {"key": "2A34", "mappings": {"default": {"default": "segno di moltiplicazione in semicerchio sinistro"}}}, {"key": "2A35", "mappings": {"default": {"default": "segno di moltiplicazione in semicerchio destro"}}}, {"key": "2A36", "mappings": {"default": {"default": "segno di moltiplicazione cerchiato con accento circonflesso"}}}, {"key": "2A37", "mappings": {"default": {"default": "segno di moltiplicazione in doppio cerchio"}}}, {"key": "2A38", "mappings": {"default": {"default": "segno di divisione cerchiato"}}}, {"key": "2A39", "mappings": {"default": {"default": "più in triangolo"}}}, {"key": "2A3A", "mappings": {"default": {"default": "segno meno nel triangolo"}}}, {"key": "2A3B", "mappings": {"default": {"default": "triangolo di segno di moltiplicazione"}}}, {"key": "2A3C", "mappings": {"default": {"default": "prodotto interno"}}}, {"key": "2A3D", "mappings": {"default": {"default": "prodotto destro interno"}}}, {"key": "2A3E", "mappings": {"default": {"default": "composizione relazionale a notazione Z"}}}, {"key": "2A3F", "mappings": {"default": {"default": "amalgamazione o coprodotto"}}}, {"key": "2A40", "mappings": {"default": {"default": "intersezione con punto"}}}, {"key": "2A41", "mappings": {"default": {"default": "unione con segno meno"}}}, {"key": "2A42", "mappings": {"default": {"default": "unione con barra sopra"}}}, {"key": "2A43", "mappings": {"default": {"default": "intersezione con barra"}}}, {"key": "2A44", "mappings": {"default": {"default": "intersezione con and"}}}, {"key": "2A45", "mappings": {"default": {"default": "unione con or"}}}, {"key": "2A46", "mappings": {"default": {"default": "unione sopra intersezione"}}}, {"key": "2A47", "mappings": {"default": {"default": "intersezione sopra unione"}}}, {"key": "2A48", "mappings": {"default": {"default": "unione sopra barra sopra intersezione"}}}, {"key": "2A49", "mappings": {"default": {"default": "intersezione sopra la barra sopra l'Unione"}}}, {"key": "2A4A", "mappings": {"default": {"default": "unione accanto e unita con unione"}}}, {"key": "2A4B", "mappings": {"default": {"default": "intersezione accanto e congiunta con intersezione"}}}, {"key": "2A4C", "mappings": {"default": {"default": "unione chiusa con serif"}}}, {"key": "2A4D", "mappings": {"default": {"default": "intersezione chiusa con serif"}}}, {"key": "2A4E", "mappings": {"default": {"default": "doppia intersezione quadrata"}}}, {"key": "2A4F", "mappings": {"default": {"default": "doppia unione quadrata"}}}, {"key": "2A50", "mappings": {"default": {"default": "unione chiusa con serif e prodotto smash"}}}, {"key": "2A51", "mappings": {"default": {"default": "and con punto <PERSON>"}}}, {"key": "2A52", "mappings": {"default": {"default": "or con punto <PERSON>pra"}}}, {"key": "2A53", "mappings": {"default": {"default": "doppio and"}}}, {"key": "2A54", "mappings": {"default": {"default": "doppio or"}}}, {"key": "2A55", "mappings": {"default": {"default": "due and che si intersezionano"}}}, {"key": "2A56", "mappings": {"default": {"default": "due or che si intersezionano"}}}, {"key": "2A57", "mappings": {"default": {"default": "or pendente grande"}}}, {"key": "2A58", "mappings": {"default": {"default": "and pendente grande"}}}, {"key": "2A59", "mappings": {"default": {"default": "or che si sovrappone a and"}}}, {"key": "2A5A", "mappings": {"default": {"default": "and con gambo medio"}}}, {"key": "2A5B", "mappings": {"default": {"default": "or con gambo medio"}}}, {"key": "2A5C", "mappings": {"default": {"default": "and con trattino or<PERSON>"}}}, {"key": "2A5D", "mappings": {"default": {"default": "or con trattino oriz<PERSON>tale"}}}, {"key": "2A5E", "mappings": {"default": {"default": "and con doppia barra sopra"}}}, {"key": "2A5F", "mappings": {"default": {"default": "and con barra sotto"}}}, {"key": "2A60", "mappings": {"default": {"default": "and con doppia barra sotto"}}}, {"key": "2A61", "mappings": {"default": {"default": "v piccola con barra sotto"}}}, {"key": "2A62", "mappings": {"default": {"default": "or con doppia barra sopra"}}}, {"key": "2A63", "mappings": {"default": {"default": "or con doppia barra sotto"}}}, {"key": "2A64", "mappings": {"default": {"default": "antirestrizione di dominio a notazione z"}}}, {"key": "2A65", "mappings": {"default": {"default": "antirestrizione di range a notazione z"}}}, {"key": "2A66", "mappings": {"default": {"default": "segno di uguale con punto sotto"}}}, {"key": "2A67", "mappings": {"default": {"default": "identico con punto sopra"}}}, {"key": "2A68", "mappings": {"default": {"default": "tripla barra orizzontale con doppio tratto verticale"}}}, {"key": "2A69", "mappings": {"default": {"default": "tripla barra orizzontale con triplo tratto verticale"}}}, {"key": "2A6A", "mappings": {"default": {"default": "tilde con punto sopra"}}}, {"key": "2A6B", "mappings": {"default": {"default": "tilde con punti in crescendo"}}}, {"key": "2A6C", "mappings": {"default": {"default": "simile meno simile"}}}, {"key": "2A6D", "mappings": {"default": {"default": "congruente con punto sopra"}}}, {"key": "2A6E", "mappings": {"default": {"default": "uguale con asterisco"}}}, {"key": "2A6F", "mappings": {"default": {"default": "circa uguale con cappuccio"}}}, {"key": "2A70", "mappings": {"default": {"default": "approssimativamente uguale o uguale a"}}}, {"key": "2A71", "mappings": {"default": {"default": "uguale con più sotto"}}}, {"key": "2A72", "mappings": {"default": {"default": "uguale con più sopra"}}}, {"key": "2A73", "mappings": {"default": {"default": "uguale sopra tilde"}}}, {"key": "2A74", "mappings": {"default": {"default": "doppi due punti uguale"}}}, {"key": "2A75", "mappings": {"default": {"default": "due uguali consecutivi"}}}, {"key": "2A76", "mappings": {"default": {"default": "tre uguali consecutivi"}}}, {"key": "2A77", "mappings": {"default": {"default": "segno di uguale con due punti sopra e due punti sotto"}}}, {"key": "2A78", "mappings": {"default": {"default": "equivalente con quattro punti sopra"}}}, {"key": "2A79", "mappings": {"default": {"default": "minore di con un cerchio all'interno"}}}, {"key": "2A7A", "mappings": {"default": {"default": "maggiore di con cerchio all'interno"}}}, {"key": "2A7B", "mappings": {"default": {"default": "minore di con punto interrogativo sopra"}}}, {"key": "2A7C", "mappings": {"default": {"default": "maggiore di con punto interrogativo sopra"}}}, {"key": "2A7D", "mappings": {"default": {"default": "minore o uguale a inclinato"}}}, {"key": "2A7E", "mappings": {"default": {"default": "maggiore o uguale a inclinato"}}}, {"key": "2A7F", "mappings": {"default": {"default": "minore o uguale a inclinato con punto dentro"}}}, {"key": "2A80", "mappings": {"default": {"default": "maggiore o uguale a inclinato con punto dentro"}}}, {"key": "2A81", "mappings": {"default": {"default": "minore o uguale a inclinato con punto sopra"}}}, {"key": "2A82", "mappings": {"default": {"default": "maggiore o uguale a inclinato con punto sopra"}}}, {"key": "2A83", "mappings": {"default": {"default": "minore o uguale a inclinato con punto sopra a destra"}}}, {"key": "2A84", "mappings": {"default": {"default": "maggiore o uguale a inclinato con punto sopra a destra"}}}, {"key": "2A85", "mappings": {"default": {"default": "minore di o approssimativo"}}}, {"key": "2A86", "mappings": {"default": {"default": "maggiore di o approssimativo"}}}, {"key": "2A87", "mappings": {"default": {"default": "minore e singola linea non uguale a"}}}, {"key": "2A88", "mappings": {"default": {"default": "maggiore e singola linea non uguale a"}}}, {"key": "2A89", "mappings": {"default": {"default": "minore e non approssimativo"}}}, {"key": "2A8A", "mappings": {"default": {"default": "maggiore di e non approssimativo"}}}, {"key": "2A8B", "mappings": {"default": {"default": "minore di sopra a doppia linea di uguale sopra a maggiore di"}}}, {"key": "2A8C", "mappings": {"default": {"default": "maggiore di sopra a doppia linea di uguale sopra a meno di"}}}, {"key": "2A8D", "mappings": {"default": {"default": "minore di sopra a simile o uguale"}}}, {"key": "2A8E", "mappings": {"default": {"default": "maggiore di sopra simile o uguale"}}}, {"key": "2A8F", "mappings": {"default": {"default": "minore di sopra simile sopra maggiore di"}}}, {"key": "2A90", "mappings": {"default": {"default": "maggiore di quanto sopra simile sopra minore di"}}}, {"key": "2A91", "mappings": {"default": {"default": "minore di sopra maggiore di sopra doppia linea di uguale"}}}, {"key": "2A92", "mappings": {"default": {"default": "maggiore di sopra minore di sopra doppia linea di uguale"}}}, {"key": "2A93", "mappings": {"default": {"default": "minore di sopra uguale inclinato  sopra maggiore di sopra uguale inclinato"}}}, {"key": "2A94", "mappings": {"default": {"default": "maggiore di sopra uguale inclinato sopra minore di sopra uguale inclinato"}}}, {"key": "2A95", "mappings": {"default": {"default": "uguale inclinato o minore di"}}}, {"key": "2A96", "mappings": {"default": {"default": "uguale inclinato o maggiore di"}}}, {"key": "2A97", "mappings": {"default": {"default": "uguale inclinato o minore di con punto dentro"}}}, {"key": "2A98", "mappings": {"default": {"default": "uguale inclinato o maggiore di con punto dentro"}}}, {"key": "2A99", "mappings": {"default": {"default": "doppia linea uguale o minore di"}}}, {"key": "2A9A", "mappings": {"default": {"default": "doppia linea uguale o maggiore di"}}}, {"key": "2A9B", "mappings": {"default": {"default": "linea doppia inclinata di uguale o inferiore a"}}}, {"key": "2A9C", "mappings": {"default": {"default": "linea doppia inclinata di uguale o maggiore di"}}}, {"key": "2A9D", "mappings": {"default": {"default": "simile o minore di"}}}, {"key": "2A9E", "mappings": {"default": {"default": "simile o maggiore di"}}}, {"key": "2A9F", "mappings": {"default": {"default": "simile sopra minore di sopra uguale"}}}, {"key": "2AA0", "mappings": {"default": {"default": "simile sopra maggiore di sopra uguale"}}}, {"key": "2AA1", "mappings": {"default": {"default": "doppio minore di uno dentro l'altro"}}}, {"key": "2AA2", "mappings": {"default": {"default": "doppio maggiore di uno dentro l'altro"}}}, {"key": "2AA3", "mappings": {"default": {"default": "doppio minore di uno dentro l'altro con barra sotto"}}}, {"key": "2AA4", "mappings": {"default": {"default": "maggiore di sovrapposto a minore di"}}}, {"key": "2AA5", "mappings": {"default": {"default": "maggiore di di fianco a minore di"}}}, {"key": "2AA6", "mappings": {"default": {"default": "meno di quanto chiuso da curva"}}}, {"key": "2AA7", "mappings": {"default": {"default": "maggiore di chiuso da curva"}}}, {"key": "2AA8", "mappings": {"default": {"default": "minore di chiuso per curva sopra uguale inclinato"}}}, {"key": "2AA9", "mappings": {"default": {"default": "maggiore di chiuso per curva sopra uguale inclinato"}}}, {"key": "2AAA", "mappings": {"default": {"default": "minore di"}}}, {"key": "2AAB", "mappings": {"default": {"default": "maggiore di"}}}, {"key": "2AAC", "mappings": {"default": {"default": "minore di o uguale a"}}}, {"key": "2AAD", "mappings": {"default": {"default": "maggiore o uguale a"}}}, {"key": "2AAE", "mappings": {"default": {"default": "segno di uguale con dosso sopra"}}}, {"key": "2AAF", "mappings": {"default": {"default": "precede sopra uguale a a linea singola"}}}, {"key": "2AB0", "mappings": {"default": {"default": "succede sopra uguale a linea singola"}}}, {"key": "2AB1", "mappings": {"default": {"default": "precede sopra diverso da a linea singola"}}}, {"key": "2AB2", "mappings": {"default": {"default": "succede sopra diverso da a linea singola"}}}, {"key": "2AB3", "mappings": {"default": {"default": "precede sopra uguale"}}}, {"key": "2AB4", "mappings": {"default": {"default": "succede sopra uguale"}}}, {"key": "2AB5", "mappings": {"default": {"default": "precede sopra non uguale a"}}}, {"key": "2AB6", "mappings": {"default": {"default": "riesce al di sopra di non uguale a"}}}, {"key": "2AB7", "mappings": {"default": {"default": "precede in alto quasi uguale a"}}}, {"key": "2AB8", "mappings": {"default": {"default": "riesce al di sopra di quasi uguale"}}}, {"key": "2AB9", "mappings": {"default": {"default": "precede sopra non quasi uguale a"}}}, {"key": "2ABA", "mappings": {"default": {"default": "riesce al di sopra di quasi uguale a"}}}, {"key": "2ABB", "mappings": {"default": {"default": "do<PERSON><PERSON> precede"}}}, {"key": "2ABC", "mappings": {"default": {"default": "doppio succede"}}}, {"key": "2ABD", "mappings": {"default": {"default": "sottoinsieme con punto"}}}, {"key": "2ABE", "mappings": {"default": {"default": "superinsieme con punto"}}}, {"key": "2ABF", "mappings": {"default": {"default": "sottoinsieme con segno più sotto"}}}, {"key": "2AC0", "mappings": {"default": {"default": "superinsieme con segno più sotto"}}}, {"key": "2AC1", "mappings": {"default": {"default": "sottoinsieme con segno di moltiplicazione sotto"}}}, {"key": "2AC2", "mappings": {"default": {"default": "superset con segno di moltiplicazione sotto"}}}, {"key": "2AC3", "mappings": {"default": {"default": "sottoinsieme di o uguale a con punto sopra"}}}, {"key": "2AC4", "mappings": {"default": {"default": "superinsieme o uguale a con punto sopra"}}}, {"key": "2AC5", "mappings": {"default": {"default": "sottoinsieme di sopra uguale"}}}, {"key": "2AC6", "mappings": {"default": {"default": "superinsieme di sopra uguale"}}}, {"key": "2AC7", "mappings": {"default": {"default": "sottoinsieme di sopra tilde"}}}, {"key": "2AC8", "mappings": {"default": {"default": "superset di Above Tilde Operator"}}}, {"key": "2AC9", "mappings": {"default": {"default": "sottoinsieme di sopra quasi uguale a"}}}, {"key": "2ACA", "mappings": {"default": {"default": "superinsieme di sopra quasi uguale a"}}}, {"key": "2ACB", "mappings": {"default": {"default": "sottoinsieme di sopra a non uguale a"}}}, {"key": "2ACC", "mappings": {"default": {"default": "superinsieme di sopra a non uguale a"}}}, {"key": "2ACD", "mappings": {"default": {"default": "operatore box quadrato aperto a sinistra"}}}, {"key": "2ACE", "mappings": {"default": {"default": "operatore box quadrato aperto a destra"}}}, {"key": "2ACF", "mappings": {"default": {"default": "sottoinsieme chiuso"}}}, {"key": "2AD0", "mappings": {"default": {"default": "superinsieme chiuso"}}}, {"key": "2AD1", "mappings": {"default": {"default": "sottoinsieme chiuso o uguale a"}}}, {"key": "2AD2", "mappings": {"default": {"default": "superinsieme chiuso o uguale a"}}}, {"key": "2AD3", "mappings": {"default": {"default": "sottoinsieme sopra superinsieme"}}}, {"key": "2AD4", "mappings": {"default": {"default": "superinsieme sopra sottoinsieme"}}}, {"key": "2AD5", "mappings": {"default": {"default": "sottoinsieme sopra sottoinsieme"}}}, {"key": "2AD6", "mappings": {"default": {"default": "superinsieme sopra superinsieme"}}}, {"key": "2AD7", "mappings": {"default": {"default": "superinsieme accanto a sottoinsieme"}}}, {"key": "2AD8", "mappings": {"default": {"default": "superinsieme accanto a e unito con trattino a sottoinsieme"}}}, {"key": "2AD9", "mappings": {"default": {"default": "elemento di apertura verso il basso"}}}, {"key": "2ADA", "mappings": {"default": {"default": "forcone con top a T"}}}, {"key": "2ADB", "mappings": {"default": {"default": "intersezione trasversale"}}}, {"key": "2ADC", "mappings": {"default": {"default": "biforcazione "}}}, {"key": "2ADD", "mappings": {"default": {"default": "nonforking"}}}, {"key": "2ADE", "mappings": {"default": {"default": "tornello corto a sinistra"}}}, {"key": "2ADF", "mappings": {"default": {"default": "tornello corto verso il basso"}}}, {"key": "2AE0", "mappings": {"default": {"default": "tornello corto verso l'alto"}}}, {"key": "2AE1", "mappings": {"default": {"default": "perpendicolare con S"}}}, {"key": "2AE2", "mappings": {"default": {"default": "tornello a destra triplo a barra verticale"}}}, {"key": "2AE3", "mappings": {"default": {"default": "doppia barra verticale con tornello a sinistra"}}}, {"key": "2AE4", "mappings": {"default": {"default": "tornello a sinistra doppio a barra verticale"}}}, {"key": "2AE5", "mappings": {"default": {"default": "doppia barra verticale con doppio tornello a sinistra"}}}, {"key": "2AE6", "mappings": {"default": {"default": "lineetta lunga che parte da elemento sinistro di doppia barra vericale"}}}, {"key": "2AE7", "mappings": {"default": {"default": "tornello verso il basso corto con barra sopra"}}}, {"key": "2AE8", "mappings": {"default": {"default": "tornello verso l'alto corto con barra sotto"}}}, {"key": "2AE9", "mappings": {"default": {"default": "tornello verso l'alto corto sopra tornello verso il basso corto"}}}, {"key": "2AEA", "mappings": {"default": {"default": "doppio tornello vero il basso"}}}, {"key": "2AEB", "mappings": {"default": {"default": "doppio tornello verso l'alto"}}}, {"key": "2AEC", "mappings": {"default": {"default": "segno di not con doppia linea orizzontale"}}}, {"key": "2AED", "mappings": {"default": {"default": "segno di not con doppia linea orizzontale invertito"}}}, {"key": "2AEE", "mappings": {"default": {"default": "non divide con barra di negazione inversa"}}}, {"key": "2AEF", "mappings": {"default": {"default": "linea verticale con cerchio sopra"}}}, {"key": "2AF0", "mappings": {"default": {"default": "linea verticale con cerchio sotto"}}}, {"key": "2AF1", "mappings": {"default": {"default": "tornello verso il basso con cerchio sotto"}}}, {"key": "2AF2", "mappings": {"default": {"default": "parallelo con tratto orizzontale"}}}, {"key": "2AF3", "mappings": {"default": {"default": "parallelo con tilde"}}}, {"key": "2AF4", "mappings": {"default": {"default": "relazione binaria a tripla barra verticale"}}}, {"key": "2AF5", "mappings": {"default": {"default": "tripla barra verticale con tratto orizzontale"}}}, {"key": "2AF6", "mappings": {"default": {"default": "operatore di tripli punti"}}}, {"key": "2AF7", "mappings": {"default": {"default": "tra minore di uno dentro l'altro"}}}, {"key": "2AF8", "mappings": {"default": {"default": "tra maggiore di uno dentro l'altro"}}}, {"key": "2AF9", "mappings": {"default": {"default": "minore o uguale a con doppia linea inclinata"}}}, {"key": "2AFA", "mappings": {"default": {"default": "maggiore o uguale a con doppia linea inclinata"}}}, {"key": "2AFB", "mappings": {"default": {"default": "triplice slash"}}}, {"key": "2AFC", "mappings": {"default": {"default": "operatore barre verticali triplo"}}}, {"key": "2AFD", "mappings": {"default": {"default": "doppio slash"}}}, {"key": "2AFE", "mappings": {"default": {"default": "barra verticale bianca"}}}, {"key": "2AFF", "mappings": {"default": {"default": "barra verticale bianca"}}}, {"key": "301C", "mappings": {"default": {"default": "t<PERSON><PERSON> ondu<PERSON>o"}}}, {"key": "FE10", "mappings": {"default": {"default": "modulo di presentazione per Virgola verticale"}}}, {"key": "FE13", "mappings": {"default": {"default": "modulo di presentazione per due punti verticale"}}}, {"key": "FE14", "mappings": {"default": {"default": "modulo di presentazione per punto e virgola verticale"}}}, {"key": "FE15", "mappings": {"default": {"default": "modulo di presentazione per il punto esclamativo verticale"}}}, {"key": "FE16", "mappings": {"default": {"default": "modulo di presentazione per il punto interrogativo verticale"}}}, {"key": "FE19", "mappings": {"default": {"default": "modulo di presentazione per l'ellisse orizzontale verticale"}}}, {"key": "FE30", "mappings": {"default": {"default": "glifo di pue punti verticali"}}}, {"key": "FE31", "mappings": {"default": {"default": "glifo di lineetta emme verticale"}}}, {"key": "FE32", "mappings": {"default": {"default": "glifo di lineetta enne verticale"}}}, {"key": "FE33", "mappings": {"default": {"default": "glifo di sottolineatura verticale"}}}, {"key": "FE34", "mappings": {"default": {"default": "glifo di sottolineatura verticale ondulata"}}}, {"key": "FE45", "mappings": {"default": {"default": "punto sesamo"}}}, {"key": "FE46", "mappings": {"default": {"default": "punto sesamo bianco"}}}, {"key": "FE49", "mappings": {"default": {"default": "tratto in alto tratteggiato"}}}, {"key": "FE4A", "mappings": {"default": {"default": "due trattini con spazio in alto"}}}, {"key": "FE4B", "mappings": {"default": {"default": "linea ondulata"}}}, {"key": "FE4C", "mappings": {"default": {"default": "linea ondulata doppia"}}}, {"key": "FE4D", "mappings": {"default": {"default": "barra sottra tratteggiata"}}}, {"key": "FE4E", "mappings": {"default": {"default": "due trattini con spazio in basso"}}}, {"key": "FE4F", "mappings": {"default": {"default": "linea ondulata"}}}, {"key": "FE50", "mappings": {"default": {"default": "virgola piccola"}}}, {"key": "FE52", "mappings": {"default": {"default": "punto piccolo"}}}, {"key": "FE54", "mappings": {"default": {"default": "punto e virgola piccolo"}}}, {"key": "FE55", "mappings": {"default": {"default": "due punti piccoli"}}}, {"key": "FE56", "mappings": {"default": {"default": "punto interrogativo piccolo"}}}, {"key": "FE57", "mappings": {"default": {"default": "punto esclamativo piccolo"}}}, {"key": "FE58", "mappings": {"default": {"default": "lineetta emme piccola"}}}, {"key": "FE5F", "mappings": {"default": {"default": "segno di numero piccolo"}}}, {"key": "FE60", "mappings": {"default": {"default": "e commerciale piccola"}}}, {"key": "FE61", "mappings": {"default": {"default": "asterisco piccolo"}}}, {"key": "FE62", "mappings": {"default": {"default": "segno più piccolo"}}}, {"key": "FE63", "mappings": {"default": {"default": "trattino-meno piccolo"}}}, {"key": "FE64", "mappings": {"default": {"default": "segno minore di piccolo"}}}, {"key": "FE65", "mappings": {"default": {"default": "segno maggiore di piccolo"}}}, {"key": "FE66", "mappings": {"default": {"default": "segno di uguale piccolo"}}}, {"key": "FE68", "mappings": {"default": {"default": "solido inverso piccolo"}}}, {"key": "FE69", "mappings": {"default": {"default": "simbolo del dollaro piccolo"}}}, {"key": "FE6A", "mappings": {"default": {"default": "segno percentuale piccolo"}}}, {"key": "FE6B", "mappings": {"default": {"default": "commerciale a piccola"}}}, {"key": "FF01", "mappings": {"default": {"default": "punto esclamativo fullwidth"}}}, {"key": "FF02", "mappings": {"default": {"default": "virgolette fullwidth"}}}, {"key": "FF03", "mappings": {"default": {"default": "segno di numero fullwidth"}}}, {"key": "FF04", "mappings": {"default": {"default": "segno di dollaro fullwidth"}}}, {"key": "FF05", "mappings": {"default": {"default": "segno di percentuale fullwidth"}}}, {"key": "FF06", "mappings": {"default": {"default": "e commerciale fullwidth"}}}, {"key": "FF07", "mappings": {"default": {"default": "apostrofo fullwidth"}}}, {"key": "FF0A", "mappings": {"default": {"default": "asterisco fullwidth"}}}, {"key": "FF0B", "mappings": {"default": {"default": "segno più fullwidth"}}}, {"key": "FF0C", "mappings": {"default": {"default": "virgola fullwidth"}}}, {"key": "FF0D", "mappings": {"default": {"default": "trattino-meno  fullwidth"}}}, {"key": "FF0E", "mappings": {"default": {"default": "punto fullwidth"}}}, {"key": "FF0F", "mappings": {"default": {"default": "slash fullwidth"}}}, {"key": "FF1A", "mappings": {"default": {"default": "due punti fullwidth"}}}, {"key": "FF1B", "mappings": {"default": {"default": "punto e virgola fullwidth"}}}, {"key": "FF1C", "mappings": {"default": {"default": "minore di  fullwidth"}}}, {"key": "FF1D", "mappings": {"default": {"default": "uguale a fullwidth"}}}, {"key": "FF1E", "mappings": {"default": {"default": "maggiore di fullwidth"}}}, {"key": "FF1F", "mappings": {"default": {"default": "punto interrogativo fullwidth"}}}, {"key": "FF20", "mappings": {"default": {"default": "a commerciale fullwidth"}}}, {"key": "FF3C", "mappings": {"default": {"default": "backslash fullwidth"}}}, {"key": "FF3E", "mappings": {"default": {"default": "accento circonflesso fullwidth"}}}, {"key": "FF3F", "mappings": {"default": {"default": "barra bassa fullwidth"}}}, {"key": "FF40", "mappings": {"default": {"default": "accento grave fullwidth"}}}, {"key": "FF5C", "mappings": {"default": {"default": "linea verticale fullwidth"}}}, {"key": "FF5E", "mappings": {"default": {"default": "tilde fullwidth"}}}, {"key": "FFE0", "mappings": {"default": {"default": "cent fullwidth"}}}, {"key": "FFE1", "mappings": {"default": {"default": "segno pound fullwidth"}}}, {"key": "FFE2", "mappings": {"default": {"default": "segno not fullwidth"}}}, {"key": "FFE3", "mappings": {"default": {"default": "macron fullwidth"}}}, {"key": "FFE4", "mappings": {"default": {"default": "barra verticale spezzata fullwidth"}}}, {"key": "FFE5", "mappings": {"default": {"default": "segno yes fullwidth"}}}, {"key": "FFE6", "mappings": {"default": {"default": "<PERSON><PERSON> won fullwidth"}}}, {"key": "FFE8", "mappings": {"default": {"default": "barra verticale halfwidth"}}}, {"key": "FFED", "mappings": {"default": {"default": "quadrato nero halfwidth"}}}, {"key": "FFEE", "mappings": {"default": {"default": "quadrato bianco halfwidth"}}}], "it/symbols/math_whitespace.min": [{"locale": "it"}, {"key": "0020", "mappings": {"default": {"default": "spazio"}}}, {"key": "00A0", "mappings": {"default": {"default": " "}}}, {"key": "00AD", "mappings": {"default": {"default": "trattino discrezionale"}}}, {"key": "2000", "mappings": {"default": {"default": "in quad"}}}, {"key": "2001", "mappings": {"default": {"default": "montone quad"}}}, {"key": "2002", "mappings": {"default": {"default": ""}}}, {"key": "2003", "mappings": {"default": {"default": ""}}}, {"key": "2004", "mappings": {"default": {"default": ""}}}, {"key": "2005", "mappings": {"default": {"default": ""}}}, {"key": "2006", "mappings": {"default": {"default": "spazio per sei persone"}}}, {"key": "2007", "mappings": {"default": {"default": ""}}}, {"key": "2008", "mappings": {"default": {"default": ""}}}, {"key": "2009", "mappings": {"default": {"default": ""}}}, {"key": "200A", "mappings": {"default": {"default": ""}}}, {"key": "200B", "mappings": {"default": {"default": ""}}}, {"key": "200C", "mappings": {"default": {"default": "zero Width Non-Joiner"}}}, {"key": "200D", "mappings": {"default": {"default": "zero Width Joiner"}}}, {"key": "200E", "mappings": {"default": {"default": "marchio da sinistra a destra"}}}, {"key": "200F", "mappings": {"default": {"default": "marchio da destra a sinistra"}}}, {"key": "2028", "mappings": {"default": {"default": "separatore di righe"}}}, {"key": "2029", "mappings": {"default": {"default": "separatore di paragrafi"}}}, {"key": "202A", "mappings": {"default": {"default": "incorporamento da sinistra a destra"}}}, {"key": "202B", "mappings": {"default": {"default": "incorporamento da destra a sinistra"}}}, {"key": "202C", "mappings": {"default": {"default": "pop formattazione direzionale"}}}, {"key": "202D", "mappings": {"default": {"default": "override da sinistra a destra"}}}, {"key": "202E", "mappings": {"default": {"default": "override da destra a sinistra"}}}, {"key": "202F", "mappings": {"default": {"default": "spazio indivisibile piccolo"}}}, {"key": "205F", "mappings": {"default": {"default": ""}}}, {"key": "2060", "mappings": {"default": {"default": ""}}}, {"key": "2061", "mappings": {"default": {"default": "di"}}}, {"key": "2062", "mappings": {"default": {"default": "per"}}}, {"key": "2063", "mappings": {"default": {"default": "virgola"}}}, {"key": "2064", "mappings": {"default": {"default": "più"}}}, {"key": "206A", "mappings": {"default": {"default": "inibire lo scambio simmetrico"}}}, {"key": "206B", "mappings": {"default": {"default": "attiva lo scambio simmetrico"}}}, {"key": "206E", "mappings": {"default": {"default": "forme di cifre nazionali"}}}, {"key": "206F", "mappings": {"default": {"default": "forme di cifre nominali"}}}, {"key": "FEFF", "mappings": {"default": {"default": ""}}}, {"key": "FFF9", "mappings": {"default": {"default": "ancora di annotazione interlineare"}}}, {"key": "FFFA", "mappings": {"default": {"default": "separatore di annotazioni interlineari"}}}, {"key": "FFFB", "mappings": {"default": {"default": "terminatore di annotazione interlineare"}}}], "it/symbols/other_stars.min": [{"locale": "it"}, {"key": "23E8", "mappings": {"default": {"default": "simbolo decimale degli esponenti"}}}, {"key": "2605", "mappings": {"default": {"default": "stella nera"}}}, {"key": "2606", "mappings": {"default": {"default": "stella bianca"}}}, {"key": "26AA", "mappings": {"default": {"default": "cerchio bianco medio"}}}, {"key": "26AB", "mappings": {"default": {"default": "cerchio nero medio"}}}, {"key": "2705", "mappings": {"default": {"default": "bianco pesante segno di spunta"}}}, {"key": "2713", "mappings": {"default": {"default": "segno di spunta"}}}, {"key": "2714", "mappings": {"default": {"default": "segno di spunta pesante"}}}, {"key": "2715", "mappings": {"default": {"default": "moltiplicazione X"}}}, {"key": "2716", "mappings": {"default": {"default": "moltiplicazione pesante X"}}}, {"key": "2717", "mappings": {"default": {"default": "ballot X"}}}, {"key": "2718", "mappings": {"default": {"default": "ballot X pesante"}}}, {"key": "271B", "mappings": {"default": {"default": "croce centrale aperta"}}}, {"key": "271C", "mappings": {"default": {"default": "croce centrale aperta grassetto"}}}, {"key": "2720", "mappings": {"default": {"default": "croce maltese"}}}, {"key": "2721", "mappings": {"default": {"default": "stella di David<PERSON>"}}}, {"key": "2722", "mappings": {"default": {"default": "quattro asteroidi a lacrima"}}}, {"key": "2723", "mappings": {"default": {"default": "quattro asterischi con palloncino"}}}, {"key": "2724", "mappings": {"default": {"default": "pesante quattro asterischi con palloncino"}}}, {"key": "2725", "mappings": {"default": {"default": "quattro asteroidi randelli"}}}, {"key": "2726", "mappings": {"default": {"default": "stella nera a quattro punte"}}}, {"key": "2727", "mappings": {"default": {"default": "stella bianca a quattro punte"}}}, {"key": "2728", "mappings": {"default": {"default": "bagliore"}}}, {"key": "2729", "mappings": {"default": {"default": "sottolineato stella bianca"}}}, {"key": "272A", "mappings": {"default": {"default": "stella bianca cerchiata"}}}, {"key": "272B", "mappings": {"default": {"default": "stella bianca centrale aperta"}}}, {"key": "272C", "mappings": {"default": {"default": "stella bianca centrale nero"}}}, {"key": "272D", "mappings": {"default": {"default": "stella nera delineato"}}}, {"key": "272E", "mappings": {"default": {"default": "stella nera profilata pesante"}}}, {"key": "272F", "mappings": {"default": {"default": "stella girandola"}}}, {"key": "2730", "mappings": {"default": {"default": "stella bianca ombreggiata"}}}, {"key": "2731", "mappings": {"default": {"default": "asterisco pesante"}}}, {"key": "2732", "mappings": {"default": {"default": "asterisco centrale aperta"}}}, {"key": "2733", "mappings": {"default": {"default": "asterisco a otto punte"}}}, {"key": "2734", "mappings": {"default": {"default": "stella nera a otto punte"}}}, {"key": "2735", "mappings": {"default": {"default": "stella a girandola a otto punte"}}}, {"key": "2736", "mappings": {"default": {"default": "stella nera a sei punte"}}}, {"key": "2739", "mappings": {"default": {"default": "stella nera a dodici punte"}}}, {"key": "273A", "mappings": {"default": {"default": "asterisco a sedici punte"}}}, {"key": "273B", "mappings": {"default": {"default": "asterisco a lacrima"}}}, {"key": "273C", "mappings": {"default": {"default": "asterisco a forma di lacrima del centro aperto"}}}, {"key": "273D", "mappings": {"default": {"default": "asterisco pesante a forma di lacrima"}}}, {"key": "273E", "mappings": {"default": {"default": "sei petali in bianco e nero Florette"}}}, {"key": "273F", "mappings": {"default": {"default": "black Florette"}}}, {"key": "2740", "mappings": {"default": {"default": "florette bianco"}}}, {"key": "2741", "mappings": {"default": {"default": "florette nera a otto petali profilata"}}}, {"key": "2742", "mappings": {"default": {"default": "stella a otto punte con centro aperto cerchiato"}}}, {"key": "2743", "mappings": {"default": {"default": "asterisco a forma di girandola pesante a goccia"}}}, {"key": "2744", "mappings": {"default": {"default": "fiocco di neve"}}}, {"key": "2745", "mappings": {"default": {"default": "fiocco di neve Trifoliate stretto"}}}, {"key": "2746", "mappings": {"default": {"default": "fiocco di neve con chevron"}}}, {"key": "2747", "mappings": {"default": {"default": "scintillare"}}}, {"key": "2748", "mappings": {"default": {"default": "sparkle pesante"}}}, {"key": "2749", "mappings": {"default": {"default": "asterisco con palloncino"}}}, {"key": "274A", "mappings": {"default": {"default": "otto asterisco a elica a forma di lacrima"}}}, {"key": "274B", "mappings": {"default": {"default": "asterisco a elica a otto squarcia a forma di lacrima"}}}, {"key": "274C", "mappings": {"default": {"default": "segno di croce"}}}, {"key": "274D", "mappings": {"default": {"default": "cerchio bianco o<PERSON>"}}}], "it/units/area.min": [{"locale": "it"}, {"key": "sq", "mappings": {"default": {"plural": "quadrati", "default": "quadrato"}}}, {"key": "sq inch", "mappings": {"default": {"plural": "pollici quadrati", "default": "pollice quadrato"}}}, {"key": "sq rd", "mappings": {"default": {"plural": "per<PERSON><PERSON> quadrati", "default": "pertica quadrata"}}}, {"key": "sq ft", "mappings": {"default": {"plural": "piedi quadrati", "default": "piede quadrato"}}}, {"key": "sq yd", "mappings": {"default": {"plural": "iarde quadrate", "default": "iarda quadrata"}}}, {"key": "sq mi", "mappings": {"default": {"plural": "miglia quadrate", "default": "miglio quadrato"}}}, {"key": "acr", "mappings": {"default": {"plural": "acri", "default": "acro"}}}, {"key": "ha", "mappings": {"default": {"plural": "ettari", "default": "ettaro"}}}], "it/units/currency.min": [{"locale": "it"}, {"key": "$", "mappings": {"default": {"plural": "dollari", "default": "dollaro"}}}, {"key": "£", "mappings": {"default": {"plural": "sterline", "default": "sterlina"}}}, {"key": "¥", "mappings": {"default": {"plural": "yen", "default": "yen"}}}, {"key": "€", "mappings": {"default": {"plural": "euro", "default": "euro"}}}, {"key": "₡", "mappings": {"default": {"plural": "colons", "default": "colón"}}}, {"key": "₢", "mappings": {"default": {"plural": "c<PERSON><PERSON><PERSON>", "default": "c<PERSON><PERSON><PERSON>"}}}, {"key": "₣", "mappings": {"default": {"plural": "franchi", "default": "franco"}}}, {"key": "₤", "mappings": {"default": {"plural": "lire", "default": "lira"}}}, {"key": "₥", "mappings": {"default": {"plural": "millesimi di dollaro", "default": "millesimo di dollaro"}}}, {"key": "₦", "mappings": {"default": {"plural": "naira", "default": "naira"}}}, {"key": "₧", "mappings": {"default": {"plural": "pesetas", "default": "peseta"}}}, {"key": "₨", "mappings": {"default": {"plural": "rupie", "default": "rupia"}}}, {"key": "₩", "mappings": {"default": {"plural": "won", "default": "won"}}}, {"key": "₪", "mappings": {"default": {"plural": "<PERSON><PERSON><PERSON>", "default": "<PERSON><PERSON><PERSON>"}}}, {"key": "₫", "mappings": {"default": {"plural": "dong", "default": "dong"}}}, {"key": "₭", "mappings": {"default": {"plural": "kip", "default": "kip"}}}, {"key": "₮", "mappings": {"default": {"plural": "<PERSON><PERSON>", "default": "<PERSON><PERSON>"}}}, {"key": "₯", "mappings": {"default": {"plural": "dracme", "default": "dracma"}}}, {"key": "₰", "mappings": {"default": {"plural": "penny tedeschi", "default": "penny tedesco"}}}, {"key": "₱", "mappings": {"default": {"plural": "peso", "default": "peso"}}}, {"key": "₲", "mappings": {"default": {"plural": "guaranis", "default": "guaranis"}}}, {"key": "₳", "mappings": {"default": {"plural": "austral", "default": "austral"}}}, {"key": "₴", "mappings": {"default": {"plural": "hryvnias", "default": "hryvnia"}}}, {"key": "₵", "mappings": {"default": {"plural": "cedis", "default": "cedis"}}}, {"key": "₸", "mappings": {"default": {"plural": "tenge", "default": "tenge"}}}, {"key": "₺", "mappings": {"default": {"plural": "lire turche", "default": "lira turca"}}}, {"key": "元", "mappings": {"default": {"plural": "yuan", "default": "yuan"}}}, {"key": "¢", "mappings": {"default": {"plural": "centesimi", "default": "centesimo"}}}], "it/units/energy.min": [{"locale": "it"}, {"key": "W", "mappings": {"default": {"plural": "watt", "default": "watt"}}}, {"key": "kwh", "mappings": {"default": {"plural": "kilowattore", "default": "kilowattora"}}}, {"key": "J", "mappings": {"default": {"plural": "joule", "default": "joule"}}}, {"key": "N", "mappings": {"default": {"plural": "newton", "default": "newton"}}}, {"key": "A", "mappings": {"default": {"plural": "ampere", "default": "ampere"}}}, {"key": "V", "mappings": {"default": {"plural": "volt", "default": "volt"}}}, {"key": "ohm", "mappings": {"default": {"plural": "ohm", "default": "ohm"}}}, {"key": "Ω", "mappings": {"default": {"plural": "ohm", "default": "ohm"}}}], "it/units/length.min": [{"locale": "it"}, {"key": "ft", "mappings": {"default": {"plural": "piedi", "default": "piede"}}}, {"key": "in", "mappings": {"default": {"plural": "pollici", "default": "pollice"}}}, {"key": "mi", "mappings": {"default": {"plural": "miglia", "default": "miglio"}}}, {"key": "yd", "mappings": {"default": {"plural": "iarde", "default": "iarda"}}}, {"key": "link", "mappings": {"default": {"plural": "links", "default": "link"}}}, {"key": "rod", "mappings": {"default": {"plural": "pertiche", "default": "pertica"}}}, {"key": "chain", "mappings": {"default": {"plural": "catene", "default": "catena"}}}, {"key": "furlong", "mappings": {"default": {"plural": "furlong", "default": "furlong"}}}, {"key": "n.m.", "mappings": {"default": {"plural": "miglia nautiche", "default": "miglio nautico"}}}, {"key": "m", "mappings": {"default": {"plural": "metri", "default": "metro"}}}], "it/units/memory.min": [{"locale": "it"}, {"key": "b", "mappings": {"default": {"plural": "bit", "default": "bit"}}}, {"key": "B", "mappings": {"default": {"plural": "byte", "default": "byte"}}}, {"key": "KB", "mappings": {"default": {"plural": "chilobyte", "default": "chilobyte"}}}], "it/units/other.min": [{"locale": "it"}, {"key": "doz", "mappings": {"default": {"plural": "dozzine", "default": "<PERSON><PERSON>na"}}}], "it/units/speed.min": [{"locale": "it"}, {"key": "kt", "mappings": {"default": {"plural": "nodi", "default": "nodo"}}, "names": ["nd"]}, {"key": "mph", "mappings": {"default": {"plural": "miglia orarie", "default": "miglio orario"}}}, {"key": "rpm", "mappings": {"default": {"plural": "giri al minuto", "default": "giro al minuto"}}}, {"key": "kmh", "mappings": {"default": {"plural": "chilometri orari", "default": "chilometro orario"}}}], "it/units/temperature.min": [{"locale": "it"}, {"key": "F", "mappings": {"default": {"plural": "gradi fahrenheit", "default": "grado fahrenheit"}}}, {"key": "C", "mappings": {"default": {"plural": "gradi celsius", "default": "grado celsius"}}}, {"key": "K", "mappings": {"default": {"plural": "kelvin", "default": "kelvin"}}}], "it/units/time.min": [{"locale": "it"}, {"key": "s", "mappings": {"default": {"plural": "secondi", "default": "secondo"}}}, {"key": "″", "mappings": {"default": {"plural": "secondi", "default": "secondo"}}}, {"key": "min", "mappings": {"default": {"plural": "minuti", "default": "minuto"}}}, {"key": "°", "mappings": {"default": {"default": "grado"}}}, {"key": "h", "mappings": {"default": {"plural": "ore", "default": "ora"}}}], "it/units/volume.min": [{"locale": "it"}, {"key": "cu", "mappings": {"default": {"plural": "cubici", "default": "cubico"}}}, {"key": "cu inch", "mappings": {"default": {"plural": "pollici cubici", "default": "pollice cubico"}}}, {"key": "cu ft", "mappings": {"default": {"plural": "piedi cubici", "default": "piede cubico"}}}, {"key": "cu yd", "mappings": {"default": {"plural": "iarde cubiche", "default": "iarda cubica"}}}, {"key": "bbl", "mappings": {"default": {"plural": "barili", "default": "barile"}}}, {"key": "fl. oz.", "mappings": {"default": {"plural": "once liquide", "default": "oncia liquida"}}}, {"key": "gal", "mappings": {"default": {"plural": "galloni", "default": "gallone"}}}, {"key": "pt", "mappings": {"default": {"plural": "pinte", "default": "pinta"}}}, {"key": "qt", "mappings": {"default": {"plural": "quarti", "default": "quarto"}}}, {"key": "fluid dram", "mappings": {"default": {"plural": "dramme liquide", "default": "dramma liquida"}}}, {"key": "tbsp", "mappings": {"default": {"plural": "cucchiai da tavola", "default": "cucchiaio da tavola"}}}, {"key": "tsp", "mappings": {"default": {"plural": "cucchiai da te", "default": "cucchiaio da te"}}}, {"key": "cup", "mappings": {"default": {"plural": "tazze", "default": "tazza"}}}, {"key": "cc", "mappings": {"default": {"plural": "centimetri cubi", "default": "centimetro cubo"}}}, {"key": "l", "mappings": {"default": {"plural": "litri", "default": "litro"}}}], "it/units/weight.min": [{"locale": "it"}, {"key": "dram", "mappings": {"default": {"plural": "dramme", "default": "dramma"}}}, {"key": "oz", "mappings": {"default": {"plural": "once", "default": "oncia"}}}, {"key": "lb", "mappings": {"default": {"plural": "libbre", "default": "libbra"}}}, {"key": "st", "mappings": {"default": {"plural": "stone", "default": "stone"}}}, {"key": "qtr", "mappings": {"default": {"plural": "quarti", "default": "quarto"}}}, {"key": "cwt", "mappings": {"default": {"plural": "hundredweight", "default": "hundredweight"}}}, {"key": "LT", "mappings": {"default": {"plural": "long ton", "default": "long ton"}}}, {"key": "gr", "mappings": {"default": {"plural": "grani", "default": "grano"}}}, {"key": "g", "mappings": {"default": {"plural": "grammi", "default": "grammo"}}}, {"key": "mcg", "mappings": {"default": {"plural": "microgrammi", "default": "microgrammo"}}}, {"key": "t", "mappings": {"default": {"plural": "tonnellate", "default": "tonnellata"}}}], "it/rules/clearspeak_italian.min": {"locale": "it", "domain": "clearspeak", "modality": "speech", "inherits": "romance", "rules": [["Precondition", "function-prefix-det", "default", "self::appl", "@role=\"prefix function\"", "children/*[1][text()=\"det\"]"], ["Precondition", "fraction-per", "Fraction_Per", "self::fraction", "contains(children/*[1]/@annotation, \"clearspeak:unit\")", "contains(children/*[2]/@annotation, \"clearspeak:unit\")"], ["Precondition", "squared-masculine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")"], ["Precondition", "squared-feminine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")", "parent::*/parent::*[@role=\"simple function\"]"]]}, "it/rules/clearspeak_italian_actions.min": {"locale": "it", "domain": "clearspeak", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "font", "[n] . (grammar:ignoreFont=@font); [t] @font (pause:short, grammar:localFont)"], ["Action", "ellipsis", "[t] \"e così via\""], ["Action", "ellipsis-andsoon", "[t] \"e così via fino a\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [t] \"valutato a\"; [n] content/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [t] \"valutato a\"; [n] content/*[1]/children/*[2] (pause:short); [t] \"meno la stessa espressione valutata a\"; [n] content/*[1]/children/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-such-that", "[t] \"tale che\""], ["Action", "vbar-divides", "[t] \"divide\""], ["Action", "vbar-always-divides", "[t] \"divide\""], ["Action", "vbar-given", "[t] \"dato\""], ["Action", "member", "[t] \"appartiene ad\""], ["Action", "member-member", "[t] \"membro di\""], ["Action", "member-element", "[t] \"elemento di\""], ["Action", "member-in", "[t] \"in\""], ["Action", "member-belongs", "[t] \"appartiene ad\""], ["Action", "not-member", "[t] \"non appartiene ad\""], ["Action", "not-member-member", "[t] \"non membro di\""], ["Action", "not-member-element", "[t] \"non elemento di\""], ["Action", "not-member-in", "[t] \"non in\""], ["Action", "not-member-belongs", "[t] \"non appartiene ad\""], ["Action", "set-member", "[t] \"in\""], ["Action", "set-member-member", "[t] \"membro di\""], ["Action", "set-member-element", "[t] \"elemento di\""], ["Action", "set-member-belongs", "[t] \"appartenente a\""], ["Action", "set-not-member", "[t] \"non in\""], ["Action", "set-not-member-member", "[t] \"non membro di\""], ["Action", "set-not-member-element", "[t] \"non elemento di\""], ["Action", "set-not-member-belongs", "[t] \"non appartenente a\""], ["Action", "appl", "[n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "appl-simple", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-simple-fenced", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"per\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [t] \"di\"; [n] children/*[2]"], ["Action", "function-prefix-det", "[n] children/*[1]; [t] \"della\"; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [t] \"di\"; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-ln", "[n] children/*[1]; [t] \"di\"; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"di\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-inverse", "[p] (pause:short); [t] \"inverso del\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-reciprocal", "[p] (pause:short); [t] \"il reciproco del\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-reciprocal-simple", "[p] (pause:short); [t] \"il reciproco del\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"arco\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"arco\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"arco\"; [n] children/*[1]/children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "function-inverse", "[n] children/*[1]; [t] \"inversa\""], ["Action", "superscript-prefix-function", "[t] \"potenza\"; [n] children/*[2] (grammar:ordinal); [t] \"di\"; [n] children/*[1]"], ["Action", "function-reciprocal", "[t] \"riciproco di\"; [n] children/*[1]"], ["Action", "superscript", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"elevato alla\"; [n] children/*[2] (pause:medium)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"elevato alla\"; [n] children/*[2] (pause:medium)"], ["Action", "superscript-ordinal", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (grammar:ordinal); [t] \"potenza\" (pause:short)"], ["Action", "superscript-non-ordinal", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-simple-function", "[n] . (grammar:functions_none)"], ["Action", "superscript-ordinal-number", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short, grammar:ordinal)"], ["Action", "superscript-ordinal-negative", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-default", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "superscript-ordinal-power-number", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (grammar:ordinal); [t] \"potenza\" (pause:short)"], ["Action", "superscript-ordinal-power-negative", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (pause:short)"], ["Action", "superscript-ordinal-power-identifier", "[n] children/*[1]; [t] \"alla\"; [n] children/*[2] (grammar:ordinal); [t] \"potenza\" (pause:short)"], ["Action", "superscript-ordinal-power-default", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "superscript-power", "[n] children/*[1]; [t] \"elevato all'esponente\"; [n] children/*[2] (pause:short, grammar:afterPower)"], ["Action", "superscript-power-default", "[n] children/*[1]; [t] \"elevato all'esponente\" (pause:short); [n] children/*[2] (pause:short); [t] \"fine esponente\" (pause:short)"], ["Action", "exponent", "[n] text() (join:\"-\"); [t] \"esima\""], ["Action", "exponent-number", "[t] CSFordinalExponent"], ["Action", "exponent-ordinal", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinal-zero", "[t] \"zero\""], ["Action", "exponent-ordinalpower", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinalpower-zero", "[t] \"zero\""], ["Action", "squared-masculine", "[t] \"quadrato\""], ["Action", "squared-feminine", "[t] \"quadrata\""], ["Action", "square", "[n] children/*[1]; [n] children/*[2] (grammar:squared)"], ["Action", "cube", "[n] children/*[1]; [t] \"cubo\""], ["Action", "fences-points", "[t] \"punto con coordinate\"; [n] children/*[1]"], ["Action", "fences-auto-interval", "[t] \"l'intervallo da\"; [n] children/*[1]/children/*[1]; [t] \"a\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "fences-interval", "[t] \"l'intervallo da\"; [n] children/*[1]/children/*[1]; [t] \"a\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "interval-open", "[t] \"che non include\"; [n] children/*[1]/children/*[1]; [t] \"o\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open", "[t] \"che include\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"ma che non include\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-closed", "[t] \"che non include\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"ma che comprende\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed", "[t] \"che include\"; [n] children/*[1]/children/*[1]; [t] \"e\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-inf-r", "[t] \"che non include\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-inf-l", "[t] \"che non include\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open-inf", "[t] \"che include\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-closed-inf", "[t] \"che include\"; [n] children/*[1]/children/*[3]"], ["Action", "set-empty", "[t] \"l'insieme vuoto\""], ["Action", "set-extended", "[t] \"l'insieme di tutti\"; [n] children/*[1]/children/*[1]; [t] \"tale che\"; [n] children/*[1]/children/*[3]"], ["Action", "set-collection", "[t] \"l'insieme\"; [n] children/*[1]"], ["Action", "set-extended-woall", "[t] \"l'insieme di\"; [n] children/*[1]/children/*[1]; [t] \"tale che\"; [n] children/*[1]/children/*[3]"], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"pedice\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"pedice\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-none", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short)"], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short); [t] \"fine frazione\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"per\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short); [t] \"fine frazione\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"frazione con numeratore\"; [n] children/*[1] (pause:short); [t] \"e denominatore\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"fine frazione\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[1]; [t] \"fratto\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "sqrt", "[t] \"radice quadrata di\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested", "[p] (pause:short); [t] \"radice quadrata di\"; [n] children/*[1] (pause:short)"], ["Action", "negative-sqrt", "[t] \"meno radice quadrata di\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "negative-sqrt-default", "[p] (pause:short); [t] \"meno radice quadrata di\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus", "[t] \"più radice quadrata di\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus", "[p] (pause:short); [t] \"più radice quadrata di\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[t] \"più radice quadrata di\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[p] (pause:short); [t] \"più radice quadrata di\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "cubic", "[t] \"radice cubica di\"; [n] children/*[2] (pause:short)"], ["Action", "cubic-nested", "[p] (pause:short); [t] \"radice cubica di\"; [n] children/*[2] (pause:short)"], ["Action", "root", "[t] \"radice\"; [n] children/*[1] (grammar:ordinal:gender=\"f\"); [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "root-nested", "[p] (pause:short); [t] \"radice\"; [n] children/*[1] (grammar:ordinal:gender=\"f\"); [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "root-endroot", "[n] . (grammar:?EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"fine radice\" (pause:short)"], ["Action", "negative", "[t] \"meno\"; [n] children/*[1]"], ["Action", "positive", "[t] \"più\"; [n] children/*[1]"], ["Action", "angle-measure", "[t] \"misura di\"; [n] content/*[1]; [n] children/*[2] (grammar:angle)"], ["Action", "set-prefix-operators", "[t] \"il\"; [n] .; [t] \"di\""], ["Action", "division", "[n] children/*[1]; [t] \"diviso per\"; [n] children/*[2]"], ["Action", "operators-after-power", "[m] children/* (rate:\"0.5\")"], ["Action", "natural-numbers", "[t] \"i numeri naturali\""], ["Action", "integers", "[t] \"gli interi\""], ["Action", "rational-numbers", "[t] \"i numeri razionali\""], ["Action", "real-numbers", "[t] \"i numeri reali\""], ["Action", "complex-numbers", "[t] \"i numeri complessi\""], ["Action", "natural-numbers-with-zero", "[t] \"i numeri naturali con lo zero\""], ["Action", "positive-integers", "[t] \"gli interi positivi\""], ["Action", "negative-integers", "[t] \"gli interi negativi\""], ["Action", "positive-rational-numbers", "[t] \"i numeri razionali positivi\""], ["Action", "negative-rational-numbers", "[t] \"i numeri razionali negativi\""], ["Action", "fences-neutral", "[p] (pause:short); [t] \"valore assoluto di\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"valore assoluto di\"; [n] children/*[1] (pause:short); [t] \"fine valore assoluto\" (pause:short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [t] \"cardinalità di\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [t] \"determinante della\"; [n] children/*[1] (pause:short)"], ["Action", "fences-metric", "[p] (pause:short); [t] \"metrica di\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"metrica di\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"fine metrica\" (span:content/*[1], pause:short)"], ["Action", "matrix", "[t] \"matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long)"], ["Action", "matrix-simple", "[t] \"matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"matrice 1 per 1 con valore\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] \"determinante della matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] \"determinante della matrice\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long)"], ["Action", "matrix-vector", "[t] \"matrice colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] \"matrice colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] \"matrice colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] \"matrice riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Colonna-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] \"matrice riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] \"matrice riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter, context:\"Colonna-,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"fine matrice\""], ["Action", "matrix-end-vector", "[n] . (grammar:?EndMatrix); [t] \"fine matrice\""], ["Action", "matrix-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"fine determinante\""], ["Action", "vector", "[t] \"vettore colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] \"vettore colonna\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] \"vettore riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Colonna-:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] \"vettore riga\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"fine matrice\""], ["Action", "vector-end-vector", "[n] . (grammar:?EndMatrix); [t] \"fine vettore\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:?EndMatrix); [t] \"fine vettore\""], ["Action", "vector-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"fine determinante\""], ["Action", "binomial", "[t] \"coefficiente binomiale\"; [n] children/*[1]/children/*[1]; [t] \"su\"; [n] children/*[2]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] count(children/*); [t] \"linee\"; [n] . (grammar:?layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"casi\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"vuoto\""], ["Action", "blank-line", "[t] \"vuota\""], ["Action", "blank-cell-empty", "[t] \"vuota\""], ["Action", "blank-line-empty", "[t] \"vuota\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Caso-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"caso\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Caso-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] count(children/*); [t] \"equazioni\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Equazioni-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] count(children/*); [t] \"passi\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Passo-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] count(children/*); [t] \"riga\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Riga-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] count(children/*); [t] \"vincoli\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Vincoli-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[t] \"il\"; [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [t] \"da\"; [n] children/*[2]; [t] \"a\"; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"sopra\"; [n] children/*[2] (pause:short)"], ["Action", "limupper", "[n] children/*[1]; [t] \"sotto\"; [n] children/*[2] (pause:short)"], ["Action", "integral", "[t] \"il\"; [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short); [n] children/*[3] (pause:short)"], ["Action", "integral-novar", "[t] \"il\"; [n] children/*[1]; [t] \"di\"; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [t] \"sotto\"; [n] children/*[2] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [t] \"a\"; [n] children/*[2]"], ["Action", "underscript", "[n] children/*[1]; [t] \"sopra\"; [n] children/*[2] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [t] \"da\"; [n] children/*[2]"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"e\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"numero\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"decimale periodico\"; [n] children/*[1] (grammar:spaceout); [t] \"virgola\"; [n] children/*[3]/children/*[1] (grammar:spaceout); [t] \"periodico\""], ["Action", "decimal-period-float", "[t] \"decimale periodico\"; [n] children/*[1] (grammar:spaceout); [t] \"seguito da\"; [n] children/*[2]/children/*[1] (grammar:spaceout); [t] \"periodico\""], ["Action", "decimal-point", "[t] \"punto\""], ["Action", "line-segment", "[t] \"linea segmento\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[t] \"coniugato complesso di\"; [n] children/*[1]"], ["Action", "defined-by", "[t] \"è definito essere\" (pause:short)"], ["Action", "adorned-sign", "[t] \"segno\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]; [t] \"sopra esso\""], ["Action", "factorial", "[t] \"fattoriale\""], ["Action", "left-super", "[t] \"sub sinistro\"; [n] text()"], ["Action", "left-super-list", "[t] \"sub sinistro\"; [m] children/*"], ["Action", "left-sub", "[t] \"sub sotto\"; [n] text()"], ["Action", "left-sub-list", "[t] \"sub sotto\"; [m] children/*"], ["Action", "right-super", "[t] \"sub sopra\"; [n] text()"], ["Action", "right-super-list", "[t] \"sub sopra\"; [m] children/*"], ["Action", "right-sub", "[t] \"sub destro\"; [n] text()"], ["Action", "right-sub-list", "[t] \"sub destro\"; [m] children/*"], ["Action", "choose", "[t] \"combinazione di\"; [n] children/*[2] (grammar:combinatorics); [t] \"per\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "permute", "[t] \"permutazione di\"; [n] children/*[2] (grammar:combinatorics); [t] \"per\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-square", "[n] children/*[1]; [t] \"quadrato\""], ["Action", "unit-cubic", "[n] children/*[1]; [t] \"cubico\""], ["Action", "unit-divide", "[n] children/*[1]; [t] \"al\"; [n] children/*[2] (grammar:singular)"], ["Action", "multi-inference", "[t] \"regola di inferenza\"; [m] content/*; [t] \"con conclusione\"; [n] children/*[1]; [t] \"e\"; [t] count(children/*[2]/children/*); [t] \"premesse\""], ["Action", "inference", "[t] \"regola di inferenza\"; [m] content/*; [t] \"con conclusione\"; [n] children/*[1]; [t] \"e\"; [t] count(children/*[2]/children/*); [t] \"premessa\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"premise \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"etichetta\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"assioma\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"assioma vuoto\""]]}, "it/rules/mathspeak_italian.min": {"domain": "mathspeak", "locale": "it", "modality": "speech", "inherits": "romance", "rules": [["Ignore", "vulgar-fraction"], ["Precondition", "squared-masculine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")"], ["Precondition", "squared-feminine", "default", "self::number[text()=2]", "contains(@grammar, \"squared\")", "parent::*/parent::*[@role=\"simple function\"]"]]}, "it/rules/mathspeak_italian_actions.min": {"domain": "mathspeak", "locale": "it", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "blank-cell-empty", "[t] \"vuoto\""], ["Action", "blank-line-empty", "[t] \"vuoto\""], ["Action", "font", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"e\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"numero\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-with-chars-brief", "[t] \"num\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"parolaMaiuscolo\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"linea di base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"linea di base\"; [n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "number-baseline-font-brief", "[t] \"base\"; [n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "negative-number", "[t] \"meno\"; [n] children/*[1]"], ["Action", "negative", "[t] \"meno\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"diviso per\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"meno\")"], ["Action", "fences-neutral", "[t] \"inizio valore assoluto\"; [n] children/*[1]; [t] \"fine valore assoluto\""], ["Action", "fences-neutral-sbrief", "[t] \"valore assoluto\"; [n] children/*[1]; [t] \"fine valore assoluto\""], ["Action", "fences-metric", "[t] \"inizio metrica\"; [n] children/*[1]; [t] \"fine metrica\""], ["Action", "fences-metric-sbrief", "[t] \"metrica\"; [n] children/*[1]; [t] \"fine metrica\""], ["Action", "empty-set", "[t] \"insieme vuoto\""], ["Action", "fences-set", "[t] \"inizio insieme\"; [n] children/*[1]; [t] \"fine insieme\""], ["Action", "fences-set-sbrief", "[t] \"insieme\"; [n] children/*[1]; [t] \"fine insieme\""], ["Action", "factorial", "[t] \"fattoriale\""], ["Action", "minus", "[t] \"meno\""], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction (grammar:fraction:gender=\"m\")"], ["Action", "continued-fraction-outer", "[t] \"continuo frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-outer-brief", "[t] \"continuo frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"inizio frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-brief", "[t] \"inizio frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"frazione\"; [n] children/*[1]; [t] \"fratto\"; [n] children/*[2]"], ["Action", "integral", "[n] children/*[1]; [t] \"pedice\"; [n] children/*[2]; [t] \"apice\"; [n] children/*[3]; [t] \"linea di base\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"sub\"; [n] children/*[2]; [t] \"sup\"; [n] children/*[3]; [t] \"base\""], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "squared-masculine", "[t] \"quadrato\""], ["Action", "squared-feminine", "[t] \"quadrata\""], ["Action", "square", "[n] children/*[1]; [n] children/*[2] (grammar:squared)"], ["Action", "cube", "[n] children/*[1]; [t] \"cubo\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"volte primo\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"volte primo\""], ["Action", "overscore", "[t] \"modificante sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "overscore-brief", "[t] \"mod sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"modificante sopra sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[t] \"mod sopra sopra\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"modificanteSotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "underscore-brief", "[t] \"modSotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"modificante<PERSON>otto sotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[t] \"modSotto sotto\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"con barra sopra\""], ["Action", "underbar", "[n] children/*[1]; [t] \"con barra sotto\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"con tilde sopra\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"con tilde sotto\""], ["Action", "matrix", "[t] \"inizio\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"matrice\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine matrice\""], ["Action", "matrix-sbrief", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"matrice\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine matrice\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFordinal<PERSON>ounter, context:\"colonna\", pause:200)"], ["Action", "row-with-label", "[t] \"con etichetta\"; [n] content/*[1]; [t] \"fine etichetta\" (pause:200); [m] children/* (ctxtFunc:CTFordinal<PERSON>ounter, context:\"colonna\")"], ["Action", "row-with-label-brief", "[t] \"etichetta\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonna\")"], ["Action", "row-with-text-label", "[t] \"etichetta\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonna\")"], ["Action", "empty-row", "[t] \"vuoto\""], ["Action", "empty-cell", "[t] \"vuoto\" (pause:300)"], ["Action", "determinant", "[t] \"inizio\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine determinante\""], ["Action", "determinant-sbrief", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga \"); [t] \"fine determinante\""], ["Action", "determinant-simple", "[t] \"inizio\"; [t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga\", grammar:simpleDet); [t] \"fine determinante\""], ["Action", "determinant-simple-sbrief", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"determinante\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"riga\", grammar:simpleDet); [t] \"fine determinante\""], ["Action", "layout", "[t] \"inizio layout\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "layout-sbrief", "[t] \"layout\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "binomial", "[t] \"inizio binomiale o matrice\"; [n] children/*[1]/children/*[1]; [t] \"scelta\"; [n] children/*[2]/children/*[1]; [t] \"fine binomiale o matrice\""], ["Action", "binomial-sbrief", "[t] \"bInomiale o matrice\"; [n] children/*[1]/children/*[1]; [t] \"scelta\"; [n] children/*[2]/children/*[1]; [t] \"fine binomiale o matrice\""], ["Action", "cases", "[t] \"inizio layout\"; [t] \"allargato\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "cases-sbrief", "[t] \"layout\"; [t] \"allargato\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"row \"); [t] \"fine layout\""], ["Action", "line-with-label", "[t] \"con etichetta\"; [n] content/*[1]; [t] \"fine etichetta\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"etichetta\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"etichetta\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"vuoto\""], ["Action", "empty-line-with-label", "[t] \"con etichetta\"; [n] content/*[1]; [t] \"fine etichetta\" (pause:200); [t] \"vuoto\""], ["Action", "empty-line-with-label-brief", "[t] \"etichetta\"; [n] content/*[1] (pause:200); [t] \"vuoto\""], ["Action", "enclose", "[t] \"inizio chiuso\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"fine chiuso\""], ["Action", "leftbar", "[t] \"barra vericale\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"barra vericale\""], ["Action", "crossout", "[t] \"cancellato\"; [n] children/*[1]; [t] \"fine cancellato\""], ["Action", "cancel", "[t] \"cancellato\"; [n] children/*[1]/children/*[1]; [t] \"con\"; [n] children/*[2]; [t] \"fine cancellato\""], ["Action", "cancel-reverse", "[t] \"cancellato\"; [n] children/*[2]/children/*[1]; [t] \"con\"; [n] children/*[1]; [t] \"fine cancellato\""], ["Action", "multi-inference", "[t] \"regola di inferenza\"; [m] content/*; [t] \"con conclusione\"; [n] children/*[1]; [t] \"e\"; [t] count(children/*[2]/children/*); [t] \"premesse\""], ["Action", "inference", "[t] \"regola di inferenza\"; [m] content/*; [t] \"con conclusione\"; [n] children/*[1]; [t] \"e\"; [t] count(children/*[2]/children/*); [t] \"premessa\""], ["Action", "premise", "[m] children/* (ctxtFunc:CTFordinalCounter, context:\"premise \")"], ["Action", "conclusion", "[n] children/*[1]"], ["Action", "label", "[t] \"etichetta\"; [n] children/*[1]"], ["Action", "axiom", "[t] \"assioma\"; [m] children/*[1]"], ["Action", "empty-axiom", "[t] \"assioma vuoto\""]]}, "it/rules/prefix_italian.min": {"modality": "prefix", "domain": "default", "locale": "it", "inherits": "base", "rules": []}, "it/rules/prefix_italian_actions.min": {"modality": "prefix", "domain": "default", "locale": "it", "kind": "actions", "rules": [["Action", "numerator", "[t] \"numeratore\" (pause:200)"], ["Action", "denominator", "[t] \"denominatore\" (pause:200)"], ["Action", "base", "[t] \"base\" (pause:200)"], ["Action", "exponent", "[t] \"esponente\" (pause:200)"], ["Action", "subscript", "[t] \"pedice\" (pause:200)"], ["Action", "overscript", "[t] \"apice\" (pause:200)"], ["Action", "underscript", "[t] \"sottoscritto\" (pause:200)"], ["Action", "radicand", "[t] \"radicando\" (pause:200)"], ["Action", "index", "[t] \"indice\" (pause:200)"], ["Action", "leftsub", "[t] \"pedice sinistro\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"pedice sinistro\" (pause:200)"], ["Action", "leftsuper", "[t] \"apice sinistro\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"apice sinistro\" (pause:200)"], ["Action", "rightsub", "[t] \"pedice destro\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"pedice destro\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"apice destro\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"apice destro\" (pause:200)"], ["Action", "choice", "[t] \"quantità scelta\" (pause:200)"], ["Action", "select", "[t] \"quantità selezione\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"riga\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"colonna\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"colonna\" (pause:200)"]]}, "it/rules/summary_italian.min": {"locale": "it", "modality": "summary", "inherits": "romance", "rules": []}, "it/rules/summary_italian_actions.min": {"locale": "it", "modality": "summary", "kind": "actions", "rules": [["Action", "collapsed-masculine", "[t] \"collassato\""], ["Action", "collapsed-feminine", "[t] \"collassata\""], ["Action", "abstr-identifier-long", "[t] \"identificatore lungo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-identifier", "[t] \"identificatore\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-number-long", "[t] \"intero lungo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-number", "[t] \"numero\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-mixed-number-long", "[t] \"numero misto lungo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-mixed-number", "[t] \"numero misto\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-text", "[t] \"testo\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-function", "[t] \"espressione funzionale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-function-brief", "[t] \"funzione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-lim", "[t] \"funzione limite\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-lim-brief", "[t] \"limite\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-fraction", "[t] \"frazione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-fraction-brief", "[t] \"frazione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-continued-fraction", "[t] \"frazione continua\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-continued-fraction-brief", "[t] \"frazione continua\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-sqrt", "[t] \"radice quadrata\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-sqrt-nested", "[t] \"radice quadrata doppia\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-end", "[t] \"radice di indice\"; [n] children/*[1] (engine:modality=speech); [t] \"indice finale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root", "[t] \"radice di indice\"; [n] children/*[1] (engine:modality=speech); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-brief", "[t] \"radice \"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-nested-end", "[t] \"radicale doppio di indice\"; [n] children/*[1] (engine:modality=speech); [t] \"indice finale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-root-nested", "[t] \"radicale doppio di indice\"; [n] children/*[1] (engine:modality=speech); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-root-nested-brief", "[t] \"radicale doppio\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-superscript", "[t] \"potenza\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-subscript", "[t] \"pedice\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-subsup", "[t] \"potenza con pedice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-infixop", "[t] @role (grammar:localRole); [t] \"con\"; [t] count(./children/*); [t] \"elementi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-infixop-var", "[t] @role (grammar:localRole); [t] \"con numero variabile di elementi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-addition", "[t] \"somma con\"; [t] count(./children/*); [t] \"addendi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-addition-brief", "[t] \"somma\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-addition-var", "[t] \"somma con numero variabile di addendi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multiplication", "[t] \"prodotto di\"; [t] count(./children/*); [t] \"fattori\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-multiplication-brief", "[t] \"prodotto\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-multiplication-var", "[t] \"prodotto con numero variabile di fattori\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector", "[t] count(./children/*); [t] \"vettore dimensionale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector-brief", "[t] \"vettore\""], ["Action", "abstr-vector-var", "[t] \"vettore di dimensione n\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-binomial", "[t] \"binomiale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant", "[t] count(./children/*); [t] \"derminante dimensionale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant-brief", "[t] \"determinante\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant-var", "[t] \"determinante di dimensione n\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-squarematrix", "[t] count(./children/*); [t] \"matrice quadrata dimensionale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-squarematrix-brief", "[t] \"matrice quadrata\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-rowvector", "[t] count(./children/row/children/*); [t] \"vettore riga dimensionale\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-rowvector-brief", "[t] \"vettore riga\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-rowvector-var", "[t] \"vettore riga di dimensione n\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"per\"; [t] count(children/*[1]/children/*); [t] \"matrice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix-brief", "[t] \"matrice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix-var", "[t] \"matrice dimensionale n pr m\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cases", "[t] \"comando switch\"; [t] \"con\"; [t] count(children/*); [t] \"casi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-cases-brief", "[t] \"comando switch\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-cases-var", "[t] \"comando di switch con numero variabile di casi\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-punctuated", "[n] content/*[1]; [t] \"lista separata\"; [t] \"di lunghezza\"; [t] count(children/*) - count(content/*); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated-brief", "[n] content/*[1]; [t] \"lista separata\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated-var", "[n] content/*[1]; [t] \"lista separata\"; [t] \"di lunghezza variabile\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-bigop", "[n] content/*[1]; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-integral", "[t] \"integrale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation", "[t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-relation-seq", "[t] @role (grammar:localRole); [t] \"sequenza\"; [t] \"con\"; [t] count(./children/*); [t] \"elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation-seq-brief", "[t] @role (grammar:localRole); [t] \"sequenza\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation-var", "[t] @role (grammar:localRole); [t] \"sequenza\"; [t] \"con numero variabile di elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel", "[t] \"sequenza di relazione\"; [t] \"con\"; [t] count(./children/*); [t] \"elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel-brief", "[t] \"sequenza di relazione\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel-var", "[t] \"sequenza di relazione con numero variabile di elementi\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-table", "[t] \"tavola con\"; [t] count(children/*); [t] \"righe e\"; [t] count(children/*[1]/children/*); [t] \"colonne\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-line", "[t] \"in\"; [t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-row", "[t] \"in\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"con\"; [t] count(children/*); [t] \"colonne\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cell", "[t] \"in\"; [t] @role (grammar:localRole); [n] . (grammar:gender=\"f\")"]]}}