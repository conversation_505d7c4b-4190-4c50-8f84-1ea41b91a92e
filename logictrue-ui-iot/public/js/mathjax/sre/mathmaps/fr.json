{"fr/messages/alphabets.min": {"kind": "alphabets", "locale": "fr", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["nabla", "alpha", "b<PERSON><PERSON>", "gamma", "delta", "epsilon", "<PERSON><PERSON><PERSON>", "<PERSON>ta", "thêta", "iota", "kappa", "lambda", "mû", "nû", "xi", "omicron", "pi", "rhô", "sigma final", "sigma", "tau", "upsilon", "phi", "chi", "psi", "oméga", "<PERSON><PERSON><PERSON><PERSON><PERSON> partielle", "epsilon", "thêta", "kappa", "phi", "rhô", "pi"], "greekCap": ["Alpha", "<PERSON><PERSON><PERSON>", "Gamma", "Delta", "Epsilon", "<PERSON><PERSON><PERSON>", "Êta", "<PERSON><PERSON><PERSON><PERSON>", "Iota", "Kappa", "Lambda", "Mû", "Nû", "Xi", "Omicron", "Pi", "Rhô", "<PERSON><PERSON><PERSON><PERSON>", "Sigma", "Tau", "Upsilon", "Phi", "<PERSON>", "Psi", "Oméga"], "capPrefix": {"default": "majuscule"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "fr/messages/messages.min": {"kind": "messages", "locale": "fr", "messages": {"MS": {"START": "début", "FRAC_V": "fraction", "FRAC_B": "frac", "FRAC_S": "frac", "END": "fin", "FRAC_OVER": "sur", "ONCE": "1", "TWICE": "2", "NEST_FRAC": "imbriquée", "ENDFRAC": "fin frac", "SUPER": "sup", "SUB": "sub", "SUP": "sup", "SUPERSCRIPT": "exposant", "SUBSCRIPT": "indice", "BASELINE": "position de base", "BASE": "position de base", "NESTED": "imbriquée", "NEST_ROOT": "imbriquée", "STARTROOT": "début racine", "ENDROOT": "fin racine", "ROOTINDEX": "indice du radical", "ROOT": "racine", "INDEX": "indice", "UNDER": "sous", "UNDERSCRIPT": "souscript", "OVER": "sus", "OVERSCRIPT": "suscript", "ENDSCRIPTS": "fin scripts"}, "MSroots": {"2": "<PERSON><PERSON><PERSON>", "3": "cubique"}, "font": {"bold": "en gras", "bold-fraktur": "en gothique gras", "bold-italic": "en italique gras", "bold-script": "de ronde en gras", "caligraphic": "en calligraphique", "caligraphic-bold": "en calligraphique gras", "double-struck": "a<PERSON><PERSON>", "double-struck-italic": "ajouré en italique", "fraktur": "en gothique", "fullwidth": "en pleine largeur", "italic": "en italique", "monospace": "en chasse fixe", "normal": "en normal", "oldstyle": "en ancien", "oldstyle-bold": "en ancien gras", "script": "de ronde", "sans-serif": "sans empattement", "sans-serif-italic": "en italique sans empattement", "sans-serif-bold": "en gras sans empattement", "sans-serif-bold-italic": "en italique gras sans empattement", "unknown": "inconnu"}, "embellish": {"super": ["exposant", "prefixCombiner"], "sub": ["indice", "prefixCombiner"], "circled": "encerclé", "parenthesized": "entre parenthèses", "period": "un point", "negative-circled": "encerclé noir", "double-circled": "encerclé double", "circled-sans-serif": "sans empattement encerclé", "negative-circled-sans-serif": "sans empattement encerclé noir", "comma": "virgule", "squared": "encadré", "negative-squared": "encadré inverse"}, "role": {"addition": "addition", "multiplication": "multiplication", "subtraction": "soustraction", "division": "division", "equality": "égalité", "inequality": "inégalité", "element": "élément", "arrow": "flèche", "determinant": "déterminant", "rowvector": "vecteur-rang<PERSON>", "binomial": "binomial", "squarematrix": "matrice car<PERSON>e", "set empty": "ensemble vide", "set extended": "extension", "set singleton": "singleton", "set collection": "collection", "label": "étiquette", "multiline": "multi-ligne", "matrix": "matrice", "vector": "vecteur", "cases": "déclaration de cas", "table": "tableau", "unknown": "inconnu"}, "enclose": {"longdiv": "longue division", "actuarial": "notation actuarielle", "radical": "radical", "box": "b<PERSON><PERSON><PERSON>", "roundedbox": "bo<PERSON><PERSON> a<PERSON>e", "circle": "cercle", "left": "barre verticale gauche", "right": "barre verticale droite", "top": "trait suscrit", "bottom": "trait souscrit", "updiagonalstrike": "texte biffé diagonal montant", "downdiagonalstrike": "texte biffé diagonal descendant", "verticalstrike": "texte biffé vertical", "horizontalstrike": "texte biffé horizontal", "madruwb": "symbole factorielle arabe", "updiagonalarrow": "flèche diagonale montante", "phasorangle": "angle de phase", "unknown": "division longue"}, "navigate": {"COLLAPSIBLE": "compressible", "EXPANDABLE": "décompressible", "LEVEL": "niveau"}, "regexp": {"TEXT": "a-zA-ZàâæçéèêëîïôœùûüÿÀÂÆÇÉÈÊËÎÏÔŒÙÛÜŸ", "NUMBER": "((\\d{1,3})(?=( ))(( )\\d{3})*(,\\d+)?)|^\\d*,\\d+|^\\d+", "DECIMAL_MARK": ",", "DIGIT_GROUP": "", "JOINER_SUBSUPER": "-", "JOINER_FRAC": " "}, "unitTimes": ""}}, "fr/messages/numbers.min": {"kind": "numbers", "locale": "fr", "messages": {"zero": "zero", "ones": ["", "un", "deux", "trois", "quatre", "cinq", "six", "sept", "huit", "neuf", "dix", "onze", "douze", "treize", "quatorze", "quinze", "seize", "dix-sept", "dix-huit", "dix-neuf"], "large": ["", "mille", "millions", "milliards", "billions", "mille billions", "trillions", "mille trillions", "quadrillions", "mille quadrillions", "quintillions", "mille quintillions"], "special": {"tens-fr": ["", "", "vingt", "trente", "quarante", "cinqua<PERSON>", "soixante", "soixante-dix", "quatre-vingts", "quatre-vingt-dix"], "tens-be": ["", "", "vingt", "trente", "quarante", "cinqua<PERSON>", "soixante", "septante", "quatre-vingts", "nonante"], "tens-ch": ["", "", "vingt", "trente", "quarante", "cinqua<PERSON>", "soixante", "septante", "huitante", "nonante"]}, "vulgarSep": "-"}}, "fr/si/prefixes.min": [{"Y": "yotta", "Z": "zetta", "E": "exa", "P": "p<PERSON><PERSON>", "T": "téra", "G": "giga", "M": "méga", "k": "kilo", "h": "hecto", "da": "déca", "d": "d<PERSON><PERSON>", "c": "centi", "m": "milli", "µ": "micro", "μ": "micro", "n": "nano", "p": "pico", "f": "femto", "a": "atto", "z": "zepto", "y": "yocto"}], "fr/functions/algebra.min": [{"locale": "fr"}, {"key": "deg", "mappings": {"default": {"default": "de<PERSON><PERSON>"}}}, {"key": "det", "mappings": {"default": {"default": "déterminant"}}}, {"key": "dim", "mappings": {"default": {"default": "dimension"}}}, {"key": "hom", "mappings": {"default": {"default": "homomorphisme"}}}, {"key": "ker", "mappings": {"default": {"default": "noyau"}}}, {"key": "Tr", "mappings": {"default": {"default": "trace"}}}], "fr/functions/elementary.min": [{"locale": "fr"}, {"key": "log", "mappings": {"default": {"default": "log"}}}, {"key": "ln", "mappings": {"default": {"default": "logarithme népérien"}, "clearspeak": {"default": "l n", "Log_LnAsNaturalLog": "logarithme népérien"}}}, {"key": "lg", "mappings": {"default": {"default": "logarithme décimal"}}}, {"key": "exp", "mappings": {"default": {"default": "exponentielle"}}}, {"key": "gcd", "mappings": {"default": {"default": "plus grand commun diviseur"}, "mathspeak": {"default": "pgcd"}, "clearspeak": {"default": "pgcd"}}, "names": ["PGCD", "pgcd"]}, {"key": "lcm", "mappings": {"default": {"default": "plus petit commun multiple"}, "mathspeak": {"default": "ppcm"}, "clearspeak": {"default": "ppcm"}}, "names": ["ppcm", "PPCM", "PPMC", "ppmc"]}, {"key": "arg", "mappings": {"default": {"default": "argument"}}}, {"key": "im", "mappings": {"default": {"default": "partie imaginaire"}}}, {"key": "re", "mappings": {"default": {"default": "partie réelle"}}}, {"key": "inf", "mappings": {"default": {"default": "borne inférieure"}}}, {"key": "lim", "mappings": {"default": {"default": "limite"}}}, {"key": "max", "mappings": {"default": {"default": "maximum"}}}, {"key": "min", "mappings": {"default": {"default": "minimum"}}}, {"key": "sup", "mappings": {"default": {"default": "borne supérieure"}}}, {"key": "liminf", "mappings": {"default": {"default": "limite inferior"}}}, {"key": "limsup", "mappings": {"default": {"default": "limite superior"}}}, {"key": "<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "limite inductive"}}}, {"key": "proj<PERSON>", "mappings": {"default": {"default": "limite projective"}}}, {"key": "mod", "mappings": {"default": {"default": "modulo"}}}, {"key": "Pr", "mappings": {"default": {"default": "probabilité"}}}], "fr/functions/hyperbolic.min": [{"locale": "fr"}, {"key": "cosh", "mappings": {"default": {"default": "cosinus hyperbolique"}}}, {"key": "coth", "mappings": {"default": {"default": "cotangente hyperbolique"}}}, {"key": "csch", "mappings": {"default": {"default": "cosécante hyperbolique"}}}, {"key": "sech", "mappings": {"default": {"default": "sécante hyperbolique"}}}, {"key": "sinh", "mappings": {"default": {"default": "sinus hyperbolique"}}}, {"key": "tanh", "mappings": {"default": {"default": "tangente hyperbolique"}}}, {"key": "arcosh", "mappings": {"default": {"default": "argument cosinus hyperbolique"}}, "names": ["argch"]}, {"key": "arcoth", "mappings": {"default": {"default": "argument cotangente hyperbolique"}}, "names": ["argcoth"]}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "argument cosécante hyperbolique"}}, "names": ["argcsch"]}, {"key": "arsech", "mappings": {"default": {"default": "argument sécante hyperbolique"}}, "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"key": "a<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "argument sinus hyperbolique"}}, "names": ["arg<PERSON>"]}, {"key": "artanh", "mappings": {"default": {"default": "argument tangente hyperbolique"}}, "names": ["argth"]}], "fr/functions/trigonometry.min": [{"locale": "fr"}, {"key": "cos", "mappings": {"default": {"default": "cosinus"}}}, {"key": "cot", "mappings": {"default": {"default": "cotangente"}}}, {"key": "csc", "mappings": {"default": {"default": "cosécante"}}}, {"key": "sec", "mappings": {"default": {"default": "sécante"}}}, {"key": "sin", "mappings": {"default": {"default": "sinus"}}}, {"key": "tan", "mappings": {"default": {"default": "tangente"}}}, {"key": "arccos", "mappings": {"default": {"default": "arc cosinus"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "arc cotangente"}}}, {"key": "arccsc", "mappings": {"default": {"default": "arc cosécante"}}}, {"key": "arcsec", "mappings": {"default": {"default": "arc sécante"}}}, {"key": "arcsin", "mappings": {"default": {"default": "arc sinus"}}}, {"key": "arctan", "mappings": {"default": {"default": "arc tangente"}}}], "fr/symbols/digits_rest.min": [{"locale": "fr"}, {"key": "00B2", "mappings": {"default": {"default": "au carré"}, "mathspeak": {"default": "au carré"}, "clearspeak": {"default": "au carré"}}}, {"key": "00B3", "mappings": {"default": {"default": "cubique"}, "mathspeak": {"default": "cubique"}, "clearspeak": {"default": "cubique"}}}, {"key": "00BC", "mappings": {"default": {"default": "un quart"}}}, {"key": "00BD", "mappings": {"default": {"default": "un demi"}}}, {"key": "00BE", "mappings": {"default": {"default": "trois quarts"}}}, {"key": "2150", "mappings": {"default": {"default": "un septième"}}}, {"key": "2151", "mappings": {"default": {"default": "un neuvième"}}}, {"key": "2152", "mappings": {"default": {"default": "un dixième"}}}, {"key": "2153", "mappings": {"default": {"default": "un tiers"}}}, {"key": "2154", "mappings": {"default": {"default": "deux tiers"}}}, {"key": "2155", "mappings": {"default": {"default": "un cinquième"}}}, {"key": "2156", "mappings": {"default": {"default": "deux cinquièmes"}}}, {"key": "2157", "mappings": {"default": {"default": "trois cinquièmes"}}}, {"key": "2158", "mappings": {"default": {"default": "quatre cinquièmes"}}}, {"key": "2159", "mappings": {"default": {"default": "un sixième"}}}, {"key": "215A", "mappings": {"default": {"default": "cinq sixièmes"}}}, {"key": "215B", "mappings": {"default": {"default": "un huitième"}}}, {"key": "215C", "mappings": {"default": {"default": "trois huitièmes"}}}, {"key": "215D", "mappings": {"default": {"default": "cinq huitièmes"}}}, {"key": "215E", "mappings": {"default": {"default": "sept huitièmes"}}}, {"key": "215F", "mappings": {"default": {"default": "numérateur un"}}}, {"key": "2189", "mappings": {"default": {"default": "zero tiers"}}}, {"key": "3248", "mappings": {"default": {"default": "dix cerclé sur carré noir"}}}, {"key": "3249", "mappings": {"default": {"default": "vingt cerclé sur carré noir"}}}, {"key": "324A", "mappings": {"default": {"default": "trente cerclé sur carré noir"}}}, {"key": "324B", "mappings": {"default": {"default": "quarante cerclé sur carré noir"}}}, {"key": "324C", "mappings": {"default": {"default": "cinquante cerclé sur carré noir"}}}, {"key": "324D", "mappings": {"default": {"default": "soixante cerclé sur carré noir"}}}, {"key": "324E", "mappings": {"default": {"default": "soixante dix cerclé sur carré noir"}}}, {"key": "324F", "mappings": {"default": {"default": "quatre vingts cerclé sur carré noir"}}}], "fr/symbols/greek-rest.min": [{"locale": "fr"}, {"key": "0394", "mappings": {"clearspeak": {"default": "triangle", "TriangleSymbol_Delta": "Delta majuscule"}}}], "fr/symbols/greek-scripts.min": [{"locale": "fr"}, {"key": "1D26", "mappings": {"default": {"default": "petite gamma majuscule"}}}, {"key": "1D27", "mappings": {"default": {"default": "petite lambda majuscule"}}}, {"key": "1D28", "mappings": {"default": {"default": "petite pi majuscule"}}}, {"key": "1D29", "mappings": {"default": {"default": "petite rhô majuscule"}}}, {"key": "1D2A", "mappings": {"default": {"default": "petite psi majuscule"}}}, {"key": "1D5E", "mappings": {"default": {"default": "gamma suscript"}}}, {"key": "1D60", "mappings": {"default": {"default": "phi suscript"}}}, {"key": "1D66", "mappings": {"default": {"default": "b<PERSON><PERSON> souscrit"}}}, {"key": "1D67", "mappings": {"default": {"default": "gamma souscrit"}}}, {"key": "1D68", "mappings": {"default": {"default": "rhô souscrit"}}}, {"key": "1D69", "mappings": {"default": {"default": "phi souscrit"}}}, {"key": "1D6A", "mappings": {"default": {"default": "khi souscrit"}}}], "fr/symbols/greek-symbols.min": [{"locale": "fr"}, {"key": "03D0", "mappings": {"default": {"default": "b<PERSON><PERSON> grec"}}}, {"key": "03D7", "mappings": {"default": {"default": "ligature kai"}}}, {"key": "03F6", "mappings": {"default": {"default": "epsilon lunaire réfléchi"}}}, {"key": "1D7CA", "mappings": {"default": {"default": "Digamma en gras"}}}, {"key": "1D7CB", "mappings": {"default": {"default": "digamma en gras"}}}], "fr/symbols/hebrew_letters.min": [{"locale": "fr"}, {"key": "2135", "mappings": {"default": {"default": "alef"}}}, {"key": "2136", "mappings": {"default": {"default": "b<PERSON><PERSON>"}}}, {"key": "2137", "mappings": {"default": {"default": "gui<PERSON>"}}}, {"key": "2138", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}], "fr/symbols/latin-lower-double-accent.min": [{"locale": "fr"}, {"key": "01D6", "mappings": {"default": {"default": "u trémaa et macron"}}}, {"key": "01D8", "mappings": {"default": {"default": "u trémaa et accent aigu"}}}, {"key": "01DA", "mappings": {"default": {"default": "u trémaa et caron"}}}, {"key": "01DC", "mappings": {"default": {"default": "u trémaa et accent grave"}}}, {"key": "01DF", "mappings": {"default": {"default": "a trémaa et macron"}}}, {"key": "01E1", "mappings": {"default": {"default": "a point en chef et macron"}}}, {"key": "01ED", "mappings": {"default": {"default": "o ogonek et macron"}}}, {"key": "01FB", "mappings": {"default": {"default": "a rond en chef et accent aigu"}}}, {"key": "022B", "mappings": {"default": {"default": "o tréma et macron"}}}, {"key": "022D", "mappings": {"default": {"default": "o tilde et macron"}}}, {"key": "0231", "mappings": {"default": {"default": "o point en chef et macron"}}}, {"key": "1E09", "mappings": {"default": {"default": "c cédille et accent aigu"}}}, {"key": "1E15", "mappings": {"default": {"default": "e macron et accent grave"}}}, {"key": "1E17", "mappings": {"default": {"default": "e macron et accent aigu"}}}, {"key": "1E1D", "mappings": {"default": {"default": "e cédille et brève"}}}, {"key": "1E2F", "mappings": {"default": {"default": "i tréma et accent aigu"}}}, {"key": "1E39", "mappings": {"default": {"default": "l point souscrit et macron"}}}, {"key": "1E4D", "mappings": {"default": {"default": "o tilde et accent aigu"}}}, {"key": "1E4F", "mappings": {"default": {"default": "o tilde et tréma"}}}, {"key": "1E51", "mappings": {"default": {"default": "o macron et accent grave"}}}, {"key": "1E53", "mappings": {"default": {"default": "o macron et accent aigu"}}}, {"key": "1E5D", "mappings": {"default": {"default": "r point souscrit et macron"}}}, {"key": "1E65", "mappings": {"default": {"default": "s accent aigu et point en chef"}}}, {"key": "1E67", "mappings": {"default": {"default": "s caron et point en chef"}}}, {"key": "1E69", "mappings": {"default": {"default": "s point souscrit et point en chef"}}}, {"key": "1E79", "mappings": {"default": {"default": "u tilde et accent aigu"}}}, {"key": "1E7B", "mappings": {"default": {"default": "u macron et tréma"}}}, {"key": "1EA5", "mappings": {"default": {"default": "a accent circonflexe et accent aigu"}}}, {"key": "1EA7", "mappings": {"default": {"default": "a accent circonflexe et accent grave"}}}, {"key": "1EA9", "mappings": {"default": {"default": "a accent circonflexe et crochet en chef"}}}, {"key": "1EAB", "mappings": {"default": {"default": "a accent circonflexe et tilde"}}}, {"key": "1EAD", "mappings": {"default": {"default": "a accent circonflexe et point souscrit"}}}, {"key": "1EAF", "mappings": {"default": {"default": "a brève et accent aigu"}}}, {"key": "1EB1", "mappings": {"default": {"default": "a brève et accent grave"}}}, {"key": "1EB3", "mappings": {"default": {"default": "a brève et crochet en chef"}}}, {"key": "1EB5", "mappings": {"default": {"default": "a brève et tilde"}}}, {"key": "1EB7", "mappings": {"default": {"default": "a brève et point souscrit"}}}, {"key": "1EBF", "mappings": {"default": {"default": "e accent circonflexe et accent aigu"}}}, {"key": "1EC1", "mappings": {"default": {"default": "e accent circonflexe et accent grave"}}}, {"key": "1EC3", "mappings": {"default": {"default": "e accent circonflexe et crochet en chef"}}}, {"key": "1EC5", "mappings": {"default": {"default": "e accent circonflexe et tilde"}}}, {"key": "1EC7", "mappings": {"default": {"default": "e accent circonflexe et point souscrit"}}}, {"key": "1ED1", "mappings": {"default": {"default": "o accent circonflexe et accent aigu"}}}, {"key": "1ED3", "mappings": {"default": {"default": "o accent circonflexe et accent grave"}}}, {"key": "1ED5", "mappings": {"default": {"default": "o accent circonflexe et crochet en chef"}}}, {"key": "1ED7", "mappings": {"default": {"default": "o accent circonflexe et tilde"}}}, {"key": "1ED9", "mappings": {"default": {"default": "o accent circonflexe et point souscrit"}}}, {"key": "1EDB", "mappings": {"default": {"default": "o cornu accent aigu"}}}, {"key": "1EDD", "mappings": {"default": {"default": "o cornu accent grave"}}}, {"key": "1EDF", "mappings": {"default": {"default": "o cornu crochet en chef"}}}, {"key": "1EE1", "mappings": {"default": {"default": "o cornu tilde"}}}, {"key": "1EE3", "mappings": {"default": {"default": "o cornu point souscrit"}}}, {"key": "1EE9", "mappings": {"default": {"default": "u cornu accent aigu"}}}, {"key": "1EEB", "mappings": {"default": {"default": "u cornu accent grave"}}}, {"key": "1EED", "mappings": {"default": {"default": "u cornu crochet en chef"}}}, {"key": "1EEF", "mappings": {"default": {"default": "u cornu tilde"}}}, {"key": "1EF1", "mappings": {"default": {"default": "u cornu point souscrit"}}}], "fr/symbols/latin-lower-phonetic.min": [{"locale": "fr"}, {"key": "00F8", "mappings": {"default": {"default": "o barré"}}}, {"key": "0111", "mappings": {"default": {"default": "d bar<PERSON>"}}}, {"key": "0127", "mappings": {"default": {"default": "h bar<PERSON>"}}}, {"key": "0142", "mappings": {"default": {"default": "l barré"}}}, {"key": "0167", "mappings": {"default": {"default": "t barré"}}}, {"key": "0180", "mappings": {"default": {"default": "b barré"}}}, {"key": "019B", "mappings": {"default": {"default": "lambda barré"}}}, {"key": "01B6", "mappings": {"default": {"default": "z barré"}}}, {"key": "01BE", "mappings": {"default": {"default": "coup de glotte barré culbuté"}}}, {"key": "01E5", "mappings": {"default": {"default": "g barré"}}}, {"key": "01FF", "mappings": {"default": {"default": "o barré accent aigu"}}}, {"key": "023C", "mappings": {"default": {"default": "c barré"}}}, {"key": "0247", "mappings": {"default": {"default": "e barré"}}}, {"key": "0249", "mappings": {"default": {"default": "j bar<PERSON>"}}}, {"key": "024D", "mappings": {"default": {"default": "r bar<PERSON>"}}}, {"key": "024F", "mappings": {"default": {"default": "y barré"}}}, {"key": "025F", "mappings": {"default": {"default": "j sans point barré"}}}, {"key": "0268", "mappings": {"default": {"default": "i barré"}}}, {"key": "0284", "mappings": {"default": {"default": "j sans point barré crosse"}}}, {"key": "02A1", "mappings": {"default": {"default": "coup de glotte barré"}}}, {"key": "02A2", "mappings": {"default": {"default": "coup de glotte barré r<PERSON>"}}}, {"key": "1D13", "mappings": {"default": {"default": "o couché barré obliquement"}}}, {"key": "1D7C", "mappings": {"default": {"default": "iota barré"}}}, {"key": "1D7D", "mappings": {"default": {"default": "p bar<PERSON>"}}}, {"key": "1D7F", "mappings": {"default": {"default": "upsilon barré"}}}, {"key": "1E9C", "mappings": {"default": {"default": "s long à barre diagonale"}}}, {"key": "1E9D", "mappings": {"default": {"default": "s long à barre haute"}}}, {"key": "018D", "mappings": {"default": {"default": "delta culbutée"}}}, {"key": "1E9B", "mappings": {"default": {"default": "s long point en chef"}}}, {"key": "1E9F", "mappings": {"default": {"default": "delta"}}}, {"key": "0138", "mappings": {"default": {"default": "kra"}}}, {"key": "017F", "mappings": {"default": {"default": "s long"}}}, {"key": "0183", "mappings": {"default": {"default": "b potence"}}}, {"key": "0185", "mappings": {"default": {"default": "sixiéme ton"}}}, {"key": "0188", "mappings": {"default": {"default": "c crosse"}}}, {"key": "018C", "mappings": {"default": {"default": "d potence"}}}, {"key": "0192", "mappings": {"default": {"default": "f cursif"}}}, {"key": "0195", "mappings": {"default": {"default": "hv (Hwair)"}}}, {"key": "0199", "mappings": {"default": {"default": "k crosse"}}}, {"key": "019A", "mappings": {"default": {"default": "l ray<PERSON>"}}}, {"key": "019E", "mappings": {"default": {"default": "n à long jambage de droite"}}}, {"key": "01A1", "mappings": {"default": {"default": "o cornu"}}}, {"key": "01A3", "mappings": {"default": {"default": "gha"}}}, {"key": "01A5", "mappings": {"default": {"default": "p crosse"}}}, {"key": "01A8", "mappings": {"default": {"default": "deuxiéme ton"}}}, {"key": "01AA", "mappings": {"default": {"default": "ech ré<PERSON><PERSON><PERSON> bou<PERSON>lé"}}}, {"key": "01AB", "mappings": {"default": {"default": "t hameçon palatal"}}}, {"key": "01AD", "mappings": {"default": {"default": "t crosse"}}}, {"key": "01B0", "mappings": {"default": {"default": "u cornu"}}}, {"key": "01B4", "mappings": {"default": {"default": "y crosse"}}}, {"key": "01B9", "mappings": {"default": {"default": "ej <PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "01BA", "mappings": {"default": {"default": "ej ha<PERSON><PERSON>"}}}, {"key": "01BD", "mappings": {"default": {"default": "cinqui<PERSON>me tun"}}}, {"key": "01BF", "mappings": {"default": {"default": "wynn"}}}, {"key": "01C6", "mappings": {"default": {"default": "dz caron"}}}, {"key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"key": "01E3", "mappings": {"default": {"default": "ae macron"}}}, {"key": "01EF", "mappings": {"default": {"default": "ej caron"}}}, {"key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"key": "021D", "mappings": {"default": {"default": "yogh"}}}, {"key": "026E", "mappings": {"default": {"default": "lej"}}}, {"key": "0292", "mappings": {"default": {"default": "ej"}}}, {"key": "0293", "mappings": {"default": {"default": "ej b<PERSON><PERSON>"}}}, {"key": "02A4", "mappings": {"default": {"default": "digram<PERSON>"}}}, {"key": "01DD", "mappings": {"default": {"default": "e culbuté"}}}, {"key": "01FD", "mappings": {"default": {"default": "ae accent aigu (ash)"}}}, {"key": "0221", "mappings": {"default": {"default": "d bouc<PERSON>"}}}, {"key": "0223", "mappings": {"default": {"default": "ou"}}}, {"key": "0225", "mappings": {"default": {"default": "z crochet"}}}, {"key": "0234", "mappings": {"default": {"default": "l bouclé"}}}, {"key": "0235", "mappings": {"default": {"default": "n bouclé"}}}, {"key": "0236", "mappings": {"default": {"default": "t bouc<PERSON>"}}}, {"key": "0238", "mappings": {"default": {"default": "Ligature minuscule latine Db"}}}, {"key": "0239", "mappings": {"default": {"default": "Ligature minuscule latine Qp"}}}, {"key": "023F", "mappings": {"default": {"default": "s à paraphe"}}}, {"key": "0240", "mappings": {"default": {"default": "z <PERSON> paraphe"}}}, {"key": "0242", "mappings": {"default": {"default": "coup de glotte"}}}, {"key": "024B", "mappings": {"default": {"default": "q avec hameçon"}}}, {"key": "0250", "mappings": {"default": {"default": "a culbuté"}}}, {"key": "0251", "mappings": {"default": {"default": "alpha"}}}, {"key": "0252", "mappings": {"default": {"default": "alpha culbuté"}}}, {"key": "0253", "mappings": {"default": {"default": "b crosse"}}}, {"key": "0254", "mappings": {"default": {"default": "o ouvert"}}}, {"key": "0255", "mappings": {"default": {"default": "c bou<PERSON><PERSON>"}}}, {"key": "0256", "mappings": {"default": {"default": "d hameçon rétroflexe"}}}, {"key": "0257", "mappings": {"default": {"default": "d crosse"}}}, {"key": "0258", "mappings": {"default": {"default": "e réfléchi"}}}, {"key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"key": "025A", "mappings": {"default": {"default": "schwa crochet"}}}, {"key": "025B", "mappings": {"default": {"default": "e ouvert"}}}, {"key": "025C", "mappings": {"default": {"default": "epsilon réfléchi"}}}, {"key": "025D", "mappings": {"default": {"default": "epsilon réfléchi crochet"}}}, {"key": "025E", "mappings": {"default": {"default": "epsilon réfléchi fermé"}}}, {"key": "0260", "mappings": {"default": {"default": "g crosse"}}}, {"key": "0261", "mappings": {"default": {"default": "g cursif"}}}, {"key": "0263", "mappings": {"default": {"default": "gamma"}}}, {"key": "0264", "mappings": {"default": {"default": "petit gamma"}}}, {"key": "0265", "mappings": {"default": {"default": "h culbuté"}}}, {"key": "0266", "mappings": {"default": {"default": "h crosse"}}}, {"key": "0267", "mappings": {"default": {"default": "eng crosse"}}}, {"key": "0269", "mappings": {"default": {"default": "iota"}}}, {"key": "026B", "mappings": {"default": {"default": "l tilde médian"}}}, {"key": "026C", "mappings": {"default": {"default": "l sang<PERSON>"}}}, {"key": "026D", "mappings": {"default": {"default": "l hameçon rétroflexe"}}}, {"key": "026F", "mappings": {"default": {"default": "m culbuté"}}}, {"key": "0270", "mappings": {"default": {"default": "m hampé culbuté"}}}, {"key": "0271", "mappings": {"default": {"default": "m hame<PERSON>on"}}}, {"key": "0272", "mappings": {"default": {"default": "n hameçon à gauche"}}}, {"key": "0273", "mappings": {"default": {"default": "n hameçon rétroflexe"}}}, {"key": "0275", "mappings": {"default": {"default": "o barré"}}}, {"key": "0277", "mappings": {"default": {"default": "oméga fermé"}}}, {"key": "0278", "mappings": {"default": {"default": "phi"}}}, {"key": "0279", "mappings": {"default": {"default": "r culbuté"}}}, {"key": "027A", "mappings": {"default": {"default": "r prolongé culbuté"}}}, {"key": "027B", "mappings": {"default": {"default": "r crosse culbuté"}}}, {"key": "027C", "mappings": {"default": {"default": "r prolongé"}}}, {"key": "027D", "mappings": {"default": {"default": "r hameçon rétroflexe"}}}, {"key": "027E", "mappings": {"default": {"default": "r sans obit"}}}, {"key": "027F", "mappings": {"default": {"default": "r sans obit réfléchi"}}}, {"key": "0282", "mappings": {"default": {"default": "s hameçon rétroflexe"}}}, {"key": "0283", "mappings": {"default": {"default": "ech"}}}, {"key": "0285", "mappings": {"default": {"default": "r sans obit réfléchi hameçon rétroflexe"}}}, {"key": "0286", "mappings": {"default": {"default": "ech bouclé"}}}, {"key": "0287", "mappings": {"default": {"default": "t culbuté"}}}, {"key": "0288", "mappings": {"default": {"default": "t hameçon rétroflexe"}}}, {"key": "0289", "mappings": {"default": {"default": "u barré"}}}, {"key": "028A", "mappings": {"default": {"default": "upsilon"}}}, {"key": "028B", "mappings": {"default": {"default": "v de ronde"}}}, {"key": "028C", "mappings": {"default": {"default": "v culbuté"}}}, {"key": "028D", "mappings": {"default": {"default": "w culbuté"}}}, {"key": "028E", "mappings": {"default": {"default": "y culbuté"}}}, {"key": "0290", "mappings": {"default": {"default": "z hameçon rétroflexe"}}}, {"key": "0291", "mappings": {"default": {"default": "z bouclé"}}}, {"key": "0295", "mappings": {"default": {"default": "coup de glotte réfléchi"}}}, {"key": "0296", "mappings": {"default": {"default": "coup de glotte culbuté"}}}, {"key": "0297", "mappings": {"default": {"default": "C étiré"}}}, {"key": "0298", "mappings": {"default": {"default": "clic bilabial"}}}, {"key": "029A", "mappings": {"default": {"default": "epsilon fermé"}}}, {"key": "029E", "mappings": {"default": {"default": "k culbuté"}}}, {"key": "02A0", "mappings": {"default": {"default": "q crosse"}}}, {"key": "02A3", "mappings": {"default": {"default": "digramme Dz"}}}, {"key": "02A5", "mappings": {"default": {"default": "digramme <PERSON> bou<PERSON>lé"}}}, {"key": "02A6", "mappings": {"default": {"default": "digramme Ts"}}}, {"key": "02A7", "mappings": {"default": {"default": "digramme Tech"}}}, {"key": "02A8", "mappings": {"default": {"default": "digramme Tc bouclé"}}}, {"key": "02A9", "mappings": {"default": {"default": "digramme <PERSON>"}}}, {"key": "02AA", "mappings": {"default": {"default": "digramme Ls"}}}, {"key": "02AB", "mappings": {"default": {"default": "digramme Lz"}}}, {"key": "02AC", "mappings": {"default": {"default": "percussion bilabiale"}}}, {"key": "02AD", "mappings": {"default": {"default": "percussion bidentale"}}}, {"key": "02AE", "mappings": {"default": {"default": "h culbuté crosse réfléchie"}}}, {"key": "02AF", "mappings": {"default": {"default": "h culbuté crosse réfléchie et hameçon rétroflexe"}}}, {"key": "1D02", "mappings": {"default": {"default": "ae culbuté"}}}, {"key": "1D08", "mappings": {"default": {"default": "epsilon culbuté"}}}, {"key": "1D09", "mappings": {"default": {"default": "i culbuté"}}}, {"key": "1D11", "mappings": {"default": {"default": "o couché"}}}, {"key": "1D12", "mappings": {"default": {"default": "o ouvert couché"}}}, {"key": "1D14", "mappings": {"default": {"default": "oe culbuté"}}}, {"key": "1D16", "mappings": {"default": {"default": "moitié supérieure de o"}}}, {"key": "1D17", "mappings": {"default": {"default": "moitié inférieure de o"}}}, {"key": "1D1D", "mappings": {"default": {"default": "u <PERSON>é"}}}, {"key": "1D1E", "mappings": {"default": {"default": "u tréma couché"}}}, {"key": "1D1F", "mappings": {"default": {"default": "m <PERSON>é"}}}, {"key": "1D24", "mappings": {"default": {"default": "fricative pharyngale voisée"}}}, {"key": "1D25", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"key": "1D6C", "mappings": {"default": {"default": "b tilde mé<PERSON>"}}}, {"key": "1D6D", "mappings": {"default": {"default": "d tilde médian"}}}, {"key": "1D6E", "mappings": {"default": {"default": "f tilde médian"}}}, {"key": "1D6F", "mappings": {"default": {"default": "m tilde médian"}}}, {"key": "1D70", "mappings": {"default": {"default": "n tilde médian"}}}, {"key": "1D71", "mappings": {"default": {"default": "p <PERSON>de médian"}}}, {"key": "1D72", "mappings": {"default": {"default": "r tilde médian"}}}, {"key": "1D73", "mappings": {"default": {"default": "r sans obit tilde médian"}}}, {"key": "1D74", "mappings": {"default": {"default": "s tilde médian"}}}, {"key": "1D75", "mappings": {"default": {"default": "t tilde médian"}}}, {"key": "1D76", "mappings": {"default": {"default": "z tilde médian"}}}, {"key": "1D77", "mappings": {"default": {"default": "g culbuté"}}}, {"key": "1D79", "mappings": {"default": {"default": "g insulaire"}}}, {"key": "1D7A", "mappings": {"default": {"default": "th barré diagonalement"}}}, {"key": "1D80", "mappings": {"default": {"default": "b hame<PERSON>on palatal"}}}, {"key": "1D81", "mappings": {"default": {"default": "d hame<PERSON>on palatal"}}}, {"key": "1D82", "mappings": {"default": {"default": "f hameçon palatal"}}}, {"key": "1D83", "mappings": {"default": {"default": "g hameçon palatal"}}}, {"key": "1D84", "mappings": {"default": {"default": "k hameçon palatal"}}}, {"key": "1D85", "mappings": {"default": {"default": "l hameçon palatal"}}}, {"key": "1D86", "mappings": {"default": {"default": "m hameçon palatal"}}}, {"key": "1D87", "mappings": {"default": {"default": "n hameçon palatal"}}}, {"key": "1D88", "mappings": {"default": {"default": "p hameçon palatal"}}}, {"key": "1D89", "mappings": {"default": {"default": "r hameçon palatal"}}}, {"key": "1D8A", "mappings": {"default": {"default": "s hameçon palatal"}}}, {"key": "1D8B", "mappings": {"default": {"default": "ech hameçon palatal"}}}, {"key": "1D8C", "mappings": {"default": {"default": "v hameçon palatal"}}}, {"key": "1D8D", "mappings": {"default": {"default": "x hameçon palatal"}}}, {"key": "1D8E", "mappings": {"default": {"default": "z hameçon palatal"}}}, {"key": "1D8F", "mappings": {"default": {"default": "a hameçon rétroflexe"}}}, {"key": "1D90", "mappings": {"default": {"default": "alpha hameçon rétroflexe"}}}, {"key": "1D91", "mappings": {"default": {"default": "d crosse et hameçon rétroflexe"}}}, {"key": "1D92", "mappings": {"default": {"default": "e hameçon rétroflexe"}}}, {"key": "1D93", "mappings": {"default": {"default": "epsilon hameçon rétroflexe"}}}, {"key": "1D94", "mappings": {"default": {"default": "epsilon réfléchi hameçon rétroflexe"}}}, {"key": "1D95", "mappings": {"default": {"default": "schwa hameçon rétroflexe"}}}, {"key": "1D96", "mappings": {"default": {"default": "i hameçon rétroflexe"}}}, {"key": "1D97", "mappings": {"default": {"default": "o ouvert hameçon rétroflexe"}}}, {"key": "1D98", "mappings": {"default": {"default": "ech hameçon rétroflexe"}}}, {"key": "1D99", "mappings": {"default": {"default": "u hameçon rétroflexe"}}}, {"key": "1D9A", "mappings": {"default": {"default": "ej hame<PERSON><PERSON> rétrofle<PERSON>"}}}, {"key": "0149", "mappings": {"default": {"default": "n précédée d'une apostrophe"}}}, {"key": "014B", "mappings": {"default": {"default": "eng"}}}], "fr/symbols/latin-lower-single-accent.min": [{"locale": "fr"}, {"key": "00E0", "mappings": {"default": {"default": "a accent grave"}}}, {"key": "00E1", "mappings": {"default": {"default": "a accent aigu"}}}, {"key": "00E2", "mappings": {"default": {"default": "a accent circonflexe"}}}, {"key": "00E3", "mappings": {"default": {"default": "a tilde"}}}, {"key": "00E4", "mappings": {"default": {"default": "a tréma"}}}, {"key": "00E5", "mappings": {"default": {"default": "a rond en chef"}}}, {"key": "00E7", "mappings": {"default": {"default": "c cédi<PERSON>"}}}, {"key": "00E8", "mappings": {"default": {"default": "e accent grave"}}}, {"key": "00E9", "mappings": {"default": {"default": "e accent aigu"}}}, {"key": "00EA", "mappings": {"default": {"default": "e accent circonflexe"}}}, {"key": "00EB", "mappings": {"default": {"default": "e tréma"}}}, {"key": "00EC", "mappings": {"default": {"default": "i accent grave"}}}, {"key": "00ED", "mappings": {"default": {"default": "i accent aigu"}}}, {"key": "00EE", "mappings": {"default": {"default": "i accent circonflexe"}}}, {"key": "00EF", "mappings": {"default": {"default": "i tréma"}}}, {"key": "00F1", "mappings": {"default": {"default": "n tilde"}}}, {"key": "00F2", "mappings": {"default": {"default": "o accent grave"}}}, {"key": "00F3", "mappings": {"default": {"default": "o accent aigu"}}}, {"key": "00F4", "mappings": {"default": {"default": "o accent circonflexe"}}}, {"key": "00F5", "mappings": {"default": {"default": "o tilde"}}}, {"key": "00F6", "mappings": {"default": {"default": "o tréma"}}}, {"key": "00F9", "mappings": {"default": {"default": "u accent grave"}}}, {"key": "00FA", "mappings": {"default": {"default": "u accent aigu"}}}, {"key": "00FB", "mappings": {"default": {"default": "u accent circonflexe"}}}, {"key": "00FC", "mappings": {"default": {"default": "u tréma"}}}, {"key": "00FD", "mappings": {"default": {"default": "y accent aigu"}}}, {"key": "00FF", "mappings": {"default": {"default": "y tréma"}}}, {"key": "0101", "mappings": {"default": {"default": "a macron"}}}, {"key": "0103", "mappings": {"default": {"default": "a brève"}}}, {"key": "0105", "mappings": {"default": {"default": "a ogonek"}}}, {"key": "0107", "mappings": {"default": {"default": "c accent aigu"}}}, {"key": "0109", "mappings": {"default": {"default": "c accent circonflexe"}}}, {"key": "010B", "mappings": {"default": {"default": "c point en chef"}}}, {"key": "010D", "mappings": {"default": {"default": "c caron"}}}, {"key": "010F", "mappings": {"default": {"default": "d caron"}}}, {"key": "0113", "mappings": {"default": {"default": "e macron"}}}, {"key": "0115", "mappings": {"default": {"default": "e brève"}}}, {"key": "0117", "mappings": {"default": {"default": "e point en chef"}}}, {"key": "0119", "mappings": {"default": {"default": "e ogonek"}}}, {"key": "011B", "mappings": {"default": {"default": "e caron"}}}, {"key": "011D", "mappings": {"default": {"default": "g accent circonflexe"}}}, {"key": "011F", "mappings": {"default": {"default": "g brève"}}}, {"key": "0121", "mappings": {"default": {"default": "g point en chef"}}}, {"key": "0123", "mappings": {"default": {"default": "g cédille"}}}, {"key": "0125", "mappings": {"default": {"default": "h accent circonflexe"}}}, {"key": "0129", "mappings": {"default": {"default": "i tilde"}}}, {"key": "012B", "mappings": {"default": {"default": "i macron"}}}, {"key": "012D", "mappings": {"default": {"default": "i brève"}}}, {"key": "012F", "mappings": {"default": {"default": "i ogonek"}}}, {"key": "0131", "mappings": {"default": {"default": "i sans point"}}}, {"key": "0135", "mappings": {"default": {"default": "j <PERSON> circonflexe"}}}, {"key": "0137", "mappings": {"default": {"default": "k cédille"}}}, {"key": "013A", "mappings": {"default": {"default": "l accent aigu"}}}, {"key": "013C", "mappings": {"default": {"default": "l cédille"}}}, {"key": "013E", "mappings": {"default": {"default": "l caron"}}}, {"key": "0140", "mappings": {"default": {"default": "l point médian"}}}, {"key": "0144", "mappings": {"default": {"default": "n accent aigu"}}}, {"key": "0146", "mappings": {"default": {"default": "n cédille"}}}, {"key": "0148", "mappings": {"default": {"default": "n caron"}}}, {"key": "014D", "mappings": {"default": {"default": "o macron"}}}, {"key": "014F", "mappings": {"default": {"default": "o brève"}}}, {"key": "0151", "mappings": {"default": {"default": "o avec double accent aigu"}}}, {"key": "0155", "mappings": {"default": {"default": "r accent aigu"}}}, {"key": "0157", "mappings": {"default": {"default": "r c<PERSON><PERSON><PERSON>"}}}, {"key": "0159", "mappings": {"default": {"default": "r caron"}}}, {"key": "015B", "mappings": {"default": {"default": "s accent aigu"}}}, {"key": "015D", "mappings": {"default": {"default": "s accent circonflexe"}}}, {"key": "015F", "mappings": {"default": {"default": "s cédille"}}}, {"key": "0161", "mappings": {"default": {"default": "s caron"}}}, {"key": "0163", "mappings": {"default": {"default": "t cédille"}}}, {"key": "0165", "mappings": {"default": {"default": "t caron"}}}, {"key": "0169", "mappings": {"default": {"default": "u tilde"}}}, {"key": "016B", "mappings": {"default": {"default": "u macron"}}}, {"key": "016D", "mappings": {"default": {"default": "u brève"}}}, {"key": "016F", "mappings": {"default": {"default": "u rond en chef"}}}, {"key": "0171", "mappings": {"default": {"default": "u avec double accent aigu"}}}, {"key": "0173", "mappings": {"default": {"default": "u ogonek"}}}, {"key": "0175", "mappings": {"default": {"default": "w accent circonflexe"}}}, {"key": "0177", "mappings": {"default": {"default": "y accent circonflexe"}}}, {"key": "017A", "mappings": {"default": {"default": "z accent aigu"}}}, {"key": "017C", "mappings": {"default": {"default": "z point en chef"}}}, {"key": "017E", "mappings": {"default": {"default": "z caron"}}}, {"key": "01CE", "mappings": {"default": {"default": "a caron"}}}, {"key": "01D0", "mappings": {"default": {"default": "i caron"}}}, {"key": "01D2", "mappings": {"default": {"default": "o caron"}}}, {"key": "01D4", "mappings": {"default": {"default": "u caron"}}}, {"key": "01E7", "mappings": {"default": {"default": "g caron"}}}, {"key": "01E9", "mappings": {"default": {"default": "k caron"}}}, {"key": "01EB", "mappings": {"default": {"default": "o ogonek"}}}, {"key": "01F0", "mappings": {"default": {"default": "j caron"}}}, {"key": "01F5", "mappings": {"default": {"default": "g accent aigu"}}}, {"key": "01F9", "mappings": {"default": {"default": "n grave"}}}, {"key": "0201", "mappings": {"default": {"default": "a double accent grave"}}}, {"key": "0203", "mappings": {"default": {"default": "a brève renversée"}}}, {"key": "0205", "mappings": {"default": {"default": "e double accent grave"}}}, {"key": "0207", "mappings": {"default": {"default": "e brève renversée"}}}, {"key": "0209", "mappings": {"default": {"default": "i double accent grave"}}}, {"key": "020B", "mappings": {"default": {"default": "i brève renversée"}}}, {"key": "020D", "mappings": {"default": {"default": "o double accent grave"}}}, {"key": "020F", "mappings": {"default": {"default": "o brève renversée"}}}, {"key": "0211", "mappings": {"default": {"default": "r double accent grave"}}}, {"key": "0213", "mappings": {"default": {"default": "r brève renversée"}}}, {"key": "0215", "mappings": {"default": {"default": "u double accent grave"}}}, {"key": "0217", "mappings": {"default": {"default": "u brève renversée"}}}, {"key": "0219", "mappings": {"default": {"default": "s virgule souscrite *"}}}, {"key": "021B", "mappings": {"default": {"default": "t virgule souscrite *"}}}, {"key": "021F", "mappings": {"default": {"default": "h caron"}}}, {"key": "0227", "mappings": {"default": {"default": "a point en chef"}}}, {"key": "0229", "mappings": {"default": {"default": "e cédille"}}}, {"key": "022F", "mappings": {"default": {"default": "o point en chef"}}}, {"key": "0233", "mappings": {"default": {"default": "y macron"}}}, {"key": "0237", "mappings": {"default": {"default": "j sans point"}}}, {"key": "1E01", "mappings": {"default": {"default": "a à rond souscrit"}}}, {"key": "1E03", "mappings": {"default": {"default": "b point en chef"}}}, {"key": "1E05", "mappings": {"default": {"default": "b point souscrit"}}}, {"key": "1E07", "mappings": {"default": {"default": "b ligne souscrite"}}}, {"key": "1E0B", "mappings": {"default": {"default": "d point en chef"}}}, {"key": "1E0D", "mappings": {"default": {"default": "d point souscrit"}}}, {"key": "1E0F", "mappings": {"default": {"default": "d ligne souscrite"}}}, {"key": "1E11", "mappings": {"default": {"default": "d cédille"}}}, {"key": "1E13", "mappings": {"default": {"default": "d accent circonflexe souscrit"}}}, {"key": "1E19", "mappings": {"default": {"default": "e accent circonflexe souscrit"}}}, {"key": "1E1B", "mappings": {"default": {"default": "e tilde souscrit"}}}, {"key": "1E1F", "mappings": {"default": {"default": "f point en chef"}}}, {"key": "1E21", "mappings": {"default": {"default": "g macron"}}}, {"key": "1E23", "mappings": {"default": {"default": "h point en chef"}}}, {"key": "1E25", "mappings": {"default": {"default": "h point souscrit"}}}, {"key": "1E27", "mappings": {"default": {"default": "h tréma"}}}, {"key": "1E29", "mappings": {"default": {"default": "h cédille"}}}, {"key": "1E2B", "mappings": {"default": {"default": "h brève souscrit"}}}, {"key": "1E2D", "mappings": {"default": {"default": "i tilde souscrit"}}}, {"key": "1E31", "mappings": {"default": {"default": "k accent aigu"}}}, {"key": "1E33", "mappings": {"default": {"default": "k point souscrit"}}}, {"key": "1E35", "mappings": {"default": {"default": "k ligne souscrite"}}}, {"key": "1E37", "mappings": {"default": {"default": "l point souscrit"}}}, {"key": "1E3B", "mappings": {"default": {"default": "l ligne souscrite"}}}, {"key": "1E3D", "mappings": {"default": {"default": "l accent circonflexe souscrit"}}}, {"key": "1E3F", "mappings": {"default": {"default": "m accent aigu"}}}, {"key": "1E41", "mappings": {"default": {"default": "m point en chef"}}}, {"key": "1E43", "mappings": {"default": {"default": "m point souscrit"}}}, {"key": "1E45", "mappings": {"default": {"default": "n point en chef"}}}, {"key": "1E47", "mappings": {"default": {"default": "n point souscrit"}}}, {"key": "1E49", "mappings": {"default": {"default": "n ligne souscrite"}}}, {"key": "1E4B", "mappings": {"default": {"default": "n accent circonflexe souscrit"}}}, {"key": "1E55", "mappings": {"default": {"default": "p accent aigu"}}}, {"key": "1E57", "mappings": {"default": {"default": "p point en chef"}}}, {"key": "1E59", "mappings": {"default": {"default": "r point en chef"}}}, {"key": "1E5B", "mappings": {"default": {"default": "r point souscrit"}}}, {"key": "1E5F", "mappings": {"default": {"default": "r ligne souscrite"}}}, {"key": "1E61", "mappings": {"default": {"default": "s point en chef"}}}, {"key": "1E63", "mappings": {"default": {"default": "s point souscrit"}}}, {"key": "1E6B", "mappings": {"default": {"default": "t point en chef"}}}, {"key": "1E6D", "mappings": {"default": {"default": "t point souscrit"}}}, {"key": "1E6F", "mappings": {"default": {"default": "t ligne souscrite"}}}, {"key": "1E71", "mappings": {"default": {"default": "t accent circonflexe souscrit"}}}, {"key": "1E73", "mappings": {"default": {"default": "u tréma souscrit"}}}, {"key": "1E75", "mappings": {"default": {"default": "u tilde souscrit"}}}, {"key": "1E77", "mappings": {"default": {"default": "u accent circonflexe souscrit"}}}, {"key": "1E7D", "mappings": {"default": {"default": "v tilde"}}}, {"key": "1E7F", "mappings": {"default": {"default": "v point souscrit"}}}, {"key": "1E81", "mappings": {"default": {"default": "w accent grave"}}}, {"key": "1E83", "mappings": {"default": {"default": "w accent aigu"}}}, {"key": "1E85", "mappings": {"default": {"default": "w tréma"}}}, {"key": "1E87", "mappings": {"default": {"default": "w point en chef"}}}, {"key": "1E89", "mappings": {"default": {"default": "w point souscrit"}}}, {"key": "1E8B", "mappings": {"default": {"default": "x point en chef"}}}, {"key": "1E8D", "mappings": {"default": {"default": "x tréma"}}}, {"key": "1E8F", "mappings": {"default": {"default": "y point en chef"}}}, {"key": "1E91", "mappings": {"default": {"default": "z accent circonflexe"}}}, {"key": "1E93", "mappings": {"default": {"default": "z point souscrit"}}}, {"key": "1E95", "mappings": {"default": {"default": "z ligne souscrite"}}}, {"key": "1E96", "mappings": {"default": {"default": "h ligne souscrite"}}}, {"key": "1E97", "mappings": {"default": {"default": "t tréma"}}}, {"key": "1E98", "mappings": {"default": {"default": "w rond en chef"}}}, {"key": "1E99", "mappings": {"default": {"default": "y rond en chef"}}}, {"key": "1E9A", "mappings": {"default": {"default": "a demi rond à droite"}}}, {"key": "1EA1", "mappings": {"default": {"default": "a point souscrit"}}}, {"key": "1EA3", "mappings": {"default": {"default": "a crochet en chef"}}}, {"key": "1EB9", "mappings": {"default": {"default": "e point souscrit"}}}, {"key": "1EBB", "mappings": {"default": {"default": "e crochet en chef"}}}, {"key": "1EBD", "mappings": {"default": {"default": "e tilde"}}}, {"key": "1EC9", "mappings": {"default": {"default": "i crochet en chef"}}}, {"key": "1ECB", "mappings": {"default": {"default": "i point souscrit"}}}, {"key": "1ECD", "mappings": {"default": {"default": "o point souscrit"}}}, {"key": "1ECF", "mappings": {"default": {"default": "o crochet en chef"}}}, {"key": "1EE5", "mappings": {"default": {"default": "u point souscrit"}}}, {"key": "1EE7", "mappings": {"default": {"default": "u crochet en chef"}}}, {"key": "1EF3", "mappings": {"default": {"default": "y accent grave"}}}, {"key": "1EF5", "mappings": {"default": {"default": "y point souscrit"}}}, {"key": "1EF7", "mappings": {"default": {"default": "y crochet en chef"}}}, {"key": "1EF9", "mappings": {"default": {"default": "y tilde"}}}], "fr/symbols/latin-rest.min": [{"locale": "fr"}, {"key": "210E", "mappings": {"default": {"physics": "constante de planck"}}}, {"key": "0363", "mappings": {"default": {"default": "diacritique a"}}}, {"key": "0364", "mappings": {"default": {"default": "diacritique e"}}}, {"key": "0365", "mappings": {"default": {"default": "diacritique i"}}}, {"key": "0366", "mappings": {"default": {"default": "diacritique o"}}}, {"key": "0367", "mappings": {"default": {"default": "diacritique u"}}}, {"key": "0368", "mappings": {"default": {"default": "diacritique c"}}}, {"key": "0369", "mappings": {"default": {"default": "diacritique d"}}}, {"key": "036A", "mappings": {"default": {"default": "diacritique h"}}}, {"key": "036B", "mappings": {"default": {"default": "diacritique m"}}}, {"key": "036C", "mappings": {"default": {"default": "diacritique r"}}}, {"key": "036D", "mappings": {"default": {"default": "diacritique t"}}}, {"key": "036E", "mappings": {"default": {"default": "diacritique v"}}}, {"key": "036F", "mappings": {"default": {"default": "diacritique x"}}}, {"key": "1D62", "mappings": {"default": {"default": "i souscrit"}}}, {"key": "1D63", "mappings": {"default": {"default": "r souscrit"}}}, {"key": "1D64", "mappings": {"default": {"default": "u souscrit"}}}, {"key": "1D65", "mappings": {"default": {"default": "v souscrit"}}}, {"key": "1DCA", "mappings": {"default": {"default": "diacritique r souscrite"}}}, {"key": "1DD3", "mappings": {"default": {"default": "diacritique a ouvert aplati suscrit"}}}, {"key": "1DD4", "mappings": {"default": {"default": "diacritique ae"}}}, {"key": "1DD5", "mappings": {"default": {"default": "diacritique ao"}}}, {"key": "1DD6", "mappings": {"default": {"default": "diacritique av"}}}, {"key": "1DD7", "mappings": {"default": {"default": "diacritique c cédille"}}}, {"key": "1DD8", "mappings": {"default": {"default": "diacritique d insulaire"}}}, {"key": "1DD9", "mappings": {"default": {"default": "diacritique eth"}}}, {"key": "1DDA", "mappings": {"default": {"default": "diacritique g"}}}, {"key": "1DDB", "mappings": {"default": {"default": "diacritique petite g majuscule"}}}, {"key": "1DDC", "mappings": {"default": {"default": "diacritique k"}}}, {"key": "1DDD", "mappings": {"default": {"default": "diacritique l"}}}, {"key": "1DDE", "mappings": {"default": {"default": "diacritique petite l majuscule"}}}, {"key": "1DDF", "mappings": {"default": {"default": "diacritique petite m majuscule"}}}, {"key": "1DE0", "mappings": {"default": {"default": "diacritique n"}}}, {"key": "1DE1", "mappings": {"default": {"default": "diacritique petite n majuscule"}}}, {"key": "1DE2", "mappings": {"default": {"default": "diacritique petite r majuscule"}}}, {"key": "1DE3", "mappings": {"default": {"default": "diacritique r rotunda"}}}, {"key": "1DE4", "mappings": {"default": {"default": "diacritique s"}}}, {"key": "1DE5", "mappings": {"default": {"default": "diacritique s long"}}}, {"key": "1DE6", "mappings": {"default": {"default": "diacritique z"}}}, {"key": "2071", "mappings": {"default": {"default": "exposant i"}}}, {"key": "207F", "mappings": {"default": {"default": "exposant n"}}}, {"key": "2090", "mappings": {"default": {"default": "indice a"}}}, {"key": "2091", "mappings": {"default": {"default": "indice e"}}}, {"key": "2092", "mappings": {"default": {"default": "indice o"}}}, {"key": "2093", "mappings": {"default": {"default": "indice x"}}}, {"key": "2094", "mappings": {"default": {"default": "indice schwa"}}}, {"key": "2095", "mappings": {"default": {"default": "souscrite h"}}}, {"key": "2096", "mappings": {"default": {"default": "souscrite k"}}}, {"key": "2097", "mappings": {"default": {"default": "souscrite l"}}}, {"key": "2098", "mappings": {"default": {"default": "souscrite m"}}}, {"key": "2099", "mappings": {"default": {"default": "souscrite n"}}}, {"key": "209A", "mappings": {"default": {"default": "souscrite p"}}}, {"key": "209B", "mappings": {"default": {"default": "souscrite s"}}}, {"key": "209C", "mappings": {"default": {"default": "souscrite t"}}}, {"key": "2C7C", "mappings": {"default": {"default": "j souscrite"}}}, {"key": "1F12A", "mappings": {"default": {"default": "s majuscule en écaille crochetée"}}}, {"key": "1F12B", "mappings": {"default": {"default": "c majuscule en italique cerclée"}}}, {"key": "1F12C", "mappings": {"default": {"default": "r majuscule en italique cerclée"}}}, {"key": "1F18A", "mappings": {"default": {"default": "P majuscule encadrée et en inversion avec croix"}}}], "fr/symbols/latin-upper-double-accent.min": [{"locale": "fr"}, {"key": "01D5", "mappings": {"default": {"default": "U majuscule trémaa et macron"}}}, {"key": "01D7", "mappings": {"default": {"default": "U majuscule trémaa et accent aigu"}}}, {"key": "01D9", "mappings": {"default": {"default": "U majuscule trémaa et caron"}}}, {"key": "01DB", "mappings": {"default": {"default": "U majuscule trémaa et accent grave"}}}, {"key": "01DE", "mappings": {"default": {"default": "A majuscule trémaa et macron"}}}, {"key": "01E0", "mappings": {"default": {"default": "A majuscule point en chef et macron"}}}, {"key": "01EC", "mappings": {"default": {"default": "O majuscule ogonek et macron"}}}, {"key": "01FA", "mappings": {"default": {"default": "A majuscule rond en chef et accent aigu"}}}, {"key": "022A", "mappings": {"default": {"default": "O majuscule tréma et macron"}}}, {"key": "022C", "mappings": {"default": {"default": "O majuscule tilde et macron"}}}, {"key": "0230", "mappings": {"default": {"default": "O majuscule point en chef et macron"}}}, {"key": "1E08", "mappings": {"default": {"default": "C cédille et accent aigu majuscule"}}}, {"key": "1E14", "mappings": {"default": {"default": "E macron et accent grave majuscule"}}}, {"key": "1E16", "mappings": {"default": {"default": "E macron et accent aigu majuscule"}}}, {"key": "1E1C", "mappings": {"default": {"default": "E cédille et brève majuscule"}}}, {"key": "1E2E", "mappings": {"default": {"default": "I tréma et accent aigu majuscule"}}}, {"key": "1E38", "mappings": {"default": {"default": "L point souscrit et macron majuscule"}}}, {"key": "1E4C", "mappings": {"default": {"default": "O tilde et accent aigu majuscule"}}}, {"key": "1E4E", "mappings": {"default": {"default": "O tilde et tréma majuscule"}}}, {"key": "1E50", "mappings": {"default": {"default": "O macron et accent grave majuscule"}}}, {"key": "1E52", "mappings": {"default": {"default": "O macron et accent aigu majuscule"}}}, {"key": "1E5C", "mappings": {"default": {"default": "R point souscrit et macron majuscule"}}}, {"key": "1E64", "mappings": {"default": {"default": "S accent aigu et point en chef majuscule"}}}, {"key": "1E66", "mappings": {"default": {"default": "S caron et point en chef majuscule"}}}, {"key": "1E68", "mappings": {"default": {"default": "S point souscrit et point en chef majuscule"}}}, {"key": "1E78", "mappings": {"default": {"default": "U tilde et accent aigu majuscule"}}}, {"key": "1E7A", "mappings": {"default": {"default": "U macron et tréma majuscule"}}}, {"key": "1EA4", "mappings": {"default": {"default": "A accent circonflexe et accent aigu majuscule"}}}, {"key": "1EA6", "mappings": {"default": {"default": "A accent circonflexe et accent grave majuscule"}}}, {"key": "1EA8", "mappings": {"default": {"default": "A accent circonflexe et crochet en chef majuscule"}}}, {"key": "1EAA", "mappings": {"default": {"default": "A accent circonflexe et tilde majuscule"}}}, {"key": "1EAC", "mappings": {"default": {"default": "A accent circonflexe et point souscrit majuscule"}}}, {"key": "1EAE", "mappings": {"default": {"default": "A brève et accent aigu majuscule"}}}, {"key": "1EB0", "mappings": {"default": {"default": "A brève et accent grave majuscule"}}}, {"key": "1EB2", "mappings": {"default": {"default": "A brève et crochet en chef majuscule"}}}, {"key": "1EB4", "mappings": {"default": {"default": "A brève et tilde majuscule"}}}, {"key": "1EB6", "mappings": {"default": {"default": "A brève et point souscrit majuscule"}}}, {"key": "1EBE", "mappings": {"default": {"default": "E accent circonflexe et accent aigu majuscule"}}}, {"key": "1EC0", "mappings": {"default": {"default": "E accent circonflexe et accent grave majuscule"}}}, {"key": "1EC2", "mappings": {"default": {"default": "E accent circonflexe et crochet en chef majuscule"}}}, {"key": "1EC4", "mappings": {"default": {"default": "E accent circonflexe et tilde majuscule"}}}, {"key": "1EC6", "mappings": {"default": {"default": "E accent circonflexe et point souscrit majuscule"}}}, {"key": "1ED0", "mappings": {"default": {"default": "O accent circonflexe et accent aigu majuscule"}}}, {"key": "1ED2", "mappings": {"default": {"default": "O accent circonflexe et accent grave majuscule"}}}, {"key": "1ED4", "mappings": {"default": {"default": "O accent circonflexe et crochet en chef majuscule"}}}, {"key": "1ED6", "mappings": {"default": {"default": "O accent circonflexe et tilde majuscule"}}}, {"key": "1ED8", "mappings": {"default": {"default": "O accent circonflexe et point souscrit majuscule"}}}, {"key": "1EDA", "mappings": {"default": {"default": "O cornu accent aigu majuscule"}}}, {"key": "1EDC", "mappings": {"default": {"default": "O cornu accent grave majuscule"}}}, {"key": "1EDE", "mappings": {"default": {"default": "O cornu crochet en chef majuscule"}}}, {"key": "1EE0", "mappings": {"default": {"default": "O cornu tilde majuscule"}}}, {"key": "1EE2", "mappings": {"default": {"default": "O cornu point souscrit majuscule"}}}, {"key": "1EE8", "mappings": {"default": {"default": "U cornu accent aigu majuscule"}}}, {"key": "1EEA", "mappings": {"default": {"default": "U cornu accent grave majuscule"}}}, {"key": "1EEC", "mappings": {"default": {"default": "U cornu crochet en chef majuscule"}}}, {"key": "1EEE", "mappings": {"default": {"default": "U cornu tilde majuscule"}}}, {"key": "1EF0", "mappings": {"default": {"default": "U cornu point souscrit majuscule"}}}], "fr/symbols/latin-upper-single-accent.min": [{"locale": "fr"}, {"key": "00C0", "mappings": {"default": {"default": "A accent grave majuscule"}}}, {"key": "00C1", "mappings": {"default": {"default": "A accent aigu majuscule"}}}, {"key": "00C2", "mappings": {"default": {"default": "A accent circonflexe majuscule"}}}, {"key": "00C3", "mappings": {"default": {"default": "A tilde majuscule"}}}, {"key": "00C4", "mappings": {"default": {"default": "A tréma majuscule"}}}, {"key": "00C5", "mappings": {"default": {"default": "angstrom"}}}, {"key": "00C7", "mappings": {"default": {"default": "C cédille majuscule"}}}, {"key": "00C8", "mappings": {"default": {"default": "E accent grave majuscule"}}}, {"key": "00C9", "mappings": {"default": {"default": "E accent aigu majuscule"}}}, {"key": "00CA", "mappings": {"default": {"default": "E accent circonflexe majuscule"}}}, {"key": "00CB", "mappings": {"default": {"default": "E tréma majuscule"}}}, {"key": "00CC", "mappings": {"default": {"default": "I accent grave majuscule"}}}, {"key": "00CD", "mappings": {"default": {"default": "I accent aigu majuscule"}}}, {"key": "00CE", "mappings": {"default": {"default": "I accent circonflexe majuscule"}}}, {"key": "00CF", "mappings": {"default": {"default": "I tréma majuscule"}}}, {"key": "00D1", "mappings": {"default": {"default": "N tilde majuscule"}}}, {"key": "00D2", "mappings": {"default": {"default": "O accent grave majuscule"}}}, {"key": "00D3", "mappings": {"default": {"default": "O accent aigu majuscule"}}}, {"key": "00D4", "mappings": {"default": {"default": "O accent circonflexe majuscule"}}}, {"key": "00D5", "mappings": {"default": {"default": "O tilde majuscule"}}}, {"key": "00D6", "mappings": {"default": {"default": "O tréma majuscule"}}}, {"key": "00D9", "mappings": {"default": {"default": "U accent grave majuscule"}}}, {"key": "00DA", "mappings": {"default": {"default": "U accent aigu majuscule"}}}, {"key": "00DB", "mappings": {"default": {"default": "U accent circonflexe majuscule"}}}, {"key": "00DC", "mappings": {"default": {"default": "U tréma majuscule"}}}, {"key": "00DD", "mappings": {"default": {"default": "Y accent aigu majuscule"}}}, {"key": "0100", "mappings": {"default": {"default": "A macron majuscule"}}}, {"key": "0102", "mappings": {"default": {"default": "A brève majuscule"}}}, {"key": "0104", "mappings": {"default": {"default": "A ogonek majuscule"}}}, {"key": "0106", "mappings": {"default": {"default": "C accent aigu majuscule"}}}, {"key": "0108", "mappings": {"default": {"default": "C accent circonflexe majuscule"}}}, {"key": "010A", "mappings": {"default": {"default": "C point en chef majuscule"}}}, {"key": "010C", "mappings": {"default": {"default": "C caron majuscule"}}}, {"key": "010E", "mappings": {"default": {"default": "D caron majuscule"}}}, {"key": "0112", "mappings": {"default": {"default": "E macron majuscule"}}}, {"key": "0114", "mappings": {"default": {"default": "E brève majuscule"}}}, {"key": "0116", "mappings": {"default": {"default": "E point en chef majuscule"}}}, {"key": "0118", "mappings": {"default": {"default": "E ogonek majuscule"}}}, {"key": "011A", "mappings": {"default": {"default": "E caron majuscule"}}}, {"key": "011C", "mappings": {"default": {"default": "G accent circonflexe majuscule"}}}, {"key": "011E", "mappings": {"default": {"default": "G brève majuscule"}}}, {"key": "0120", "mappings": {"default": {"default": "G point en chef majuscule"}}}, {"key": "0122", "mappings": {"default": {"default": "G cédille majuscule"}}}, {"key": "0124", "mappings": {"default": {"default": "H accent circonflexe majuscule"}}}, {"key": "0128", "mappings": {"default": {"default": "I tilde majuscule"}}}, {"key": "012A", "mappings": {"default": {"default": "I macron majuscule"}}}, {"key": "012C", "mappings": {"default": {"default": "I brève majuscule"}}}, {"key": "012E", "mappings": {"default": {"default": "I ogonek majuscule"}}}, {"key": "0130", "mappings": {"default": {"default": "I point en chef majuscule"}}}, {"key": "0134", "mappings": {"default": {"default": "J accent circonflexe majuscule"}}}, {"key": "0136", "mappings": {"default": {"default": "K cédille majuscule"}}}, {"key": "0139", "mappings": {"default": {"default": "L accent aigu majuscule"}}}, {"key": "013B", "mappings": {"default": {"default": "L cédille majuscule"}}}, {"key": "013D", "mappings": {"default": {"default": "L caron majuscule"}}}, {"key": "013F", "mappings": {"default": {"default": "L point médian majuscule"}}}, {"key": "0143", "mappings": {"default": {"default": "N accent aigu majuscule"}}}, {"key": "0145", "mappings": {"default": {"default": "N cédille majuscule"}}}, {"key": "0147", "mappings": {"default": {"default": "N caron majuscule"}}}, {"key": "014C", "mappings": {"default": {"default": "O macron majuscule"}}}, {"key": "014E", "mappings": {"default": {"default": "O brève majuscule"}}}, {"key": "0150", "mappings": {"default": {"default": "O avec double accent aigu majuscule"}}}, {"key": "0154", "mappings": {"default": {"default": "R accent aigu majuscule"}}}, {"key": "0156", "mappings": {"default": {"default": "R cédille majuscule"}}}, {"key": "0158", "mappings": {"default": {"default": "R caron majuscule"}}}, {"key": "015A", "mappings": {"default": {"default": "S accent aigu majuscule"}}}, {"key": "015C", "mappings": {"default": {"default": "S accent circonflexe majuscule"}}}, {"key": "015E", "mappings": {"default": {"default": "S cédille majuscule"}}}, {"key": "0160", "mappings": {"default": {"default": "S caron majuscule"}}}, {"key": "0162", "mappings": {"default": {"default": "T cédille majuscule"}}}, {"key": "0164", "mappings": {"default": {"default": "T caron majuscule"}}}, {"key": "0168", "mappings": {"default": {"default": "U tilde majuscule"}}}, {"key": "016A", "mappings": {"default": {"default": "U macron majuscule"}}}, {"key": "016C", "mappings": {"default": {"default": "U brève majuscule"}}}, {"key": "016E", "mappings": {"default": {"default": "U rond en chef majuscule"}}}, {"key": "0170", "mappings": {"default": {"default": "U avec double accent aigu majuscule"}}}, {"key": "0172", "mappings": {"default": {"default": "U ogonek majuscule"}}}, {"key": "0174", "mappings": {"default": {"default": "W accent circonflexe majuscule"}}}, {"key": "0176", "mappings": {"default": {"default": "Y accent circonflexe majuscule"}}}, {"key": "0178", "mappings": {"default": {"default": "Y tréma majuscule"}}}, {"key": "0179", "mappings": {"default": {"default": "Z accent aigu majuscule"}}}, {"key": "017B", "mappings": {"default": {"default": "Z point en chef majuscule"}}}, {"key": "017D", "mappings": {"default": {"default": "Z caron majuscule"}}}, {"key": "01CD", "mappings": {"default": {"default": "A caron majuscule"}}}, {"key": "01CF", "mappings": {"default": {"default": "I caron majuscule"}}}, {"key": "01D1", "mappings": {"default": {"default": "O caron majuscule"}}}, {"key": "01D3", "mappings": {"default": {"default": "U caron majuscule"}}}, {"key": "01E6", "mappings": {"default": {"default": "G caron majuscule"}}}, {"key": "01E8", "mappings": {"default": {"default": "K caron majuscule"}}}, {"key": "01EA", "mappings": {"default": {"default": "O ogonek majuscule"}}}, {"key": "01F4", "mappings": {"default": {"default": "G accent aigu majuscule"}}}, {"key": "01F8", "mappings": {"default": {"default": "N grave majuscule"}}}, {"key": "0200", "mappings": {"default": {"default": "A double accent grave majuscule"}}}, {"key": "0202", "mappings": {"default": {"default": "A brève renversée majuscule"}}}, {"key": "0204", "mappings": {"default": {"default": "E double accent grave majuscule"}}}, {"key": "0206", "mappings": {"default": {"default": "E brève renversée majuscule"}}}, {"key": "0208", "mappings": {"default": {"default": "I double accent grave majuscule"}}}, {"key": "020A", "mappings": {"default": {"default": "I brève renversée majuscule"}}}, {"key": "020C", "mappings": {"default": {"default": "O double accent grave majuscule"}}}, {"key": "020E", "mappings": {"default": {"default": "O brève renversée majuscule"}}}, {"key": "0210", "mappings": {"default": {"default": "R double accent grave majuscule"}}}, {"key": "0212", "mappings": {"default": {"default": "R brève renversée majuscule"}}}, {"key": "0214", "mappings": {"default": {"default": "U double accent grave majuscule"}}}, {"key": "0216", "mappings": {"default": {"default": "U brève renversée majuscule"}}}, {"key": "0218", "mappings": {"default": {"default": "S virgule souscrite * majuscule"}}}, {"key": "021A", "mappings": {"default": {"default": "T virgule souscrite * majuscule"}}}, {"key": "021E", "mappings": {"default": {"default": "H caron majuscule"}}}, {"key": "0226", "mappings": {"default": {"default": "A point en chef majuscule"}}}, {"key": "0228", "mappings": {"default": {"default": "E cédille majuscule"}}}, {"key": "022E", "mappings": {"default": {"default": "O point en chef majuscule"}}}, {"key": "0232", "mappings": {"default": {"default": "Y macron majuscule"}}}, {"key": "1E00", "mappings": {"default": {"default": "A à rond souscrit majuscule"}}}, {"key": "1E02", "mappings": {"default": {"default": "B point en chef majuscule"}}}, {"key": "1E04", "mappings": {"default": {"default": "B point souscrit majuscule"}}}, {"key": "1E06", "mappings": {"default": {"default": "B ligne souscrite majuscule"}}}, {"key": "1E0A", "mappings": {"default": {"default": "D point en chef majuscule"}}}, {"key": "1E0C", "mappings": {"default": {"default": "D point souscrit majuscule"}}}, {"key": "1E0E", "mappings": {"default": {"default": "D ligne souscrite majuscule"}}}, {"key": "1E10", "mappings": {"default": {"default": "D cédille majuscule"}}}, {"key": "1E12", "mappings": {"default": {"default": "D accent circonflexe souscrit majuscule"}}}, {"key": "1E18", "mappings": {"default": {"default": "E accent circonflexe souscrit majuscule"}}}, {"key": "1E1A", "mappings": {"default": {"default": "E tilde souscrit majuscule"}}}, {"key": "1E1E", "mappings": {"default": {"default": "F point en chef majuscule"}}}, {"key": "1E20", "mappings": {"default": {"default": "G macron majuscule"}}}, {"key": "1E22", "mappings": {"default": {"default": "H point en chef majuscule"}}}, {"key": "1E24", "mappings": {"default": {"default": "H point souscrit majuscule"}}}, {"key": "1E26", "mappings": {"default": {"default": "H tréma majuscule"}}}, {"key": "1E28", "mappings": {"default": {"default": "H cédille majuscule"}}}, {"key": "1E2A", "mappings": {"default": {"default": "H brève souscrit majuscule"}}}, {"key": "1E2C", "mappings": {"default": {"default": "I tilde souscrit majuscule"}}}, {"key": "1E30", "mappings": {"default": {"default": "K accent aigu majuscule"}}}, {"key": "1E32", "mappings": {"default": {"default": "K point souscrit majuscule"}}}, {"key": "1E34", "mappings": {"default": {"default": "K ligne souscrite majuscule"}}}, {"key": "1E36", "mappings": {"default": {"default": "L point souscrit majuscule"}}}, {"key": "1E3A", "mappings": {"default": {"default": "L ligne souscrite majuscule"}}}, {"key": "1E3C", "mappings": {"default": {"default": "L accent circonflexe souscrit majuscule"}}}, {"key": "1E3E", "mappings": {"default": {"default": "M accent aigu majuscule"}}}, {"key": "1E40", "mappings": {"default": {"default": "M point en chef majuscule"}}}, {"key": "1E42", "mappings": {"default": {"default": "M point souscrit majuscule"}}}, {"key": "1E44", "mappings": {"default": {"default": "N point en chef majuscule"}}}, {"key": "1E46", "mappings": {"default": {"default": "N point souscrit majuscule"}}}, {"key": "1E48", "mappings": {"default": {"default": "N ligne souscrite majuscule"}}}, {"key": "1E4A", "mappings": {"default": {"default": "N accent circonflexe souscrit majuscule"}}}, {"key": "1E54", "mappings": {"default": {"default": "P accent aigu majuscule"}}}, {"key": "1E56", "mappings": {"default": {"default": "P point en chef majuscule"}}}, {"key": "1E58", "mappings": {"default": {"default": "R point en chef majuscule"}}}, {"key": "1E5A", "mappings": {"default": {"default": "R point souscrit majuscule"}}}, {"key": "1E5E", "mappings": {"default": {"default": "R ligne souscrite majuscule"}}}, {"key": "1E60", "mappings": {"default": {"default": "S point en chef majuscule"}}}, {"key": "1E62", "mappings": {"default": {"default": "S point souscrit majuscule"}}}, {"key": "1E6A", "mappings": {"default": {"default": "T point en chef majuscule"}}}, {"key": "1E6C", "mappings": {"default": {"default": "T point souscrit majuscule"}}}, {"key": "1E6E", "mappings": {"default": {"default": "T ligne souscrite majuscule"}}}, {"key": "1E70", "mappings": {"default": {"default": "T accent circonflexe souscrit majuscule"}}}, {"key": "1E72", "mappings": {"default": {"default": "U tréma souscrit majuscule"}}}, {"key": "1E74", "mappings": {"default": {"default": "U tilde souscrit majuscule"}}}, {"key": "1E76", "mappings": {"default": {"default": "U accent circonflexe souscrit majuscule"}}}, {"key": "1E7C", "mappings": {"default": {"default": "V tilde majuscule"}}}, {"key": "1E7E", "mappings": {"default": {"default": "V point souscrit majuscule"}}}, {"key": "1E80", "mappings": {"default": {"default": "W accent grave majuscule"}}}, {"key": "1E82", "mappings": {"default": {"default": "W accent aigu majuscule"}}}, {"key": "1E84", "mappings": {"default": {"default": "W tréma majuscule"}}}, {"key": "1E86", "mappings": {"default": {"default": "W point en chef majuscule"}}}, {"key": "1E88", "mappings": {"default": {"default": "W point souscrit majuscule"}}}, {"key": "1E8A", "mappings": {"default": {"default": "X point en chef majuscule"}}}, {"key": "1E8C", "mappings": {"default": {"default": "X tréma majuscule"}}}, {"key": "1E8E", "mappings": {"default": {"default": "Y point en chef majuscule"}}}, {"key": "1E90", "mappings": {"default": {"default": "Z accent circonflexe majuscule"}}}, {"key": "1E92", "mappings": {"default": {"default": "Z point souscrit majuscule"}}}, {"key": "1E94", "mappings": {"default": {"default": "Z ligne souscrite majuscule"}}}, {"key": "1EA0", "mappings": {"default": {"default": "A point souscrit majuscule"}}}, {"key": "1EA2", "mappings": {"default": {"default": "A crochet en chef majuscule"}}}, {"key": "1EB8", "mappings": {"default": {"default": "E point souscrit majuscule"}}}, {"key": "1EBA", "mappings": {"default": {"default": "E crochet en chef majuscule"}}}, {"key": "1EBC", "mappings": {"default": {"default": "E tilde majuscule"}}}, {"key": "1EC8", "mappings": {"default": {"default": "I crochet en chef majuscule"}}}, {"key": "1ECA", "mappings": {"default": {"default": "I point souscrit majuscule"}}}, {"key": "1ECC", "mappings": {"default": {"default": "O point souscrit majuscule"}}}, {"key": "1ECE", "mappings": {"default": {"default": "O crochet en chef majuscule"}}}, {"key": "1EE4", "mappings": {"default": {"default": "U point souscrit majuscule"}}}, {"key": "1EE6", "mappings": {"default": {"default": "U crochet en chef majuscule"}}}, {"key": "1EF2", "mappings": {"default": {"default": "Y accent grave majuscule"}}}, {"key": "1EF4", "mappings": {"default": {"default": "Y point souscrit majuscule"}}}, {"key": "1EF6", "mappings": {"default": {"default": "Y crochet en chef majuscule"}}}, {"key": "1EF8", "mappings": {"default": {"default": "Y tilde majuscule"}}}], "fr/symbols/math_angles.min": [{"locale": "fr"}, {"key": "22BE", "mappings": {"default": {"default": "angle droit avec arc"}}}, {"key": "237C", "mappings": {"default": {"default": "angle droit avec flèche en zigzag vers le bas"}}}, {"key": "27C0", "mappings": {"default": {"default": "angle tridimensionnel"}}}, {"key": "299B", "mappings": {"default": {"default": "angle mesuré s'ouvrant vers la gauche"}}}, {"key": "299C", "mappings": {"default": {"default": "variante d'angle avec carré"}}}, {"key": "299D", "mappings": {"default": {"default": "angle droit mesuré pointé"}}}, {"key": "299E", "mappings": {"default": {"default": "angle avec s inscrit"}}}, {"key": "299F", "mappings": {"default": {"default": "angle aigu"}}}, {"key": "29A0", "mappings": {"default": {"default": "angle sphérique s'ouvrant vers la gauche"}}}, {"key": "29A1", "mappings": {"default": {"default": "angle sphérique s'ouvrant vers le haut"}}}, {"key": "29A2", "mappings": {"default": {"default": "angle culbuté"}}}, {"key": "29A3", "mappings": {"default": {"default": "angle réfléchi"}}}, {"key": "29A4", "mappings": {"default": {"default": "angle à barre souscrite"}}}, {"key": "29A5", "mappings": {"default": {"default": "angle à barre souscrite réfléchie"}}}, {"key": "29A6", "mappings": {"default": {"default": "angle oblique s'ouvrant vers le haut"}}}, {"key": "29A7", "mappings": {"default": {"default": "angle oblique s'ouvrant vers le bas"}}}, {"key": "29A8", "mappings": {"default": {"default": "angle calculé à bras vers l'est et bras fléché vers le nord est"}}}, {"key": "29A9", "mappings": {"default": {"default": "angle calculé à bras vers l'ouest et bras fléché vers le nord ouest"}}}, {"key": "29AA", "mappings": {"default": {"default": "angle calculé à bras vers l'est et bras fléché vers le sud est"}}}, {"key": "29AB", "mappings": {"default": {"default": "angle calculé à bras vers l'ouest et bras fléché vers le sud ouest"}}}, {"key": "29AC", "mappings": {"default": {"default": "angle calculé à bras vers le nord et bras fléché vers le nord est"}}}, {"key": "29AD", "mappings": {"default": {"default": "angle calculé à bras vers le nord et bras fléché vers le nord ouest"}}}, {"key": "29AE", "mappings": {"default": {"default": "angle calculé à bras vers le sud et bras fléché vers le sud est"}}}, {"key": "29AF", "mappings": {"default": {"default": "angle calculé à bras vers le sud et bras fléché vers le sud ouest"}}}], "fr/symbols/math_arrows.min": [{"locale": "fr"}, {"key": "2190", "mappings": {"default": {"default": "flèche gauche"}}}, {"key": "2191", "mappings": {"default": {"default": "flèche vers le haut"}}}, {"key": "2192", "mappings": {"default": {"default": "flèche droite"}}}, {"key": "2193", "mappings": {"default": {"default": "flèche vers le bas"}}}, {"key": "2194", "mappings": {"default": {"default": "flèche bilatérale"}}}, {"key": "2195", "mappings": {"default": {"default": "flèche haut et bas"}}}, {"key": "2196", "mappings": {"default": {"default": "flèche nord ouest"}}}, {"key": "2197", "mappings": {"default": {"default": "flèche nord est"}}}, {"key": "2198", "mappings": {"default": {"default": "flèche sud est"}}}, {"key": "2199", "mappings": {"default": {"default": "flèche sud ouest"}}}, {"key": "219A", "mappings": {"default": {"default": "flèche barrée gauche"}}}, {"key": "219B", "mappings": {"default": {"default": "flèche barrée droite"}}}, {"key": "219C", "mappings": {"default": {"default": "flèche ondulée gauche"}}}, {"key": "219D", "mappings": {"default": {"default": "flèche ondulée droite"}}}, {"key": "219E", "mappings": {"default": {"default": "flèche à deux pointes gauche"}}}, {"key": "219F", "mappings": {"default": {"default": "flèche à deux pointes vers le haut"}}}, {"key": "21A0", "mappings": {"default": {"default": "flèche à deux pointes droite"}}}, {"key": "21A1", "mappings": {"default": {"default": "flèche à deux pointes vers le bas"}}}, {"key": "21A2", "mappings": {"default": {"default": "flèche empennée gauche"}}}, {"key": "21A3", "mappings": {"default": {"default": "flèche empennée droite"}}}, {"key": "21A4", "mappings": {"default": {"default": "flèche d'un taquet gauche"}}}, {"key": "21A5", "mappings": {"default": {"default": "flèche d'un taquet vers le haut"}}}, {"key": "21A6", "mappings": {"default": {"default": "flèche d'un taquet droite"}}}, {"key": "21A7", "mappings": {"default": {"default": "flèche d'un taquet vers le bas"}}}, {"key": "21A8", "mappings": {"default": {"default": "flèche haut bas sur base"}}}, {"key": "21A9", "mappings": {"default": {"default": "flèche avec crochet gauche"}}}, {"key": "21AA", "mappings": {"default": {"default": "flèche avec crochet droite"}}}, {"key": "21AB", "mappings": {"default": {"default": "flèche avec boucle gauche"}}}, {"key": "21AC", "mappings": {"default": {"default": "flèche avec boucle droite"}}}, {"key": "21AD", "mappings": {"default": {"default": "flèche ondulée bilatérale"}}}, {"key": "21AE", "mappings": {"default": {"default": "flèche barrée bilatérale"}}}, {"key": "21AF", "mappings": {"default": {"default": "flèche vers le bas en zigzag"}}}, {"key": "21B0", "mappings": {"default": {"default": "flèche vers le haut avec pointe gauche"}}}, {"key": "21B1", "mappings": {"default": {"default": "flèche vers le haut avec pointe droite"}}}, {"key": "21B2", "mappings": {"default": {"default": "flèche vers le bas avec pointe gauche"}}}, {"key": "21B3", "mappings": {"default": {"default": "flèche vers le bas avec pointe droite"}}}, {"key": "21B4", "mappings": {"default": {"default": "flèche vers le bas avec coin droit"}}}, {"key": "21B5", "mappings": {"default": {"default": "flèche vers le bas avec coin gauche"}}}, {"key": "21B6", "mappings": {"default": {"default": "flèche semi circulaire en sens positif"}}}, {"key": "21B7", "mappings": {"default": {"default": "flèche semi circulaire en sens négatif"}}}, {"key": "21B8", "mappings": {"default": {"default": "flèche nord ouest vers une barre horizontale longue"}}}, {"key": "21B9", "mappings": {"default": {"default": "flèche gauche vers un taquet sur flèche droite vers un taquet"}}}, {"key": "21BA", "mappings": {"default": {"default": "flèche en sens positif à cercle ouvert"}}}, {"key": "21BB", "mappings": {"default": {"default": "flèche en sens négatif à cercle ouvert"}}}, {"key": "21C4", "mappings": {"default": {"default": "flèche vers le haut par dessus flèche gauche"}}}, {"key": "21C5", "mappings": {"default": {"default": "flèche vers le haut à gauche d'une flèche vers le bas"}}}, {"key": "21C6", "mappings": {"default": {"default": "flèche gauche par dessus flèche droite"}}}, {"key": "21C7", "mappings": {"default": {"default": "paire de flèches gauche"}}}, {"key": "21C8", "mappings": {"default": {"default": "paire de flèches vers le haut"}}}, {"key": "21C9", "mappings": {"default": {"default": "paire de flèches droite"}}}, {"key": "21CA", "mappings": {"default": {"default": "paire de flèches vers le bas"}}}, {"key": "21CD", "mappings": {"default": {"default": "double flèche barrée gauche"}}}, {"key": "21CE", "mappings": {"default": {"default": "double flèche barrée bilatérale"}}}, {"key": "21CF", "mappings": {"default": {"default": "double flèche barrée droite"}}}, {"key": "21D0", "mappings": {"default": {"default": "double flèche gauche"}}}, {"key": "21D1", "mappings": {"default": {"default": "double flèche vers le haut"}}}, {"key": "21D2", "mappings": {"default": {"default": "double flèche droite"}}}, {"key": "21D3", "mappings": {"default": {"default": "double flèche vers le bas"}}}, {"key": "21D4", "mappings": {"default": {"default": "double flèche bilatérale"}}}, {"key": "21D5", "mappings": {"default": {"default": "double flèche haut bas"}}}, {"key": "21D6", "mappings": {"default": {"default": "double flèche nord ouest"}}}, {"key": "21D7", "mappings": {"default": {"default": "double flèche nord est"}}}, {"key": "21D8", "mappings": {"default": {"default": "double flèche sud est"}}}, {"key": "21D9", "mappings": {"default": {"default": "double flèche sud ouest"}}}, {"key": "21DA", "mappings": {"default": {"default": "triple flèche gauche"}}}, {"key": "21DB", "mappings": {"default": {"default": "triple flèche droite"}}}, {"key": "21DC", "mappings": {"default": {"default": "flèche en tire bouchon gauche"}}}, {"key": "21DD", "mappings": {"default": {"default": "flèche en tire bouchon droite"}}}, {"key": "21DE", "mappings": {"default": {"default": "flèche vers le haut avec double barre"}}}, {"key": "21DF", "mappings": {"default": {"default": "flèche vers le bas avec double barre"}}}, {"key": "21E0", "mappings": {"default": {"default": "flèche pointillée gauche"}}}, {"key": "21E1", "mappings": {"default": {"default": "flèche pointillée vers le haut"}}}, {"key": "21E2", "mappings": {"default": {"default": "flèche pointillée droite"}}}, {"key": "21E3", "mappings": {"default": {"default": "flèche pointillée vers le bas"}}}, {"key": "21E4", "mappings": {"default": {"default": "flèche gauche vers un taquet"}}}, {"key": "21E5", "mappings": {"default": {"default": "flèche droite vers un taquet"}}}, {"key": "21E6", "mappings": {"default": {"default": "flèche blanche gauche"}}}, {"key": "21E7", "mappings": {"default": {"default": "flèche blanche vers le haut"}}}, {"key": "21E8", "mappings": {"default": {"default": "flèche blanche droite"}}}, {"key": "21E9", "mappings": {"default": {"default": "flèche blanche vers le bas"}}}, {"key": "21EA", "mappings": {"default": {"default": "flèche blanche vers le haut à partir d'un taquet"}}}, {"key": "21EB", "mappings": {"default": {"default": "flèche blanche vers le haut sur socle"}}}, {"key": "21EC", "mappings": {"default": {"default": "flèche blanche vers le haut sur socle et trait horizontal"}}}, {"key": "21ED", "mappings": {"default": {"default": "flèche blanche vers le haut sur socle et barre verticale"}}}, {"key": "21EE", "mappings": {"default": {"default": "double flèche blanche vers le haut"}}}, {"key": "21EF", "mappings": {"default": {"default": "double flèche blanche vers le haut sur socle"}}}, {"key": "21F0", "mappings": {"default": {"default": "flèche blanche vers la droite appuyée sur mur"}}}, {"key": "21F1", "mappings": {"default": {"default": "flèche nord ouest en encoignure"}}}, {"key": "21F2", "mappings": {"default": {"default": "flèche sud est en encoignure"}}}, {"key": "21F3", "mappings": {"default": {"default": "flèche blanche vers le haut et le bas"}}}, {"key": "21F4", "mappings": {"default": {"default": "flèche vers la droite à petit cercle"}}}, {"key": "21F5", "mappings": {"default": {"default": "flèche descendante à gauche d'une flèche montante"}}}, {"key": "21F6", "mappings": {"default": {"default": "trois flèches vers la droite"}}}, {"key": "21F7", "mappings": {"default": {"default": "flèche vers la gauche barrée verticalement"}}}, {"key": "21F8", "mappings": {"default": {"default": "flèche vers la droite barrée verticalement"}}}, {"key": "21F9", "mappings": {"default": {"default": "flèche vers la droite et la gauche barrée verticalement"}}}, {"key": "21FA", "mappings": {"default": {"default": "flèche vers la gauche barrée deux fois verticalement"}}}, {"key": "21FB", "mappings": {"default": {"default": "flèche vers la droite barrée deux fois verticalement"}}}, {"key": "21FC", "mappings": {"default": {"default": "flèche bilatérale barrée deux fois verticalement"}}}, {"key": "21FD", "mappings": {"default": {"default": "flèche à tête creuse gauche"}}}, {"key": "21FE", "mappings": {"default": {"default": "flèche à tête creuse droite"}}}, {"key": "21FF", "mappings": {"default": {"default": "flèche à tête creuse bilatérale"}}}, {"key": "2301", "mappings": {"default": {"default": "flèche électrique"}}}, {"key": "2303", "mappings": {"default": {"default": "pointe de flèche vers le haut"}}}, {"key": "2304", "mappings": {"default": {"default": "pointe de flèche vers le bas"}}}, {"key": "2324", "mappings": {"default": {"default": "pointe de flèche vers le haut entre deux traits horizontaux"}}}, {"key": "238B", "mappings": {"default": {"default": "cercle brisé à flèche nord ouest (échappement)"}}}, {"key": "2794", "mappings": {"default": {"default": "flèche grasse vers la droite à pointe large"}}}, {"key": "2798", "mappings": {"default": {"default": "flèche grasse sud est"}}}, {"key": "2799", "mappings": {"default": {"default": "flèche grasse vers la droite grasse"}}}, {"key": "279A", "mappings": {"default": {"default": "flèche grasse nord est"}}}, {"key": "279B", "mappings": {"default": {"default": "flèche de traçage vers la droite"}}}, {"key": "279C", "mappings": {"default": {"default": "flèche grasse à pointe arrondie vers la droite"}}}, {"key": "279D", "mappings": {"default": {"default": "flèche à pointe en triangle vers la droite"}}}, {"key": "279E", "mappings": {"default": {"default": "flèche grasse à pointe en triangle vers la droite"}}}, {"key": "279F", "mappings": {"default": {"default": "flèche avec pointillés à pointe en triangle vers la droite"}}}, {"key": "27A0", "mappings": {"default": {"default": "flèche grasse avec pointillés à pointe en triangle vers la droite"}}}, {"key": "27A1", "mappings": {"default": {"default": "flèche noire vers la droite"}}}, {"key": "27A2", "mappings": {"default": {"default": "pointe de flèche vers la droite en trompe l'œil éclairée par le haut"}}}, {"key": "27A3", "mappings": {"default": {"default": "pointe de flèche vers la droite en trompe l'œil éclairée par le bas"}}}, {"key": "27A4", "mappings": {"default": {"default": "pointe de flèche noire vers la droite"}}}, {"key": "27A5", "mappings": {"default": {"default": "flèche noire grasse courbée vers le bas et vers la droite"}}}, {"key": "27A6", "mappings": {"default": {"default": "flèche noire courbée vers le haut et vers la droite"}}}, {"key": "27A7", "mappings": {"default": {"default": "flèche noire trapue vers la droite"}}}, {"key": "27A8", "mappings": {"default": {"default": "flèche noire grasse à pointe concave vers la droite"}}}, {"key": "27A9", "mappings": {"default": {"default": "flèche blanche vers la droite ombrée à l'avant"}}}, {"key": "27AA", "mappings": {"default": {"default": "flèche blanche vers la droite ombrée à l'arrière"}}}, {"key": "27AB", "mappings": {"default": {"default": "flèche blanche vers la droite penchée vers l'arrière et ombrée"}}}, {"key": "27AC", "mappings": {"default": {"default": "flèche blanche vers la droite penchée vers l'avant et ombrée"}}}, {"key": "27AD", "mappings": {"default": {"default": "flèche blanche grasse vers la droite à ombre inférieure droite"}}}, {"key": "27AE", "mappings": {"default": {"default": "flèche blanche grasse vers la droite à ombre supérieure droite"}}}, {"key": "27AF", "mappings": {"default": {"default": "flèche encochée blanche vers la droite ombrée à l'avant et par dessous"}}}, {"key": "27B1", "mappings": {"default": {"default": "flèche encochée blanche vers la droite ombrée à l'avant et par dessus"}}}, {"key": "27B2", "mappings": {"default": {"default": "flèche blanche grasse vers la droite à contour circulaire"}}}, {"key": "27B3", "mappings": {"default": {"default": "flèche empennée blanche vers la droite"}}}, {"key": "27B4", "mappings": {"default": {"default": "flèche empennée noir sud est"}}}, {"key": "27B5", "mappings": {"default": {"default": "flèche empennée noir vers la droite"}}}, {"key": "27B6", "mappings": {"default": {"default": "flèche empennée noir nord est"}}}, {"key": "27B7", "mappings": {"default": {"default": "flèche empennée gras noire sud est"}}}, {"key": "27B8", "mappings": {"default": {"default": "flèche empennée gras noire vers la droite"}}}, {"key": "27B9", "mappings": {"default": {"default": "flèche empennée gras noire nord est"}}}, {"key": "27BA", "mappings": {"default": {"default": "flèche vers la droite à pointe larmée"}}}, {"key": "27BB", "mappings": {"default": {"default": "flèche vers la droite à hampe larmée"}}}, {"key": "27BC", "mappings": {"default": {"default": "flèche vers la droite à queue en forme de coin"}}}, {"key": "27BD", "mappings": {"default": {"default": "flèche grasse vers la droite à queue en forme de coin"}}}, {"key": "27BE", "mappings": {"default": {"default": "flèche vers la droite à contour ouvert"}}}, {"key": "27F0", "mappings": {"default": {"default": "quadruple flèche vers le haut"}}}, {"key": "27F1", "mappings": {"default": {"default": "quadruple flèche vers le bas"}}}, {"key": "27F2", "mappings": {"default": {"default": "flèche sens antihoraire entrouverte"}}}, {"key": "27F3", "mappings": {"default": {"default": "flèche sens horaire entrouverte"}}}, {"key": "27F4", "mappings": {"default": {"default": "flèche vers la droite à signe plus cerclé"}}}, {"key": "27F5", "mappings": {"default": {"default": "longue flèche gauche"}}}, {"key": "27F6", "mappings": {"default": {"default": "longue flèche droite"}}}, {"key": "27F7", "mappings": {"default": {"default": "longue flèche bilatérale"}}}, {"key": "27F8", "mappings": {"default": {"default": "longue double flèche gauche"}}}, {"key": "27F9", "mappings": {"default": {"default": "longue double flèche droite"}}}, {"key": "27FA", "mappings": {"default": {"default": "longue double flèche bilatérale"}}}, {"key": "27FB", "mappings": {"default": {"default": "longue flèche d'un taquet vers la gauche"}}}, {"key": "27FC", "mappings": {"default": {"default": "longue flèche d'un taquet droite"}}}, {"key": "27FD", "mappings": {"default": {"default": "longue double flèche d'un taquet vers la gauche"}}}, {"key": "27FE", "mappings": {"default": {"default": "longue double flèche d'un taquet vers la droite"}}}, {"key": "27FF", "mappings": {"default": {"default": "longue flèche en tire bouchon droite"}}}, {"key": "2900", "mappings": {"default": {"default": "flèche à deux pointes vers la droite barrée verticalement"}}}, {"key": "2901", "mappings": {"default": {"default": "flèche à deux pointes vers la droite barrée deux fois verticalement"}}}, {"key": "2902", "mappings": {"default": {"default": "double flèche vers la gauche barrée verticalement"}}}, {"key": "2903", "mappings": {"default": {"default": "double flèche vers la droite barrée verticalement"}}}, {"key": "2904", "mappings": {"default": {"default": "flèche bilatérale double barrée verticalement"}}}, {"key": "2905", "mappings": {"default": {"default": "flèche à deux têtes d'un taquet droite"}}}, {"key": "2906", "mappings": {"default": {"default": "double flèche d'un taquet vers la gauche"}}}, {"key": "2907", "mappings": {"default": {"default": "double flèche d'un taquet vers la droite"}}}, {"key": "2908", "mappings": {"default": {"default": "flèche vers le bas barrée horizontalement"}}}, {"key": "2909", "mappings": {"default": {"default": "flèche vers le haut barrée horizontalement"}}}, {"key": "290A", "mappings": {"default": {"default": "triple flèche vers le haut"}}}, {"key": "290B", "mappings": {"default": {"default": "triple flèche vers le bas"}}}, {"key": "290C", "mappings": {"default": {"default": "flèche à deux traits gauche"}}}, {"key": "290D", "mappings": {"default": {"default": "flèche à deux traits droite"}}}, {"key": "290E", "mappings": {"default": {"default": "flèche à trois traits gauche"}}}, {"key": "290F", "mappings": {"default": {"default": "flèche à trois traits droite"}}}, {"key": "2910", "mappings": {"default": {"default": "flèche à trois traits et à deux têtes droite"}}}, {"key": "2911", "mappings": {"default": {"default": "flèche à fût pointillé droite"}}}, {"key": "2912", "mappings": {"default": {"default": "flèche jusqu'à taquet vers le haut"}}}, {"key": "2913", "mappings": {"default": {"default": "flèche jusqu'à taquet vers le bas"}}}, {"key": "2914", "mappings": {"default": {"default": "flèche empennée vers la droite barrée verticalement"}}}, {"key": "2915", "mappings": {"default": {"default": "flèche empennée vers la droite barrée deux fois verticalement"}}}, {"key": "2916", "mappings": {"default": {"default": "flèche empennée à deux têtes droite"}}}, {"key": "2917", "mappings": {"default": {"default": "flèche empennée à deux têtes vers la droite barrée verticalement"}}}, {"key": "2918", "mappings": {"default": {"default": "flèche empennée à deux têtes vers la droite barrée deux fois verticalement"}}}, {"key": "2919", "mappings": {"default": {"default": "empenne gauche"}}}, {"key": "291A", "mappings": {"default": {"default": "empenne vers la droite"}}}, {"key": "291B", "mappings": {"default": {"default": "double empenne gauche"}}}, {"key": "291C", "mappings": {"default": {"default": "double empenne droite"}}}, {"key": "291D", "mappings": {"default": {"default": "flèche vers un losange noir à gauche"}}}, {"key": "291E", "mappings": {"default": {"default": "flèche vers un losange noir à droite"}}}, {"key": "291F", "mappings": {"default": {"default": "flèche d'un taquet vers un losange noir à gauche"}}}, {"key": "2920", "mappings": {"default": {"default": "flèche d'un taquet vers un losange noir à droite"}}}, {"key": "2921", "mappings": {"default": {"default": "flèche nord ouest et sud est"}}}, {"key": "2922", "mappings": {"default": {"default": "flèche nord est et sud ouest"}}}, {"key": "2923", "mappings": {"default": {"default": "flèche nord ouest à crochet"}}}, {"key": "2924", "mappings": {"default": {"default": "flèche nord est à crochet"}}}, {"key": "2925", "mappings": {"default": {"default": "flèche sud est à crochet"}}}, {"key": "2926", "mappings": {"default": {"default": "flèche sud ouest à crochet"}}}, {"key": "2927", "mappings": {"default": {"default": "flèches nord ouest et nord est"}}}, {"key": "2928", "mappings": {"default": {"default": "flèches nord est et sud est"}}}, {"key": "2929", "mappings": {"default": {"default": "flèches sud est et sud ouest"}}}, {"key": "292A", "mappings": {"default": {"default": "flèches sud ouest et nord ouest"}}}, {"key": "292D", "mappings": {"default": {"default": "flèche sud est sur flèche nord est"}}}, {"key": "292E", "mappings": {"default": {"default": "flèche nord est sur flèche sud est"}}}, {"key": "292F", "mappings": {"default": {"default": "diagonale descendante sur flèche nord est"}}}, {"key": "2930", "mappings": {"default": {"default": "diagonale montante sur flèche sud est"}}}, {"key": "2931", "mappings": {"default": {"default": "flèche nord est sur flèche nord ouest"}}}, {"key": "2932", "mappings": {"default": {"default": "flèche nord ouest sur flèche nord est"}}}, {"key": "2933", "mappings": {"default": {"default": "flèche bossue droite"}}}, {"key": "2934", "mappings": {"default": {"default": "flèche courbe vers la droite puis le haut"}}}, {"key": "2935", "mappings": {"default": {"default": "flèche courbe vers la droite puis le bas"}}}, {"key": "2936", "mappings": {"default": {"default": "flèche courbe vers le bas puis la gauche"}}}, {"key": "2937", "mappings": {"default": {"default": "flèche courbe vers le bas puis la droite"}}}, {"key": "2938", "mappings": {"default": {"default": "côté droit d'arc fléché sens horaire"}}}, {"key": "2939", "mappings": {"default": {"default": "côté gauche d'arc fléché sens antihoraire"}}}, {"key": "293A", "mappings": {"default": {"default": "arc supérieur fléché sens antihoraire"}}}, {"key": "293B", "mappings": {"default": {"default": "arc inférieur fléché sens antihoraire"}}}, {"key": "293C", "mappings": {"default": {"default": "arc fléché sens horaire et signe moins souscrit"}}}, {"key": "293D", "mappings": {"default": {"default": "arc fléché sens antihoraire et signe plus souscrit"}}}, {"key": "293E", "mappings": {"default": {"default": "demi cercle fléché inférieur droit sens horaire"}}}, {"key": "293F", "mappings": {"default": {"default": "demi cercle fléché inférieur gauche sens antihoraire"}}}, {"key": "2940", "mappings": {"default": {"default": "cercle fléché sens antihoraire"}}}, {"key": "2941", "mappings": {"default": {"default": "cercle fléché sens horaire"}}}, {"key": "2942", "mappings": {"default": {"default": "flèche vers la droite surmontant fléchette vers la gauche"}}}, {"key": "2943", "mappings": {"default": {"default": "flèche vers la gauche surmontant fléchette vers la droite"}}}, {"key": "2944", "mappings": {"default": {"default": "fléchette vers la droite surmontant flèche vers la gauche"}}}, {"key": "2945", "mappings": {"default": {"default": "flèche droite surmontant signe moins"}}}, {"key": "2946", "mappings": {"default": {"default": "flèche vers la gauche surmontant signe plus"}}}, {"key": "2947", "mappings": {"default": {"default": "flèche vers la droite au travers un x"}}}, {"key": "2948", "mappings": {"default": {"default": "petit cercle traversé d'une flèche bilatérale"}}}, {"key": "2949", "mappings": {"default": {"default": "petit cercle surmonté d'une flèche montante à deux têtes"}}}, {"key": "2970", "mappings": {"default": {"default": "flèche en épingle à cheveu droite"}}}, {"key": "2971", "mappings": {"default": {"default": "flèche droite surmontée d'un égal"}}}, {"key": "2972", "mappings": {"default": {"default": "flèche droite surmontée d'un tilde"}}}, {"key": "2973", "mappings": {"default": {"default": "tilde surmonté d'une flèche gauche"}}}, {"key": "2974", "mappings": {"default": {"default": "tilde surmonté d'une flèche droite"}}}, {"key": "2975", "mappings": {"default": {"default": "presque égal à surmonté d'une flèche droite"}}}, {"key": "2976", "mappings": {"default": {"default": "inférieur à surmonté d'une flèche gauche"}}}, {"key": "2977", "mappings": {"default": {"default": "flèche vers la gauche à travers un inférieur à"}}}, {"key": "2978", "mappings": {"default": {"default": "flèche droite surmontée d'un supérieur à"}}}, {"key": "2979", "mappings": {"default": {"default": "flèche droite surmontée d'un sous ensemble de"}}}, {"key": "297A", "mappings": {"default": {"default": "flèche vers la gauche au travers d'un sous ensemble de"}}}, {"key": "297B", "mappings": {"default": {"default": "flèche gauche surmontée d'un sur ensemble de"}}}, {"key": "29B3", "mappings": {"default": {"default": "ensemble vide flèche droite en chef"}}}, {"key": "29B4", "mappings": {"default": {"default": "ensemble vide flèche gauche en chef"}}}, {"key": "29BD", "mappings": {"default": {"default": "cercle traversé d'une flèche montante"}}}, {"key": "29EA", "mappings": {"default": {"default": "losange noir à flèche vers le bas"}}}, {"key": "29EC", "mappings": {"default": {"default": "cercle blanc à flèche vers le bas"}}}, {"key": "29ED", "mappings": {"default": {"default": "cercle noir à flèche vers le bas"}}}, {"key": "2A17", "mappings": {"default": {"default": "intégrale à crosse fléchée gauche"}}}, {"key": "2B00", "mappings": {"default": {"default": "flèche blanche nord est"}}}, {"key": "2B01", "mappings": {"default": {"default": "flèche blanche nord ouest"}}}, {"key": "2B02", "mappings": {"default": {"default": "flèche blanche sud est"}}}, {"key": "2B03", "mappings": {"default": {"default": "flèche blanche sud ouest"}}}, {"key": "2B04", "mappings": {"default": {"default": "flèche blanche bilatérale"}}}, {"key": "2B05", "mappings": {"default": {"default": "flèche noire vers la gauche"}}}, {"key": "2B06", "mappings": {"default": {"default": "flèche noire vers le haut"}}}, {"key": "2B07", "mappings": {"default": {"default": "flèche noire vers le bas"}}}, {"key": "2B08", "mappings": {"default": {"default": "flèche noire nord est"}}}, {"key": "2B09", "mappings": {"default": {"default": "flèche noire nord ouest"}}}, {"key": "2B0A", "mappings": {"default": {"default": "flèche noire sud est"}}}, {"key": "2B0B", "mappings": {"default": {"default": "flèche noire sud ouest"}}}, {"key": "2B0C", "mappings": {"default": {"default": "flèche noire bilatérale"}}}, {"key": "2B0D", "mappings": {"default": {"default": "flèche noire haut et bas"}}}, {"key": "2B0E", "mappings": {"default": {"default": "flèche vers la droite à pointe pliée vers le bas"}}}, {"key": "2B0F", "mappings": {"default": {"default": "flèche vers la droite à pointe pliée vers le haut"}}}, {"key": "2B10", "mappings": {"default": {"default": "flèche vers la gauche à pointe pliée vers le bas"}}}, {"key": "2B11", "mappings": {"default": {"default": "flèche vers la gauche à pointe pliée vers le haut"}}}, {"key": "2B30", "mappings": {"default": {"default": "flèche vers la gauche à petit cercle"}}}, {"key": "2B31", "mappings": {"default": {"default": "trois flèches vers la gauche"}}}, {"key": "2B32", "mappings": {"default": {"default": "flèche vers la gauche à signe plus cerclé"}}}, {"key": "2B33", "mappings": {"default": {"default": "longue flèche vers la gauche en tire bouchon"}}}, {"key": "2B34", "mappings": {"default": {"default": "flèche à deux pointes vers la gauche barrée verticalement"}}}, {"key": "2B35", "mappings": {"default": {"default": "flèche à deux pointes vers la gauche barrée deux fois verticalement"}}}, {"key": "2B36", "mappings": {"default": {"default": "flèche à deux têtes d'un taquet vers la gauche"}}}, {"key": "2B37", "mappings": {"default": {"default": "flèche à trois traits et à deux têtes vers la gauche"}}}, {"key": "2B38", "mappings": {"default": {"default": "flèche vers la gauche à fût pointillé"}}}, {"key": "2B39", "mappings": {"default": {"default": "flèche empennée vers la gauche barrée verticalement"}}}, {"key": "2B3A", "mappings": {"default": {"default": "flèche empennée vers la gauche barrée deux fois verticalement"}}}, {"key": "2B3B", "mappings": {"default": {"default": "flèche empennée à deux têtes vers la gauche"}}}, {"key": "2B3C", "mappings": {"default": {"default": "flèche empennée à deux têtes vers la gauche barrée verticalement"}}}, {"key": "2B3D", "mappings": {"default": {"default": "flèche empennée à deux têtes vers la gauche barrée deux fois verticalement"}}}, {"key": "2B3E", "mappings": {"default": {"default": "flèche vers la gauche au travers un x"}}}, {"key": "2B3F", "mappings": {"default": {"default": "flèche vers la gauche bossue"}}}, {"key": "2B40", "mappings": {"default": {"default": "flèche vers la gauche surmontée d'un égal"}}}, {"key": "2B41", "mappings": {"default": {"default": "flèche vers la gauche surmontée d'un tilde"}}}, {"key": "2B42", "mappings": {"default": {"default": "presque égal à réfléchi surmonté d'une flèche vers la gauche"}}}, {"key": "2B43", "mappings": {"default": {"default": "flèche vers la droite à travers un inférieur à"}}}, {"key": "2B44", "mappings": {"default": {"default": "flèche vers la droite au travers d'un sous ensemble de"}}}, {"key": "2B45", "mappings": {"default": {"default": "quadruple flèche vers la gauche"}}}, {"key": "2B46", "mappings": {"default": {"default": "quadruple flèche vers la droite"}}}, {"key": "2B47", "mappings": {"default": {"default": "flèche vers la droite surmontée d'un opérateur tilde réfléchi"}}}, {"key": "2B48", "mappings": {"default": {"default": "presque égal à réfléchi surmonté d'une flèche vers la droite"}}}, {"key": "2B49", "mappings": {"default": {"default": "flèche vers la gauche surmontée de l'opérateur tilde"}}}, {"key": "2B4A", "mappings": {"default": {"default": "flèche vers la gauche par dessus presque égal à"}}}, {"key": "2B4B", "mappings": {"default": {"default": "tilde réf<PERSON>chi surmonté d'une flèche vers la gauche"}}}, {"key": "2B4C", "mappings": {"default": {"default": "ilde surmonté d'une flèche vers la droite"}}}, {"key": "FFE9", "mappings": {"default": {"default": "flèche vers la gauche demi chasse"}}}, {"key": "FFEA", "mappings": {"default": {"default": "flèche vers le haut demi chasse"}}}, {"key": "FFEB", "mappings": {"default": {"default": "flèche vers la droite demi chasse"}}}, {"key": "FFEC", "mappings": {"default": {"default": "flèche vers le bas demi chasse"}}}], "fr/symbols/math_characters.min": [{"locale": "fr"}, {"key": "2113", "mappings": {"default": {"default": "l minuscule en script"}}}, {"key": "2118", "mappings": {"default": {"default": "fonction elliptique de weierstrass"}}}, {"key": "213C", "mappings": {"default": {"default": "pi a<PERSON><PERSON>"}}}, {"key": "213D", "mappings": {"default": {"default": "gamma ajouré"}}}, {"key": "213E", "mappings": {"default": {"default": "Gamma majuscule ajouré"}}}, {"key": "213F", "mappings": {"default": {"default": "Pi majuscule ajouré"}}}, {"key": "2140", "mappings": {"default": {"default": "sommation de la famille ajouré"}}}, {"key": "2145", "mappings": {"default": {"default": "D majuscule"}}}, {"key": "2146", "mappings": {"default": {"default": "d ajouré en italique"}}}, {"key": "2147", "mappings": {"default": {"default": "e ajouré en italique"}}}, {"key": "2148", "mappings": {"default": {"default": "i ajouré en italique"}}}, {"key": "2149", "mappings": {"default": {"default": "j ajouré en italique"}}}, {"key": "1D6A4", "mappings": {"default": {"default": "dotless i"}}}, {"key": "1D6A5", "mappings": {"default": {"default": "dotless j"}}}], "fr/symbols/math_delimiters.min": [{"locale": "fr"}, {"key": "0028", "mappings": {"default": {"default": "parenthèse gauche", "alternative": "parenthèse ouvrante"}}}, {"key": "0029", "mappings": {"default": {"default": "parenthèse droite", "alternative": "parenthèse fermante"}}}, {"key": "005B", "mappings": {"default": {"default": "crochet gauche", "alternative": "crochet ouvrant"}}}, {"key": "005D", "mappings": {"default": {"default": "crochet droit", "alternative": "crochet fermant"}}}, {"key": "007B", "mappings": {"default": {"default": "accolade gauche", "alternative": "accolade ouvrante"}}}, {"key": "007D", "mappings": {"default": {"default": "accolade droite", "alternative": "accolade fermante"}}}, {"key": "2045", "mappings": {"default": {"default": "crochet gauche avec pique"}}}, {"key": "2046", "mappings": {"default": {"default": "crochet droit avec pique"}}}, {"key": "2308", "mappings": {"default": {"default": "plafond à gauche"}}}, {"key": "2309", "mappings": {"default": {"default": "plafond à droite"}}}, {"key": "230A", "mappings": {"default": {"default": "plancher à gauche"}}}, {"key": "230B", "mappings": {"default": {"default": "plancher à droite"}}}, {"key": "230C", "mappings": {"default": {"default": "repère sud est"}}}, {"key": "230D", "mappings": {"default": {"default": "repère sud ouest"}}}, {"key": "230E", "mappings": {"default": {"default": "repère nord est"}}}, {"key": "230F", "mappings": {"default": {"default": "repère nord ouest"}}}, {"key": "231C", "mappings": {"default": {"default": "coin nord ouest"}}}, {"key": "231D", "mappings": {"default": {"default": "coin nord est"}}}, {"key": "231E", "mappings": {"default": {"default": "coin sud ouest"}}}, {"key": "231F", "mappings": {"default": {"default": "coin sud est"}}}, {"key": "2320", "mappings": {"default": {"default": "moitié supérieure d'intégrale"}}}, {"key": "2321", "mappings": {"default": {"default": "moitié inférieure d'intégrale"}}}, {"key": "2329", "mappings": {"default": {"default": "chevron gauche"}}}, {"key": "232A", "mappings": {"default": {"default": "chevron droite"}}}, {"key": "239B", "mappings": {"default": {"default": "arc supérieur de parenthèse gauche"}}}, {"key": "239C", "mappings": {"default": {"default": "rallonge de parenthèse gauche"}}}, {"key": "239D", "mappings": {"default": {"default": "arc inférieur de parenthèse gauche"}}}, {"key": "239E", "mappings": {"default": {"default": "arc supérieur de parenthèse droite"}}}, {"key": "239F", "mappings": {"default": {"default": "rallonge de parenthèse droite"}}}, {"key": "23A0", "mappings": {"default": {"default": "arc inférieur de parenthèse droite"}}}, {"key": "23A1", "mappings": {"default": {"default": "coin supérieur de crochet gauche"}}}, {"key": "23A2", "mappings": {"default": {"default": "rallonge de crochet gauche"}}}, {"key": "23A3", "mappings": {"default": {"default": "coin inférieur de crochet gauche"}}}, {"key": "23A4", "mappings": {"default": {"default": "coin supérieur de crochet droit"}}}, {"key": "23A5", "mappings": {"default": {"default": "rallonge de crochet droit"}}}, {"key": "23A6", "mappings": {"default": {"default": "coin inférieur de crochet droit"}}}, {"key": "23A7", "mappings": {"default": {"default": "arc supérieur d'accolade gauche"}}}, {"key": "23A8", "mappings": {"default": {"default": "milieu d'accolade gauche"}}}, {"key": "23A9", "mappings": {"default": {"default": "arc inférieur d'accolade gauche"}}}, {"key": "23AA", "mappings": {"default": {"default": "rallonge d'accolade"}}}, {"key": "23AB", "mappings": {"default": {"default": "arc supérieur d'accolade droite"}}}, {"key": "23AC", "mappings": {"default": {"default": "milieu d'accolade droite"}}}, {"key": "23AD", "mappings": {"default": {"default": "arc inférieur d'accolade droite"}}}, {"key": "23AE", "mappings": {"default": {"default": "rallonge d'intégrale"}}}, {"key": "23AF", "mappings": {"default": {"default": "rallonge de ligne horizontale"}}}, {"key": "23B0", "mappings": {"default": {"default": "moitié supérieure gauche ou inférieure droite d'accolade"}}}, {"key": "23B1", "mappings": {"default": {"default": "moitié supérieure droite ou inférieure gauche d'accolade"}}}, {"key": "23B2", "mappings": {"default": {"default": "haut du signe de somme"}}}, {"key": "23B3", "mappings": {"default": {"default": "bas du signe de somme"}}}, {"key": "23B4", "mappings": {"default": {"default": "crochet vertical supérieur"}}}, {"key": "23B5", "mappings": {"default": {"default": "crochet vertical inférieur"}}}, {"key": "23B6", "mappings": {"default": {"default": "crochet verticaux en défilé"}}}, {"key": "23B7", "mappings": {"default": {"default": "bas du radical"}}}, {"key": "23B8", "mappings": {"default": {"default": "filet vertical gauche"}}}, {"key": "23B9", "mappings": {"default": {"default": "filet vertical droit"}}}, {"key": "23DC", "mappings": {"default": {"default": "parenthèse supérieure"}}}, {"key": "23DD", "mappings": {"default": {"default": "parenthèse inférieure"}}}, {"key": "23DE", "mappings": {"default": {"default": "accolade supérieure"}}}, {"key": "23DF", "mappings": {"default": {"default": "accolade inférieure"}}}, {"key": "23E0", "mappings": {"default": {"default": "crochet en écaille supérieur"}}}, {"key": "23E1", "mappings": {"default": {"default": "crochet en écaille inférieur"}}}, {"key": "2768", "mappings": {"default": {"default": "parenthèse de fantaisie gauche demi grasse"}}}, {"key": "2769", "mappings": {"default": {"default": "parenthèse de fantaisie droite demi grasse"}}}, {"key": "276A", "mappings": {"default": {"default": "parenthèse de fantaisie gauche demi grasse aplatie"}}}, {"key": "276B", "mappings": {"default": {"default": "parenthèse de fantaisie droite demi grasse aplatie"}}}, {"key": "276C", "mappings": {"default": {"default": "chevron de fantaisie vers la gauche demi gras"}}}, {"key": "276D", "mappings": {"default": {"default": "chevron de fantaisie vers la droite demi gras"}}}, {"key": "276E", "mappings": {"default": {"default": "guillem<PERSON> de fantaisie vers la gauche gras"}}}, {"key": "276F", "mappings": {"default": {"default": "guillem<PERSON> de fantaisie vers la droite gras"}}}, {"key": "2770", "mappings": {"default": {"default": "crochet de fantaisie vers la gauche gras"}}}, {"key": "2771", "mappings": {"default": {"default": "crochet de fantaisie vers la droite gras"}}}, {"key": "2772", "mappings": {"default": {"default": "crochet de fantaisie gauche maigre en écaille"}}}, {"key": "2773", "mappings": {"default": {"default": "crochet de fantaisie droit maigre en écaille"}}}, {"key": "2774", "mappings": {"default": {"default": "accolade de fantaisie gauche moyenne"}}}, {"key": "2775", "mappings": {"default": {"default": "accolade de fantaisie droite moyenne"}}}, {"key": "27C5", "mappings": {"default": {"default": "délimiteur de sac en s gauche"}}}, {"key": "27C6", "mappings": {"default": {"default": "délimiteur de sac en s droite"}}}, {"key": "27E6", "mappings": {"default": {"default": "crochet blanc gauche"}}}, {"key": "27E7", "mappings": {"default": {"default": "crochet blanc droit"}}}, {"key": "27E8", "mappings": {"default": {"default": "chevron mathématique gauche"}}}, {"key": "27E9", "mappings": {"default": {"default": "chevron droit"}}}, {"key": "27EA", "mappings": {"default": {"default": "double chevron gauche"}}}, {"key": "27EB", "mappings": {"default": {"default": "double chevron droit"}}}, {"key": "27EC", "mappings": {"default": {"default": "crochet gauche en écaille blanche"}}}, {"key": "27ED", "mappings": {"default": {"default": "crochet droit en écaille blanche"}}}, {"key": "27EE", "mappings": {"default": {"default": "parenthèse gauche aplatie"}}}, {"key": "27EF", "mappings": {"default": {"default": "parenthèse droite aplatie"}}}, {"key": "2983", "mappings": {"default": {"default": "accolade gauche ajourée"}}}, {"key": "2984", "mappings": {"default": {"default": "accolade droite a<PERSON>e"}}}, {"key": "2985", "mappings": {"default": {"default": "parenthèse gauche ajourée"}}}, {"key": "2986", "mappings": {"default": {"default": "parenthèse droite ajourée"}}}, {"key": "2987", "mappings": {"default": {"default": "parenthèse image de gauche en notation z"}}}, {"key": "2988", "mappings": {"default": {"default": "parenthèse image de droite en notation z"}}}, {"key": "2989", "mappings": {"default": {"default": "z notation left binding bracket"}}}, {"key": "298A", "mappings": {"default": {"default": "z notation right binding bracket"}}}, {"key": "298B", "mappings": {"default": {"default": "crochet gauche avec barre souscrite"}}}, {"key": "298C", "mappings": {"default": {"default": "crochet droit avec barre souscrite"}}}, {"key": "298D", "mappings": {"default": {"default": "crochet gauche avec trait dans le coin supérieur"}}}, {"key": "298E", "mappings": {"default": {"default": "crochet droit avec trait dans le coin inférieur"}}}, {"key": "298F", "mappings": {"default": {"default": "crochet gauche avec trait dans le coin inférieur"}}}, {"key": "2990", "mappings": {"default": {"default": "crochet droit avec trait dans le coin supérieur"}}}, {"key": "2991", "mappings": {"default": {"default": "chevron gauche pointé"}}}, {"key": "2992", "mappings": {"default": {"default": "chevron droit pointé"}}}, {"key": "2993", "mappings": {"default": {"default": "parenthèse arc gauche et inférieur à"}}}, {"key": "2994", "mappings": {"default": {"default": "parenthèse arc droite et supérieur à"}}}, {"key": "2995", "mappings": {"default": {"default": "parenthèse double arc droite et supérieur à"}}}, {"key": "2996", "mappings": {"default": {"default": "parenthèse double arc gauche et inférieur à"}}}, {"key": "2997", "mappings": {"default": {"default": "crochet noir gauche en écaille"}}}, {"key": "2998", "mappings": {"default": {"default": "crochet noir droite en écaille"}}}, {"key": "29D8", "mappings": {"default": {"default": "clôture dentelée gauche"}}}, {"key": "29D9", "mappings": {"default": {"default": "clôture dentelée droite"}}}, {"key": "29DA", "mappings": {"default": {"default": "double clôture dentelée gauche"}}}, {"key": "29DB", "mappings": {"default": {"default": "right double wiggly fence"}}}, {"key": "29FC", "mappings": {"default": {"default": "chevron vers la gauche courbé"}}}, {"key": "29FD", "mappings": {"default": {"default": "chevron vers la droite courbé"}}}, {"key": "2E22", "mappings": {"default": {"default": "anglet supérieur gauche"}}}, {"key": "2E23", "mappings": {"default": {"default": "anglet supérieur droit"}}}, {"key": "2E24", "mappings": {"default": {"default": "anglet inférieur gauche"}}}, {"key": "2E25", "mappings": {"default": {"default": "anglet inférieur droit"}}}, {"key": "2E26", "mappings": {"default": {"default": "crampillon couché gauche"}}}, {"key": "2E27", "mappings": {"default": {"default": "crampillon couché droit"}}}, {"key": "2E28", "mappings": {"default": {"default": "double parenthèse gauche"}}}, {"key": "2E29", "mappings": {"default": {"default": "double parenthèse droite"}}}, {"key": "3008", "mappings": {"default": {"default": "chevron gauche"}}}, {"key": "3009", "mappings": {"default": {"default": "chevron droit"}}}, {"key": "300A", "mappings": {"default": {"default": "double chevron gauche"}}}, {"key": "300B", "mappings": {"default": {"default": "double chevron droit"}}}, {"key": "300C", "mappings": {"default": {"default": "anglet gauche"}}}, {"key": "300D", "mappings": {"default": {"default": "anglet droit"}}}, {"key": "300E", "mappings": {"default": {"default": "anglet ajouré gauche"}}}, {"key": "300F", "mappings": {"default": {"default": "anglet ajouré droit"}}}, {"key": "3010", "mappings": {"default": {"default": "crochet noir lenticulaire gauche"}}}, {"key": "3011", "mappings": {"default": {"default": "crochet noir lenticulaire droit"}}}, {"key": "3014", "mappings": {"default": {"default": "crochet gauche en écaille"}}}, {"key": "3015", "mappings": {"default": {"default": "crochet droit en écaille"}}}, {"key": "3016", "mappings": {"default": {"default": "crochet blanc lenticulaire gauche"}}}, {"key": "3017", "mappings": {"default": {"default": "crochet blanc lenticulaire droit"}}}, {"key": "3018", "mappings": {"default": {"default": "crochet blanc gauche en écaille"}}}, {"key": "3019", "mappings": {"default": {"default": "crochet blanc droit en écaille"}}}, {"key": "301A", "mappings": {"default": {"default": "crochet blanc gauche"}}}, {"key": "301B", "mappings": {"default": {"default": "crochet blanc droit"}}}, {"key": "301D", "mappings": {"default": {"default": "guillemet double prime ré<PERSON><PERSON><PERSON>"}}}, {"key": "301E", "mappings": {"default": {"default": "guillemet double prime"}}}, {"key": "301F", "mappings": {"default": {"default": "guillemet double prime inférieur"}}}, {"key": "FD3E", "mappings": {"default": {"default": "parenthèse gauche ornée"}}}, {"key": "FD3F", "mappings": {"default": {"default": "parenthèse droite ornée"}}}, {"key": "FE17", "mappings": {"default": {"default": "forme de présentation de crochet blanc lenticulaire gauche vertical"}}}, {"key": "FE18", "mappings": {"default": {"default": "forme de présentation de crochet blanc lenticulaire droit vertical"}}}, {"key": "FE35", "mappings": {"default": {"default": "forme de présentation de parenthèse gauche verticale"}}}, {"key": "FE36", "mappings": {"default": {"default": "forme de présentation de parenthèse droite verticale"}}}, {"key": "FE37", "mappings": {"default": {"default": "forme de présentation d'accolade gauche verticale"}}}, {"key": "FE38", "mappings": {"default": {"default": "forme de présentation d'accolade droite verticale"}}}, {"key": "FE39", "mappings": {"default": {"default": "forme de présentation de crochet gauche vertical en écaille"}}}, {"key": "FE3A", "mappings": {"default": {"default": "forme de présentation de crochet droit vertical en écaille"}}}, {"key": "FE3B", "mappings": {"default": {"default": "forme de présentation de crochet gauche lenticulaire noir vertical"}}}, {"key": "FE3C", "mappings": {"default": {"default": "forme de présentation de crochet droit lenticulaire noir vertical"}}}, {"key": "FE3D", "mappings": {"default": {"default": "forme de présentation de crochet double gauche vertical"}}}, {"key": "FE3E", "mappings": {"default": {"default": "forme de présentation de crochet double droit vertical"}}}, {"key": "FE3F", "mappings": {"default": {"default": "over angle bracket"}}}, {"key": "FE40", "mappings": {"default": {"default": "under angle bracket"}}}, {"key": "FE41", "mappings": {"default": {"default": "forme de présentation de crochet vertical gauche en coin"}}}, {"key": "FE42", "mappings": {"default": {"default": "forme de présentation de crochet vertical droit en coin"}}}, {"key": "FE43", "mappings": {"default": {"default": "forme de présentation de crochet blanc vertical gauche en coin"}}}, {"key": "FE44", "mappings": {"default": {"default": "forme de présentation de crochet blanc vertical droit en coin"}}}, {"key": "FE47", "mappings": {"default": {"default": "forme de présentation de crochet gauche vertical"}}}, {"key": "FE48", "mappings": {"default": {"default": "forme de présentation de crochet droit vertical"}}}, {"key": "FE59", "mappings": {"default": {"default": "parenthèse gauche minuscule"}}}, {"key": "FE5A", "mappings": {"default": {"default": "parenthèse droite minuscule"}}}, {"key": "FE5B", "mappings": {"default": {"default": "accolade gauche minuscule"}}}, {"key": "FE5C", "mappings": {"default": {"default": "accolade droite minuscule"}}}, {"key": "FE5D", "mappings": {"default": {"default": "crochet gauche en écaille minuscule"}}}, {"key": "FE5E", "mappings": {"default": {"default": "crochet droit en écaille minuscule"}}}, {"key": "FF08", "mappings": {"default": {"default": "parenthèse gauche pleine chasse"}}}, {"key": "FF09", "mappings": {"default": {"default": "parenthèse droite pleine chasse"}}}, {"key": "FF3B", "mappings": {"default": {"default": "crochet gauche pleine chasse"}}}, {"key": "FF3D", "mappings": {"default": {"default": "crochet droit pleine chasse"}}}, {"key": "FF5B", "mappings": {"default": {"default": "accolade gauche pleine chasse"}}}, {"key": "FF5D", "mappings": {"default": {"default": "accolade droite pleine chasse"}}}, {"key": "FF5F", "mappings": {"default": {"default": "parenthèse gauche ajourée pleine chasse *"}}}, {"key": "FF60", "mappings": {"default": {"default": "parenthèse droite ajourée pleine chasse *"}}}, {"key": "FF62", "mappings": {"default": {"default": "crochet gauche en coin demi chasse"}}}, {"key": "FF63", "mappings": {"default": {"default": "crochet droit en coin demi chasse"}}}], "fr/symbols/math_geometry.min": [{"locale": "fr"}, {"key": "2500", "mappings": {"default": {"default": "filet horizontal fin"}}}, {"key": "2501", "mappings": {"default": {"default": "filet horizontal gras"}}}, {"key": "2502", "mappings": {"default": {"default": "filet vertical fin"}}}, {"key": "2503", "mappings": {"default": {"default": "filet vertical gras"}}}, {"key": "2504", "mappings": {"default": {"default": "filet brisé triple horizontal fin"}}}, {"key": "2505", "mappings": {"default": {"default": "filet brisé triple horizontal gras"}}}, {"key": "2506", "mappings": {"default": {"default": "filet brisé triple vertical fin"}}}, {"key": "2507", "mappings": {"default": {"default": "filet brisé triple vertical gras"}}}, {"key": "2508", "mappings": {"default": {"default": "filet brisé quadruple horizontal fin"}}}, {"key": "2509", "mappings": {"default": {"default": "filet brisé quadruple horizontal gras"}}}, {"key": "250A", "mappings": {"default": {"default": "filet brisé quadruple vertical fin"}}}, {"key": "250B", "mappings": {"default": {"default": "filet brisé quadruple vertical gras"}}}, {"key": "250C", "mappings": {"default": {"default": "filet fin vers le bas et vers la droite"}}}, {"key": "250D", "mappings": {"default": {"default": "filet fin vers le bas et gras vers la droite"}}}, {"key": "250E", "mappings": {"default": {"default": "filet gras vers le bas et fin vers la droite"}}}, {"key": "250F", "mappings": {"default": {"default": "filet gras vers le bas et vers la droite"}}}, {"key": "2510", "mappings": {"default": {"default": "filet fin vers le bas et vers la gauche"}}}, {"key": "2511", "mappings": {"default": {"default": "filet fin vers le bas et gras vers la gauche"}}}, {"key": "2512", "mappings": {"default": {"default": "filet gras vers le bas et fin vers la gauche"}}}, {"key": "2513", "mappings": {"default": {"default": "filet gras vers le bas et vers la gauche"}}}, {"key": "2514", "mappings": {"default": {"default": "filet fin vers le haut et vers la droite"}}}, {"key": "2515", "mappings": {"default": {"default": "filet fin vers le haut et gras vers la droite"}}}, {"key": "2516", "mappings": {"default": {"default": "filet gras vers le haut et fin vers la droite"}}}, {"key": "2517", "mappings": {"default": {"default": "filet gras vers le haut et vers la droite"}}}, {"key": "2518", "mappings": {"default": {"default": "filet fin vers le haut et vers la gauche"}}}, {"key": "2519", "mappings": {"default": {"default": "filet fin vers le haut et gras vers la gauche"}}}, {"key": "251A", "mappings": {"default": {"default": "filet gras vers le haut et fin vers la gauche"}}}, {"key": "251B", "mappings": {"default": {"default": "filet gras vers le haut et vers la gauche"}}}, {"key": "251C", "mappings": {"default": {"default": "filet fin vertical et vers la droite"}}}, {"key": "251D", "mappings": {"default": {"default": "filet fin vertical et gras vers la droite"}}}, {"key": "251E", "mappings": {"default": {"default": "filet gras vers le haut et fin vers la droite et vers le bas"}}}, {"key": "251F", "mappings": {"default": {"default": "filet gras vers le bas et fin vers la droite et vers le haut"}}}, {"key": "2520", "mappings": {"default": {"default": "filet gras vertical et fin vers la droite"}}}, {"key": "2521", "mappings": {"default": {"default": "filet fin vers le bas et gras vers la droite et vers le haut"}}}, {"key": "2522", "mappings": {"default": {"default": "filet fin vers le haut et gras vers la droite et vers le bas"}}}, {"key": "2523", "mappings": {"default": {"default": "filet gras vertical et vers la droite"}}}, {"key": "2524", "mappings": {"default": {"default": "filet fin vertical et vers la gauche"}}}, {"key": "2525", "mappings": {"default": {"default": "filet fin vertical et gras vers la gauche"}}}, {"key": "2526", "mappings": {"default": {"default": "filet gras vers le haut et fin vers la gauche et vers le bas"}}}, {"key": "2527", "mappings": {"default": {"default": "filet gras vers le bas et fin vers la gauche et vers le haut"}}}, {"key": "2528", "mappings": {"default": {"default": "filet gras vertical et fin vers la gauche"}}}, {"key": "2529", "mappings": {"default": {"default": "filet fin vers le bas et gras vers la gauche et vers le haut"}}}, {"key": "252A", "mappings": {"default": {"default": "filet fin vers le haut et gras vers la gauche et vers le bas"}}}, {"key": "252B", "mappings": {"default": {"default": "filet gras vertical et vers la gauche"}}}, {"key": "252C", "mappings": {"default": {"default": "filet fin vers le bas et horizontal"}}}, {"key": "252D", "mappings": {"default": {"default": "filet gras vers la gauche et fin vers la droite et vers le bas"}}}, {"key": "252E", "mappings": {"default": {"default": "filet gras vers la droite et fin vers la gauche et vers le bas"}}}, {"key": "252F", "mappings": {"default": {"default": "filet fin vers le bas et gras horizontal"}}}, {"key": "2530", "mappings": {"default": {"default": "filet gras vers le bas et fin horizontal"}}}, {"key": "2531", "mappings": {"default": {"default": "filet fin vers la droite et gras vers la gauche et vers le bas"}}}, {"key": "2532", "mappings": {"default": {"default": "filet fin vers la gauche et gras vers la droite et vers le bas"}}}, {"key": "2533", "mappings": {"default": {"default": "filet gras vers le bas et horizontal"}}}, {"key": "2534", "mappings": {"default": {"default": "filet fin vers le haut et horizontal"}}}, {"key": "2535", "mappings": {"default": {"default": "filet gras vers la gauche et fin vers la droite et vers le haut"}}}, {"key": "2536", "mappings": {"default": {"default": "filet gras vers la droite et fin vers la gauche et vers le haut"}}}, {"key": "2537", "mappings": {"default": {"default": "filet fin vers le haut et gras horizontal"}}}, {"key": "2538", "mappings": {"default": {"default": "filet gras vers le haut et fin horizontal"}}}, {"key": "2539", "mappings": {"default": {"default": "filet fin vers la droite et gras vers la gauche et vers le haut"}}}, {"key": "253A", "mappings": {"default": {"default": "filet fin vers la gauche et gras vers la droite et vers le haut"}}}, {"key": "253B", "mappings": {"default": {"default": "filet gras vers le haut et horizontal"}}}, {"key": "253C", "mappings": {"default": {"default": "filet fin vertical et horizontal"}}}, {"key": "253D", "mappings": {"default": {"default": "filet gras vers la gauche et fin vers la droite et vertical"}}}, {"key": "253E", "mappings": {"default": {"default": "filet gras vers la droite et fin vers la gauche et vertical"}}}, {"key": "253F", "mappings": {"default": {"default": "filet vertical fin et horizontal gras"}}}, {"key": "2540", "mappings": {"default": {"default": "filet gras vers le haut et fin vers le bas et horizontal"}}}, {"key": "2541", "mappings": {"default": {"default": "filet gras vers le bas et fin vers le haut et horizontal"}}}, {"key": "2542", "mappings": {"default": {"default": "filet vertical gras et horizontal fin"}}}, {"key": "2543", "mappings": {"default": {"default": "filet gras vers la gauche et vers le haut et fin vers la droite et vers le bas"}}}, {"key": "2544", "mappings": {"default": {"default": "filet gras vers la droite et vers le haut et fin vers la gauche et vers le bas"}}}, {"key": "2545", "mappings": {"default": {"default": "filet gras vers la gauche et vers le bas et fin vers la droite et vers le haut"}}}, {"key": "2546", "mappings": {"default": {"default": "filet gras vers la droite et vers le bas et fin vers la gauche et vers le haut"}}}, {"key": "2547", "mappings": {"default": {"default": "filet fin vers le bas et gras vers le haut et horizontal"}}}, {"key": "2548", "mappings": {"default": {"default": "filet fin vers le haut et gras vers le bas et horizontal"}}}, {"key": "2549", "mappings": {"default": {"default": "filet fin vers la droite et gras vers la gauche et vertical"}}}, {"key": "254A", "mappings": {"default": {"default": "filet fin vers la gauche et gras vers la droite et vertical"}}}, {"key": "254B", "mappings": {"default": {"default": "filet vertical et horizontal gras"}}}, {"key": "254C", "mappings": {"default": {"default": "filet brisé double horizontal fin"}}}, {"key": "254D", "mappings": {"default": {"default": "filet brisé double horizontal gras"}}}, {"key": "254E", "mappings": {"default": {"default": "filet brisé double vertical fin"}}}, {"key": "254F", "mappings": {"default": {"default": "filet brisé double vertical gras"}}}, {"key": "2550", "mappings": {"default": {"default": "filet double horizontal"}}}, {"key": "2551", "mappings": {"default": {"default": "filet double vertical"}}}, {"key": "2552", "mappings": {"default": {"default": "filet simple le bas et double vers la droite"}}}, {"key": "2553", "mappings": {"default": {"default": "filet double vers le bas et simple vers la droite"}}}, {"key": "2554", "mappings": {"default": {"default": "filet double vers le bas et vers la droite"}}}, {"key": "2555", "mappings": {"default": {"default": "filet simple vers le bas et double vers la gauche"}}}, {"key": "2556", "mappings": {"default": {"default": "filet double vers le bas et simple vers la gauche"}}}, {"key": "2557", "mappings": {"default": {"default": "filet double vers le bas et vers la gauche"}}}, {"key": "2558", "mappings": {"default": {"default": "filet simple vers le haut et double vers la droite"}}}, {"key": "2559", "mappings": {"default": {"default": "filet double vers le haut et simple vers la droite"}}}, {"key": "255A", "mappings": {"default": {"default": "filet double vers le haut et vers la droite"}}}, {"key": "255B", "mappings": {"default": {"default": "filet simple vers le haut et double vers la gauche"}}}, {"key": "255C", "mappings": {"default": {"default": "filet double vers le haut et simple vers la gauche"}}}, {"key": "255D", "mappings": {"default": {"default": "filet double vers le haut et vers la gauche"}}}, {"key": "255E", "mappings": {"default": {"default": "filet vertical simple et droit double"}}}, {"key": "255F", "mappings": {"default": {"default": "filet vertical double et droit simple"}}}, {"key": "2560", "mappings": {"default": {"default": "filet double vertical et vers la droite"}}}, {"key": "2561", "mappings": {"default": {"default": "filet vertical simple et gauche double"}}}, {"key": "2562", "mappings": {"default": {"default": "filet vertical double et gauche simple"}}}, {"key": "2563", "mappings": {"default": {"default": "filet double vertical et vers la gauche"}}}, {"key": "2564", "mappings": {"default": {"default": "filet vers le bas simple et horizontal double"}}}, {"key": "2565", "mappings": {"default": {"default": "filet vers le bas double et horizontal simple"}}}, {"key": "2566", "mappings": {"default": {"default": "filet double vers le bas et horizontal"}}}, {"key": "2567", "mappings": {"default": {"default": "filet vers le haut simple et horizontal double"}}}, {"key": "2568", "mappings": {"default": {"default": "filet vers le haut double et horizontal simple"}}}, {"key": "2569", "mappings": {"default": {"default": "filet double vers le haut et horizontal"}}}, {"key": "256A", "mappings": {"default": {"default": "filet vertical simple et horizontal double"}}}, {"key": "256B", "mappings": {"default": {"default": "filet vertical double et horizontal simple"}}}, {"key": "256C", "mappings": {"default": {"default": "filet double vertical et horizontall"}}}, {"key": "256D", "mappings": {"default": {"default": "filet fin arc vers le bas et vers la droite"}}}, {"key": "256E", "mappings": {"default": {"default": "filet fin arc vers le bas et vers la gauche"}}}, {"key": "256F", "mappings": {"default": {"default": "filet fin arc vers le haut et vers la gauche"}}}, {"key": "2570", "mappings": {"default": {"default": "filet fin arc vers le haut et vers la droite"}}}, {"key": "2571", "mappings": {"default": {"default": "filet diagonal fin du coin supérieur droit au coin inférieur gauche"}}}, {"key": "2572", "mappings": {"default": {"default": "filet diagonal fin du coin supérieur gauche vers le coin inférieur droit"}}}, {"key": "2573", "mappings": {"default": {"default": "filet diagonal fin en croix"}}}, {"key": "2574", "mappings": {"default": {"default": "filet fin vers la gauche"}}}, {"key": "2575", "mappings": {"default": {"default": "filet fin vers le haut"}}}, {"key": "2576", "mappings": {"default": {"default": "filet fin vers la droite"}}}, {"key": "2577", "mappings": {"default": {"default": "filet fin vers le bas"}}}, {"key": "2578", "mappings": {"default": {"default": "filet gras vers la gauche"}}}, {"key": "2579", "mappings": {"default": {"default": "filet gras vers le haut"}}}, {"key": "257A", "mappings": {"default": {"default": "filet gras vers la droite"}}}, {"key": "257B", "mappings": {"default": {"default": "filet gras vers le bas"}}}, {"key": "257C", "mappings": {"default": {"default": "filet fin vers la gauche et gras vers la droite"}}}, {"key": "257D", "mappings": {"default": {"default": "filet fin vers le haut et gras vers le bas"}}}, {"key": "257E", "mappings": {"default": {"default": "filet gras vers la gauche et fin vers la droite"}}}, {"key": "257F", "mappings": {"default": {"default": "filet gras vers le haut et fin vers le bas"}}}, {"key": "2580", "mappings": {"default": {"default": "moitié supérieure de pavé"}}}, {"key": "2581", "mappings": {"default": {"default": "huitième inférieur de pavé"}}}, {"key": "2582", "mappings": {"default": {"default": "quart inférieur de pavé"}}}, {"key": "2583", "mappings": {"default": {"default": "trois huitièmes inférieurs de pavé"}}}, {"key": "2584", "mappings": {"default": {"default": "moitié inférieure de pavé"}}}, {"key": "2585", "mappings": {"default": {"default": "cinq huitièmes inférieurs de pavé"}}}, {"key": "2586", "mappings": {"default": {"default": "trois quarts inférieurs de pavé"}}}, {"key": "2587", "mappings": {"default": {"default": "sept huitièmes inférieurs de pavé"}}}, {"key": "2588", "mappings": {"default": {"default": "pavé plein"}}}, {"key": "2589", "mappings": {"default": {"default": "sept huitièmes gauches de pavé"}}}, {"key": "258A", "mappings": {"default": {"default": "trois quarts gauches de pavé"}}}, {"key": "258B", "mappings": {"default": {"default": "cinq huitièmes gauches de pavé"}}}, {"key": "258C", "mappings": {"default": {"default": "moitié gauche de pavé"}}}, {"key": "258D", "mappings": {"default": {"default": "trois huitièmes gauches de pavé"}}}, {"key": "258E", "mappings": {"default": {"default": "un quart gauche de pavé"}}}, {"key": "258F", "mappings": {"default": {"default": "un huitième gauche de pavé"}}}, {"key": "2590", "mappings": {"default": {"default": "moitié droite de pavé"}}}, {"key": "2591", "mappings": {"default": {"default": "ombre légère"}}}, {"key": "2592", "mappings": {"default": {"default": "ombre moyenne"}}}, {"key": "2593", "mappings": {"default": {"default": "ombre foncée"}}}, {"key": "2594", "mappings": {"default": {"default": "huitième supérieur de pavé"}}}, {"key": "2595", "mappings": {"default": {"default": "huitième droit de pavé"}}}, {"key": "2596", "mappings": {"default": {"default": "quadrant inférieur gauche"}}}, {"key": "2597", "mappings": {"default": {"default": "quadrant inférieur droit"}}}, {"key": "2598", "mappings": {"default": {"default": "quadrant supérieur gauche"}}}, {"key": "2599", "mappings": {"default": {"default": "quadrant supérieur gauche inférieur gauche et inférieur droit"}}}, {"key": "259A", "mappings": {"default": {"default": "quadrant supérieur gauche et inférieur droit"}}}, {"key": "259B", "mappings": {"default": {"default": "quadrant supérieur gauche supérieur droit et inférieur gauche"}}}, {"key": "259C", "mappings": {"default": {"default": "quadrant supérieur gauche supérieur droit et inférieur droit"}}}, {"key": "259D", "mappings": {"default": {"default": "quadrant supérieur droit"}}}, {"key": "259E", "mappings": {"default": {"default": "quadrant supérieur droit et inférieur gauche"}}}, {"key": "259F", "mappings": {"default": {"default": "quadrant supérieur droit inférieur gauche et inférieur droit"}}}, {"key": "25A0", "mappings": {"default": {"default": "petit carré noir"}}}, {"key": "25A1", "mappings": {"default": {"default": "<PERSON><PERSON> blanc"}}}, {"key": "25A2", "mappings": {"default": {"default": "carré blanc aux coins arrondis"}}}, {"key": "25A3", "mappings": {"default": {"default": "petit carré noir inscrit dans un carré blanc"}}}, {"key": "25A4", "mappings": {"default": {"default": "car<PERSON> ha<PERSON>ré horizontalement"}}}, {"key": "25A5", "mappings": {"default": {"default": "car<PERSON> ha<PERSON>ré verticalement"}}}, {"key": "25A6", "mappings": {"default": {"default": "car<PERSON> quadrillé"}}}, {"key": "25A7", "mappings": {"default": {"default": "car<PERSON> hachuré du haut à gauche vers le bas à droite"}}}, {"key": "25A8", "mappings": {"default": {"default": "car<PERSON> hachuré du haut à droite vers le bas à gauche"}}}, {"key": "25A9", "mappings": {"default": {"default": "carré quadrillé en diagonale"}}}, {"key": "25AA", "mappings": {"default": {"default": "petit carré noir"}}}, {"key": "25AB", "mappings": {"default": {"default": "petit car<PERSON> blanc"}}}, {"key": "25AC", "mappings": {"default": {"default": "rectangle noir"}}}, {"key": "25AD", "mappings": {"default": {"default": "rectangle blanc"}}}, {"key": "25AE", "mappings": {"default": {"default": "rectangle vertical noir"}}}, {"key": "25AF", "mappings": {"default": {"default": "white vertical rectangle"}}}, {"key": "25B0", "mappings": {"default": {"default": "parallélogramme noir"}}}, {"key": "25B1", "mappings": {"default": {"default": "parallélogramme blanc"}}}, {"key": "25B2", "mappings": {"default": {"default": "petit triangle noir pointant vers le haut"}}}, {"key": "25B3", "mappings": {"default": {"default": "triangle blanc pointant vers le haut"}}}, {"key": "25B4", "mappings": {"default": {"default": "petit triangle noir pointant vers le haut"}}}, {"key": "25B5", "mappings": {"default": {"default": "petit triangle blanc pointant vers le haut"}}}, {"key": "25B6", "mappings": {"default": {"default": "petit triangle noir pointant vers la droite"}}}, {"key": "25B7", "mappings": {"default": {"default": "triangle blanc pointant vers la droite"}}}, {"key": "25B8", "mappings": {"default": {"default": "petit triangle noir pointant vers la droite"}}}, {"key": "25B9", "mappings": {"default": {"default": "petit triangle blanc pointant vers la droite"}}}, {"key": "25BA", "mappings": {"default": {"default": "pointeur noir vers la droite"}}}, {"key": "25BB", "mappings": {"default": {"default": "pointeur blanc vers la droite"}}}, {"key": "25BC", "mappings": {"default": {"default": "petit triangle noir pointant vers le bas"}}}, {"key": "25BD", "mappings": {"default": {"default": "triangle blanc pointant vers le bas"}}}, {"key": "25BE", "mappings": {"default": {"default": "petit triangle noir pointant vers le bas"}}}, {"key": "25BF", "mappings": {"default": {"default": "petit triangle blanc pointant vers le bas"}}}, {"key": "25C0", "mappings": {"default": {"default": "petit triangle noir pointant vers la gauche"}}}, {"key": "25C1", "mappings": {"default": {"default": "petit triangle blanc pointant vers la gauche"}}}, {"key": "25C2", "mappings": {"default": {"default": "petit triangle noir pointant vers la gauche"}}}, {"key": "25C3", "mappings": {"default": {"default": "petit triangle blanc pointant vers la gauche"}}}, {"key": "25C4", "mappings": {"default": {"default": "pointeur noir vers la gauche"}}}, {"key": "25C5", "mappings": {"default": {"default": "pointeur blanc vers la gauche"}}}, {"key": "25C6", "mappings": {"default": {"default": "losange noir"}}}, {"key": "25C7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> blanc"}}}, {"key": "25C8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> blanc <PERSON>"}}}, {"key": "25C9", "mappings": {"default": {"default": "cercle blanc <PERSON>"}}}, {"key": "25CA", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "25CB", "mappings": {"default": {"default": "cercle blanc"}}}, {"key": "25CC", "mappings": {"default": {"default": "cercle en pointillés"}}}, {"key": "25CD", "mappings": {"default": {"default": "cercle hachuré verticalement"}}}, {"key": "25CE", "mappings": {"default": {"default": "deux cercles concentriques"}}}, {"key": "25CF", "mappings": {"default": {"default": "cercle noir"}}}, {"key": "25D0", "mappings": {"default": {"default": "cercle avec moitié gauche noire"}}}, {"key": "25D1", "mappings": {"default": {"default": "cercle avec moitié droite noire"}}}, {"key": "25D2", "mappings": {"default": {"default": "cercle avec moitié inférieure"}}}, {"key": "25D3", "mappings": {"default": {"default": "cercle avec moitié supérieure"}}}, {"key": "25D4", "mappings": {"default": {"default": "cercle avec quadrant supérieur droit noir"}}}, {"key": "25D5", "mappings": {"default": {"default": "disque avec quadrant supérieur gauche blanc"}}}, {"key": "25D6", "mappings": {"default": {"default": "demi disque gauche noir"}}}, {"key": "25D7", "mappings": {"default": {"default": "demi disque droite noir"}}}, {"key": "25D8", "mappings": {"default": {"default": "point noir au blanc"}}}, {"key": "25D9", "mappings": {"default": {"default": "cercle noir au blanc"}}}, {"key": "25DA", "mappings": {"default": {"default": "demi cercle supérieur noir au  blanc"}}}, {"key": "25DB", "mappings": {"default": {"default": "demi cercle inférieur noir au  blanc"}}}, {"key": "25DC", "mappings": {"default": {"default": "arc de cercle dans le quadrant supérieur gauchearc de cercle dans le quadrant supérieur gauche"}}}, {"key": "25DD", "mappings": {"default": {"default": "arc de cercle dans le quadrant supérieur droit"}}}, {"key": "25DE", "mappings": {"default": {"default": "arc de cercle dans le quadrant inférieur droit"}}}, {"key": "25DF", "mappings": {"default": {"default": "arc de cercle dans le quadrant inférieur gauche"}}}, {"key": "25E0", "mappings": {"default": {"default": "demi cercle supérieur"}}}, {"key": "25E1", "mappings": {"default": {"default": "demi cercle inférieur"}}}, {"key": "25E2", "mappings": {"default": {"default": "coin triangulaire noir inférieur droit"}}}, {"key": "25E3", "mappings": {"default": {"default": "coin triangulaire noir inférieur gauche"}}}, {"key": "25E4", "mappings": {"default": {"default": "coin triangulaire noir supérieur gauche"}}}, {"key": "25E5", "mappings": {"default": {"default": "coin triangulaire noir supérieur droit"}}}, {"key": "25E6", "mappings": {"default": {"default": "opérateur rond"}}}, {"key": "25E7", "mappings": {"default": {"default": "carré à moitié gauche noire"}}}, {"key": "25E8", "mappings": {"default": {"default": "car<PERSON> à moitié droite noire"}}}, {"key": "25E9", "mappings": {"default": {"default": "carré à moitié supérieure gauche noire"}}}, {"key": "25EA", "mappings": {"default": {"default": "carré à moitié inférieure droite noire"}}}, {"key": "25EB", "mappings": {"default": {"default": "car<PERSON> blanc à ligne bissectrice verticale"}}}, {"key": "25EC", "mappings": {"default": {"default": "triangle blanc pointant vers le haut pointé"}}}, {"key": "25ED", "mappings": {"default": {"default": "triangle pointe vers le haut À moitié gauche noire"}}}, {"key": "25EE", "mappings": {"default": {"default": "triangle pointe vers le haut À moitié droite noire"}}}, {"key": "25EF", "mappings": {"default": {"default": "grand cercle"}}}, {"key": "25F0", "mappings": {"default": {"default": "car<PERSON> blanc à quartier supérieur gauche"}}}, {"key": "25F1", "mappings": {"default": {"default": "carré blanc à quartier inférieur gauche"}}}, {"key": "25F2", "mappings": {"default": {"default": "car<PERSON> blanc à quartier inférieur droit"}}}, {"key": "25F3", "mappings": {"default": {"default": "car<PERSON> blanc à quartier supérieur droit"}}}, {"key": "25F4", "mappings": {"default": {"default": "cercle blanc à quartier supérieur gauche"}}}, {"key": "25F5", "mappings": {"default": {"default": "cercle blanc à quartier inférieur gauche"}}}, {"key": "25F6", "mappings": {"default": {"default": "cercle blanc à quartier inférieur droit"}}}, {"key": "25F7", "mappings": {"default": {"default": "cercle blanc à quartier supérieur droit"}}}, {"key": "25F8", "mappings": {"default": {"default": "triangle rectangle supérieur gauche"}}}, {"key": "25F9", "mappings": {"default": {"default": "triangle rectangle supérieur droit"}}}, {"key": "25FA", "mappings": {"default": {"default": "triangle rectangle inférieur gauche"}}}, {"key": "25FB", "mappings": {"default": {"default": "<PERSON><PERSON> moyen blanc"}}}, {"key": "25FC", "mappings": {"default": {"default": "carré moyen noir"}}}, {"key": "25FD", "mappings": {"default": {"default": "<PERSON><PERSON> moyen blanc"}}}, {"key": "25FE", "mappings": {"default": {"default": "carré moyen noir"}}}, {"key": "25FF", "mappings": {"default": {"default": "triangle rectangle inférieur droit"}}}, {"key": "2B12", "mappings": {"default": {"default": "carré à moitié supérieure noire"}}}, {"key": "2B13", "mappings": {"default": {"default": "carré à moitié inférieure noire"}}}, {"key": "2B14", "mappings": {"default": {"default": "carré à moitié diagonale supérieure droite noire"}}}, {"key": "2B15", "mappings": {"default": {"default": "carré à moitié diagonale inférieure droite noire"}}}, {"key": "2B16", "mappings": {"default": {"default": "losange à moitié gauche noire"}}}, {"key": "2B17", "mappings": {"default": {"default": "losange à moitié droite noire"}}}, {"key": "2B18", "mappings": {"default": {"default": "losange à moitié supérieure noire"}}}, {"key": "2B19", "mappings": {"default": {"default": "losange à moitié inférieure noire"}}}, {"key": "2B1A", "mappings": {"default": {"default": "car<PERSON> en pointillés"}}}, {"key": "2B1B", "mappings": {"default": {"default": "grand carré noir"}}}, {"key": "2B1C", "mappings": {"default": {"default": "grand carré blanc"}}}, {"key": "2B1D", "mappings": {"default": {"default": "très petit carré noir"}}}, {"key": "2B1E", "mappings": {"default": {"default": "très petit carré blanc"}}}, {"key": "2B1F", "mappings": {"default": {"default": "pentagone noir"}}}, {"key": "2B20", "mappings": {"default": {"default": "pentagone blanc"}}}, {"key": "2B21", "mappings": {"default": {"default": "hexagone blanc"}}}, {"key": "2B22", "mappings": {"default": {"default": "hexagone noir"}}}, {"key": "2B23", "mappings": {"default": {"default": "hexagone noir posé sur un côté"}}}, {"key": "2B24", "mappings": {"default": {"default": "grand cercle noir"}}}, {"key": "2B25", "mappings": {"default": {"default": "losange moyen noir"}}}, {"key": "2B26", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> moyen blanc"}}}, {"key": "2B27", "mappings": {"default": {"default": "rhombe moyen noir"}}}, {"key": "2B28", "mappings": {"default": {"default": "rhombe moyen blanc"}}}, {"key": "2B29", "mappings": {"default": {"default": "petit losange noir"}}}, {"key": "2B2A", "mappings": {"default": {"default": "petit rhombe noir"}}}, {"key": "2B2B", "mappings": {"default": {"default": "petit rhombe blanc"}}}, {"key": "2B2C", "mappings": {"default": {"default": "ellipse horizontale noire"}}}, {"key": "2B2D", "mappings": {"default": {"default": "ellipse horizontale blanche"}}}, {"key": "2B2E", "mappings": {"default": {"default": "ellipse verticale noire"}}}, {"key": "2B2F", "mappings": {"default": {"default": "ellipse verticale blanche"}}}, {"key": "2B50", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> blanche moyenne"}}}, {"key": "2B51", "mappings": {"default": {"default": "petite étoile noire"}}}, {"key": "2B52", "mappings": {"default": {"default": "petite é<PERSON>ile blanche"}}}, {"key": "2B53", "mappings": {"default": {"default": "pentagone noir pointant vers la droite"}}}, {"key": "2B54", "mappings": {"default": {"default": "pentagone blanc pointant vers la droite"}}}, {"key": "2B55", "mappings": {"default": {"default": "grand cercle gras"}}}, {"key": "2B56", "mappings": {"default": {"default": "ovale gras avec ovale inscrit"}}}, {"key": "2B57", "mappings": {"default": {"default": "cercle gras avec cercle inscrit"}}}, {"key": "2B58", "mappings": {"default": {"default": "cercle gras"}}}, {"key": "2B59", "mappings": {"default": {"default": "sautoir cerclé gras"}}}], "fr/symbols/math_harpoons.min": [{"locale": "fr"}, {"key": "21BC", "mappings": {"default": {"default": "harpon gauche avec dent dressée"}}}, {"key": "21BD", "mappings": {"default": {"default": "harpon gauche avec dent baissée"}}}, {"key": "21BE", "mappings": {"default": {"default": "harpon vers le haut avec dent droite"}}}, {"key": "21BF", "mappings": {"default": {"default": "harpon vers le haut avec dent gauche"}}}, {"key": "21C0", "mappings": {"default": {"default": "harpon droit avec dent dressée"}}}, {"key": "21C1", "mappings": {"default": {"default": "harpon droit avec dent baissée"}}}, {"key": "21C2", "mappings": {"default": {"default": "harpon vers le bas avec dent droite"}}}, {"key": "21C3", "mappings": {"default": {"default": "harpon vers le bas avec dent gauche"}}}, {"key": "21CB", "mappings": {"default": {"default": "harpon gauche par dessus harpon droit"}}}, {"key": "21CC", "mappings": {"default": {"default": "harpon droit par dessus harpon gauche"}}}, {"key": "294A", "mappings": {"default": {"default": "javelot à dents gauche dressée et droite baissée"}}}, {"key": "294B", "mappings": {"default": {"default": "javelot à dents gauche baissée et droite dressée"}}}, {"key": "294C", "mappings": {"default": {"default": "crampon à dents supérieure droite et inférieure gauche"}}}, {"key": "294D", "mappings": {"default": {"default": "crampon à dents supérieure gauche et inférieure droite"}}}, {"key": "294E", "mappings": {"default": {"default": "javelot à dents droite et gauche dressées"}}}, {"key": "294F", "mappings": {"default": {"default": "javelot à dents supérieure et inférieure droites"}}}, {"key": "2950", "mappings": {"default": {"default": "javelot à dents droite et gauche baissées"}}}, {"key": "2951", "mappings": {"default": {"default": "javelot à dents supérieure et inférieure gauches"}}}, {"key": "2952", "mappings": {"default": {"default": "harpon gauche à dent dressée contre taquet"}}}, {"key": "2953", "mappings": {"default": {"default": "harpon droit à dent dressée contre taquet"}}}, {"key": "2954", "mappings": {"default": {"default": "harpon vers le haut à dent droite contre taquet"}}}, {"key": "2955", "mappings": {"default": {"default": "harpon vers le bas à dent droite contre taquet"}}}, {"key": "2956", "mappings": {"default": {"default": "harpon gauche à dent baissée contre taquet"}}}, {"key": "2957", "mappings": {"default": {"default": "harpon droit à dent baissée contre taquet"}}}, {"key": "2958", "mappings": {"default": {"default": "harpon vers le haut à dent gauche contre taquet"}}}, {"key": "2959", "mappings": {"default": {"default": "harpon vers le bas à dent gauche contre taquet"}}}, {"key": "295A", "mappings": {"default": {"default": "harpon gauche à dent dressée depuis taquet"}}}, {"key": "295B", "mappings": {"default": {"default": "harpon droit à dent dressée depuis taquet"}}}, {"key": "295C", "mappings": {"default": {"default": "harpon vers le haut à dent droite depuis taquet"}}}, {"key": "295D", "mappings": {"default": {"default": "harpon vers le bas à dent droite depuis taquet"}}}, {"key": "295E", "mappings": {"default": {"default": "harpon gauche à dent baissée depuis taquet"}}}, {"key": "295F", "mappings": {"default": {"default": "harpon droit à dent baissée depuis taquet"}}}, {"key": "2960", "mappings": {"default": {"default": "harpon vers le haut à dent gauche depuis taquet"}}}, {"key": "2961", "mappings": {"default": {"default": "harpon vers le bas à dent gauche depuis taquet"}}}, {"key": "2962", "mappings": {"default": {"default": "harpon gauche à dent dressée sur harpon gauche à dent baissée"}}}, {"key": "2963", "mappings": {"default": {"default": "harpon vers le haut à dent gauche à côté de harpon vers le haut à dent droite"}}}, {"key": "2964", "mappings": {"default": {"default": "harpon droit à dent dressée par dessus harpon droit à dent baissée"}}}, {"key": "2965", "mappings": {"default": {"default": "harpon vers le bas à dent gauche à côté de harpon vers le bas à dent droite"}}}, {"key": "2966", "mappings": {"default": {"default": "harpon à dent gauche dressée par dessus harpon à dent droite dressée"}}}, {"key": "2967", "mappings": {"default": {"default": "harpon à dent gauche baissée par dessus harpon à dent droite baissée"}}}, {"key": "2968", "mappings": {"default": {"default": "harpon à dent droite dressée par dessus harpon à dent gauche dressée"}}}, {"key": "2969", "mappings": {"default": {"default": "harpon à dent droite baissée par dessus harpon à dent gauche baissée"}}}, {"key": "296A", "mappings": {"default": {"default": "harpon à dent gauche dressée par dessus long trait"}}}, {"key": "296B", "mappings": {"default": {"default": "long trait par dessus harpon à dent gauche baissée"}}}, {"key": "296C", "mappings": {"default": {"default": "harpon à dent droite dressée par dessus long trait"}}}, {"key": "296D", "mappings": {"default": {"default": "long trait par dessus harpon à dent droite baissée"}}}, {"key": "296E", "mappings": {"default": {"default": "harpon vers le haut à dent gauche à côté de harpon vers le bas à dent droite"}}}, {"key": "296F", "mappings": {"default": {"default": "harpon vers le bas à dent gauche à côté de harpon vers le haut à dent droite"}}}, {"key": "297C", "mappings": {"default": {"default": "ancre gauche"}}}, {"key": "297D", "mappings": {"default": {"default": "ancre droite"}}}, {"key": "297E", "mappings": {"default": {"default": "ancre vers le haut"}}}, {"key": "297F", "mappings": {"default": {"default": "ancre vers le bas"}}}], "fr/symbols/math_non_characters.min": [{"locale": "fr"}, {"key": "210F", "mappings": {"default": {"default": "constante de planck sur deux pi"}}}, {"key": "2114", "mappings": {"default": {"default": "symbole l b barré"}}}, {"key": "2116", "mappings": {"default": {"default": "symbole numéro"}}}, {"key": "2117", "mappings": {"default": {"default": "copyright de programme sonore"}}}, {"key": "211E", "mappings": {"default": {"default": "ordonnances"}}}, {"key": "211F", "mappings": {"default": {"default": "r<PERSON><PERSON><PERSON>"}}}, {"key": "2120", "mappings": {"default": {"default": "symbole anglais marque de service"}}}, {"key": "2121", "mappings": {"default": {"default": "signe de téléphone"}}}, {"key": "2122", "mappings": {"default": {"default": "symbole anglais marque de commerce"}}}, {"key": "2123", "mappings": {"default": {"default": "versicule"}}}, {"key": "2125", "mappings": {"default": {"default": "symbole once"}}}, {"key": "2126", "mappings": {"default": {"default": "ohm"}}}, {"key": "2127", "mappings": {"default": {"default": "ohm culbuté"}}}, {"key": "212A", "mappings": {"default": {"default": "degré kelvin"}}}, {"key": "212B", "mappings": {"default": {"default": "angstroms"}}}, {"key": "212E", "mappings": {"default": {"default": "symbole estimé"}}}, {"key": "2132", "mappings": {"default": {"default": "f culbuté majuscule"}}}, {"key": "2139", "mappings": {"default": {"default": "bureau d'information"}}}, {"key": "213A", "mappings": {"default": {"default": "Q majuscule couché"}}}, {"key": "213B", "mappings": {"default": {"default": "symbole télécopie"}}}, {"key": "2141", "mappings": {"default": {"default": "G culbuté majuscule sans empattement"}}}, {"key": "2142", "mappings": {"default": {"default": "L culbuté majuscule sans empattement"}}}, {"key": "2143", "mappings": {"default": {"default": "L réfléchi majuscule sans empattement"}}}, {"key": "2144", "mappings": {"default": {"default": "Y culbuté majuscule sans empattement"}}}], "fr/symbols/math_symbols.min": [{"locale": "fr"}, {"key": "0021", "mappings": {"default": {"default": "factorielle"}}}, {"key": "0022", "mappings": {"default": {"default": "petit guillemet"}}}, {"key": "0023", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "0024", "mappings": {"default": {"default": "dollars"}}}, {"key": "0025", "mappings": {"default": {"default": "pourcent"}}}, {"key": "0026", "mappings": {"default": {"default": "esperluette"}}}, {"key": "0027", "mappings": {"default": {"default": "prime", "alternative": "apostrophe"}}}, {"key": "002A", "mappings": {"default": {"default": "astérisque"}}}, {"key": "002B", "mappings": {"default": {"default": "plus"}}}, {"key": "002C", "mappings": {"default": {"default": "virgule"}}}, {"key": "002D", "mappings": {"default": {"default": "moins"}}}, {"key": "002E", "mappings": {"default": {"default": "point"}}}, {"key": "002F", "mappings": {"default": {"default": "barre oblique"}}}, {"key": "003A", "mappings": {"default": {"default": "deux points"}}}, {"key": "003B", "mappings": {"default": {"default": "point virgule"}}}, {"key": "003C", "mappings": {"default": {"default": "inférieur à"}}}, {"key": "003D", "mappings": {"default": {"default": "égale"}}}, {"key": "003E", "mappings": {"default": {"default": "supérieur à"}}}, {"key": "003F", "mappings": {"default": {"default": "point d'interrogation"}}}, {"key": "0040", "mappings": {"default": {"default": "arobase"}}}, {"key": "005C", "mappings": {"default": {"default": "barre oblique inversée"}}}, {"key": "005E", "mappings": {"default": {"default": "circonflexe"}}}, {"key": "005F", "mappings": {"default": {"default": "tiret bas"}}}, {"key": "0060", "mappings": {"default": {"default": "accent grave"}}}, {"key": "007C", "mappings": {"default": {"default": "barre verticale"}}}, {"key": "007E", "mappings": {"default": {"default": "tilde"}}}, {"key": "00A1", "mappings": {"default": {"default": "point d'exclamation renversé"}}}, {"key": "00A2", "mappings": {"default": {"default": "centimes"}}}, {"key": "00A3", "mappings": {"default": {"default": "livres"}}}, {"key": "00A4", "mappings": {"default": {"default": "symbole mon<PERSON>taire"}}}, {"key": "00A5", "mappings": {"default": {"default": "yen"}}}, {"key": "00A6", "mappings": {"default": {"default": "barre déjointe"}}}, {"key": "00A7", "mappings": {"default": {"default": "paragraphe"}}}, {"key": "00A8", "mappings": {"default": {"default": "t<PERSON><PERSON>"}}}, {"key": "00A9", "mappings": {"default": {"default": "copyright"}}}, {"key": "00AA", "mappings": {"default": {"default": "indicateur ordinal féminin"}}}, {"key": "00AB", "mappings": {"default": {"default": "guillemet chevron pointant gauche"}}}, {"key": "00AC", "mappings": {"default": {"default": "négation"}}}, {"key": "00AE", "mappings": {"default": {"default": "symbole marque déposée"}}}, {"key": "00AF", "mappings": {"default": {"default": "macron"}}}, {"key": "00B0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "00B1", "mappings": {"default": {"default": "plus ou moins"}}}, {"key": "00B4", "mappings": {"default": {"default": "accent aigu"}}}, {"key": "00B5", "mappings": {"default": {"default": "symbole micro"}}}, {"key": "00B6", "mappings": {"default": {"default": "fin de paragraphe"}}}, {"key": "00B7", "mappings": {"default": {"default": "point médian"}}}, {"key": "00B8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "00BA", "mappings": {"default": {"default": "indicateur ordinal masculin"}}}, {"key": "00BB", "mappings": {"default": {"default": "guillemet chevron pointant droite"}}}, {"key": "00BF", "mappings": {"default": {"default": "point d'interrogation renversé"}}}, {"key": "00D7", "mappings": {"default": {"default": "multiplié par"}}}, {"key": "00F7", "mappings": {"default": {"default": "divisé par"}}}, {"key": "02B9", "mappings": {"default": {"default": "prime"}}}, {"key": "02BA", "mappings": {"default": {"default": "double prime"}}}, {"key": "02D8", "mappings": {"default": {"default": "br<PERSON>ve"}}}, {"key": "02D9", "mappings": {"default": {"default": "point en chef"}}}, {"key": "02DA", "mappings": {"default": {"default": "rond en chef"}}}, {"key": "02DB", "mappings": {"default": {"default": "ogonek"}}}, {"key": "02DC", "mappings": {"default": {"default": "tilde"}}}, {"key": "02DD", "mappings": {"default": {"default": "double accent aigu"}}}, {"key": "2010", "mappings": {"default": {"default": "trait d'union"}}}, {"key": "2011", "mappings": {"default": {"default": "trait d'union insécable"}}}, {"key": "2012", "mappings": {"default": {"default": "tiret numérique"}}}, {"key": "2013", "mappings": {"default": {"default": "trait d'union insécable"}}}, {"key": "2014", "mappings": {"default": {"default": "tiret numérique"}}}, {"key": "2015", "mappings": {"default": {"default": "barre <PERSON>le"}}}, {"key": "2016", "mappings": {"default": {"default": "double ligne verticale"}}}, {"key": "2017", "mappings": {"default": {"default": "double trait souscrit"}}}, {"key": "2018", "mappings": {"default": {"default": "guillemet apostrophe culbuté"}}}, {"key": "2019", "mappings": {"default": {"default": "guillemet apostrophe"}}}, {"key": "201A", "mappings": {"default": {"default": "guillemet virgule inférieur"}}}, {"key": "201B", "mappings": {"default": {"default": "guillemet virgule supérieur culbuté"}}}, {"key": "201C", "mappings": {"default": {"default": "guillemet apostrophe double culbuté"}}}, {"key": "201D", "mappings": {"default": {"default": "guillemet apostrophe double"}}}, {"key": "201E", "mappings": {"default": {"default": "guillemet double prime"}}}, {"key": "201F", "mappings": {"default": {"default": "guillemet virgule double supérieur culbuté"}}}, {"key": "2020", "mappings": {"default": {"default": "obèle"}}}, {"key": "2021", "mappings": {"default": {"default": "double obèle"}}}, {"key": "2022", "mappings": {"default": {"default": "puce"}}}, {"key": "2023", "mappings": {"default": {"default": "puce triangulaire"}}}, {"key": "2024", "mappings": {"default": {"default": "point de conduite simple"}}}, {"key": "2025", "mappings": {"default": {"default": "point de conduite double"}}}, {"key": "2026", "mappings": {"default": {"default": "points de suspension"}}}, {"key": "2027", "mappings": {"default": {"default": "point de coupure de mot"}}}, {"key": "2030", "mappings": {"default": {"default": "symbole pour mille"}}}, {"key": "2031", "mappings": {"default": {"default": "symbole pour dix mille"}}}, {"key": "2032", "mappings": {"default": {"default": "prime"}}}, {"key": "2033", "mappings": {"default": {"default": "double prime"}}}, {"key": "2034", "mappings": {"default": {"default": "triple prime"}}}, {"key": "2035", "mappings": {"default": {"default": "prime réf<PERSON>chi"}}}, {"key": "2036", "mappings": {"default": {"default": "double prime r<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2037", "mappings": {"default": {"default": "triple prime r<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2038", "mappings": {"default": {"default": "chevron d'insertion"}}}, {"key": "2039", "mappings": {"default": {"default": "guillemet simple gauche"}}}, {"key": "203A", "mappings": {"default": {"default": "guillemet simple droite"}}}, {"key": "203B", "mappings": {"default": {"default": "marque de référence"}}}, {"key": "203C", "mappings": {"default": {"default": "double point d'exclamation"}}}, {"key": "203D", "mappings": {"default": {"default": "point exclarrogatif"}}}, {"key": "203E", "mappings": {"default": {"default": "tiret en chef"}}}, {"key": "203F", "mappings": {"default": {"default": "tirant souscrit"}}}, {"key": "2040", "mappings": {"default": {"default": "tirant en chef"}}}, {"key": "2041", "mappings": {"default": {"default": "lambda d'insertion"}}}, {"key": "2042", "mappings": {"default": {"default": "astérisme"}}}, {"key": "2043", "mappings": {"default": {"default": "puce trait d'union"}}}, {"key": "2044", "mappings": {"default": {"default": "barre de fraction"}}}, {"key": "2047", "mappings": {"default": {"default": "double point d'interrogation"}}}, {"key": "2048", "mappings": {"default": {"default": "point d'interrogation exclamation"}}}, {"key": "2049", "mappings": {"default": {"default": "point d'exclamation interrogation"}}}, {"key": "204B", "mappings": {"default": {"default": "pied de mouche réfléchi"}}}, {"key": "204C", "mappings": {"default": {"default": "puce noire tronquée à droite"}}}, {"key": "204D", "mappings": {"default": {"default": "puce noire tronquée à gauche"}}}, {"key": "204E", "mappings": {"default": {"default": "astérisque baissé"}}}, {"key": "204F", "mappings": {"default": {"default": "point virgule réf<PERSON>chi"}}}, {"key": "2050", "mappings": {"default": {"default": "re<PERSON><PERSON>"}}}, {"key": "2051", "mappings": {"default": {"default": "deux astérisques alignés verticalement"}}}, {"key": "2052", "mappings": {"default": {"default": "moins commercial"}}}, {"key": "2053", "mappings": {"default": {"default": "tiret ondé"}}}, {"key": "2054", "mappings": {"default": {"default": "dos d'âne souscrit"}}}, {"key": "2055", "mappings": {"default": {"default": "point fleur"}}}, {"key": "2056", "mappings": {"default": {"default": "trois points"}}}, {"key": "2057", "mappings": {"default": {"default": "quadruple prime"}}}, {"key": "2058", "mappings": {"default": {"default": "quatre points en losange"}}}, {"key": "2059", "mappings": {"default": {"default": "cinq points en quinconce"}}}, {"key": "205A", "mappings": {"default": {"default": "deux points en capitale"}}}, {"key": "205B", "mappings": {"default": {"default": "quatre points en croix"}}}, {"key": "205C", "mappings": {"default": {"default": "croix pointée"}}}, {"key": "205D", "mappings": {"default": {"default": "trois points vertical"}}}, {"key": "205E", "mappings": {"default": {"default": "quatre points vertical"}}}, {"key": "207A", "mappings": {"default": {"default": "exposant signe plus"}}}, {"key": "207B", "mappings": {"default": {"default": "exposant signe moins"}}}, {"key": "207C", "mappings": {"default": {"default": "exposant signe égal"}}}, {"key": "207D", "mappings": {"default": {"default": "exposant parenthèse gauche"}}}, {"key": "207E", "mappings": {"default": {"default": "exposant parenthèse droite"}}}, {"key": "208A", "mappings": {"default": {"default": "indice signe plus"}}}, {"key": "208B", "mappings": {"default": {"default": "indice signe moins"}}}, {"key": "208C", "mappings": {"default": {"default": "indice signe <PERSON>"}}}, {"key": "208D", "mappings": {"default": {"default": "indice parenthèse gauche"}}}, {"key": "208E", "mappings": {"default": {"default": "indice parenthèse droite"}}}, {"key": "214A", "mappings": {"default": {"default": "limite de propriété"}}}, {"key": "214B", "mappings": {"default": {"default": "perluète culbutée"}}}, {"key": "214C", "mappings": {"default": {"default": "symbole per"}}}, {"key": "214D", "mappings": {"default": {"default": "aktieselskab"}}}, {"key": "214E", "mappings": {"default": {"default": "minuscule f culbuté"}}}, {"key": "2200", "mappings": {"default": {"default": "pour tous"}}}, {"key": "2201", "mappings": {"default": {"default": "complément"}}}, {"key": "2203", "mappings": {"default": {"default": "il existe"}}}, {"key": "2204", "mappings": {"default": {"default": "il n'existe pas"}}}, {"key": "2205", "mappings": {"default": {"default": "ensemble vide"}}}, {"key": "2206", "mappings": {"default": {"default": "incrément"}}}, {"key": "2208", "mappings": {"default": {"default": "appartient à"}}}, {"key": "2209", "mappings": {"default": {"default": "n'appartient pas à"}}}, {"key": "220A", "mappings": {"default": {"default": "appartient à"}}}, {"key": "220B", "mappings": {"default": {"default": "contient comme élément"}}}, {"key": "220C", "mappings": {"default": {"default": "ne contient pas comme élément"}}}, {"key": "220D", "mappings": {"default": {"default": "contient comme élément"}}}, {"key": "220E", "mappings": {"default": {"default": "ce qu'il fallait d<PERSON>rer"}}}, {"key": "220F", "mappings": {"default": {"default": "produit"}}}, {"key": "2210", "mappings": {"default": {"default": "coproduit"}}}, {"key": "2211", "mappings": {"default": {"default": "sommation"}}}, {"key": "2212", "mappings": {"default": {"default": "moins"}}}, {"key": "2213", "mappings": {"default": {"default": "moins ou plus"}}}, {"key": "2214", "mappings": {"default": {"default": "plus pointé"}}}, {"key": "2215", "mappings": {"default": {"default": "division"}}}, {"key": "2216", "mappings": {"default": {"default": "différence d'ensembles"}}}, {"key": "2217", "mappings": {"default": {"default": "opérateur astérisque"}}}, {"key": "2218", "mappings": {"default": {"default": "opérateur rond"}}}, {"key": "2219", "mappings": {"default": {"default": "point médian"}}}, {"key": "221A", "mappings": {"default": {"default": "racine <PERSON>e"}}}, {"key": "221B", "mappings": {"default": {"default": "racine cubique"}}}, {"key": "221C", "mappings": {"default": {"default": "racine quatrième"}}}, {"key": "221D", "mappings": {"default": {"default": "proportionnel à"}}}, {"key": "221E", "mappings": {"default": {"default": "infini"}}}, {"key": "221F", "mappings": {"default": {"default": "angle droit"}}}, {"key": "2220", "mappings": {"default": {"default": "angle"}}}, {"key": "2221", "mappings": {"default": {"default": "angle mesuré"}}}, {"key": "2222", "mappings": {"default": {"default": "angle sphérique"}}}, {"key": "2223", "mappings": {"default": {"default": "est un diviseur de"}}}, {"key": "2224", "mappings": {"default": {"default": "n'est pas un diviseur de"}}}, {"key": "2225", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2226", "mappings": {"default": {"default": "non parallèle à"}}}, {"key": "2227", "mappings": {"default": {"default": "et logique"}}}, {"key": "2228", "mappings": {"default": {"default": "ou logique"}}}, {"key": "2229", "mappings": {"default": {"default": "intersection"}}}, {"key": "222A", "mappings": {"default": {"default": "union"}}}, {"key": "222B", "mappings": {"default": {"default": "intégrale"}}}, {"key": "222C", "mappings": {"default": {"default": "intégrale double"}}}, {"key": "222D", "mappings": {"default": {"default": "intégrale triple"}}}, {"key": "222E", "mappings": {"default": {"default": "intégrale de contour"}}}, {"key": "222F", "mappings": {"default": {"default": "intégrale de surface"}}}, {"key": "2230", "mappings": {"default": {"default": "intégrale de volume"}}}, {"key": "2231", "mappings": {"default": {"default": "intégrale en sens négatif"}}}, {"key": "2232", "mappings": {"default": {"default": "intégrale de contour en sens négatif"}}}, {"key": "2233", "mappings": {"default": {"default": "intégrale de contour en sens positif"}}}, {"key": "2234", "mappings": {"default": {"default": "par conséquent"}}}, {"key": "2235", "mappings": {"default": {"default": "parce que"}}}, {"key": "2236", "mappings": {"default": {"default": "rapport"}}}, {"key": "2237", "mappings": {"default": {"default": "proportion"}}}, {"key": "2238", "mappings": {"default": {"default": "moins pointé"}}}, {"key": "2239", "mappings": {"default": {"default": "excès"}}}, {"key": "223A", "mappings": {"default": {"default": "proportion géométrique"}}}, {"key": "223B", "mappings": {"default": {"default": "homothétique"}}}, {"key": "223C", "mappings": {"default": {"default": "opérateur tilde"}}}, {"key": "223D", "mappings": {"default": {"default": "opérateur tilde renversé"}}}, {"key": "223E", "mappings": {"default": {"default": "s <PERSON>é ren<PERSON>"}}}, {"key": "223F", "mappings": {"default": {"default": "sinusoïde"}}}, {"key": "2240", "mappings": {"default": {"default": "produit couronne"}}}, {"key": "2241", "mappings": {"default": {"default": "non tilde"}}}, {"key": "2242", "mappings": {"default": {"default": "moins tilde"}}}, {"key": "2243", "mappings": {"default": {"default": "asymptotiquement égal à"}}}, {"key": "2244", "mappings": {"default": {"default": "non asymptotiquement égal à"}}}, {"key": "2245", "mappings": {"default": {"default": "approximativement égal à"}}}, {"key": "2246", "mappings": {"default": {"default": "approximativement mais non strictement égal à"}}}, {"key": "2247", "mappings": {"default": {"default": "ni approximativement ni strictement égal à"}}}, {"key": "2248", "mappings": {"default": {"default": "presque égal à"}}}, {"key": "2249", "mappings": {"default": {"default": "non presque égal à"}}}, {"key": "224A", "mappings": {"default": {"default": "presque égal ou égal à"}}}, {"key": "224B", "mappings": {"default": {"default": "triple tilde"}}}, {"key": "224C", "mappings": {"default": {"default": "entièrement égal à"}}}, {"key": "224D", "mappings": {"default": {"default": "équivalent à"}}}, {"key": "224E", "mappings": {"default": {"default": "géométriquement équivalent à"}}}, {"key": "224F", "mappings": {"default": {"default": "différence entre"}}}, {"key": "2250", "mappings": {"default": {"default": "tend vers la limite"}}}, {"key": "2251", "mappings": {"default": {"default": "géométriquement égal à"}}}, {"key": "2252", "mappings": {"default": {"default": "approximativement égal à ou image de"}}}, {"key": "2253", "mappings": {"default": {"default": "image de ou approximativement égal à"}}}, {"key": "2254", "mappings": {"default": {"default": "deux points égal"}}}, {"key": "2255", "mappings": {"default": {"default": "<PERSON><PERSON> deux points"}}}, {"key": "2256", "mappings": {"default": {"default": "rond dans égal"}}}, {"key": "2257", "mappings": {"default": {"default": "<PERSON>gal avec rond en chef"}}}, {"key": "2258", "mappings": {"default": {"default": "correspond à"}}}, {"key": "2259", "mappings": {"default": {"default": "estime"}}}, {"key": "225A", "mappings": {"default": {"default": "équiangulaire à"}}}, {"key": "225B", "mappings": {"default": {"default": "<PERSON>gal avec étoile en chef"}}}, {"key": "225C", "mappings": {"default": {"default": "égal delta"}}}, {"key": "225D", "mappings": {"default": {"default": "égal par définition à"}}}, {"key": "225E", "mappings": {"default": {"default": "mesuré par"}}}, {"key": "225F", "mappings": {"default": {"default": "égalité en doute"}}}, {"key": "2260", "mappings": {"default": {"default": "pas égal à"}}}, {"key": "2261", "mappings": {"default": {"default": "identique à"}}}, {"key": "2262", "mappings": {"default": {"default": "non identique à"}}}, {"key": "2263", "mappings": {"default": {"default": "strictement équivalent à"}}}, {"key": "2264", "mappings": {"default": {"default": "plus petit ou égal à"}}}, {"key": "2265", "mappings": {"default": {"default": "plus grand ou égal à"}}}, {"key": "2266", "mappings": {"default": {"default": "plus petit que par dessus égal à"}}}, {"key": "2267", "mappings": {"default": {"default": "plus grand que par dessus égal à"}}}, {"key": "2268", "mappings": {"default": {"default": "plus petit mais pas égal à"}}}, {"key": "2269", "mappings": {"default": {"default": "plus grand mais pas égal à"}}}, {"key": "226A", "mappings": {"default": {"default": "beaucoup plus petit que"}}}, {"key": "226B", "mappings": {"default": {"default": "beaucoup plus grand que"}}}, {"key": "226C", "mappings": {"default": {"default": "entre"}}}, {"key": "226D", "mappings": {"default": {"default": "non équivalent à"}}}, {"key": "226E", "mappings": {"default": {"default": "pas plus petit que"}}}, {"key": "226F", "mappings": {"default": {"default": "pas plus grand que"}}}, {"key": "2270", "mappings": {"default": {"default": "ni plus petit ni égal à"}}}, {"key": "2271", "mappings": {"default": {"default": "ni plus grand ni égal à"}}}, {"key": "2272", "mappings": {"default": {"default": "plus petit ou équivalent à"}}}, {"key": "2273", "mappings": {"default": {"default": "plus grand ou équivalent à"}}}, {"key": "2274", "mappings": {"default": {"default": "ni plus petit ni équivalent à"}}}, {"key": "2275", "mappings": {"default": {"default": "ni plus grand ni équivalent à"}}}, {"key": "2276", "mappings": {"default": {"default": "plus petit ou plus grand que"}}}, {"key": "2277", "mappings": {"default": {"default": "plus grand ou plus petit que"}}}, {"key": "2278", "mappings": {"default": {"default": "ni plus petit ni plus grand que"}}}, {"key": "2279", "mappings": {"default": {"default": "ni plus grand ni plus petit que"}}}, {"key": "227A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "227B", "mappings": {"default": {"default": "suit"}}}, {"key": "227C", "mappings": {"default": {"default": "précède ou est égal à"}}}, {"key": "227D", "mappings": {"default": {"default": "suit ou est égal à"}}}, {"key": "227E", "mappings": {"default": {"default": "précède ou est équivalent à"}}}, {"key": "227F", "mappings": {"default": {"default": "suit ou est équivalent à"}}}, {"key": "2280", "mappings": {"default": {"default": "ne précède pas"}}}, {"key": "2281", "mappings": {"default": {"default": "ne suit pas"}}}, {"key": "2282", "mappings": {"default": {"default": "sous ensemble de"}}}, {"key": "2283", "mappings": {"default": {"default": "sur ensemble de"}}}, {"key": "2284", "mappings": {"default": {"default": "pas un sous ensemble de"}}}, {"key": "2285", "mappings": {"default": {"default": "pas un sur ensemble de"}}}, {"key": "2286", "mappings": {"default": {"default": "sous ensemble ou égal à"}}}, {"key": "2287", "mappings": {"default": {"default": "sur ensemble ou égal à"}}}, {"key": "2288", "mappings": {"default": {"default": "ni un sous ensemble ni égal à"}}}, {"key": "2289", "mappings": {"default": {"default": "ni un sur ensemble ni égal à"}}}, {"key": "228A", "mappings": {"default": {"default": "sous ensemble mais pas égal à"}}}, {"key": "228B", "mappings": {"default": {"default": "sur ensemble mais pas égal à"}}}, {"key": "228C", "mappings": {"default": {"default": "multiensemble"}}}, {"key": "228D", "mappings": {"default": {"default": "multiplication de multiensemble"}}}, {"key": "228E", "mappings": {"default": {"default": "union de multiensemble"}}}, {"key": "228F", "mappings": {"default": {"default": "image carrée de"}}}, {"key": "2290", "mappings": {"default": {"default": "original car<PERSON> de"}}}, {"key": "2291", "mappings": {"default": {"default": "image carrée ou égal à"}}}, {"key": "2292", "mappings": {"default": {"default": "original carré ou égal à"}}}, {"key": "2293", "mappings": {"default": {"default": "chapeau carré"}}}, {"key": "2294", "mappings": {"default": {"default": "coupe carrée"}}}, {"key": "2295", "mappings": {"default": {"default": "plus cerclé"}}}, {"key": "2296", "mappings": {"default": {"default": "moins cerclé"}}}, {"key": "2297", "mappings": {"default": {"default": "multiplié par cerclé"}}}, {"key": "2298", "mappings": {"default": {"default": "barre oblique de division cerclée"}}}, {"key": "2299", "mappings": {"default": {"default": "opérateur point cerclé"}}}, {"key": "229A", "mappings": {"default": {"default": "opérateur rond cerclé"}}}, {"key": "229B", "mappings": {"default": {"default": "opérateur astérisque cerclé"}}}, {"key": "229C", "mappings": {"default": {"default": "<PERSON>gal cerclé"}}}, {"key": "229D", "mappings": {"default": {"default": "tiret cerclé"}}}, {"key": "229E", "mappings": {"default": {"default": "plus encadré"}}}, {"key": "229F", "mappings": {"default": {"default": "moins encadré"}}}, {"key": "22A0", "mappings": {"default": {"default": "multiplié par encadré"}}}, {"key": "22A1", "mappings": {"default": {"default": "opérateur point encadré"}}}, {"key": "22A2", "mappings": {"default": {"default": "taquet droit"}}}, {"key": "22A3", "mappings": {"default": {"default": "taquet gauche"}}}, {"key": "22A4", "mappings": {"default": {"default": "taquet vers le bas"}}}, {"key": "22A5", "mappings": {"default": {"default": "taquet vers le haut"}}}, {"key": "22A6", "mappings": {"default": {"default": "assertion"}}}, {"key": "22A7", "mappings": {"default": {"default": "mod<PERSON><PERSON><PERSON>"}}}, {"key": "22A8", "mappings": {"default": {"default": "vrai"}}}, {"key": "22A9", "mappings": {"default": {"default": "oblige"}}}, {"key": "22AA", "mappings": {"default": {"default": "barre verticale triple avec tourniquet à droite"}}}, {"key": "22AB", "mappings": {"default": {"default": "barre verticale double avec tourniquet à droite double"}}}, {"key": "22AC", "mappings": {"default": {"default": "ne prouve pas"}}}, {"key": "22AD", "mappings": {"default": {"default": "pas vrai"}}}, {"key": "22AE", "mappings": {"default": {"default": "n'oblige pas"}}}, {"key": "22AF", "mappings": {"default": {"default": "non barre verticale double avec tourniquet à droite double"}}}, {"key": "22B0", "mappings": {"default": {"default": "précède sous relation"}}}, {"key": "22B1", "mappings": {"default": {"default": "suit sous relation"}}}, {"key": "22B2", "mappings": {"default": {"default": "sous groupe normal de"}}}, {"key": "22B3", "mappings": {"default": {"default": "contient comme sous groupe normal"}}}, {"key": "22B4", "mappings": {"default": {"default": "sous groupe normal ou égal à"}}}, {"key": "22B5", "mappings": {"default": {"default": "contient comme sous groupe normal ou égal à"}}}, {"key": "22B6", "mappings": {"default": {"default": "original de"}}}, {"key": "22B7", "mappings": {"default": {"default": "image de"}}}, {"key": "22B8", "mappings": {"default": {"default": "multijection"}}}, {"key": "22B9", "mappings": {"default": {"default": "matrice hermitienne conjuguée"}}}, {"key": "22BA", "mappings": {"default": {"default": "intercale"}}}, {"key": "22BB", "mappings": {"default": {"default": "ou exclusif"}}}, {"key": "22BC", "mappings": {"default": {"default": "non et"}}}, {"key": "22BD", "mappings": {"default": {"default": "non ou"}}}, {"key": "22BF", "mappings": {"default": {"default": "triangle rectangle"}}}, {"key": "22C0", "mappings": {"default": {"default": "et logique de la famille"}}}, {"key": "22C1", "mappings": {"default": {"default": "ou logique de la famille"}}}, {"key": "22C2", "mappings": {"default": {"default": "intersection de la famille"}}}, {"key": "22C3", "mappings": {"default": {"default": "réunion de la famille"}}}, {"key": "22C4", "mappings": {"default": {"default": "opérateur losange"}}}, {"key": "22C5", "mappings": {"default": {"default": "opérateur point"}}}, {"key": "22C6", "mappings": {"default": {"default": "opérateur étoile"}}}, {"key": "22C7", "mappings": {"default": {"default": "divisé multiplié"}}}, {"key": "22C8", "mappings": {"default": {"default": "nœud papillon"}}}, {"key": "22C9", "mappings": {"default": {"default": "produit semi direct à gauche de facteur normal"}}}, {"key": "22CA", "mappings": {"default": {"default": "produit semi direct à droite de facteur normal"}}}, {"key": "22CB", "mappings": {"default": {"default": "produit semi direct à gauche"}}}, {"key": "22CC", "mappings": {"default": {"default": "produit semi direct à droite"}}}, {"key": "22CD", "mappings": {"default": {"default": "moins tilde renversé"}}}, {"key": "22CE", "mappings": {"default": {"default": "ou logique recourbé"}}}, {"key": "22CF", "mappings": {"default": {"default": "et logique recourbé"}}}, {"key": "22D0", "mappings": {"default": {"default": "sous ensemble double"}}}, {"key": "22D1", "mappings": {"default": {"default": "sur ensemble double"}}}, {"key": "22D2", "mappings": {"default": {"default": "intersection double"}}}, {"key": "22D3", "mappings": {"default": {"default": "union double"}}}, {"key": "22D4", "mappings": {"default": {"default": "fourche"}}}, {"key": "22D5", "mappings": {"default": {"default": "égal et parallèle à"}}}, {"key": "22D6", "mappings": {"default": {"default": "plus petit que pointé"}}}, {"key": "22D7", "mappings": {"default": {"default": "plus grand que pointé"}}}, {"key": "22D8", "mappings": {"default": {"default": "considérablement plus petit que"}}}, {"key": "22D9", "mappings": {"default": {"default": "considérablement plus grand que"}}}, {"key": "22DA", "mappings": {"default": {"default": "plus petit ou égal ou plus grand que"}}}, {"key": "22DB", "mappings": {"default": {"default": "plus grand ou égal ou plus petit que"}}}, {"key": "22DC", "mappings": {"default": {"default": "égal ou plus petit que"}}}, {"key": "22DD", "mappings": {"default": {"default": "égal à deux lignes ou supérieur à"}}}, {"key": "22DE", "mappings": {"default": {"default": "égal ou précède"}}}, {"key": "22DF", "mappings": {"default": {"default": "égal ou suit"}}}, {"key": "22E0", "mappings": {"default": {"default": "ni précédant ni égal à"}}}, {"key": "22E1", "mappings": {"default": {"default": "ni suivant ni égal à"}}}, {"key": "22E2", "mappings": {"default": {"default": "ni image carrée ni égal à"}}}, {"key": "22E3", "mappings": {"default": {"default": "ni original carré ni égal à"}}}, {"key": "22E4", "mappings": {"default": {"default": "image carré ou différent de"}}}, {"key": "22E5", "mappings": {"default": {"default": "original carré ou différent de"}}}, {"key": "22E6", "mappings": {"default": {"default": "plus petit mais non équivalent à"}}}, {"key": "22E7", "mappings": {"default": {"default": "plus grand mais non équivalent à"}}}, {"key": "22E8", "mappings": {"default": {"default": "précédant mais non équivalent à"}}}, {"key": "22E9", "mappings": {"default": {"default": "suivant mais non équivalent à"}}}, {"key": "22EA", "mappings": {"default": {"default": "pas un sous groupe normal de"}}}, {"key": "22EB", "mappings": {"default": {"default": "ne contient pas comme sous groupe normal"}}}, {"key": "22EC", "mappings": {"default": {"default": "ni sous groupe normal ni égal à"}}}, {"key": "22ED", "mappings": {"default": {"default": "ni égal ni contenant comme sous  groupe normal"}}}, {"key": "22EE", "mappings": {"default": {"default": "trois points suspendus"}}}, {"key": "22EF", "mappings": {"default": {"default": "trois points médians"}}}, {"key": "22F0", "mappings": {"default": {"default": "trois points diagonaux vers le coin haut à droite"}}}, {"key": "22F1", "mappings": {"default": {"default": "trois points diagonaux vers le coin bas à droite"}}}, {"key": "22F2", "mappings": {"default": {"default": "appartient à avec long trait horizontal"}}}, {"key": "22F3", "mappings": {"default": {"default": "appartient à avec barre verticale au bout du trait horizontal"}}}, {"key": "22F4", "mappings": {"default": {"default": "petit appartient à avec barre verticale au bout du trait horizontal"}}}, {"key": "22F5", "mappings": {"default": {"default": "appartient à avec point en chef"}}}, {"key": "22F6", "mappings": {"default": {"default": "appartient à avec trait en chef"}}}, {"key": "22F7", "mappings": {"default": {"default": "petit appartient à avec trait en chef"}}}, {"key": "22F8", "mappings": {"default": {"default": "appartient à avec trait souscrit"}}}, {"key": "22F9", "mappings": {"default": {"default": "appartient à avec deux traits horizontaux"}}}, {"key": "22FA", "mappings": {"default": {"default": "contient avec long trait horizontal"}}}, {"key": "22FB", "mappings": {"default": {"default": "contient avec barre verticale au bout du trait horizontal"}}}, {"key": "22FC", "mappings": {"default": {"default": "petit contient avec barre verticale au bout du trait horizontal"}}}, {"key": "22FD", "mappings": {"default": {"default": "contient avec trait en chef"}}}, {"key": "22FE", "mappings": {"default": {"default": "petit contient avec trait en chef"}}}, {"key": "22FF", "mappings": {"default": {"default": "appartient à un sac en notation z"}}}, {"key": "2300", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2302", "mappings": {"default": {"default": "maison"}}}, {"key": "2305", "mappings": {"default": {"default": "projective"}}}, {"key": "2306", "mappings": {"default": {"default": "perspective"}}}, {"key": "2307", "mappings": {"default": {"default": "ligne ondulée"}}}, {"key": "2310", "mappings": {"default": {"default": "négation réfléchi"}}}, {"key": "2311", "mappings": {"default": {"default": "pastille carrée"}}}, {"key": "2312", "mappings": {"default": {"default": "arc"}}}, {"key": "2313", "mappings": {"default": {"default": "segment"}}}, {"key": "2314", "mappings": {"default": {"default": "secteur"}}}, {"key": "2795", "mappings": {"default": {"default": "plus gras"}}}, {"key": "2796", "mappings": {"default": {"default": "moins gras"}}}, {"key": "2797", "mappings": {"default": {"default": "division gras"}}}, {"key": "27B0", "mappings": {"default": {"default": "boucle recourbée"}}}, {"key": "27BF", "mappings": {"default": {"default": "double boucle recourbée"}}}, {"key": "27C1", "mappings": {"default": {"default": "petit triangle blanc inscrit dans triangle blanc"}}}, {"key": "27C2", "mappings": {"default": {"default": "perpendiculaire à"}}}, {"key": "27C3", "mappings": {"default": {"default": "sous ensemble ouvert"}}}, {"key": "27C4", "mappings": {"default": {"default": "sur ensemble ouvert"}}}, {"key": "27C7", "mappings": {"default": {"default": "ou avec point inscrit"}}}, {"key": "27C8", "mappings": {"default": {"default": "sous ensemble précédé d'une barre oblique inversée"}}}, {"key": "27C9", "mappings": {"default": {"default": "sur ensemble suivi d'une barre oblique"}}}, {"key": "27CA", "mappings": {"default": {"default": "barre verticale À une traverse"}}}, {"key": "27CB", "mappings": {"default": {"default": "diagonale mathématique ascendante"}}}, {"key": "27CC", "mappings": {"default": {"default": "grande division"}}}, {"key": "27CD", "mappings": {"default": {"default": "diagonale mathématique descendante"}}}, {"key": "27CE", "mappings": {"default": {"default": "et logique encadré"}}}, {"key": "27CF", "mappings": {"default": {"default": "ou logique encadré"}}}, {"key": "27D0", "mappings": {"default": {"default": "losange blanc à point centré"}}}, {"key": "27D1", "mappings": {"default": {"default": "et pointé"}}}, {"key": "27D2", "mappings": {"default": {"default": "appartient À ouvert vers le haut"}}}, {"key": "27D3", "mappings": {"default": {"default": "coin inférieur droit pointé"}}}, {"key": "27D4", "mappings": {"default": {"default": "coin supérieur gauche pointé"}}}, {"key": "27D5", "mappings": {"default": {"default": "jointure externe gauche"}}}, {"key": "27D6", "mappings": {"default": {"default": "jointure externe droite"}}}, {"key": "27D7", "mappings": {"default": {"default": "jointure externe complète"}}}, {"key": "27D8", "mappings": {"default": {"default": "grand taquet vers le haut"}}}, {"key": "27D9", "mappings": {"default": {"default": "grand taquet vers le bas"}}}, {"key": "27DA", "mappings": {"default": {"default": "double tourniquet bilatéral"}}}, {"key": "27DB", "mappings": {"default": {"default": "tourniquet bilatéral"}}}, {"key": "27DC", "mappings": {"default": {"default": "multijection gauche"}}}, {"key": "27DD", "mappings": {"default": {"default": "long taquet droit"}}}, {"key": "27DE", "mappings": {"default": {"default": "long taquet gauche"}}}, {"key": "27DF", "mappings": {"default": {"default": "taquet vers le haut surmonté d'un cercle"}}}, {"key": "27E0", "mappings": {"default": {"default": "los<PERSON>e coupé"}}}, {"key": "27E1", "mappings": {"default": {"default": "losange concave blanc"}}}, {"key": "27E2", "mappings": {"default": {"default": "losange concave blanc avec trait à gauche"}}}, {"key": "27E3", "mappings": {"default": {"default": "losange concave blanc avec trait à droite"}}}, {"key": "27E4", "mappings": {"default": {"default": "carré blanc avec trait à gauche"}}}, {"key": "27E5", "mappings": {"default": {"default": "carré blanc avec trait à droite"}}}, {"key": "292B", "mappings": {"default": {"default": "diagonale montante sur diagonale descendante"}}}, {"key": "292C", "mappings": {"default": {"default": "diagonale descendante sur diagonale montante"}}}, {"key": "2980", "mappings": {"default": {"default": "délimiteur triple barre verticale"}}}, {"key": "2981", "mappings": {"default": {"default": "boulet"}}}, {"key": "2982", "mappings": {"default": {"default": "deux points de la notation z"}}}, {"key": "2999", "mappings": {"default": {"default": "clôture pointillée"}}}, {"key": "299A", "mappings": {"default": {"default": "ligne verticale en zigzag"}}}, {"key": "29B0", "mappings": {"default": {"default": "ensemble vide réfléchi"}}}, {"key": "29B1", "mappings": {"default": {"default": "ensemble vide barre en chef"}}}, {"key": "29B2", "mappings": {"default": {"default": "ensemble vide petit cercle en chef"}}}, {"key": "29B5", "mappings": {"default": {"default": "cercle à barre horizontale"}}}, {"key": "29B6", "mappings": {"default": {"default": "barre verticale cerclée"}}}, {"key": "29B7", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>"}}}, {"key": "29B8", "mappings": {"default": {"default": "barre oblique inversée cerclée"}}}, {"key": "29B9", "mappings": {"default": {"default": "perpendiculaire cerclé"}}}, {"key": "29BA", "mappings": {"default": {"default": "cercle divisé par une barre horizontale et une barre verticale dans la moitié supérieure"}}}, {"key": "29BB", "mappings": {"default": {"default": "cercle superposé d'un x"}}}, {"key": "29BC", "mappings": {"default": {"default": "division pivoté en sens antihoraire cerclé"}}}, {"key": "29BE", "mappings": {"default": {"default": "puce blanche cerclée"}}}, {"key": "29BF", "mappings": {"default": {"default": "puce cerclée"}}}, {"key": "29C0", "mappings": {"default": {"default": "inférieur à cerclé"}}}, {"key": "29C1", "mappings": {"default": {"default": "supérieur à cerclé"}}}, {"key": "29C2", "mappings": {"default": {"default": "cercle avec petit cercle à droite"}}}, {"key": "29C3", "mappings": {"default": {"default": "cercle avec deux petits traits horizontaux à droite"}}}, {"key": "29C4", "mappings": {"default": {"default": "oblique encadrée"}}}, {"key": "29C5", "mappings": {"default": {"default": "oblique inversée encadrée"}}}, {"key": "29C6", "mappings": {"default": {"default": "astérisque encadré"}}}, {"key": "29C7", "mappings": {"default": {"default": "petite cercle encadré"}}}, {"key": "29C8", "mappings": {"default": {"default": "<PERSON><PERSON> en<PERSON>"}}}, {"key": "29C9", "mappings": {"default": {"default": "car<PERSON>s se chevauchant"}}}, {"key": "29CA", "mappings": {"default": {"default": "triangle pointé en chef"}}}, {"key": "29CB", "mappings": {"default": {"default": "triangle à barre souscrite"}}}, {"key": "29CC", "mappings": {"default": {"default": "s inscrit dans triangle"}}}, {"key": "29CD", "mappings": {"default": {"default": "triangle à empattements inférieurs"}}}, {"key": "29CE", "mappings": {"default": {"default": "triangle droit par dessus triangle gauche"}}}, {"key": "29CF", "mappings": {"default": {"default": "triangle gauche à gauche d'une barre verticale"}}}, {"key": "29D0", "mappings": {"default": {"default": "triangle droit à droite d'une barre verticale"}}}, {"key": "29D1", "mappings": {"default": {"default": "nœud papillon à aile gauche noire"}}}, {"key": "29D2", "mappings": {"default": {"default": "nœud papillon à aile droite noire"}}}, {"key": "29D3", "mappings": {"default": {"default": "nœud papillon noir"}}}, {"key": "29D4", "mappings": {"default": {"default": "multiplication à moitié gauche noircie"}}}, {"key": "29D5", "mappings": {"default": {"default": "multiplication à moitié droite noircie"}}}, {"key": "29D6", "mappings": {"default": {"default": "sablier blanc"}}}, {"key": "29D7", "mappings": {"default": {"default": "sablier noir"}}}, {"key": "29DC", "mappings": {"default": {"default": "infini incomplet"}}}, {"key": "29DD", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "29DE", "mappings": {"default": {"default": "infini nié par une barre verticale"}}}, {"key": "29DF", "mappings": {"default": {"default": "multijection à deux têtes"}}}, {"key": "29E0", "mappings": {"default": {"default": "<PERSON><PERSON> omb<PERSON>"}}}, {"key": "29E1", "mappings": {"default": {"default": "augmente avec"}}}, {"key": "29E2", "mappings": {"default": {"default": "produit de permutation"}}}, {"key": "29E3", "mappings": {"default": {"default": "égale à et parallèle à incliné"}}}, {"key": "29E4", "mappings": {"default": {"default": "égale à et parallèle à incliné surmontés d'un tilde"}}}, {"key": "29E5", "mappings": {"default": {"default": "identique à et parallèle à incliné"}}}, {"key": "29E6", "mappings": {"default": {"default": "gleich stark"}}}, {"key": "29E7", "mappings": {"default": {"default": "thermodynamique"}}}, {"key": "29E8", "mappings": {"default": {"default": "triangle pointe vers le bas à moitié gauche noire"}}}, {"key": "29E9", "mappings": {"default": {"default": "triangle pointe vers le bas à moitié droite noire"}}}, {"key": "29EB", "mappings": {"default": {"default": "losange fuselé noir"}}}, {"key": "29EE", "mappings": {"default": {"default": "barre d'erreur à carré blanc"}}}, {"key": "29EF", "mappings": {"default": {"default": "barre d'erreur à carré noir"}}}, {"key": "29F0", "mappings": {"default": {"default": "barre d'erreur à losange blanc"}}}, {"key": "29F1", "mappings": {"default": {"default": "barre d'erreur à losange noir"}}}, {"key": "29F2", "mappings": {"default": {"default": "barre d'erreur à cercle blanc"}}}, {"key": "29F3", "mappings": {"default": {"default": "barre d'erreur à cercle noir"}}}, {"key": "29F4", "mappings": {"default": {"default": "r<PERSON><PERSON> di<PERSON><PERSON>"}}}, {"key": "29F5", "mappings": {"default": {"default": "opérateur barre oblique inversée"}}}, {"key": "29F6", "mappings": {"default": {"default": "barre oblique avec barre en chef"}}}, {"key": "29F7", "mappings": {"default": {"default": "barre oblique inversée à traverse horizontale"}}}, {"key": "29F8", "mappings": {"default": {"default": "grande barre oblique"}}}, {"key": "29F9", "mappings": {"default": {"default": "grande barre oblique inversée"}}}, {"key": "29FA", "mappings": {"default": {"default": "double plus"}}}, {"key": "29FB", "mappings": {"default": {"default": "triple plus"}}}, {"key": "29FE", "mappings": {"default": {"default": "tiny"}}}, {"key": "29FF", "mappings": {"default": {"default": "miny"}}}, {"key": "2A00", "mappings": {"default": {"default": "opérateur point cerclé n aire"}}}, {"key": "2A01", "mappings": {"default": {"default": "opérateur plus cerclé n aire"}}}, {"key": "2A02", "mappings": {"default": {"default": "opérateur multiplié par cerclé n aire"}}}, {"key": "2A03", "mappings": {"default": {"default": "opérateur union n aire pointé"}}}, {"key": "2A04", "mappings": {"default": {"default": "opérateur union n aire avec plus"}}}, {"key": "2A05", "mappings": {"default": {"default": "opérateur intersection carré n aire"}}}, {"key": "2A06", "mappings": {"default": {"default": "opérateur union carré n aire"}}}, {"key": "2A07", "mappings": {"default": {"default": "opérateur deux et logiques"}}}, {"key": "2A08", "mappings": {"default": {"default": "opérateur deux ou logiques"}}}, {"key": "2A09", "mappings": {"default": {"default": "opérateur multiplié par n aire"}}}, {"key": "2A0A", "mappings": {"default": {"default": "sommation modulo deux"}}}, {"key": "2A0B", "mappings": {"default": {"default": "sommation avec intégrale"}}}, {"key": "2A0C", "mappings": {"default": {"default": "intégrale quadruple"}}}, {"key": "2A0D", "mappings": {"default": {"default": "intégrale à partie finie"}}}, {"key": "2A0E", "mappings": {"default": {"default": "intégrale à deux barres horizontales"}}}, {"key": "2A0F", "mappings": {"default": {"default": "moyenne d'intégrale à barre oblique inversée"}}}, {"key": "2A10", "mappings": {"default": {"default": "fonction de circulation"}}}, {"key": "2A11", "mappings": {"default": {"default": "intégrale en sens positif"}}}, {"key": "2A12", "mappings": {"default": {"default": "intégrale de ligne à chemin rectangulaire autour du pôle"}}}, {"key": "2A13", "mappings": {"default": {"default": "intégrale de ligne à chemin semi circulaire autour du pôle"}}}, {"key": "2A14", "mappings": {"default": {"default": "intégrale de ligne évitant le pôle"}}}, {"key": "2A15", "mappings": {"default": {"default": "intégrale autour d'un opérateur point"}}}, {"key": "2A16", "mappings": {"default": {"default": "intégrale quaternion"}}}, {"key": "2A18", "mappings": {"default": {"default": "intégrale au signe multiplié par"}}}, {"key": "2A19", "mappings": {"default": {"default": "intégrale à signe intersection"}}}, {"key": "2A1A", "mappings": {"default": {"default": "intégrale à signe union"}}}, {"key": "2A1B", "mappings": {"default": {"default": "intégrale à barre en chef"}}}, {"key": "2A1C", "mappings": {"default": {"default": "intégrale à barre souscrite"}}}, {"key": "2A1D", "mappings": {"default": {"default": "jointure"}}}, {"key": "2A1E", "mappings": {"default": {"default": "gros opérateur triangle gauche"}}}, {"key": "2A1F", "mappings": {"default": {"default": "composition de schémas en notation z"}}}, {"key": "2A20", "mappings": {"default": {"default": "tubage de schémas en notation z"}}}, {"key": "2A21", "mappings": {"default": {"default": "projection de schémas en notation z"}}}, {"key": "2A22", "mappings": {"default": {"default": "plus surmonté d'un petit cercle"}}}, {"key": "2A23", "mappings": {"default": {"default": "plus surmonté d'un circonflexe"}}}, {"key": "2A24", "mappings": {"default": {"default": "plus surmonté d'un tilde"}}}, {"key": "2A25", "mappings": {"default": {"default": "plus à point souscrit"}}}, {"key": "2A26", "mappings": {"default": {"default": "plus à tilde souscrit"}}}, {"key": "2A27", "mappings": {"default": {"default": "plus à chiffre deux souscrit"}}}, {"key": "2A28", "mappings": {"default": {"default": "plus à triangle noir"}}}, {"key": "2A29", "mappings": {"default": {"default": "moins surmonté d'une virgule"}}}, {"key": "2A2A", "mappings": {"default": {"default": "moins à point souscrit"}}}, {"key": "2A2B", "mappings": {"default": {"default": "moins à points descendants"}}}, {"key": "2A2C", "mappings": {"default": {"default": "moins à points ascendants"}}}, {"key": "2A2D", "mappings": {"default": {"default": "plus dans demi cercle gauche"}}}, {"key": "2A2E", "mappings": {"default": {"default": "plus dans demi cercle droit"}}}, {"key": "2A2F", "mappings": {"default": {"default": "produit vectoriel"}}}, {"key": "2A30", "mappings": {"default": {"default": "multiplication à point en chef"}}}, {"key": "2A31", "mappings": {"default": {"default": "multiplication à barre souscrite"}}}, {"key": "2A32", "mappings": {"default": {"default": "produit semi direct fermé en bas"}}}, {"key": "2A33", "mappings": {"default": {"default": "produit fusionn<PERSON>"}}}, {"key": "2A34", "mappings": {"default": {"default": "multiplication dans demi cercle gauche"}}}, {"key": "2A35", "mappings": {"default": {"default": "multiplication dans demi cercle droit"}}}, {"key": "2A36", "mappings": {"default": {"default": "multiplication cerclé circonflexe"}}}, {"key": "2A37", "mappings": {"default": {"default": "multiplication doublement cerclé"}}}, {"key": "2A38", "mappings": {"default": {"default": "division cerclé"}}}, {"key": "2A39", "mappings": {"default": {"default": "plus dans triangle"}}}, {"key": "2A3A", "mappings": {"default": {"default": "moins dans triangle"}}}, {"key": "2A3B", "mappings": {"default": {"default": "multiplication dans triangle"}}}, {"key": "2A3C", "mappings": {"default": {"default": "produit interieur"}}}, {"key": "2A3D", "mappings": {"default": {"default": "produit intérieur à droite"}}}, {"key": "2A3E", "mappings": {"default": {"default": "composition relationnelle en notation z"}}}, {"key": "2A3F", "mappings": {"default": {"default": "amalgamation ou coproduit"}}}, {"key": "2A40", "mappings": {"default": {"default": "intersection pointée"}}}, {"key": "2A41", "mappings": {"default": {"default": "union à signe moins"}}}, {"key": "2A42", "mappings": {"default": {"default": "union à barre en chef"}}}, {"key": "2A43", "mappings": {"default": {"default": "intersection à barre en chef"}}}, {"key": "2A44", "mappings": {"default": {"default": "intersection avec et logique"}}}, {"key": "2A45", "mappings": {"default": {"default": "union avec ou logique"}}}, {"key": "2A46", "mappings": {"default": {"default": "union par dessus intersection"}}}, {"key": "2A47", "mappings": {"default": {"default": "intersection par dessus union"}}}, {"key": "2A48", "mappings": {"default": {"default": "union par dessus bar par dessus intersection"}}}, {"key": "2A49", "mappings": {"default": {"default": "intersection par dessus trait par dessus union"}}}, {"key": "2A4A", "mappings": {"default": {"default": "deux unions soudées côte à côte"}}}, {"key": "2A4B", "mappings": {"default": {"default": "deux intersections soudées côte à côte"}}}, {"key": "2A4C", "mappings": {"default": {"default": "union fermée à empattements"}}}, {"key": "2A4D", "mappings": {"default": {"default": "intersection fermée à empattements"}}}, {"key": "2A4E", "mappings": {"default": {"default": "deux intersections carrées emboîtées"}}}, {"key": "2A4F", "mappings": {"default": {"default": "deux unions carrées emboîtées"}}}, {"key": "2A50", "mappings": {"default": {"default": "union fermée à empattements et produit fusionné"}}}, {"key": "2A51", "mappings": {"default": {"default": "et logique à point en chef"}}}, {"key": "2A52", "mappings": {"default": {"default": "ou logique à point en chef"}}}, {"key": "2A53", "mappings": {"default": {"default": "double et logique"}}}, {"key": "2A54", "mappings": {"default": {"default": "double ou logique"}}}, {"key": "2A55", "mappings": {"default": {"default": "deux et logiques entrecroisés"}}}, {"key": "2A56", "mappings": {"default": {"default": "deux ou logiques entrecroisés"}}}, {"key": "2A57", "mappings": {"default": {"default": "grand ou pentu"}}}, {"key": "2A58", "mappings": {"default": {"default": "grand et pentu"}}}, {"key": "2A59", "mappings": {"default": {"default": "et et ou logiques entrecroisés"}}}, {"key": "2A5A", "mappings": {"default": {"default": "et logique à hampe médiane"}}}, {"key": "2A5B", "mappings": {"default": {"default": "ou logique à hampe médiane"}}}, {"key": "2A5C", "mappings": {"default": {"default": "et logique à tiret horizontal"}}}, {"key": "2A5D", "mappings": {"default": {"default": "ou logique à tiret horizontal"}}}, {"key": "2A5E", "mappings": {"default": {"default": "et logique à deux barres en chef"}}}, {"key": "2A5F", "mappings": {"default": {"default": "et logique à barre souscrite"}}}, {"key": "2A60", "mappings": {"default": {"default": "et logique à deux barres souscrites"}}}, {"key": "2A61", "mappings": {"default": {"default": "petit v à barre souscrite"}}}, {"key": "2A62", "mappings": {"default": {"default": "ou logique à deux barres en chef"}}}, {"key": "2A63", "mappings": {"default": {"default": "ou logique à deux barres souscrites"}}}, {"key": "2A64", "mappings": {"default": {"default": "antirestriction de domaine en notation z"}}}, {"key": "2A65", "mappings": {"default": {"default": "antirestriction de codomaine en notation z"}}}, {"key": "2A66", "mappings": {"default": {"default": "égal point souscrit"}}}, {"key": "2A67", "mappings": {"default": {"default": "identique à surmonté d'un point"}}}, {"key": "2A68", "mappings": {"default": {"default": "deux barres verticales à trois traverses"}}}, {"key": "2A69", "mappings": {"default": {"default": "trois barres verticales à trois traverses"}}}, {"key": "2A6A", "mappings": {"default": {"default": "opérateur tilde point en chef"}}}, {"key": "2A6B", "mappings": {"default": {"default": "opérateur tilde à points ascendants"}}}, {"key": "2A6C", "mappings": {"default": {"default": "similaire à moins similaire à"}}}, {"key": "2A6D", "mappings": {"default": {"default": "congruent point en chef"}}}, {"key": "2A6E", "mappings": {"default": {"default": "<PERSON>gal astérisque en chef"}}}, {"key": "2A6F", "mappings": {"default": {"default": "presque égal à circonflexe"}}}, {"key": "2A70", "mappings": {"default": {"default": "approximativement égal à ou égal à"}}}, {"key": "2A71", "mappings": {"default": {"default": "plus signe <PERSON> en chef"}}}, {"key": "2A72", "mappings": {"default": {"default": "égal à signe plus en chef"}}}, {"key": "2A73", "mappings": {"default": {"default": "tilde signe égal en chef"}}}, {"key": "2A74", "mappings": {"default": {"default": "double deux points égal à"}}}, {"key": "2A75", "mappings": {"default": {"default": "deux signes égal à consécutifs"}}}, {"key": "2A76", "mappings": {"default": {"default": "trois signes égal à consécutifs"}}}, {"key": "2A77", "mappings": {"default": {"default": "égal à deux points en chef deux points souscrits"}}}, {"key": "2A78", "mappings": {"default": {"default": "équivalent quatre points en chef"}}}, {"key": "2A79", "mappings": {"default": {"default": "inférieur à avec cercle inscrit"}}}, {"key": "2A7A", "mappings": {"default": {"default": "supérieur à avec cercle inscrit"}}}, {"key": "2A7B", "mappings": {"default": {"default": "inférieur à point d'interrogation en chef"}}}, {"key": "2A7C", "mappings": {"default": {"default": "supérieur à point d'interrogation en chef"}}}, {"key": "2A7D", "mappings": {"default": {"default": "plus petit ou égal à pentu"}}}, {"key": "2A7E", "mappings": {"default": {"default": "supérieur à ou égal à pentu"}}}, {"key": "2A7F", "mappings": {"default": {"default": "inférieur à ou égal à pentu point inscrit"}}}, {"key": "2A80", "mappings": {"default": {"default": "supérieur à ou égal à pentu point inscrit"}}}, {"key": "2A81", "mappings": {"default": {"default": "inférieur à ou égal à pentu point en chef"}}}, {"key": "2A82", "mappings": {"default": {"default": "supérieur à ou égal à pentu point en chef"}}}, {"key": "2A83", "mappings": {"default": {"default": "inférieur à ou égal à pentu point en chef droit"}}}, {"key": "2A84", "mappings": {"default": {"default": "supérieur à ou égal à pentu point en chef gauche"}}}, {"key": "2A85", "mappings": {"default": {"default": "inférieur à ou approximatif"}}}, {"key": "2A86", "mappings": {"default": {"default": "supérieur à ou approximatif"}}}, {"key": "2A87", "mappings": {"default": {"default": "inférieur à et pas égal à une ligne"}}}, {"key": "2A88", "mappings": {"default": {"default": "supérieur à et pas égal à une ligne"}}}, {"key": "2A89", "mappings": {"default": {"default": "inférieur à et non approximatif"}}}, {"key": "2A8A", "mappings": {"default": {"default": "supérieur à et non approximatif"}}}, {"key": "2A8B", "mappings": {"default": {"default": "inférieur à par dessus égal à deux lignes par dessus supérieur à"}}}, {"key": "2A8C", "mappings": {"default": {"default": "supérieur à par dessus égal à deux lignes par dessus inférieur à"}}}, {"key": "2A8D", "mappings": {"default": {"default": "inférieur à par dessus similaire à ou égal à"}}}, {"key": "2A8E", "mappings": {"default": {"default": "supérieur à par dessus similaire à ou égal à"}}}, {"key": "2A8F", "mappings": {"default": {"default": "inférieur à par dessus similaire à par dessus supérieur à"}}}, {"key": "2A90", "mappings": {"default": {"default": "supérieur à par dessus similaire à par dessus inférieur à"}}}, {"key": "2A91", "mappings": {"default": {"default": "inférieur à par dessus supérieur à par dessus égal à deux lignes"}}}, {"key": "2A92", "mappings": {"default": {"default": "supérieur à par dessus inférieur à par dessus égal à deux lignes"}}}, {"key": "2A93", "mappings": {"default": {"default": "inférieur à par dessus égal à pentu par dessus supérieur à par dessus égal à pentu"}}}, {"key": "2A94", "mappings": {"default": {"default": "supérieur à par dessus égal à pentu par dessus inférieur à par dessus égal à pentu"}}}, {"key": "2A95", "mappings": {"default": {"default": "Égal à incliné ou inférieur à"}}}, {"key": "2A96", "mappings": {"default": {"default": "Égal à incliné ou supérieur à"}}}, {"key": "2A97", "mappings": {"default": {"default": "égal à pentu ou inférieur à point inscrit"}}}, {"key": "2A98", "mappings": {"default": {"default": "égal à pentu ou supérieur à point inscrit"}}}, {"key": "2A99", "mappings": {"default": {"default": "égal à deux lignes ou inférieur à"}}}, {"key": "2A9A", "mappings": {"default": {"default": "égal à deux lignes ou supérieur à"}}}, {"key": "2A9B", "mappings": {"default": {"default": "inférieur à ou égal à à deux lignes inclinées"}}}, {"key": "2A9C", "mappings": {"default": {"default": "supérieur à ou égal à à deux lignes inclinées"}}}, {"key": "2A9D", "mappings": {"default": {"default": "similaire à ou inférieur à"}}}, {"key": "2A9E", "mappings": {"default": {"default": "similair<PERSON> à ou supérieur à"}}}, {"key": "2A9F", "mappings": {"default": {"default": "similaire à par dessus inférieur à par dessus signe égal"}}}, {"key": "2AA0", "mappings": {"default": {"default": "similaire à par dessus supérieur à par dessus signe égal"}}}, {"key": "2AA1", "mappings": {"default": {"default": "deux inférieur à emboîtés"}}}, {"key": "2AA2", "mappings": {"default": {"default": "deux supérieur à emboîtés"}}}, {"key": "2AA3", "mappings": {"default": {"default": "deux inférieur à emboîtés avec barre souscrite"}}}, {"key": "2AA4", "mappings": {"default": {"default": "deux supérieur à entrecroisés"}}}, {"key": "2AA5", "mappings": {"default": {"default": "supérieur à à côté de inférieur à"}}}, {"key": "2AA6", "mappings": {"default": {"default": "inférieur à fermé d'un arc"}}}, {"key": "2AA7", "mappings": {"default": {"default": "supérieur à fermé d'un arc"}}}, {"key": "2AA8", "mappings": {"default": {"default": "inférieur à fermé d'un arc par dessus égal à pentu"}}}, {"key": "2AA9", "mappings": {"default": {"default": "supérieur à fermé d'un arc par dessus égal à pentu"}}}, {"key": "2AAA", "mappings": {"default": {"default": "plus petit que en fourche"}}}, {"key": "2AAB", "mappings": {"default": {"default": "plus grand que en fourche"}}}, {"key": "2AAC", "mappings": {"default": {"default": "plus petit ou égal que en fourche"}}}, {"key": "2AAD", "mappings": {"default": {"default": "plus grand ou égal que en fourche"}}}, {"key": "2AAE", "mappings": {"default": {"default": "égal et dos d'âne"}}}, {"key": "2AAF", "mappings": {"default": {"default": "précède par dessus signe égal à une ligne"}}}, {"key": "2AB0", "mappings": {"default": {"default": "suit par dessus signe égal à une ligne"}}}, {"key": "2AB1", "mappings": {"default": {"default": "précède par dessus pas égal à une ligne"}}}, {"key": "2AB2", "mappings": {"default": {"default": "suit par dessus pas égal à une ligne"}}}, {"key": "2AB3", "mappings": {"default": {"default": "précède par dessus signe égal"}}}, {"key": "2AB4", "mappings": {"default": {"default": "suit par dessus signe égal"}}}, {"key": "2AB5", "mappings": {"default": {"default": "précède par dessus pas égal à"}}}, {"key": "2AB6", "mappings": {"default": {"default": "suit par dessus pas égal à"}}}, {"key": "2AB7", "mappings": {"default": {"default": "précède par dessus presque égal à"}}}, {"key": "2AB8", "mappings": {"default": {"default": "suit par dessus presque égal à"}}}, {"key": "2AB9", "mappings": {"default": {"default": "précède par dessus non presque égal à"}}}, {"key": "2ABA", "mappings": {"default": {"default": "suit par dessus non presque égal à"}}}, {"key": "2ABB", "mappings": {"default": {"default": "double précède"}}}, {"key": "2ABC", "mappings": {"default": {"default": "double suit"}}}, {"key": "2ABD", "mappings": {"default": {"default": "sous ensemble pointé"}}}, {"key": "2ABE", "mappings": {"default": {"default": "sur ensemble pointé"}}}, {"key": "2ABF", "mappings": {"default": {"default": "sous ensemble plus souscrit"}}}, {"key": "2AC0", "mappings": {"default": {"default": "sur ensemble plus souscrit"}}}, {"key": "2AC1", "mappings": {"default": {"default": "sous ensemble à signe de multiplication souscrit"}}}, {"key": "2AC2", "mappings": {"default": {"default": "sur ensemble à signe de multiplication souscrit"}}}, {"key": "2AC3", "mappings": {"default": {"default": "sous ensemble de ou égal à point en chef"}}}, {"key": "2AC4", "mappings": {"default": {"default": "sur ensemble de ou égal à point en chef"}}}, {"key": "2AC5", "mappings": {"default": {"default": "sous ensemble de par dessus signe égal"}}}, {"key": "2AC6", "mappings": {"default": {"default": "sur ensemble de par dessus signe égal"}}}, {"key": "2AC7", "mappings": {"default": {"default": "sous ensemble de par dessus opérateur tilde"}}}, {"key": "2AC8", "mappings": {"default": {"default": "sur ensemble de par dessus opérateur tilde"}}}, {"key": "2AC9", "mappings": {"default": {"default": "sous ensemble de par dessus presque égal à"}}}, {"key": "2ACA", "mappings": {"default": {"default": "sur ensemble de par dessus presque égal à"}}}, {"key": "2ACB", "mappings": {"default": {"default": "sous ensemble de par dessus pas égal à"}}}, {"key": "2ACC", "mappings": {"default": {"default": "sur ensemble de par dessus pas égal à"}}}, {"key": "2ACD", "mappings": {"default": {"default": "opérateur bo<PERSON><PERSON> carrée ouverte gauche"}}}, {"key": "2ACE", "mappings": {"default": {"default": "opérateur bo<PERSON><PERSON> carrée ouverte droite"}}}, {"key": "2ACF", "mappings": {"default": {"default": "sous ensemble clos"}}}, {"key": "2AD0", "mappings": {"default": {"default": "sur ensemble clos"}}}, {"key": "2AD1", "mappings": {"default": {"default": "sous ensemble clos ou égal à"}}}, {"key": "2AD2", "mappings": {"default": {"default": "sur ensemble clos ou égal à"}}}, {"key": "2AD3", "mappings": {"default": {"default": "sous ensemble par dessus sur ensemble"}}}, {"key": "2AD4", "mappings": {"default": {"default": "sur ensemble par dessus sous ensemble"}}}, {"key": "2AD5", "mappings": {"default": {"default": "sous ensemble par dessus sous ensemble"}}}, {"key": "2AD6", "mappings": {"default": {"default": "sur ensemble par dessus sur ensemble"}}}, {"key": "2AD7", "mappings": {"default": {"default": "sur ensemble à côté de sous ensemble"}}}, {"key": "2AD8", "mappings": {"default": {"default": "sur ensemble chaîné à sous ensemble"}}}, {"key": "2AD9", "mappings": {"default": {"default": "appartient à ouvert vers le bas"}}}, {"key": "2ADA", "mappings": {"default": {"default": "fourche surmontée d'un t"}}}, {"key": "2ADB", "mappings": {"default": {"default": "intersection transversale"}}}, {"key": "2ADC", "mappings": {"default": {"default": "bifurcation (non indépendant)"}}}, {"key": "2ADD", "mappings": {"default": {"default": "non bifurcation (indépendant)"}}}, {"key": "2ADE", "mappings": {"default": {"default": "taquet gauche court"}}}, {"key": "2ADF", "mappings": {"default": {"default": "taquet court vers le bas"}}}, {"key": "2AE0", "mappings": {"default": {"default": "taquet court vers le haut"}}}, {"key": "2AE1", "mappings": {"default": {"default": "perpendiculaire à s"}}}, {"key": "2AE2", "mappings": {"default": {"default": "triple tourniquet droit à barre verticale"}}}, {"key": "2AE3", "mappings": {"default": {"default": "tourniquet gauche à double barre verticale"}}}, {"key": "2AE4", "mappings": {"default": {"default": "double tourniquet gauche à barre verticale"}}}, {"key": "2AE5", "mappings": {"default": {"default": "double tourniquet gauche à double barre verticale"}}}, {"key": "2AE6", "mappings": {"default": {"default": "double barre verticale gauche à traverse"}}}, {"key": "2AE7", "mappings": {"default": {"default": "taquet court vers le bas avec barre en chef"}}}, {"key": "2AE8", "mappings": {"default": {"default": "taquet court vers le haut avec barre souscrite"}}}, {"key": "2AE9", "mappings": {"default": {"default": "taquet court vers le haut par dessus taquet court vers le bas"}}}, {"key": "2AEA", "mappings": {"default": {"default": "double taquet vers le bas"}}}, {"key": "2AEB", "mappings": {"default": {"default": "double taquet vers le haut"}}}, {"key": "2AEC", "mappings": {"default": {"default": "négation à deux traits"}}}, {"key": "2AED", "mappings": {"default": {"default": "négation à deux traits réfléchi"}}}, {"key": "2AEE", "mappings": {"default": {"default": "n'est pas un diviseur de à barre de négation réfléchie"}}}, {"key": "2AEF", "mappings": {"default": {"default": "ligne verticale cercle en chef"}}}, {"key": "2AF0", "mappings": {"default": {"default": "ligne verticale cercle souscrit"}}}, {"key": "2AF1", "mappings": {"default": {"default": "taquet vers le bas surmontant un cercle"}}}, {"key": "2AF2", "mappings": {"default": {"default": "parallèlle à vertical à une traverse"}}}, {"key": "2AF3", "mappings": {"default": {"default": "parallèle à avec opérateur tilde"}}}, {"key": "2AF4", "mappings": {"default": {"default": "relation binaire triple barre verticale"}}}, {"key": "2AF5", "mappings": {"default": {"default": "triple barre verticale à une traverse"}}}, {"key": "2AF6", "mappings": {"default": {"default": "opérateur trois points"}}}, {"key": "2AF7", "mappings": {"default": {"default": "trois inférieur à emboîtés"}}}, {"key": "2AF8", "mappings": {"default": {"default": "trois supérieur à emboîtés"}}}, {"key": "2AF9", "mappings": {"default": {"default": "inférieur à ou égal à et deux lignes inclinées"}}}, {"key": "2AFA", "mappings": {"default": {"default": "supérieur à ou égal à et deux lignes inclinées"}}}, {"key": "2AFB", "mappings": {"default": {"default": "relation binaire triple oblique"}}}, {"key": "2AFC", "mappings": {"default": {"default": "grand opérateur à trois barres verticales"}}}, {"key": "2AFD", "mappings": {"default": {"default": "opérateur double oblique"}}}, {"key": "2AFE", "mappings": {"default": {"default": "barre verticale blanche"}}}, {"key": "2AFF", "mappings": {"default": {"default": "barre verticale blanche n aire"}}}, {"key": "301C", "mappings": {"default": {"default": "trait d'union en esse"}}}, {"key": "FE10", "mappings": {"default": {"default": "forme de présentation de virgule verticale"}}}, {"key": "FE13", "mappings": {"default": {"default": "forme de présentation de deux points vertical"}}}, {"key": "FE14", "mappings": {"default": {"default": "forme de présentation de point virgule vertical"}}}, {"key": "FE15", "mappings": {"default": {"default": "forme de présentation de point d'exclamation vertical"}}}, {"key": "FE16", "mappings": {"default": {"default": "forme de présentation de point d'interrogation vertical"}}}, {"key": "FE19", "mappings": {"default": {"default": "forme de présentation de points de suspension verticaux"}}}, {"key": "FE30", "mappings": {"default": {"default": "forme de présentation de point de conduite double vertical"}}}, {"key": "FE31", "mappings": {"default": {"default": "forme de présentation de tiret cadratin vertical"}}}, {"key": "FE32", "mappings": {"default": {"default": "forme de présentation de tiret demi cadratin vertical"}}}, {"key": "FE33", "mappings": {"default": {"default": "forme de présentation de tiret bas vertical"}}}, {"key": "FE34", "mappings": {"default": {"default": "forme de présentation de tiret bas ondulé vertical"}}}, {"key": "FE45", "mappings": {"default": {"default": "point sésame"}}}, {"key": "FE46", "mappings": {"default": {"default": "point s<PERSON>ame a<PERSON>"}}}, {"key": "FE49", "mappings": {"default": {"default": "tiret haut en pointillés"}}}, {"key": "FE4A", "mappings": {"default": {"default": "tiret haut à point central"}}}, {"key": "FE4B", "mappings": {"default": {"default": "tiret haut ondulé"}}}, {"key": "FE4C", "mappings": {"default": {"default": "tiret haut double ondulé"}}}, {"key": "FE4D", "mappings": {"default": {"default": "tiret bas avec pointillés"}}}, {"key": "FE4E", "mappings": {"default": {"default": "tiret bas à point central"}}}, {"key": "FE4F", "mappings": {"default": {"default": "tiret bas ondulé"}}}, {"key": "FE50", "mappings": {"default": {"default": "virgule minuscule"}}}, {"key": "FE52", "mappings": {"default": {"default": "point minuscule"}}}, {"key": "FE54", "mappings": {"default": {"default": "point virgule minuscule"}}}, {"key": "FE55", "mappings": {"default": {"default": "deux points minuscule"}}}, {"key": "FE56", "mappings": {"default": {"default": "point d'interrogation minuscule"}}}, {"key": "FE57", "mappings": {"default": {"default": "point d'exclamation minuscule"}}}, {"key": "FE58", "mappings": {"default": {"default": "trait d'union cadratin minuscule"}}}, {"key": "FE5F", "mappings": {"default": {"default": "croisillon minuscule"}}}, {"key": "FE60", "mappings": {"default": {"default": "perluète minuscule"}}}, {"key": "FE61", "mappings": {"default": {"default": "astérisque minuscule"}}}, {"key": "FE62", "mappings": {"default": {"default": "plus minuscule"}}}, {"key": "FE63", "mappings": {"default": {"default": "tiret minuscule"}}}, {"key": "FE64", "mappings": {"default": {"default": "inférieur à minuscule"}}}, {"key": "FE65", "mappings": {"default": {"default": "supérieur à minuscule"}}}, {"key": "FE66", "mappings": {"default": {"default": "égal à minuscule"}}}, {"key": "FE68", "mappings": {"default": {"default": "integer divide"}}}, {"key": "FE69", "mappings": {"default": {"default": "symbole dollar minuscule"}}}, {"key": "FE6A", "mappings": {"default": {"default": "symbole pour cent minuscule"}}}, {"key": "FE6B", "mappings": {"default": {"default": "arrobe minuscule"}}}, {"key": "FF01", "mappings": {"default": {"default": "point d'exclamation"}}}, {"key": "FF02", "mappings": {"default": {"default": "guil<PERSON><PERSON>"}}}, {"key": "FF03", "mappings": {"default": {"default": "croisillon"}}}, {"key": "FF04", "mappings": {"default": {"default": "symbole dollar"}}}, {"key": "FF05", "mappings": {"default": {"default": "symbole pour cent"}}}, {"key": "FF06", "mappings": {"default": {"default": "perluète"}}}, {"key": "FF07", "mappings": {"default": {"default": "apostrophe"}}}, {"key": "FF0A", "mappings": {"default": {"default": "astérisque"}}}, {"key": "FF0B", "mappings": {"default": {"default": "plus"}}}, {"key": "FF0C", "mappings": {"default": {"default": "virgule"}}}, {"key": "FF0D", "mappings": {"default": {"default": "tiret"}}}, {"key": "FF0E", "mappings": {"default": {"default": "point"}}}, {"key": "FF0F", "mappings": {"default": {"default": "barre oblique"}}}, {"key": "FF1A", "mappings": {"default": {"default": "deux points colon"}}}, {"key": "FF1B", "mappings": {"default": {"default": "point virgule"}}}, {"key": "FF1C", "mappings": {"default": {"default": "inférieur à"}}}, {"key": "FF1D", "mappings": {"default": {"default": "égal à"}}}, {"key": "FF1E", "mappings": {"default": {"default": "supérieur à"}}}, {"key": "FF1F", "mappings": {"default": {"default": "point d'interrogation"}}}, {"key": "FF20", "mappings": {"default": {"default": "arrobe"}}}, {"key": "FF3C", "mappings": {"default": {"default": "barre oblique inversée"}}}, {"key": "FF3E", "mappings": {"default": {"default": "accent circonflexe"}}}, {"key": "FF3F", "mappings": {"default": {"default": "tiret bas"}}}, {"key": "FF40", "mappings": {"default": {"default": "accent grave"}}}, {"key": "FF5C", "mappings": {"default": {"default": "barre verticale"}}}, {"key": "FF5E", "mappings": {"default": {"default": "tilde"}}}, {"key": "FFE0", "mappings": {"default": {"default": "symbole centime"}}}, {"key": "FFE1", "mappings": {"default": {"default": "symbole livre"}}}, {"key": "FFE2", "mappings": {"default": {"default": "négation"}}}, {"key": "FFE3", "mappings": {"default": {"default": "macron *"}}}, {"key": "FFE4", "mappings": {"default": {"default": "ligne brisée"}}}, {"key": "FFE5", "mappings": {"default": {"default": "symbole yen"}}}, {"key": "FFE6", "mappings": {"default": {"default": "symbole won"}}}, {"key": "FFE8", "mappings": {"default": {"default": "ligne verticale mince"}}}, {"key": "FFED", "mappings": {"default": {"default": "carré noir"}}}, {"key": "FFEE", "mappings": {"default": {"default": "cercle blanc"}}}], "fr/symbols/math_whitespace.min": [{"locale": "fr"}, {"key": "0020", "mappings": {"default": {"default": "espace"}}}, {"key": "00A0", "mappings": {"default": {"default": " "}}}, {"key": "00AD", "mappings": {"default": {"default": "trait d'union conditionnel"}}}, {"key": "2000", "mappings": {"default": {"default": "demi cadratin"}}}, {"key": "2001", "mappings": {"default": {"default": "cadratin"}}}, {"key": "2002", "mappings": {"default": {"default": ""}}}, {"key": "2003", "mappings": {"default": {"default": ""}}}, {"key": "2004", "mappings": {"default": {"default": ""}}}, {"key": "2005", "mappings": {"default": {"default": ""}}}, {"key": "2006", "mappings": {"default": {"default": "sixième de cadratin"}}}, {"key": "2007", "mappings": {"default": {"default": ""}}}, {"key": "2008", "mappings": {"default": {"default": ""}}}, {"key": "2009", "mappings": {"default": {"default": ""}}}, {"key": "200A", "mappings": {"default": {"default": ""}}}, {"key": "200B", "mappings": {"default": {"default": ""}}}, {"key": "200C", "mappings": {"default": {"default": "antiliant sans chasse"}}}, {"key": "200D", "mappings": {"default": {"default": "liant sans chasse"}}}, {"key": "200E", "mappings": {"default": {"default": "marque gauche à droite"}}}, {"key": "200F", "mappings": {"default": {"default": "marque droite à gauche"}}}, {"key": "2028", "mappings": {"default": {"default": "sépar<PERSON>ur de lignes"}}}, {"key": "2029", "mappings": {"default": {"default": "séparateur de paragraphes"}}}, {"key": "202A", "mappings": {"default": {"default": "enchâssement gauche à droite"}}}, {"key": "202B", "mappings": {"default": {"default": "enchâssement droite à gauche"}}}, {"key": "202C", "mappings": {"default": {"default": "dépilement de formatage directionnel"}}}, {"key": "202D", "mappings": {"default": {"default": "forçage gauche à droite"}}}, {"key": "202E", "mappings": {"default": {"default": "forçage droite à gauche"}}}, {"key": "202F", "mappings": {"default": {"default": "espace insécable étroite"}}}, {"key": "205F", "mappings": {"default": {"default": "espace moyenne mathématique"}}}, {"key": "2060", "mappings": {"default": {"default": ""}}}, {"key": "2061", "mappings": {"default": {"default": "de"}}}, {"key": "2062", "mappings": {"default": {"default": "multiplié par"}}}, {"key": "2063", "mappings": {"default": {"default": "virgule"}}}, {"key": "2064", "mappings": {"default": {"default": "plus"}}}, {"key": "206A", "mappings": {"default": {"default": "inhibiteur d'échange symétrique"}}}, {"key": "206B", "mappings": {"default": {"default": "activateur d'échange symétrique"}}}, {"key": "206E", "mappings": {"default": {"default": "sélecteur de formes numérales nationales"}}}, {"key": "206F", "mappings": {"default": {"default": "sélecteur de formes numérales de référence"}}}, {"key": "FEFF", "mappings": {"default": {"default": "espace ultrafine"}}}, {"key": "FFF9", "mappings": {"default": {"default": "ancre d'annotation interlinéaire"}}}, {"key": "FFFA", "mappings": {"default": {"default": "séparateur d'annotation interlinéaire"}}}, {"key": "FFFB", "mappings": {"default": {"default": "terminateur d'annotation interlinéaire"}}}], "fr/symbols/other_stars.min": [{"locale": "fr"}, {"key": "23E8", "mappings": {"default": {"default": "symbole exposant décimal"}}}, {"key": "2605", "mappings": {"default": {"default": "étoile noire"}}}, {"key": "2606", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> blanche"}}}, {"key": "26AA", "mappings": {"default": {"default": "cercle moyen blanc"}}}, {"key": "26AB", "mappings": {"default": {"default": "cercle moyen noir"}}}, {"key": "2705", "mappings": {"default": {"default": "signe de vérification blanc gras"}}}, {"key": "2713", "mappings": {"default": {"default": "signe de vérification"}}}, {"key": "2714", "mappings": {"default": {"default": "gros signe de vérification"}}}, {"key": "2715", "mappings": {"default": {"default": "x de multiplication"}}}, {"key": "2716", "mappings": {"default": {"default": "gros x de multiplication"}}}, {"key": "2717", "mappings": {"default": {"default": "x de bulletin de vote"}}}, {"key": "2718", "mappings": {"default": {"default": "gros x de bulletin de vote"}}}, {"key": "271B", "mappings": {"default": {"default": "croix percée d'un carré"}}}, {"key": "271C", "mappings": {"default": {"default": "grosse croix percée d'un carré"}}}, {"key": "2720", "mappings": {"default": {"default": "croix de malte"}}}, {"key": "2721", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2722", "mappings": {"default": {"default": "croix larmée"}}}, {"key": "2723", "mappings": {"default": {"default": "croix pommetée"}}}, {"key": "2724", "mappings": {"default": {"default": "grosse croix pommetée"}}}, {"key": "2725", "mappings": {"default": {"default": "croix tréf<PERSON>e"}}}, {"key": "2726", "mappings": {"default": {"default": "Étoile noire à quatre branches"}}}, {"key": "2727", "mappings": {"default": {"default": "Étoile blanche à quatre branches"}}}, {"key": "2728", "mappings": {"default": {"default": "scintillements"}}}, {"key": "2729", "mappings": {"default": {"default": "<PERSON><PERSON>ile blanche à contour accentué"}}}, {"key": "272A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> blanche cerc<PERSON>e"}}}, {"key": "272B", "mappings": {"default": {"default": "molette noire"}}}, {"key": "272C", "mappings": {"default": {"default": "molette blanche"}}}, {"key": "272D", "mappings": {"default": {"default": "Étoile noire avec contour"}}}, {"key": "272E", "mappings": {"default": {"default": "Étoile noire avec gros contour"}}}, {"key": "272F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2730", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> blanche ombrée"}}}, {"key": "2731", "mappings": {"default": {"default": "gros astérisque"}}}, {"key": "2732", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> percé"}}}, {"key": "2733", "mappings": {"default": {"default": "astérisque à huit branches"}}}, {"key": "2734", "mappings": {"default": {"default": "Étoile noire à huit branches"}}}, {"key": "2735", "mappings": {"default": {"default": "Étoile rayonnante à huit branches"}}}, {"key": "2736", "mappings": {"default": {"default": "étoile noire à six branches"}}}, {"key": "2739", "mappings": {"default": {"default": "Étoile noire à douze branches"}}}, {"key": "273A", "mappings": {"default": {"default": "astérisque à seize branches"}}}, {"key": "273B", "mappings": {"default": {"default": "astéris<PERSON> larm<PERSON>"}}}, {"key": "273C", "mappings": {"default": {"default": "astérisque larmé percé"}}}, {"key": "273D", "mappings": {"default": {"default": "gros astérisque larmé"}}}, {"key": "273E", "mappings": {"default": {"default": "sixtefeuille blanche et noire"}}}, {"key": "273F", "mappings": {"default": {"default": "quintefeuille noire"}}}, {"key": "2740", "mappings": {"default": {"default": "quinte<PERSON>uille blanche"}}}, {"key": "2741", "mappings": {"default": {"default": "double quartefeuille noire avec contour"}}}, {"key": "2742", "mappings": {"default": {"default": "<PERSON><PERSON>ile cerclée percée à huit branches"}}}, {"key": "2743", "mappings": {"default": {"default": "gros astérisque rayonnant larmé"}}}, {"key": "2744", "mappings": {"default": {"default": "flocon de neige"}}}, {"key": "2745", "mappings": {"default": {"default": "flocon de neige à trois folioles transpercé"}}}, {"key": "2746", "mappings": {"default": {"default": "gros flocon de neige à chevrons"}}}, {"key": "2747", "mappings": {"default": {"default": "Étincellement"}}}, {"key": "2748", "mappings": {"default": {"default": "gros étincellement"}}}, {"key": "2749", "mappings": {"default": {"default": "astérisque pommeté"}}}, {"key": "274A", "mappings": {"default": {"default": "astérisque hélice à huit branches larmées"}}}, {"key": "274B", "mappings": {"default": {"default": "gros astérisque hélice à huit branches larmées"}}}, {"key": "274C", "mappings": {"default": {"default": "croix grasse"}}}, {"key": "274D", "mappings": {"default": {"default": "cercle blanc ombré"}}}], "fr/units/area.min": [{"locale": "fr"}, {"key": "sq", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "sq inch", "mappings": {"default": {"default": "pouce carré", "plural": "pouces carré"}}}, {"key": "sq rd", "mappings": {"default": {"default": "rod carré", "plural": "rods carré"}}}, {"key": "sq ft", "mappings": {"default": {"default": "pied carré", "plural": "pieds carré"}}}, {"key": "sq yd", "mappings": {"default": {"default": "verge car<PERSON>e", "plural": "verges carrée"}}}, {"key": "sq mi", "mappings": {"default": {"default": "mile carré", "plural": "miles carré"}}}, {"key": "acr", "mappings": {"default": {"default": "acre"}}}, {"key": "ha", "mappings": {"default": {"default": "hectare"}}}], "fr/units/currency.min": [{"locale": "fr"}, {"key": "$", "mappings": {"default": {"default": "dollar"}}}, {"key": "£", "mappings": {"default": {"default": "livre"}}}, {"key": "¥", "mappings": {"default": {"default": "yen"}}}, {"key": "€", "mappings": {"default": {"default": "euro"}}}, {"key": "₡", "mappings": {"default": {"default": "colon"}}}, {"key": "₢", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON>"}}}, {"key": "₣", "mappings": {"default": {"default": "franc"}}}, {"key": "₤", "mappings": {"default": {"default": "lire"}}}, {"key": "₥", "mappings": {"default": {"default": "millième"}}}, {"key": "₦", "mappings": {"default": {"default": "naira"}}}, {"key": "₧", "mappings": {"default": {"default": "peseta"}}}, {"key": "₨", "mappings": {"default": {"default": "roupie"}}}, {"key": "₩", "mappings": {"default": {"default": "won"}}}, {"key": "₪", "mappings": {"default": {"default": "shekel"}}}, {"key": "₫", "mappings": {"default": {"default": "dong"}}}, {"key": "₭", "mappings": {"default": {"default": "kip"}}}, {"key": "₮", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₯", "mappings": {"default": {"default": "drac<PERSON>e"}}}, {"key": "₰", "mappings": {"default": {"default": "pfennig", "plural": "pfennig"}}}, {"key": "₱", "mappings": {"default": {"default": "peso"}}}, {"key": "₲", "mappings": {"default": {"default": "guarani"}}}, {"key": "₳", "mappings": {"default": {"default": "austral"}}}, {"key": "₴", "mappings": {"default": {"default": "hryvnia"}}}, {"key": "₵", "mappings": {"default": {"default": "cedi"}}}, {"key": "₸", "mappings": {"default": {"default": "tenge", "plural": "tenge"}}}, {"key": "₺", "mappings": {"default": {"default": "livre turque", "plural": "livres turque"}}}, {"key": "元", "mappings": {"default": {"default": "yuan"}}}, {"key": "¢", "mappings": {"default": {"default": "centime"}}}], "fr/units/energy.min": [{"locale": "fr"}, {"key": "W", "mappings": {"default": {"default": "watt"}}}, {"key": "kwh", "mappings": {"default": {"default": "kilowattheure"}}}, {"key": "J", "mappings": {"default": {"default": "joule"}}}, {"key": "N", "mappings": {"default": {"default": "newton"}}}, {"key": "A", "mappings": {"default": {"default": "ampère"}}}, {"key": "V", "mappings": {"default": {"default": "volt"}}}, {"key": "ohm", "mappings": {"default": {"default": "ohm"}}}, {"key": "Ω", "mappings": {"default": {"default": "ohm"}}}], "fr/units/length.min": [{"locale": "fr"}, {"key": "m", "mappings": {"default": {"default": "m<PERSON>tre"}}}, {"key": "ft", "mappings": {"default": {"default": "pied"}}}, {"key": "in", "mappings": {"default": {"default": "pouce"}}}, {"key": "mi", "mappings": {"default": {"default": "mile"}}}, {"key": "yd", "mappings": {"default": {"default": "yard"}}}, {"key": "link", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "rod", "mappings": {"default": {"default": "rod"}}}, {"key": "chain", "mappings": {"default": {"default": "cha<PERSON><PERSON>"}}}, {"key": "furlong", "mappings": {"default": {"default": "furlong"}}}, {"key": "n.m.", "mappings": {"default": {"default": "mille marin"}}}], "fr/units/memory.min": [{"locale": "fr"}, {"key": "b", "mappings": {"default": {"default": "bit"}}}, {"key": "B", "mappings": {"default": {"default": "octet"}}, "si": true, "names": ["o"]}, {"key": "KB", "mappings": {"default": {"default": "kilooctet"}}, "names": ["Ko"]}], "fr/units/other.min": [{"locale": "fr"}, {"key": "doz", "mappings": {"default": {"default": "douzaine"}}, "names": ["douz", "douz."]}], "fr/units/speed.min": [{"locale": "fr"}, {"key": "kt", "mappings": {"default": {"default": "nœud marin"}}}, {"key": "rpm", "mappings": {"default": {"plural": "tours par minute", "default": "tour par minute"}}, "names": ["tpm"]}, {"key": "kmh", "mappings": {"default": {"plural": "kilomètres par heure", "default": "kilomètre par heure"}}}, {"key": "mph", "mappings": {"default": {"plural": "miles par heure", "default": "mile par heure"}}}], "fr/units/temperature.min": [{"locale": "fr"}, {"key": "F", "mappings": {"default": {"default": "farad"}}}, {"key": "C", "mappings": {"default": {"default": "cel<PERSON>"}}}, {"key": "K", "mappings": {"default": {"default": "kelvin"}}}], "fr/units/time.min": [{"locale": "fr"}, {"key": "s", "mappings": {"default": {"default": "seconde"}}}, {"key": "″", "mappings": {"default": {"default": "seconde"}}}, {"key": "min", "mappings": {"default": {"default": "minute"}}}, {"key": "°", "mappings": {"default": {"default": "de<PERSON><PERSON>"}}}, {"key": "h", "mappings": {"default": {"default": "heure"}}}], "fr/units/volume.min": [{"locale": "fr"}, {"key": "bbl", "mappings": {"default": {"default": "baril"}}}, {"key": "gal", "mappings": {"default": {"default": "gallon"}}}, {"key": "pt", "mappings": {"default": {"default": "pinte"}}}, {"key": "qt", "mappings": {"default": {"default": "quart"}}}, {"key": "tbsp", "mappings": {"default": {"plural": "cuillères à soupe", "default": "cuillèe à soupe"}}}, {"key": "tsp", "mappings": {"default": {"plural": "cuillères à thé", "default": "cuillère à thé"}}}, {"key": "l", "mappings": {"default": {"default": "litre"}}}, {"key": "cu", "mappings": {"default": {"default": "cubique"}}}, {"key": "cu inch", "mappings": {"default": {"default": "pouce cube"}}}, {"key": "cu ft", "mappings": {"default": {"default": "pied cube"}}}, {"key": "cu yd", "mappings": {"default": {"default": "yard cube"}}}, {"key": "fl. oz.", "mappings": {"default": {"default": "once liquide"}}}, {"key": "fluid dram", "mappings": {"default": {"default": "drachme liquide"}}}, {"key": "cup", "mappings": {"default": {"default": "tasse"}}}, {"key": "cc", "mappings": {"default": {"default": "centimètre cube"}}}], "fr/units/weight.min": [{"locale": "fr"}, {"key": "lb", "mappings": {"default": {"default": "livre"}}}, {"key": "oz", "mappings": {"default": {"default": "once"}}}, {"key": "gr", "mappings": {"default": {"default": "gramme"}}}, {"key": "g", "mappings": {"default": {"default": "gramme"}}}, {"key": "t", "mappings": {"default": {"default": "tonne"}}}, {"key": "dram", "mappings": {"default": {"default": "drac<PERSON>e"}}}, {"key": "st", "mappings": {"default": {"default": "stone"}}}, {"key": "qtr", "mappings": {"default": {"default": "quarter"}}}, {"key": "LT", "mappings": {"default": {"default": "long ton"}}}, {"key": "mcg", "mappings": {"default": {"default": "microgramme"}}}, {"key": "cwt", "mappings": {"default": {"default": "quintal"}}, "names": ["qq"]}], "fr/rules/clearspeak_french.min": {"locale": "fr", "domain": "clearspeak", "modality": "speech", "inherits": "romance", "rules": [["Precondition", "superscript-ordinal-letter", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\"]"], ["Precondition", "superscript-ordinal-number", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"number\"", "children/*[2][@role=\"integer\"]", "children/*[2][text()!=\"2\"]", "children/*[2][text()!=\"3\"]"], ["Precondition", "superscript-ordinal-cap", "Exponent_Ordinal", "self::superscript", "name(children/*[2])=\"identifier\"", "children/*[2][@role=\"latinletter\"]", "children/*[2][@category=\"Lu\"]"], ["Precondition", "element-noarticle", "default", "self::infixop[contains(@role, \"element\")]"], ["Precondition", "element", "SetMemberSymbol_Element", "self::infixop[contains(@role, \"element\")]"], ["Specialized", "element", "SetMemberSymbol_Element", "SetMemberSymbol_Belongs"]]}, "fr/rules/clearspeak_french_actions.min": {"locale": "fr", "domain": "clearspeak", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "font", "[n] . (grammar:ignoreFont=@font); [t] @font (pause:short, grammar:localFont)"], ["Action", "ellipsis", "[t] \"et ainsi de suite\""], ["Action", "ellipsis-andsoon", "[t] \"et ainsi de suite jusqu'à\""], ["Action", "vbar-evaluated", "[n] children/*[1] (pause:short); [t] \"évalué à\"; [n] content/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-evaluated-both", "[n] children/*[1] (pause:short); [t] \"évalué à\"; [n] content/*[1]/children/*[2] (pause:short); [t] \"moins la même expression évaluée à\"; [n] content/*[1]/children/*[1]/children/*[2] (pause:short)"], ["Action", "vbar-such-that", "[t] \"tel que\""], ["Action", "vbar-divides", "[t] \"diviseur de\""], ["Action", "vbar-always-divides", "[t] \"diviseur de\""], ["Action", "vbar-given", "[t] \"sachant\""], ["Action", "element-noarticle", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2] (grammar:noArticle)"], ["Action", "element", "[n] children/*[1]; [n] content/*[1]; [n] children/*[2] (grammar:!noArticle)"], ["Action", "member", "[t] \"est un\""], ["Action", "member-element", "[t] \"est un élément de\""], ["Action", "member-in", "[t] \"est dans\""], ["Action", "member-belongs", "[t] \"appartient à\""], ["Action", "not-member", "[t] \"n'est pas un\""], ["Action", "not-member-element", "[t] \"n'est pas un élément de\""], ["Action", "not-member-in", "[t] \"n'est pas dans\""], ["Action", "not-member-belongs", "[t] \"n'appartient pas à\""], ["Action", "set-member", "[t] \"est un\""], ["Action", "set-member-element", "[t] \"est un élément de\""], ["Action", "set-member-in", "[t] \"est dans\""], ["Action", "set-member-belongs", "[t] \"appartenant à\""], ["Action", "set-not-member", "[t] \"n'est pas un\""], ["Action", "set-not-member-in", "[t] \"n'est pas dans\""], ["Action", "set-not-member-element", "[t] \"n'est pas un élément de\""], ["Action", "set-not-member-belongs", "[t] \"n'appartenant pas à\""], ["Action", "appl", "[n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "appl-simple", "[n] children/*[1]; [t] \"de\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-simple-fenced", "[n] children/*[1]; [t] \"de\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "appl-times", "[p] (pause:short); [n] children/*[1]; [t] \"fois\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-simple-arg", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-prefix-embell", "[p] (pause:short); [n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-fenced-or-frac-arg", "[p] (pause:short); [n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript", "[p] (pause:short); [n] children/*[1]; [t] \"de\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-ln", "[n] children/*[1]; [n] children/*[2]"], ["Action", "function-ln-pause", "[n] children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-ln-of", "[n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-as-exp", "[n] children/*[1]; [t] \"de\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-subscript-as-exp", "[n] children/*[1]; [t] \"de\" (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-hyper", "[p] (pause:short); [n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-inverse", "[p] (pause:short); [n] children/*[1]/children/*[1]; [t] \"inverse de\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-reciprocal", "[p] (pause:short); [t] \"la reciproque de\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-reciprocal-simple", "[p] (pause:short); [t] \"la reciproque de\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "appl-triginverse", "[p] (pause:short); [n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple", "[p] (pause:short); [t] \"arc\"; [n] children/*[1]/children/*[1]; [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc-simple-fenced", "[p] (pause:short); [t] \"arc\"; [n] children/*[1]/children/*[1] (pause:short); [n] children/*[2] (pause:short)"], ["Action", "function-prefix-arc", "[p] (pause:short); [t] \"arc\"; [n] children/*[1]/children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "function-inverse", "[n] children/*[1]; [t] \"inverse\""], ["Action", "function-reciprocal", "[t] \"la reciproque de\"; [n] children/*[1]"], ["Action", "superscript", "[n] children/*[1]; [t] \"à l'exposant\" (pause:short); [n] children/*[2] (pause:short); [t] \"fin exposant\" (pause:short)"], ["Action", "superscript-simple-exponent", "[n] children/*[1]; [t] \"à la puissance\"; [n] children/*[2] (pause:medium)"], ["Action", "superscript-simple-exponent-end", "[n] children/*[1]; [t] \"à la puissance\"; [n] children/*[2] (pause:medium)"], ["Action", "superscript-simple-function", "[n] . (grammar:functions_none)"], ["Action", "superscript-ordinal-letter", "[n] children/*[1]; [t] \"à la\"; [n] children/*[2] (grammar:ordinal); [t] \"puissance\" (pause:medium)"], ["Action", "superscript-ordinal-number", "[n] children/*[1]; [t] \"à la\"; [n] children/*[2] (grammar:ordinal); [t] \"puissance\" (pause:medium)"], ["Action", "superscript-ordinal-cap", "[n] children/*[1]; [t] \"à la puissance\"; [n] children/*[2] (pause:medium)"], ["Action", "exponent", "[n] text() (join:\"-\"); [t] \"ième\""], ["Action", "exponent-number", "[t] CSFordinalExponent"], ["Action", "exponent-ordinal", "[t] CSFwordOrdinal"], ["Action", "exponent-ordinal-zero", "[t] \"zero\""], ["Action", "square", "[n] children/*[1]; [t] \"au carré\""], ["Action", "cube", "[n] children/*[1]; [t] \"au cube\""], ["Action", "fences-points", "[t] \"le point avec coordonées\"; [n] children/*[1]"], ["Action", "fences-auto-interval", "[t] \"un intervalle de\"; [n] children/*[1]/children/*[1]; [t] \"à\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "fences-interval", "[t] \"un intervalle de\"; [n] children/*[1]/children/*[1]; [t] \"à\"; [n] children/*[1]/children/*[3] (pause:short); [n] . (grammar:interval)"], ["Action", "interval-open", "[t] \"sans inclure\"; [n] children/*[1]/children/*[1]; [t] \"ni\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open", "[t] \"avec\"; [n] children/*[1]/children/*[1]; [t] \"inclus\" (pause:short); [t] \"mais sans inclure\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-open-closed", "[t] \"sans inclure\"; [n] children/*[1]/children/*[1] (pause:short); [t] \"mais avec\"; [n] children/*[1]/children/*[3]; [t] \"inclus\""], ["Action", "interval-closed", "[t] \"avec\"; [n] children/*[1]/children/*[1]; [t] \"et\"; [n] children/*[1]/children/*[3]; [t] \"inclus\""], ["Action", "interval-open-inf-r", "[t] \"sans inclure\"; [n] children/*[1]/children/*[1]"], ["Action", "interval-open-inf-l", "[t] \"sans inclure\"; [n] children/*[1]/children/*[3]"], ["Action", "interval-closed-open-inf", "[t] \"avec\"; [n] children/*[1]/children/*[1]; [t] \"inclus\""], ["Action", "interval-open-closed-inf", "[t] \"avec\"; [n] children/*[1]/children/*[3]; [t] \"inclus\""], ["Action", "set-empty", "[t] \"ensemble vide\""], ["Action", "set-extended", "[t] \"ensemble des\"; [n] children/*[1]/children/*[1]; [t] \"tel que\"; [n] children/*[1]/children/*[3]"], ["Action", "set-collection", "[t] \"ensemble\"; [n] children/*[1]"], ["Action", "set-extended-woall", "[t] \"ensemble de\"; [n] children/*[1]/children/*[1]; [t] \"tel que\"; [n] children/*[1]/children/*[3]"], ["Action", "subscript", "[p] (pause:short); [n] children/*[1]; [t] \"sub\"; [n] children/*[2] (pause:short)"], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "subscript-index", "[n] children/*[1]; [t] \"sub\"; [n] children/*[2]"], ["Action", "fraction", "[p] (pause:short); [t] \"fraction avec numérateur\"; [n] children/*[1] (pause:short); [t] \"et dénominateur\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-none", "[p] (pause:short); [t] \"fraction avec numérateur\"; [n] children/*[1] (pause:short); [t] \"et dénominateur\"; [n] children/*[2] (pause:short)"], ["Action", "simple-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "simple-text-fraction", "[p] (pause:short); [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "vulgar-fraction", "[t] CSFvulgarFraction"], ["Action", "fraction-over", "[p] (pause:short); [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-overendfrac", "[p] (pause:short); [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short); [t] \"fin fraction\" (pause:short)"], ["Action", "fraction-fracover", "[p] (pause:short); [t] \"fraction\"; [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-per", "[p] (pause:short); [n] children/*[1]; [t] \"par\"; [n] children/*[2] (pause:short)"], ["Action", "fraction-general<PERSON><PERSON><PERSON>", "[p] (pause:short); [t] \"fraction avec numérateur\"; [n] children/*[1] (pause:short); [t] \"et dénominateur\"; [n] children/*[2] (pause:short); [t] \"fin fraction\" (pause:short)"], ["Action", "fraction-general", "[p] (pause:short); [t] \"fraction avec numérateur\"; [n] children/*[1] (pause:short); [t] \"et dénominateur\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-ordinal", "[t] CSFvulgarFraction"], ["Action", "fraction-endfrac", "[p] (pause:short); [n] . (grammar:endfrac); [t] \"fin fraction\" (pause:short)"], ["Action", "vulgar-fraction-endfrac", "[p] (pause:short); [n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "simple-vulgar-fraction-endfrac", "[t] CSFvulgarFraction"], ["Action", "sqrt", "[t] \"la racine carrée de\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested", "[p] (pause:\"short\"); [t] \"la racine carrée de\"; [n] children/*[1] (pause:short)"], ["Action", "negative-sqrt", "[t] \"la racine carrée négative de\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "negative-sqrt-default", "[p] (pause:\"short\"); [t] \"la racine carrée négative de\"; [n] children/*[1]/children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus", "[t] \"la racine carrée positive de\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus", "[p] (pause:\"short\"); [t] \"la racine carrée positive de\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-plus-minus-posnegsqrootend", "[t] \"la racine carrée positive de\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-nested-plus-minus-posnegsqrootend", "[p] (pause:\"short\"); [t] \"la racine carrée positive de\"; [n] children/*[1] (pause:short)"], ["Action", "sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"fin racine\" (pause:short)"], ["Action", "negative-sqrt-endroot", "[n] . (grammar:?EndRoot); [t] \"fin racine\" (pause:short)"], ["Action", "sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"fin racine\" (pause:short)"], ["Action", "negative-sqrt-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"fin racine\" (pause:short)"], ["Action", "cubic", "[t] \"la racine cubique de\"; [n] children/*[2] (pause:short)"], ["Action", "cubic-nested", "[p] (pause:short); [t] \"la racine cubique de\"; [n] children/*[2] (pause:short)"], ["Action", "root", "[t] \"la\"; [n] children/*[1] (grammar:ordinal); [t] \"racine de\"; [n] children/*[2] (pause:short)"], ["Action", "root-nested", "[p] (pause:short); [t] \"la\"; [n] children/*[1] (grammar:ordinal); [t] \"racine de\"; [n] children/*[2] (pause:short)"], ["Action", "root-endroot", "[n] . (grammar:?EndRoot); [t] \"fin racine\" (pause:short)"], ["Action", "root-posnegsqrootend", "[n] . (grammar:?EndRoot); [t] \"fin racine\" (pause:short)"], ["Action", "negative", "[t] \"négatif\"; [n] children/*[1]"], ["Action", "positive", "[t] \"positif\"; [n] children/*[1]"], ["Action", "angle-measure", "[t] \"la mesure de l'\" (join:\"\"); [n] content/*[1]; [n] children/*[2] (grammar:angle)"], ["Action", "set-prefix-operators", "[t] \"le\"; [n] .; [t] \"de\""], ["Action", "division", "[n] children/*[1]; [t] \"divisé par\"; [n] children/*[2]"], ["Action", "natural-numbers", "[t] \"les\" (grammar:article); [t] \"nombres entier naturel\""], ["Action", "integers", "[t] \"les\" (grammar:article); [t] \"nombres entiers\""], ["Action", "rational-numbers", "[t] \"les\" (grammar:article); [t] \"nombres rationnels\""], ["Action", "real-numbers", "[t] \"les\" (grammar:article); [t] \"nombres réels\""], ["Action", "complex-numbers", "[t] \"les\" (grammar:article); [t] \"nombres complexes\""], ["Action", "natural-numbers-with-zero", "[t] \"les\" (grammar:article); [t] \"nombres entiers naturel avec zero\""], ["Action", "positive-integers", "[t] \"les\" (grammar:article); [t] \"nombres entiers positif\""], ["Action", "negative-integers", "[t] \"les\" (grammar:article); [t] \"nombres entiers négatif\""], ["Action", "positive-rational-numbers", "[t] \"les\" (grammar:article); [t] \"nombres rationnels positif\""], ["Action", "negative-rational-numbers", "[t] \"les\" (grammar:article); [t] \"nombres rationnels négatif\""], ["Action", "fences-neutral", "[p] (pause:short); [t] \"la valeur absolue de\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-absend", "[p] (pause:short); [t] \"la valeur absolue de\"; [n] children/*[1] (pause:short); [t] \"fin de valeur absolue\" (pause:short)"], ["Action", "fences-neutral-cardinality", "[p] (pause:short); [t] \"la cardinalité de\"; [n] children/*[1] (pause:short)"], ["Action", "fences-neutral-determinant", "[p] (pause:short); [t] \"le déterminant de\"; [n] children/*[1] (pause:short)"], ["Action", "fences-metric", "[p] (pause:short); [t] \"métrique de\" (span:.); [n] children/*[1] (pause:short)"], ["Action", "fences-metric-absend", "[p] (pause:short); [t] \"métrique de\" (span:content/*[1]); [n] children/*[1] (pause:short); [t] \"fin de métrique\" (span:content/*[1], pause:short)"], ["Action", "matrix", "[t] \"la matrice de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", pause:long)"], ["Action", "matrix-simple", "[t] \"la matrice de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-trivial", "[t] \"la matrice de dimension 1 par 1 avec élément\"; [n] children/*[1] (pause:long)"], ["Action", "determinant", "[t] \"le déterminant de la matrice de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", pause:long, grammar:simpleDet)"], ["Action", "determinant-simple", "[t] \"le déterminant de la matrice de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", pause:long)"], ["Action", "matrix-vector", "[t] \"la matrice colonne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple", "[t] \"la matrice colonne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-vector-simple-silentcolnum", "[t] \"la matrice colonne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector", "[t] \"la matrice ligne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Colonne-:\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple", "[t] \"la matrice ligne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row-vector-simple-silentcolnum", "[t] \"la matrice ligne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFnodeCounter, context:\"Colonne-,- \", sepFunc:CTFpauseSeparator, separator:\"medium\", pause:long)"], ["Action", "matrix-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"fin matrice\""], ["Action", "matrix-end-vector", "[n] . (grammar:?EndMatrix); [t] \"fin matrice\""], ["Action", "matrix-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"fin déterminant\""], ["Action", "vector", "[t] \"le vecteur colonne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", pause:long, grammar:simpleDet)"], ["Action", "vector-simple", "[t] \"le vecteur colonne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "row-vector", "[t] \"le vecteur ligne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (ctxtFunc:CTFnodeCounter, context:\"Colonne-:\", pause:long, grammar:simpleDet)"], ["Action", "row-vector-simple", "[t] \"le vecteur ligne de dimension\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*) (pause:long); [m] children/*[1]/children/* (sepFunc:CTFpauseSeparator, separator:\"short\", pause:long, grammar:simpleDet)"], ["Action", "vector-end-matrix", "[n] . (grammar:?EndMatrix); [t] \"fin matrice\""], ["Action", "vector-end-vector", "[n] . (grammar:?EndMatrix); [t] \"fin vecteur\""], ["Action", "vector-end-vector-endvector", "[n] . (grammar:?EndMatrix); [t] \"fin vecteur\""], ["Action", "vector-end-determinant", "[n] . (grammar:?EndMatrix); [t] \"fin déterminant\""], ["Action", "binomial", "[n] children/*[2]/children/*[1]; [t] \"parmi\"; [n] children/*[1]/children/*[1]"], ["Action", "lines-summary", "[p] (pause:short); [t] count(children/*); [t] \"lignes\"; [n] . (grammar:?layoutSummary)"], ["Action", "cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"cas\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Ligne-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "blank-cell", "[t] \"vide\""], ["Action", "blank-line", "[t] \"vide\""], ["Action", "blank-cell-empty", "[t] \"vide\""], ["Action", "blank-line-empty", "[t] \"vide\""], ["Action", "cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Cas-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-cases-summary", "[p] (pause:short); [t] count(children/*); [t] \"cas\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-cases", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Cas-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-equations-summary", "[p] (pause:short); [t] count(children/*); [t] \"équations\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-equations", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Équation-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-steps-summary", "[p] (pause:short); [t] count(children/*); [t] \" étapes\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-steps", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\" Étape-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-rows-summary", "[p] (pause:short); [t] count(children/*); [t] \"colonnes\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-rows", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Rangée-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "lines-constraints-summary", "[p] (pause:short); [t] count(children/*); [t] \"contraintes\"; [n] . (grammar:?layoutSummary)"], ["Action", "lines-constraints", "[p] (pause:short); [m] children/* (ctxtFunc:CTFnodeCounter, context:\"Contrainte-:\", sepFunc:CTFpauseSeparator, separator:\"long\", pause:long)"], ["Action", "bigop", "[t] \"le\"; [n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "limboth", "[n] children/*[1]; [t] \"de\"; [n] children/*[2]; [t] \"à\"; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "limupper", "[n] children/*[1]; [t] \"sous\"; [n] children/*[2] (pause:short)"], ["Action", "integral", "[t] \"le\"; [n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short); [n] children/*[3] (pause:short)"], ["Action", "integral-novar", "[t] \"le\"; [n] children/*[1]; [t] \"de\"; [n] children/*[2] (pause:short)"], ["Action", "overscript", "[n] children/*[1]; [t] \"sous\"; [n] children/*[2] (pause:short)"], ["Action", "overscript-limits", "[n] children/*[1]; [t] \"à\"; [n] children/*[2]"], ["Action", "underscript", "[n] children/*[1]; [t] \"sur\"; [n] children/*[2] (pause:short)"], ["Action", "underscript-limits", "[n] children/*[1]; [t] \"de\"; [n] children/*[2]"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"et\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"nombre\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "decimal-period", "[t] \"la décimale\"; [n] children/*[1] (grammar:spaceout); [t] \"virgule suivi par les chiffres répétés\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-float", "[t] \"la décimale\"; [n] children/*[1] (grammar:spaceout); [t] \"suivi par les chiffres répétés\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular", "[t] \"la décimale\"; [n] children/*[1] (grammar:spaceout); [t] \"virgule suivi par le chiffre répété\"; [n] children/*[3]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-period-singular-float", "[t] \"la décimale\"; [n] children/*[1] (grammar:spaceout); [t] \"suivi par le chiffre répété\"; [n] children/*[2]/children/*[1] (grammar:spaceout)"], ["Action", "decimal-point", "[t] \"point\""], ["Action", "line-segment", "[t] \"le segment\"; [n] children/*[1]/children/*[1]; [n] children/*[1]/children/*[2] (pause:short)"], ["Action", "conjugate", "[t] \"le complexe conjugué de\"; [n] children/*[1]"], ["Action", "defined-by", "[t] \"est défini par\" (pause:short)"], ["Action", "adorned-sign", "[t] \"signe\"; [n] children/*[1]; [t] \"avec\"; [n] children/*[2]; [t] \"dessus\""], ["Action", "factorial", "[t] \"factorielle\""], ["Action", "left-super", "[t] \"exposant gauche\"; [n] text()"], ["Action", "left-super-list", "[t] \"exposant gauche\"; [m] children/*"], ["Action", "left-sub", "[t] \"indice gauche\"; [n] text()"], ["Action", "left-sub-list", "[t] \"indice gauche\"; [m] children/*"], ["Action", "right-super", "[t] \"exposant droite\"; [n] text()"], ["Action", "right-super-list", "[t] \"exposant droite\"; [m] children/*"], ["Action", "right-sub", "[t] \"indice droite\"; [n] text()"], ["Action", "right-sub-list", "[t] \"indice droite\"; [m] children/*"], ["Action", "choose", "[t] \"combinaison de\"; [n] children/*[2] (grammar:combinatorics); [t] \"parmi\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "permute", "[t] \"permutation de\"; [n] children/*[2] (grammar:combinatorics); [t] \"parmi\"; [n] children/*[4] (grammar:combinatorics)"], ["Action", "unit-square", "[n] children/*[1]; [t] \"carré\""], ["Action", "unit-cubic", "[n] children/*[1]; [t] \"cube\""], ["Action", "unit-reciprocal", "[t] \"réciproque\"; [n] children/*[1]"], ["Action", "unit-reciprocal-singular", "[t] \"par\"; [n] children/*[1] (grammar:singular)"], ["Action", "unit-reciprocal-multi", "[t] \"par\"; [n] children/*[1] (grammar:singular)"], ["Action", "unit-divide", "[n] children/*[1]; [t] \"par\"; [n] children/*[2] (grammar:singular)"]]}, "fr/rules/mathspeak_french.min": {"locale": "fr", "modality": "speech", "domain": "mathspeak", "inherits": "romance", "rules": [["Ignore", "subscript-simple", "default"], ["Ignore", "prime-subscript-simple", "default"], ["Precondition", "overbar-enclose", "default", "self::enclose", "@role=\"top\""], ["Precondition", "underbar-enclose", "default", "self::enclose", "@role=\"bottom\""]]}, "fr/rules/mathspeak_french_actions.min": {"locale": "fr", "modality": "speech", "domain": "mathspeak", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:collapsed)"], ["Action", "blank-cell-empty", "[t] \"vide\""], ["Action", "blank-line-empty", "[t] \"vide\""], ["Action", "font", "[n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"et\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"nombre\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"MotMajuscule\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"position de base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-font", "[t] \"position de base\"; [n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "number-baseline-font-brief", "[t] \"base\"; [n] . (grammar:ignoreFont=@font); [t] @font (grammar:localFont)"], ["Action", "negative-number", "[t] \"négatif\"; [n] children/*[1]"], ["Action", "negative", "[t] \"négatif\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"divisé par\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"moins\")"], ["Action", "fences-neutral", "[t] \"début valeur absolue\"; [n] children/*[1]; [t] \"fin valeur absolue\""], ["Action", "fences-neutral-sbrief", "[t] \"valeur absolue\"; [n] children/*[1]; [t] \"fin valeur absolue\""], ["Action", "fences-metric", "[t] \"début métrique\"; [n] children/*[1]; [t] \"fin métrique\""], ["Action", "fences-metric-sbrief", "[t] \"métrique\"; [n] children/*[1]; [t] \"fin métrique\""], ["Action", "empty-set", "[t] \"ensemble vide\""], ["Action", "fences-set", "[t] \"début ensemble\"; [n] children/*[1]; [t] \"fin ensemble\""], ["Action", "fences-set-sbrief", "[t] \"ensemble\"; [n] children/*[1]; [t] \"fin ensemble\""], ["Action", "factorial", "[t] \"factorielle\""], ["Action", "minus", "[t] \"moins\""], ["Action", "continued-fraction-outer", "[t] \"fraction continue\"; [n] children/*[1]; [t] \"sur\"; [n] children/*[2]"], ["Action", "continued-fraction-outer-brief", "[t] \"frac continue\"; [n] children/*[1]; [t] \"sur\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"début fraction\"; [n] children/*[1]; [t] \"sur\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-brief", "[t] \"début frac\"; [n] children/*[1]; [t] \"sur\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"frac\"; [n] children/*[1]; [t] \"sur\"; [n] children/*[2]"], ["Action", "limboth", "[n] children/*[1]; [t] \"début\"; [t] CSFunderscript; [n] children/*[2]; [t] \"début\"; [t] CSFoverscript; [n] children/*[3]"], ["Action", "limlower", "[n] children/*[1]; [t] \"début\"; [t] CSFunderscript; [n] children/*[2]"], ["Action", "limupper", "[n] children/*[1]; [t] \"début\"; [t] CSFoverscript; [n] children/*[2]"], ["Action", "limboth-end", "[n] children/*[1]; [t] \"début\"; [t] CSFunderscript; [n] children/*[2]; [t] \"début\"; [t] CSFoverscript; [n] children/*[3]; [t] \"fin scripts\""], ["Action", "limlower-end", "[n] children/*[1]; [t] \"début\"; [t] CSFunderscript; [n] children/*[2]; [t] \"fin scripts\""], ["Action", "limupper-end", "[n] children/*[1]; [t] \"début\"; [t] CSFoverscript; [n] children/*[2]; [t] \"fin scripts\""], ["Action", "integral", "[n] children/*[1]; [t] \"indice inférieur\"; [n] children/*[2]; [t] \"indice supérieur\"; [n] children/*[3]; [t] \"position de base\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"inf\"; [n] children/*[2]; [t] \"sup\"; [n] children/*[3]; [t] \"position de base\""], ["Action", "logarithm-base", "[n] children/*[1]; [t] \"base\"; [n] children/*[2]"], ["Action", "square", "[n] children/*[1]; [t] \"au carré\""], ["Action", "cube", "[n] children/*[1]; [t] \"cubique\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"prime\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"prime\""], ["Action", "overscore", "[t] \"suscrire\"; [n] children/*[1]; [t] \"avec\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"sus-suscrire\"; [n] children/*[1]; [t] \"avec\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"souscrire\"; [n] children/*[1]; [t] \"avec\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"sous-souscrire\"; [n] children/*[1]; [t] \"avec\"; [n] children/*[2]"], ["Action", "matrix", "[t] \"début matrice\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin matrice\""], ["Action", "matrix-sbrief", "[t] \"matrice\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin matrice\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFordinal<PERSON>ounter, context:\"colonne\", pause:200)"], ["Action", "row-with-label", "[t] \"avec étiquette\"; [n] content/*[1]; [t] \"fin étiquette\" (pause:200); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonne\")"], ["Action", "row-with-label-brief", "[t] \"étiquette\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonne\")"], ["Action", "row-with-text-label", "[t] \"étiquette\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"colonne\")"], ["Action", "empty-row", "[t] \"vide\""], ["Action", "empty-cell", "[t] \"vide\" (pause:300)"], ["Action", "determinant", "[t] \"début déterminant\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [t] \"\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin déterminant\""], ["Action", "determinant-sbrief", "[t] \"déterminant\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin déterminant\""], ["Action", "determinant-simple", "[t] \"début déterminant\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée\", grammar:simpleDet); [t] \"fin déterminant\""], ["Action", "determinant-simple-sbrief", "[t] \"déterminant\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée\", grammar:simpleDet); [t] \"fin déterminant\""], ["Action", "layout", "[t] \"début tableau\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin tableau\""], ["Action", "layout-sbrief", "[t] \"tableau\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin tableau\""], ["Action", "binomial", "[t] \"début binomiale\"; [n] children/*[2]/children/*[1]; [t] \"parmi\"; [n] children/*[1]/children/*[1]; [t] \"fin binomiale\""], ["Action", "binomial-sbrief", "[t] \"binomiale\"; [n] children/*[1]/children/*[1]; [t] \"parmi\"; [n] children/*[2]/children/*[1]; [t] \"fin binomiale\""], ["Action", "cases", "[t] \"début tableau\"; [n] content/*[1]; [t] \"élargie\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin tableau\""], ["Action", "cases-sbrief", "[t] \"tableau\"; [n] content/*[1]; [t] \"élargie\"; [m] children/* (ctxtFunc:CTFordinalCounter, context:\"rangée \"); [t] \"fin tableau\""], ["Action", "line-with-label", "[t] \"avec etiquette\"; [n] content/*[1]; [t] \"fin etiquette\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"etiquette\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"etiquette\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"vide\""], ["Action", "empty-line-with-label", "[t] \"avec etiquette\"; [n] content/*[1]; [t] \"fin etiquette\" (pause:200); [t] \"vide\""], ["Action", "empty-line-with-label-brief", "[t] \"etiquette\"; [n] content/*[1] (pause:200); [t] \"vide\""], ["Action", "enclose", "[t] \"début enfermer en\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"fin enfermer\""], ["Action", "overbar-enclose", "[t] \"début trait suscrit\"; [n] children/*[1]; [t] \"fin trait suscrit\""], ["Action", "underbar-enclose", "[t] \"début trait souscrit\"; [n] children/*[1]; [t] \"fin trait souscrit\""], ["Action", "leftbar", "[t] \"barre verticale\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"barre verticale\""], ["Action", "crossout", "[t] \"début biffé\"; [n] children/*[1]; [t] \"fin biffé\""], ["Action", "cancel", "[t] \"début biffé\"; [n] children/*[1]/children/*[1]; [t] \"avec\"; [n] children/*[2]; [t] \"fin biffé\""], ["Action", "cancel-reverse", "[t] \"début biffé\"; [n] children/*[2]/children/*[1]; [t] \"avec\"; [n] children/*[1]; [t] \"fin biffé\""], ["Action", "unit-square", "[n] children/*[1]; [t] \"carré\""], ["Action", "unit-cubic", "[n] children/*[1]; [t] \"cube\""]]}, "fr/rules/prefix_french.min": {"locale": "fr", "inherits": "base", "modality": "prefix", "domain": "default", "rules": []}, "fr/rules/prefix_french_actions.min": {"locale": "fr", "modality": "prefix", "domain": "default", "kind": "actions", "rules": [["Action", "numerator", "[t] \"numérateur\" (pause:200)"], ["Action", "denominator", "[t] \"dénominateur\" (pause:200)"], ["Action", "base", "[t] \"base\" (pause:200)"], ["Action", "exponent", "[t] \"exposant\" (pause:200)"], ["Action", "subscript", "[t] \"indice\" (pause:200)"], ["Action", "overscript", "[t] \"indice suscrit\" (pause:200)"], ["Action", "underscript", "[t] \"indice souscrit\" (pause:200)"], ["Action", "radicand", "[t] \"radicande\" (pause:200)"], ["Action", "index", "[t] \"indice\" (pause:200)"], ["Action", "leftsub", "[t] \"indice inférieur gauche\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"indice inférieur gauche\" (pause:200)"], ["Action", "leftsuper", "[t] \"indice supérieur gauche\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"indice supérieur gauche\" (pause:200)"], ["Action", "rightsub", "[t] \"indice inférieur droite\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"indice inférieur droite\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"indice supérieur droite\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"indice supérieur droite\" (pause:200)"], ["Action", "choice", "[t] \"nombre d'éléments disponibles\" (pause:200)"], ["Action", "select", "[t] \"nombre d'éléments choisis\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"rangée\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"colonne\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"colonne\" (pause:200)"]]}, "fr/rules/summary_french.min": {"locale": "fr", "modality": "summary", "inherits": "romance", "rules": []}, "fr/rules/summary_french_actions.min": {"locale": "fr", "modality": "summary", "kind": "actions", "rules": [["Action", "collapsed-masculine", "[t] \"compressé\""], ["Action", "collapsed-feminine", "[t] \"compressée\""], ["Action", "abstr-identifier-long", "[t] \"identifiant long\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-identifier", "[t] \"identifiant\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-number-long", "[t] \"nombre long\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-number", "[t] \"nombre\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-mixed-number-long", "[t] \"nombre fractionnaire long\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-mixed-number", "[t] \"nombre fractionnaire\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-text", "[t] \"texte\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-function", "[t] \"expression fonctionnelle\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-function-brief", "[t] \"fonction\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-lim", "[t] \"fonction de limitation\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-lim-brief", "[t] \"lim\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-fraction", "[t] \"fraction\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-fraction-brief", "[t] \"frac\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-continued-fraction", "[t] \"fraction continue\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-continued-fraction-brief", "[t] \"frac continue\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-sqrt", "[t] \"racine carrée\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-sqrt-nested", "[t] \"racine carrée imbriquée\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-end", "[t] \"racine d'indice\"; [n] children/*[1] (engine:modality=speech); [t] \"fin indice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root", "[t] \"racine d'indice\"; [n] children/*[1] (engine:modality=speech); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-brief", "[t] \"racine\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-nested-end", "[t] \"racine imbriquée d'indice\"; [n] children/*[1] (engine:modality=speech); [t] \"fin indice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-nested", "[t] \"racine imbriquée d'indice\"; [n] children/*[1] (engine:modality=speech); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-root-nested-brief", "[t] \"racine imbriquée\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-superscript", "[t] \"puissance\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-subscript", "[t] \"indice\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-subsup", "[t] \"puissance avec index\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-infixop", "[t] @role (grammar:localRole); [t] \"avec\"; [t] count(./children/*); [t] \"éléments\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-infixop-var", "[t] @role (grammar:localRole); [t] \"avec un nombre d'éléments variable\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-addition", "[t] \"somme avec\"; [t] count(./children/*); [t] \"opérandes\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-addition-brief", "[t] \"somme\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-addition-var", "[t] \"somme avec un nombre variable d'opérandes\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multiplication", "[t] \"produit avec\"; [t] count(./children/*); [t] \"facteurs\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-multiplication-brief", "[t] \"produit\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-multiplication-var", "[t] \"produit avec un nombre de facteurs variable\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector", "[t] \"vecteur de dimension\"; [t] count(./children/*); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector-brief", "[t] \"vecteur\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-vector-var", "[t] \"vecteur colonne de dimension n\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-binomial", "[t] \"binomial\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant", "[t] \"déterminant de dimension\"; [t] count(./children/*); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant-brief", "[t] \"déterminant\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-determinant-var", "[t] \"déterminant de dimension n\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-squarematrix", "[t] \"matrice carrée de dimension\"; [t] count(./children/*); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-squarematrix-brief", "[t] \"matrice carrée\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-rowvector", "[t] \"vecteur ligne de dimension\"; [t] count(./children/row/children/*); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-rowvector-brief", "[t] \"vecteur ligne\"; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-rowvector-var", "[t] \"vecteur ligne de dimension n\""], ["Action", "abstr-matrix", "[t] \"matrice\"; [t] count(children/*); [t] \"par\"; [t] count(children/*[1]/children/*); [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix-brief", "[t] \"matrice\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-matrix-var", "[t] \"matrice de dimension n par m\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cases", "[t] \"déclaration de cas\"; [t] \"avec\"; [t] count(children/*); [t] \"cas\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cases-brief", "[t] \"déclaration de cas\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cases-var", "[t] \"déclaration de cas variable\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated", "[t] \"liste de longueur\"; [t] count(children/*) - count(content/*); [t] \"séparée par des\"; [n] content/*[1] (join:\"\"); [t] \"s\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated-brief", "[t] \"liste séparée par des\"; [n] content/*[1] (join:\"\"); [t] \"s\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-punctuated-var", "[t] \"liste de longueur variable séparée par des\"; [n] content/*[1] (join:\"\"); [t] \"s\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-bigop", "[n] content/*[1]; [n] . (grammar:gender=\"m\")"], ["Action", "abstr-integral", "[t] \"intégrale\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation", "[t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-relation-seq", "[t] @role (grammar:localRole); [t] \"séquence\"; [t] \"avec\"; [t] count(./children/*); [t] \"éléments\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation-seq-brief", "[t] @role (grammar:localRole); [t] \"séquence\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-relation-var", "[t] @role (grammar:localRole); [t] \"séquence\"; [t] \"avec un nombre de éléments variable\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel", "[t] \"séquence de relation\"; [t] \"avec\"; [t] count(./children/*); [t] \"éléments\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel-brief", "[t] \"séquence de relation\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-multirel-var", "[t] \"séquence de relation avec un nombre de éléments variable\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-table", "[t] \"table avec\"; [t] count(children/*); [t] \"lignes et\"; [t] count(children/*[1]/children/*); [t] \"colonnes\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-line", "[t] \"dans\"; [t] @role (grammar:localRole); [n] . (grammar:gender=\"m\")"], ["Action", "abstr-row", "[t] \"dans\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"avec\"; [t] count(children/*); [t] \"colonnes\"; [n] . (grammar:gender=\"f\")"], ["Action", "abstr-cell", "[t] \"dans\"; [t] @role (grammar:localRole); [n] . (grammar:gender=\"f\")"]]}}