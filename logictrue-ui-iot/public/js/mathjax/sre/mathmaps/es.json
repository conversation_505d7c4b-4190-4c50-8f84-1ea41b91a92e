{"es/messages/alphabets.min": {"kind": "alphabets", "locale": "es", "messages": {"latinSmall": ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"], "latinCap": ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z"], "greekSmall": ["nabla", "alfa", "beta", "gamma", "delta", "épsilon", "zeta", "eta", "theta", "iota", "kappa", "lambda", "mi", "ni", "xi", "ómicron", "pi", "rho", "sigma final", "sigma", "tau", "ípsilon", "phi", "ji", "psi", "omega", "diferencial parcial", "épsilon", "theta", "kappa", "phi", "rho", "pi"], "greekCap": ["Alfa", "Beta", "Gamma", "Delta", "Épsilon", "Zeta", "Eta", "Theta", "Iota", "Kappa", "Lambda", "<PERSON>", "<PERSON>", "Xi", "Ómicron", "Pi", "Rho", "Theta", "Sigma", "Tau", "Ípsilon", "Phi", "<PERSON>", "Psi", "Omega"], "capPrefix": {"default": "<PERSON><PERSON><PERSON>"}, "smallPrefix": {"default": ""}, "digitPrefix": {"default": ""}}}, "es/messages/messages.min": {"kind": "messages", "locale": "es", "messages": {"MS": {"START": "empezar", "FRAC_V": "fracción", "FRAC_B": "frac", "FRAC_S": "frac", "END": "finalizar", "FRAC_OVER": "entre", "TWICE": "", "NEST_FRAC": "", "ENDFRAC": "", "SUPER": "super", "SUB": "sub", "SUP": "sup", "SUPERSCRIPT": "superíndice", "SUBSCRIPT": "subíndice", "BASELINE": "línea base", "BASE": "base", "NESTED": "", "NEST_ROOT": "", "STARTROOT": "empezar ra<PERSON>z", "ENDROOT": "final<PERSON>r ra<PERSON><PERSON>", "ROOTINDEX": "índice de raíz", "ROOT": "raíz", "INDEX": "", "UNDER": "bajo", "UNDERSCRIPT": "bajoín<PERSON>", "OVER": "sobre", "OVERSCRIPT": "sobreíndice", "ENDSCRIPTS": "finalizar índices"}, "MSroots": {"2": "cuadrada", "3": "cúbica", "4": "a la cuarta", "5": "a la quinta", "6": "a la sexta", "7": "a la séptima", "8": "a la octava", "9": "a la novena", "10": "a la décima"}, "font": {"bold": "negrita", "bold-fraktur": "negrita Frak<PERSON>", "bold-italic": "negrita cursiva", "bold-script": "negrita script", "caligraphic": "caligráfica", "caligraphic-bold": "caligráfica negrita", "double-struck": "negrita de pizarra", "double-struck-italic": "negrita de pizarra cursiva", "fraktur": "Fraktur", "fullwidth": "ancho completo", "italic": "cursiva", "monospace": "monoespacio", "normal": "normal", "oldstyle": "estilo anti<PERSON>o", "oldstyle-bold": "estilo antiguo negrita", "script": "script", "sans-serif": "sans serif", "sans-serif-italic": "sans serif cursiva", "sans-serif-bold": "sans serif negrita", "sans-serif-bold-italic": "sans serif negrita cursiva", "unknown": "desconocida"}, "embellish": {"super": "superíndice", "sub": "subíndice", "circled": ["en circulo", "postfix<PERSON><PERSON><PERSON>"], "parenthesized": ["entre par<PERSON><PERSON>", "postfix<PERSON><PERSON><PERSON>"], "period": ["punto", "postfix<PERSON><PERSON><PERSON>"], "negative-circled": ["en circulo negro", "postfix<PERSON><PERSON><PERSON>"], "double-circled": ["en doble circulo", "postfix<PERSON><PERSON><PERSON>"], "circled-sans-serif": ["en circulo", "sansserif"], "negative-circled-sans-serif": ["en circulo negro", "sansserif"], "comma": ["coma", "postfix<PERSON><PERSON><PERSON>"], "squared": ["en cuadrado", "postfix<PERSON><PERSON><PERSON>"], "negative-squared": ["en cuadrado negro", "postfix<PERSON><PERSON><PERSON>"]}, "role": {"addition": "adición", "multiplication": "multiplicación", "subtraction": "resta", "division": "división", "equality": "<PERSON><PERSON><PERSON><PERSON>", "inequality": "desigualdad", "element": "elemento", "arrow": "flecha", "determinant": "determinante", "rowvector": "fila vector", "binomial": "binomial", "squarematrix": "matriz cu<PERSON>", "multiline": "l<PERSON><PERSON> m<PERSON>", "matrix": "matriz", "vector": "vector", "cases": "declaración de caso", "table": "mesa", "unknown": "desconocida"}, "enclose": {"longdiv": "división larga", "actuarial": "símbolo actuarial", "radical": "ra<PERSON><PERSON> cu<PERSON>", "box": "caja", "roundedbox": "caja redondeada", "circle": "<PERSON><PERSON><PERSON><PERSON>", "left": "barra vertical izquierda", "right": "barra vertical derecha", "top": "barra", "bottom": "subbarra", "updiagonalstrike": "tacha<PERSON>ra", "downdiagonalstrike": "tacha<PERSON>ra", "verticalstrike": "ponchado vertical", "horizontalstrike": "cruce", "madruwb": "símbolo factorial árabe", "updiagonalarrow": "flecha diagonal", "phasorangle": "ángulo de fasores", "unknown": "división larga"}, "navigate": {"COLLAPSIBLE": "plegable", "EXPANDABLE": "ampliable", "LEVEL": "nivel"}, "regexp": {"TEXT": "a-zA-ZáéíóúñÁÉÍÓÚÑ", "NUMBER": "((\\d{1,3})(?=( ))(( )\\d{3})*(,\\d+)?)|^\\d*,\\d+|^\\d+", "DECIMAL_MARK": ",", "DIGIT_GROUP": "", "JOINER_SUBSUPER": " ", "JOINER_FRAC": " "}, "unitTimes": "por"}}, "es/messages/numbers.min": {"kind": "numbers", "locale": "es", "messages": {"zero": "cero", "ones": ["", "uno", "dos", "tres", "cuatro", "cinco", "seis", "siete", "ocho", "nueve", "diez", "once", "doce", "trece", "catorce", "quince", "<PERSON><PERSON><PERSON><PERSON>", "die<PERSON><PERSON>e", "<PERSON><PERSON><PERSON>", "die<PERSON><PERSON>ve", "veinte", "<PERSON><PERSON><PERSON>", "veintid<PERSON>", "vein<PERSON><PERSON><PERSON>", "veinticuatro", "veinticinco", "<PERSON><PERSON><PERSON><PERSON>", "veintisiete", "veintiocho", "veintinueve"], "tens": ["", "", "", "treinta", "cuarenta", "cincuenta", "sesenta", "<PERSON><PERSON>a", "ochenta", "noventa"], "large": ["", "mil", "mill<PERSON>", "mil millónes", "bill<PERSON>", "mil bill<PERSON>es", "trillón", "mil trillónes", "cuatrilló", "mil cuatrillóes", "quintill<PERSON>", "mil quintillónes", "sextillón", "mil sextillónes", "septill<PERSON>", "mil septillónes", "octillón", "mil octillónes", "nonillón", "mil nonillónes", "decill<PERSON>", "mil decillónes"], "special": {"hundreds": ["", "cien", "doscientos", "trescientos", "cuatrocientos", "quinientos", "seiscientos", "setecientos", "ochocientos", "novecientos"], "onesOrdinals": ["primera", "segunda", "tercera", "cuarta", "quinta", "sexta", "séptima", "octava", "novena", "déci<PERSON>", "undécima", "duodécima"], "tensOrdinals": ["déci<PERSON>", "vigésima", "trigésima", "cuadragésima", "quincuagésima", "sexagésima", "septuagésima", "octogésima", "nonagésima"], "hundredsOrdinals": ["centésima", "<PERSON><PERSON><PERSON><PERSON>", "tricentésima", "cuadringentésima", "quingentésima", "sexcentésima", "septingentésima", "octingentésima", "noningentésima"]}, "vulgarSep": "-"}}, "es/si/prefixes.min": [{"Y": "yotta", "Z": "zetta", "E": "exa", "P": "peta", "T": "tera", "G": "giga", "M": "mega", "k": "kilo", "h": "hecto", "da": "deca", "d": "deci", "c": "centi", "m": "mili", "µ": "micro", "μ": "micro", "n": "nano", "p": "pico", "f": "femto", "a": "atto", "z": "zepto", "y": "yocto"}], "es/functions/algebra.min": [{"locale": "es"}, {"key": "deg", "mappings": {"default": {"default": "grados"}}}, {"key": "det", "mappings": {"default": {"default": "determinante"}}}, {"key": "dim", "mappings": {"default": {"default": "dimensión"}}}, {"key": "hom", "mappings": {"default": {"default": "homomorfismo"}}}, {"key": "ker", "mappings": {"default": {"default": "kernel"}}}, {"key": "Tr", "mappings": {"default": {"default": "traza"}}}], "es/functions/elementary.min": [{"locale": "es"}, {"key": "log", "mappings": {"default": {"default": "logaritmo"}}}, {"key": "ln", "mappings": {"default": {"default": "logarit<PERSON>"}}}, {"key": "lg", "mappings": {"default": {"default": "logaritmo base 10"}}}, {"key": "exp", "mappings": {"default": {"default": "exponente"}}}, {"key": "gcd", "mappings": {"default": {"default": "MCD"}}, "names": ["mcd", "MCD"]}, {"key": "lcm", "mappings": {"default": {"default": "mcm"}}, "names": ["mcm", "MCM"]}, {"key": "arg", "mappings": {"default": {"default": "argumento"}}}, {"key": "im", "mappings": {"default": {"default": "parte imaginaria"}}}, {"key": "re", "mappings": {"default": {"default": "residuo"}}}, {"key": "inf", "mappings": {"default": {"default": "extremo inferior"}}}, {"key": "lim", "mappings": {"default": {"default": "límite"}}}, {"key": "max", "mappings": {"default": {"default": "máxi<PERSON>"}}}, {"key": "min", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "sup", "mappings": {"default": {"default": "superior"}}}, {"key": "liminf", "mappings": {"default": {"default": "límite inferior"}}}, {"key": "limsup", "mappings": {"default": {"default": "límite superior"}}}, {"key": "<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "límite directo"}}}, {"key": "proj<PERSON>", "mappings": {"default": {"default": "límite inverso"}}}, {"key": "mod", "mappings": {"default": {"default": "m<PERSON><PERSON><PERSON>"}}}, {"key": "Pr", "mappings": {"default": {"default": "probabilidad"}}}], "es/functions/hyperbolic.min": [{"locale": "es"}, {"key": "cosh", "mappings": {"default": {"default": "coseno <PERSON>"}}}, {"key": "coth", "mappings": {"default": {"default": "cotangente hiperbólica"}}}, {"key": "csch", "mappings": {"default": {"default": "cosecante hiperbólica"}}}, {"key": "sech", "mappings": {"default": {"default": "secante hiperbólica"}}}, {"key": "sinh", "mappings": {"default": {"default": "seno hiper<PERSON><PERSON><PERSON>o"}}}, {"key": "tanh", "mappings": {"default": {"default": "tangente hiperbólica"}}}, {"key": "arcosh", "mappings": {"default": {"default": "area coseno hip<PERSON>bólico"}}}, {"key": "arcoth", "mappings": {"default": {"default": "area cotangente hiperbólica"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "area cosecante hiperbólica"}}}, {"key": "arsech", "mappings": {"default": {"default": "area secante hiperbólica"}}}, {"key": "a<PERSON><PERSON><PERSON>", "mappings": {"default": {"default": "area seno hiperbólico"}}}, {"key": "artanh", "mappings": {"default": {"default": "area tangente hiperbólica"}}}], "es/functions/trigonometry.min": [{"locale": "es"}, {"key": "cos", "mappings": {"default": {"default": "coseno"}}}, {"key": "cot", "mappings": {"default": {"default": "cotangente"}}}, {"key": "csc", "mappings": {"default": {"default": "cosecante"}}}, {"key": "sec", "mappings": {"default": {"default": "secant"}}}, {"key": "sin", "mappings": {"default": {"default": "seno"}}, "names": ["sen"]}, {"key": "tan", "mappings": {"default": {"default": "tangente"}}}, {"key": "arccos", "mappings": {"default": {"default": "arco coseno"}}}, {"key": "<PERSON><PERSON>", "mappings": {"default": {"default": "arco cotangente"}}}, {"key": "arccsc", "mappings": {"default": {"default": "arco cosecante"}}}, {"key": "arcsec", "mappings": {"default": {"default": "arco secante"}}}, {"key": "arcsin", "mappings": {"default": {"default": "arco seno"}}, "names": ["<PERSON><PERSON>"]}, {"key": "arctan", "mappings": {"default": {"default": "arco tangente"}}}], "es/symbols/digits_rest.min": [{"locale": "es"}, {"key": "00B2", "mappings": {"default": {"default": "al cuadrado"}, "mathspeak": {"default": "al cuadrado"}, "clearspeak": {"default": "al cuadrado"}}}, {"key": "00B3", "mappings": {"default": {"default": "al cubo"}, "mathspeak": {"default": "al cubo"}, "clearspeak": {"default": "al cubo"}}}, {"key": "00BC", "mappings": {"default": {"default": "un cuarto"}}}, {"key": "00BD", "mappings": {"default": {"default": "un medio"}}}, {"key": "00BE", "mappings": {"default": {"default": "tres cuartos"}}}, {"key": "2150", "mappings": {"default": {"default": "Vulgar Fracción Un Séptimo"}}}, {"key": "2151", "mappings": {"default": {"default": "Vulgar Fracción Un Noveno"}}}, {"key": "2152", "mappings": {"default": {"default": "Vulgar Fracción Un Décimo"}}}, {"key": "2153", "mappings": {"default": {"default": "Vulgar Fracción Un Tercero"}}}, {"key": "2154", "mappings": {"default": {"default": "Fracción vulgar dos tercios"}}}, {"key": "2155", "mappings": {"default": {"default": "Vulgar Fraction One Fifth"}}}, {"key": "2156", "mappings": {"default": {"default": "Fracción vulgar dos quintos"}}}, {"key": "2157", "mappings": {"default": {"default": "Fracción vulgar tres quintos"}}}, {"key": "2158", "mappings": {"default": {"default": "Fracción vulgar cuatro quintos"}}}, {"key": "2159", "mappings": {"default": {"default": "Vulgar Fracción Un Sexto"}}}, {"key": "215A", "mappings": {"default": {"default": "Vulgar fracción cinco sextos"}}}, {"key": "215B", "mappings": {"default": {"default": "Vulgar Fracción Un Octavo"}}}, {"key": "215C", "mappings": {"default": {"default": "Fracción vulgar tres octavos"}}}, {"key": "215D", "mappings": {"default": {"default": "Fracción vulgar cinco octavos"}}}, {"key": "215E", "mappings": {"default": {"default": "Fracción vulgar siete octavos"}}}, {"key": "215F", "mappings": {"default": {"default": "Numerador de fracciones uno"}}}, {"key": "2189", "mappings": {"default": {"default": "Vulgar Fraction Zero Thirds"}}}, {"key": "3248", "mappings": {"default": {"default": "Número diez en un círculo en la Plaza Negra"}}}, {"key": "3249", "mappings": {"default": {"default": "Número veinte en un círculo en la Plaza Negra"}}}, {"key": "324A", "mappings": {"default": {"default": "Número circundado treinta en cuadrado negro"}}}, {"key": "324B", "mappings": {"default": {"default": "Número circundado Cuarenta en cuadrado negro"}}}, {"key": "324C", "mappings": {"default": {"default": "Círculo número cincuenta en la plaza negra"}}}, {"key": "324D", "mappings": {"default": {"default": "Círculo número sesenta en la plaza negra"}}}, {"key": "324E", "mappings": {"default": {"default": "Número setenta en un círculo en la Plaza Negra"}}}, {"key": "324F", "mappings": {"default": {"default": "Círculo número o<PERSON>ta en cuadrado negro"}}}], "es/symbols/greek-rest.min": [{"locale": "es"}, {"key": "0394", "mappings": {"clearspeak": {"default": "trián<PERSON><PERSON>", "TriangleSymbol_Delta": "Delta mayúsculo"}}}], "es/symbols/greek-scripts.min": [{"locale": "es"}, {"key": "1D26", "mappings": {"default": {"default": "pequeño mayúscula gamma"}}}, {"key": "1D27", "mappings": {"default": {"default": "peque<PERSON> may<PERSON> la<PERSON>da"}}}, {"key": "1D28", "mappings": {"default": {"default": "pequeña may<PERSON>"}}}, {"key": "1D29", "mappings": {"default": {"default": "peque<PERSON> may<PERSON>"}}}, {"key": "1D2A", "mappings": {"default": {"default": "pequeña may<PERSON>cula Psi"}}}, {"key": "1D5E", "mappings": {"default": {"default": "modificador de pequeño gamma"}}}, {"key": "1D60", "mappings": {"default": {"default": "modificador de pequeño phi"}}}, {"key": "1D66", "mappings": {"default": {"default": "subscripción pequeña beta"}}}, {"key": "1D67", "mappings": {"default": {"default": "subíndice pequeña gamma"}}}, {"key": "1D68", "mappings": {"default": {"default": "subíndice rho"}}}, {"key": "1D69", "mappings": {"default": {"default": "subíndice phi"}}}, {"key": "1D6A", "mappings": {"default": {"default": "subscripción chi"}}}], "es/symbols/greek-symbols.min": [{"locale": "es"}, {"key": "03D0", "mappings": {"default": {"default": "símbolo beta"}}}, {"key": "03D7", "mappings": {"default": {"default": "sí<PERSON>lo kai griego"}}}, {"key": "03F6", "mappings": {"default": {"default": "épsilon invertido"}}}, {"key": "1D7CA", "mappings": {"default": {"default": "Matemática Bold Capital Digamma"}}}, {"key": "1D7CB", "mappings": {"default": {"default": "Matemática negrita pequeño digamma"}}}], "es/symbols/hebrew_letters.min": [{"locale": "es"}, {"key": "2135", "mappings": {"default": {"default": "alef"}}}, {"key": "2136", "mappings": {"default": {"default": "bet"}}}, {"key": "2137", "mappings": {"default": {"default": "guímel"}}}, {"key": "2138", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}], "es/symbols/latin-lower-double-accent.min": [{"locale": "es"}, {"key": "01D6", "mappings": {"default": {"default": "u con diéresis y macron"}}}, {"key": "01D8", "mappings": {"default": {"default": "u con diéresis y acento agudo"}}}, {"key": "01DA", "mappings": {"default": {"default": "u con diéresis y acento"}}}, {"key": "01DC", "mappings": {"default": {"default": "u con diéresis y grave"}}}, {"key": "01DF", "mappings": {"default": {"default": "a con diéresis y macron"}}}, {"key": "01E1", "mappings": {"default": {"default": "a con punto arriba y macron"}}}, {"key": "01ED", "mappings": {"default": {"default": "o con pecíolo y macron"}}}, {"key": "01FB", "mappings": {"default": {"default": "a con ring above y acento agudo"}}}, {"key": "022B", "mappings": {"default": {"default": "o con diéresis y macron"}}}, {"key": "022D", "mappings": {"default": {"default": "o con tilde y macron"}}}, {"key": "0231", "mappings": {"default": {"default": "o con Dot Above y Macron"}}}, {"key": "1E09", "mappings": {"default": {"default": "c con cedilla y agudo"}}}, {"key": "1E15", "mappings": {"default": {"default": "e con macrón y grave"}}}, {"key": "1E17", "mappings": {"default": {"default": "e con macrón y agudo"}}}, {"key": "1E1D", "mappings": {"default": {"default": "e con cedilla y breve"}}}, {"key": "1E2F", "mappings": {"default": {"default": "i con diéresis y agudo"}}}, {"key": "1E39", "mappings": {"default": {"default": "l con punto debajo y macrón"}}}, {"key": "1E4D", "mappings": {"default": {"default": "o con tilde y acute"}}}, {"key": "1E4F", "mappings": {"default": {"default": "o con tilde y diéresis"}}}, {"key": "1E51", "mappings": {"default": {"default": "o con macrón y grave"}}}, {"key": "1E53", "mappings": {"default": {"default": "o con macrón y agudo"}}}, {"key": "1E5D", "mappings": {"default": {"default": "r con punto debajo y macrón"}}}, {"key": "1E65", "mappings": {"default": {"default": "s con agudo y punto arriba"}}}, {"key": "1E67", "mappings": {"default": {"default": "s con carón y punto arriba"}}}, {"key": "1E69", "mappings": {"default": {"default": "s con punto debajo y punto arriba"}}}, {"key": "1E79", "mappings": {"default": {"default": "u con tilde y agudo"}}}, {"key": "1E7B", "mappings": {"default": {"default": "u con macrón y diéresis"}}}, {"key": "1EA5", "mappings": {"default": {"default": "a con acento circunflejo y agudo"}}}, {"key": "1EA7", "mappings": {"default": {"default": "a con acento circunflejo y grave"}}}, {"key": "1EA9", "mappings": {"default": {"default": "a con acento circunflejo y gancho arriba"}}}, {"key": "1EAB", "mappings": {"default": {"default": "a con acento circunflejo y tilde"}}}, {"key": "1EAD", "mappings": {"default": {"default": "a con acento circunflejo y punto debajo"}}}, {"key": "1EAF", "mappings": {"default": {"default": "a con breve y agudo"}}}, {"key": "1EB1", "mappings": {"default": {"default": "a con breve y grave"}}}, {"key": "1EB3", "mappings": {"default": {"default": "a con breve y gancho arriba"}}}, {"key": "1EB5", "mappings": {"default": {"default": "a con breve y tilde"}}}, {"key": "1EB7", "mappings": {"default": {"default": "a con breve y punto debajo"}}}, {"key": "1EBF", "mappings": {"default": {"default": "e con acento circunflejo y agudo"}}}, {"key": "1EC1", "mappings": {"default": {"default": "e con acento circunflejo y grave"}}}, {"key": "1EC3", "mappings": {"default": {"default": "e con acento circunflejo y gancho arriba"}}}, {"key": "1EC5", "mappings": {"default": {"default": "e con acento circunflejo y tilde"}}}, {"key": "1EC7", "mappings": {"default": {"default": "e con acento circunflejo y punto debajo"}}}, {"key": "1ED1", "mappings": {"default": {"default": "o con acento circunflejo y agudo"}}}, {"key": "1ED3", "mappings": {"default": {"default": "o con acento circunflejo y grave"}}}, {"key": "1ED5", "mappings": {"default": {"default": "o con acento circunflejo y gancho arriba"}}}, {"key": "1ED7", "mappings": {"default": {"default": "o con acento circunflejo y tilde"}}}, {"key": "1ED9", "mappings": {"default": {"default": "o con acento circunflejo y punto debajo"}}}, {"key": "1EDB", "mappings": {"default": {"default": "o with horn and acute"}}}, {"key": "1EDD", "mappings": {"default": {"default": "o with horn and grave"}}}, {"key": "1EDF", "mappings": {"default": {"default": "o with horn and hook above"}}}, {"key": "1EE1", "mappings": {"default": {"default": "o with horn and tilde"}}}, {"key": "1EE3", "mappings": {"default": {"default": "o con cuerno y punto debajo"}}}, {"key": "1EE9", "mappings": {"default": {"default": "u con cuerno y agudo"}}}, {"key": "1EEB", "mappings": {"default": {"default": "u con cuerno y grave"}}}, {"key": "1EED", "mappings": {"default": {"default": "u con cuerno y gancho arriba"}}}, {"key": "1EEF", "mappings": {"default": {"default": "u con cuerno y tilde"}}}, {"key": "1EF1", "mappings": {"default": {"default": "u con cuerno y punto debajo"}}}], "es/symbols/latin-lower-phonetic.min": [{"locale": "es"}, {"key": "00F8", "mappings": {"default": {"default": "o barrada"}}}, {"key": "0111", "mappings": {"default": {"default": "d con barra"}}}, {"key": "0127", "mappings": {"default": {"default": "h con guión"}}}, {"key": "0142", "mappings": {"default": {"default": "l con guión"}}}, {"key": "0167", "mappings": {"default": {"default": "t con guión"}}}, {"key": "0180", "mappings": {"default": {"default": "b con línea"}}}, {"key": "019B", "mappings": {"default": {"default": "lambda tachada"}}}, {"key": "01B6", "mappings": {"default": {"default": "z con línea"}}}, {"key": "01BE", "mappings": {"default": {"default": "parada glotal invertida con línea"}}}, {"key": "01E5", "mappings": {"default": {"default": "g con línea"}}}, {"key": "01FF", "mappings": {"default": {"default": "o con línea y acento agudo"}}}, {"key": "023C", "mappings": {"default": {"default": "c con trazo"}}}, {"key": "0247", "mappings": {"default": {"default": "pequeña e con trazo"}}}, {"key": "0249", "mappings": {"default": {"default": "j con trazo"}}}, {"key": "024D", "mappings": {"default": {"default": "con trazo"}}}, {"key": "024F", "mappings": {"default": {"default": "y con trazo"}}}, {"key": "025F", "mappings": {"default": {"default": "pequeña sin puntos j con trazo"}}}, {"key": "0268", "mappings": {"default": {"default": "i con trazo"}}}, {"key": "0284", "mappings": {"default": {"default": "pequeña sin puntos j con trazo y gancho"}}}, {"key": "02A1", "mappings": {"default": {"default": "parada glotal con trazo"}}}, {"key": "02A2", "mappings": {"default": {"default": "parada invertida glotal con trazo"}}}, {"key": "1D13", "mappings": {"default": {"default": "de lado O con trazo"}}}, {"key": "1D7C", "mappings": {"default": {"default": "iota con trazo"}}}, {"key": "1D7D", "mappings": {"default": {"default": "p con trazo"}}}, {"key": "1D7F", "mappings": {"default": {"default": "upsilon con trazo"}}}, {"key": "1E9C", "mappings": {"default": {"default": "larga s con trazo diagonal"}}}, {"key": "1E9D", "mappings": {"default": {"default": "pequeña s larga con trazo alto"}}}, {"key": "018D", "mappings": {"default": {"default": "delta convertido"}}}, {"key": "1E9B", "mappings": {"default": {"default": "s larga con punto arriba"}}}, {"key": "1E9F", "mappings": {"default": {"default": "delta pequeña latina"}}}, {"key": "0138", "mappings": {"default": {"default": "kra"}}}, {"key": "017F", "mappings": {"default": {"default": "s larga"}}}, {"key": "0183", "mappings": {"default": {"default": "b con barra encima"}}}, {"key": "0185", "mappings": {"default": {"default": "tono seis"}}}, {"key": "0188", "mappings": {"default": {"default": "c con gancho"}}}, {"key": "018C", "mappings": {"default": {"default": "d con barra encima"}}}, {"key": "0192", "mappings": {"default": {"default": "f con gancho"}}}, {"key": "0195", "mappings": {"default": {"default": "hv"}}}, {"key": "0199", "mappings": {"default": {"default": "k con gancho"}}}, {"key": "019A", "mappings": {"default": {"default": "l con barra"}}}, {"key": "019E", "mappings": {"default": {"default": "n con pierna derecha larga"}}}, {"key": "01A1", "mappings": {"default": {"default": "o con cuerno"}}}, {"key": "01A3", "mappings": {"default": {"default": "oi"}}}, {"key": "01A5", "mappings": {"default": {"default": "p con gancho"}}}, {"key": "01A8", "mappings": {"default": {"default": "dos tonos"}}}, {"key": "01AA", "mappings": {"default": {"default": "latin letter invertido Esh bucle"}}}, {"key": "01AB", "mappings": {"default": {"default": "t con palatal gancho"}}}, {"key": "01AD", "mappings": {"default": {"default": "t con gancho"}}}, {"key": "01B0", "mappings": {"default": {"default": "u con horn"}}}, {"key": "01B4", "mappings": {"default": {"default": "y con gancho"}}}, {"key": "01B9", "mappings": {"default": {"default": "ezh invertido"}}}, {"key": "01BA", "mappings": {"default": {"default": "ezh con cola"}}}, {"key": "01BD", "mappings": {"default": {"default": "tono cinco"}}}, {"key": "01BF", "mappings": {"default": {"default": "wynn"}}}, {"key": "01C6", "mappings": {"default": {"default": "dz con acento"}}}, {"key": "01C9", "mappings": {"default": {"default": "lj"}}}, {"key": "01CC", "mappings": {"default": {"default": "nj"}}}, {"key": "01E3", "mappings": {"default": {"default": "ae con macron"}}}, {"key": "01EF", "mappings": {"default": {"default": "ezh con acento"}}}, {"key": "01F3", "mappings": {"default": {"default": "dz"}}}, {"key": "021D", "mappings": {"default": {"default": "latín letra pequeña yogh"}}}, {"key": "026E", "mappings": {"default": {"default": "lezh letra pequeña latina"}}}, {"key": "0292", "mappings": {"default": {"default": "ezh"}}}, {"key": "0293", "mappings": {"default": {"default": "pequeña Ezh con Curl"}}}, {"key": "02A4", "mappings": {"default": {"default": "latín letra pequeña Dezh Digraph"}}}, {"key": "01DD", "mappings": {"default": {"default": "e convertido"}}}, {"key": "01FD", "mappings": {"default": {"default": "ae con acento agudo"}}}, {"key": "0221", "mappings": {"default": {"default": "d con rizo"}}}, {"key": "0223", "mappings": {"default": {"default": "ou"}}}, {"key": "0225", "mappings": {"default": {"default": "z con gancho"}}}, {"key": "0234", "mappings": {"default": {"default": "l con rizo"}}}, {"key": "0235", "mappings": {"default": {"default": "n con rizo"}}}, {"key": "0236", "mappings": {"default": {"default": "t con rizo"}}}, {"key": "0238", "mappings": {"default": {"default": "letra pequeña latina db digraph"}}}, {"key": "0239", "mappings": {"default": {"default": "latín letra pequeña Qp Digraph"}}}, {"key": "023F", "mappings": {"default": {"default": "s con cola cruzada"}}}, {"key": "0240", "mappings": {"default": {"default": "z con cola swash"}}}, {"key": "0242", "mappings": {"default": {"default": "latín letra pequeña parada glotal"}}}, {"key": "024B", "mappings": {"default": {"default": "q con cola de gancho"}}}, {"key": "0250", "mappings": {"default": {"default": "convertida en una"}}}, {"key": "0251", "mappings": {"default": {"default": "alfa minúscula latina"}}}, {"key": "0252", "mappings": {"default": {"default": "convertida en alfa"}}}, {"key": "0253", "mappings": {"default": {"default": "b con gancho"}}}, {"key": "0254", "mappings": {"default": {"default": "abierta o"}}}, {"key": "0255", "mappings": {"default": {"default": "c con rizo"}}}, {"key": "0256", "mappings": {"default": {"default": "d con cola"}}}, {"key": "0257", "mappings": {"default": {"default": "d con gancho"}}}, {"key": "0258", "mappings": {"default": {"default": "invertida E"}}}, {"key": "0259", "mappings": {"default": {"default": "schwa"}}}, {"key": "025A", "mappings": {"default": {"default": "peque<PERSON> con gancho"}}}, {"key": "025B", "mappings": {"default": {"default": "e abierta"}}}, {"key": "025C", "mappings": {"default": {"default": "invertida abierta E"}}}, {"key": "025D", "mappings": {"default": {"default": "letra pequeña latina invertida E abierta con gancho"}}}, {"key": "025E", "mappings": {"default": {"default": "cerrada invertida abierta E"}}}, {"key": "0260", "mappings": {"default": {"default": "g con gancho"}}}, {"key": "0261", "mappings": {"default": {"default": "escritura latina de letras pequeñas G"}}}, {"key": "0263", "mappings": {"default": {"default": "latín letra pequeña gamma"}}}, {"key": "0264", "mappings": {"default": {"default": "cuerno de carnero de letra pequeña latina"}}}, {"key": "0265", "mappings": {"default": {"default": "girada h"}}}, {"key": "0266", "mappings": {"default": {"default": "h con gancho"}}}, {"key": "0267", "mappings": {"default": {"default": "pequeña letra latina Heng con gancho"}}}, {"key": "0269", "mappings": {"default": {"default": "latín letra minúscula Iota"}}}, {"key": "026B", "mappings": {"default": {"default": "l con tilde medio"}}}, {"key": "026C", "mappings": {"default": {"default": "l con cinturón"}}}, {"key": "026D", "mappings": {"default": {"default": "letra L latina pequeña con gancho Retroflex"}}}, {"key": "026F", "mappings": {"default": {"default": "girada m"}}}, {"key": "0270", "mappings": {"default": {"default": "vuelta M con pierna larga"}}}, {"key": "0271", "mappings": {"default": {"default": "m con gancho"}}}, {"key": "0272", "mappings": {"default": {"default": "n con gancho <PERSON>o"}}}, {"key": "0273", "mappings": {"default": {"default": "n con gancho retroflex"}}}, {"key": "0275", "mappings": {"default": {"default": "letra minúscula omega"}}}, {"key": "0277", "mappings": {"default": {"default": "latin Small Letter Closed Omega"}}}, {"key": "0278", "mappings": {"default": {"default": "Letra pequeña latina"}}}, {"key": "0279", "mappings": {"default": {"default": "vuelta r"}}}, {"key": "027A", "mappings": {"default": {"default": "vuelta R con pierna larga"}}}, {"key": "027B", "mappings": {"default": {"default": "vuelta R con gancho"}}}, {"key": "027C", "mappings": {"default": {"default": "R con pierna larga"}}}, {"key": "027D", "mappings": {"default": {"default": "R con cola"}}}, {"key": "027E", "mappings": {"default": {"default": "R con anzuelo"}}}, {"key": "027F", "mappings": {"default": {"default": "invertida R con anzuelo"}}}, {"key": "0282", "mappings": {"default": {"default": "S con gancho"}}}, {"key": "0283", "mappings": {"default": {"default": "Pequeña letra latina esh"}}}, {"key": "0285", "mappings": {"default": {"default": "Latín letra pequeña en cuclillas invertida Esh"}}}, {"key": "0286", "mappings": {"default": {"default": "<PERSON><PERSON> minúscula latina con rizo"}}}, {"key": "0287", "mappings": {"default": {"default": "vuelta t"}}}, {"key": "0288", "mappings": {"default": {"default": "T con gancho retroflex"}}}, {"key": "0289", "mappings": {"default": {"default": "Letra de letra pequeña latina U"}}}, {"key": "028A", "mappings": {"default": {"default": "Upsilon"}}}, {"key": "028B", "mappings": {"default": {"default": "V con gancho"}}}, {"key": "028C", "mappings": {"default": {"default": "convertida V"}}}, {"key": "028D", "mappings": {"default": {"default": "girada w"}}}, {"key": "028E", "mappings": {"default": {"default": "vuelta y"}}}, {"key": "0290", "mappings": {"default": {"default": "Z con gancho retroflex"}}}, {"key": "0291", "mappings": {"default": {"default": "z con rizo"}}}, {"key": "0295", "mappings": {"default": {"default": "faríngea con voz fricativa"}}}, {"key": "0296", "mappings": {"default": {"default": "invertida parada glotal"}}}, {"key": "0297", "mappings": {"default": {"default": "estirada c"}}}, {"key": "0298", "mappings": {"default": {"default": "Carta Bilabial Click"}}}, {"key": "029A", "mappings": {"default": {"default": "cerrada abierta e"}}}, {"key": "029E", "mappings": {"default": {"default": "vuelta k"}}}, {"key": "02A0", "mappings": {"default": {"default": "q con gancho"}}}, {"key": "02A3", "mappings": {"default": {"default": "pequeña letra Dz Digraph"}}}, {"key": "02A5", "mappings": {"default": {"default": "Letra pequeña latina Dz Digraph con Curl"}}}, {"key": "02A6", "mappings": {"default": {"default": "Latín letra pequeña Ts Digraph"}}}, {"key": "02A7", "mappings": {"default": {"default": "Letra pequeña latina Tesh <PERSON>graph"}}}, {"key": "02A8", "mappings": {"default": {"default": "Latín letra pequeña Tc Digraph con Curl"}}}, {"key": "02A9", "mappings": {"default": {"default": "Letra pequeña latina Feng Digraph"}}}, {"key": "02AA", "mappings": {"default": {"default": "pequeña letra Ls Digraph"}}}, {"key": "02AB", "mappings": {"default": {"default": "pequeña letra Lz Digraph"}}}, {"key": "02AC", "mappings": {"default": {"default": "percusion bilabial"}}}, {"key": "02AD", "mappings": {"default": {"default": "de percusión bidental"}}}, {"key": "02AE", "mappings": {"default": {"default": "convertida en H con anzuelo"}}}, {"key": "02AF", "mappings": {"default": {"default": "convertida en H con anzuelo y cola"}}}, {"key": "1D02", "mappings": {"default": {"default": "vuelta ae"}}}, {"key": "1D08", "mappings": {"default": {"default": "convertida abierta E"}}}, {"key": "1D09", "mappings": {"default": {"default": "convertida i"}}}, {"key": "1D11", "mappings": {"default": {"default": "de lado O"}}}, {"key": "1D12", "mappings": {"default": {"default": "de lado abierto O"}}}, {"key": "1D14", "mappings": {"default": {"default": "vuelta oe"}}}, {"key": "1D16", "mappings": {"default": {"default": "mitad superior O"}}}, {"key": "1D17", "mappings": {"default": {"default": "Letra latina, letra inferior, mitad inferior O"}}}, {"key": "1D1D", "mappings": {"default": {"default": "hacia los lados U"}}}, {"key": "1D1E", "mappings": {"default": {"default": "de lado diaeresizada U"}}}, {"key": "1D1F", "mappings": {"default": {"default": "girada hacia los lados M"}}}, {"key": "1D24", "mappings": {"default": {"default": "Carta latina expresada espirante laríngeo"}}}, {"key": "1D25", "mappings": {"default": {"default": "ain"}}}, {"key": "1D6B", "mappings": {"default": {"default": "ue"}}}, {"key": "1D6C", "mappings": {"default": {"default": "B con tilde medio"}}}, {"key": "1D6D", "mappings": {"default": {"default": "D con tilde medio"}}}, {"key": "1D6E", "mappings": {"default": {"default": "F con tilde medio"}}}, {"key": "1D6F", "mappings": {"default": {"default": "M con tilde medio"}}}, {"key": "1D70", "mappings": {"default": {"default": "N con tilde medio"}}}, {"key": "1D71", "mappings": {"default": {"default": "P con tilde medio"}}}, {"key": "1D72", "mappings": {"default": {"default": "R con tilde medio"}}}, {"key": "1D73", "mappings": {"default": {"default": "R con anzuelo y tilde medio"}}}, {"key": "1D74", "mappings": {"default": {"default": "S con tilde medio"}}}, {"key": "1D75", "mappings": {"default": {"default": "T con tilde medio"}}}, {"key": "1D76", "mappings": {"default": {"default": "z con tilde media"}}}, {"key": "1D77", "mappings": {"default": {"default": "girada g"}}}, {"key": "1D79", "mappings": {"default": {"default": "insular g"}}}, {"key": "1D7A", "mappings": {"default": {"default": "th con tachado"}}}, {"key": "1D80", "mappings": {"default": {"default": "B con gancho palatal"}}}, {"key": "1D81", "mappings": {"default": {"default": "D con gancho palatal"}}}, {"key": "1D82", "mappings": {"default": {"default": "F con gancho palatino"}}}, {"key": "1D83", "mappings": {"default": {"default": "G con gancho palatal"}}}, {"key": "1D84", "mappings": {"default": {"default": "K con gancho palatino"}}}, {"key": "1D85", "mappings": {"default": {"default": "L con gancho palatino"}}}, {"key": "1D86", "mappings": {"default": {"default": "M con gancho palatino"}}}, {"key": "1D87", "mappings": {"default": {"default": "N con gancho palatal"}}}, {"key": "1D88", "mappings": {"default": {"default": "P con gancho palatino"}}}, {"key": "1D89", "mappings": {"default": {"default": "R con gancho palatal"}}}, {"key": "1D8A", "mappings": {"default": {"default": "S con gancho palatal"}}}, {"key": "1D8B", "mappings": {"default": {"default": "Pequeña letra latina Esh con gancho palatal"}}}, {"key": "1D8C", "mappings": {"default": {"default": "V con gancho palatal"}}}, {"key": "1D8D", "mappings": {"default": {"default": "X con gancho palatal"}}}, {"key": "1D8E", "mappings": {"default": {"default": "Z con gancho palatal"}}}, {"key": "1D8F", "mappings": {"default": {"default": "a con gancho retroflex"}}}, {"key": "1D90", "mappings": {"default": {"default": "Alfa latina pequeña letra con gancho retroflex"}}}, {"key": "1D91", "mappings": {"default": {"default": "D con gancho y cola"}}}, {"key": "1D92", "mappings": {"default": {"default": "pequeña E con gancho retroflex"}}}, {"key": "1D93", "mappings": {"default": {"default": "E Pequeña Open con Gan<PERSON>"}}}, {"key": "1D94", "mappings": {"default": {"default": "E abierta invertido con gancho retroflex"}}}, {"key": "1D95", "mappings": {"default": {"default": "peque<PERSON> con gancho retroflex"}}}, {"key": "1D96", "mappings": {"default": {"default": "I con gancho retroflex"}}}, {"key": "1D97", "mappings": {"default": {"default": "pequeña latina Open O con gancho retroflex"}}}, {"key": "1D98", "mappings": {"default": {"default": "Esh de letra pequeña latina con gancho retroflex"}}}, {"key": "1D99", "mappings": {"default": {"default": "U con gancho retroflex"}}}, {"key": "1D9A", "mappings": {"default": {"default": "pequeña Ezh con gancho retroflex"}}}, {"key": "0149", "mappings": {"default": {"default": "n precedida por apóstrofe"}}}, {"key": "014B", "mappings": {"default": {"default": "eng"}}}], "es/symbols/latin-lower-single-accent.min": [{"locale": "es"}, {"key": "00E0", "mappings": {"default": {"default": "a grave"}}}, {"key": "00E1", "mappings": {"default": {"default": "a aguda"}}}, {"key": "00E2", "mappings": {"default": {"default": "a acento circunflejo"}}}, {"key": "00E3", "mappings": {"default": {"default": "a con tilde"}}}, {"key": "00E4", "mappings": {"default": {"default": "a diéresis"}}}, {"key": "00E5", "mappings": {"default": {"default": "a con anillo"}}}, {"key": "00E7", "mappings": {"default": {"default": "c cedilla"}}}, {"key": "00E8", "mappings": {"default": {"default": "e grave"}}}, {"key": "00E9", "mappings": {"default": {"default": "e aguda"}}}, {"key": "00EA", "mappings": {"default": {"default": "e acento circunflejo"}}}, {"key": "00EB", "mappings": {"default": {"default": "e diéresis"}}}, {"key": "00EC", "mappings": {"default": {"default": "i grave"}}}, {"key": "00ED", "mappings": {"default": {"default": "i aguda"}}}, {"key": "00EE", "mappings": {"default": {"default": "i acento circunflejo"}}}, {"key": "00EF", "mappings": {"default": {"default": "i diéresis"}}}, {"key": "00F1", "mappings": {"default": {"default": "eñe"}}}, {"key": "00F2", "mappings": {"default": {"default": "o grave"}}}, {"key": "00F3", "mappings": {"default": {"default": "o aguda"}}}, {"key": "00F4", "mappings": {"default": {"default": "o acento circunflejo"}}}, {"key": "00F5", "mappings": {"default": {"default": "o con tilde"}}}, {"key": "00F6", "mappings": {"default": {"default": "o diéresis"}}}, {"key": "00F9", "mappings": {"default": {"default": "u grave"}}}, {"key": "00FA", "mappings": {"default": {"default": "u aguda"}}}, {"key": "00FB", "mappings": {"default": {"default": "u acento circunflejo"}}}, {"key": "00FC", "mappings": {"default": {"default": "u diéresis"}}}, {"key": "00FD", "mappings": {"default": {"default": "Y aguda", "defaultMP": "ye aguda"}}}, {"key": "00FF", "mappings": {"default": {"default": "y diéresis", "defaultMP": "ye diéresis"}}}, {"key": "0101", "mappings": {"default": {"default": "a barra"}}}, {"key": "0103", "mappings": {"default": {"default": "a con breve"}}}, {"key": "0105", "mappings": {"default": {"default": "a con ogonek"}}}, {"key": "0107", "mappings": {"default": {"default": "c con agudo"}}}, {"key": "0109", "mappings": {"default": {"default": "c con acento circunflejo"}}}, {"key": "010B", "mappings": {"default": {"default": "c con punto arriba"}}}, {"key": "010D", "mappings": {"default": {"default": "c con carón"}}}, {"key": "010F", "mappings": {"default": {"default": "d con carón"}}}, {"key": "0113", "mappings": {"default": {"default": "e barra"}}}, {"key": "0115", "mappings": {"default": {"default": "e con breve"}}}, {"key": "0117", "mappings": {"default": {"default": "e con punto arriba"}}}, {"key": "0119", "mappings": {"default": {"default": "e con ogonek"}}}, {"key": "011B", "mappings": {"default": {"default": "e con carón"}}}, {"key": "011D", "mappings": {"default": {"default": "g con acento circunflejo"}}}, {"key": "011F", "mappings": {"default": {"default": "g con breve"}}}, {"key": "0121", "mappings": {"default": {"default": "g con punto arriba"}}}, {"key": "0123", "mappings": {"default": {"default": "g con cedilla"}}}, {"key": "0125", "mappings": {"default": {"default": "h con acento circunflejo"}}}, {"key": "0129", "mappings": {"default": {"default": "i con tilde"}}}, {"key": "012B", "mappings": {"default": {"default": "i barra"}}}, {"key": "012D", "mappings": {"default": {"default": "i con breve"}}}, {"key": "012F", "mappings": {"default": {"default": "i con ogonek"}}}, {"key": "0131", "mappings": {"default": {"default": "i sin punto"}}}, {"key": "0135", "mappings": {"default": {"default": "j con acento circunflejo"}}}, {"key": "0137", "mappings": {"default": {"default": "k con cedilla"}}}, {"key": "013A", "mappings": {"default": {"default": "l con agudo"}}}, {"key": "013C", "mappings": {"default": {"default": "l con cedilla"}}}, {"key": "013E", "mappings": {"default": {"default": "l con carón"}}}, {"key": "0140", "mappings": {"default": {"default": "l con punto mediano"}}}, {"key": "0144", "mappings": {"default": {"default": "n con agudo"}}}, {"key": "0146", "mappings": {"default": {"default": "n con cedilla"}}}, {"key": "0148", "mappings": {"default": {"default": "n con carón"}}}, {"key": "014D", "mappings": {"default": {"default": "o barra"}}}, {"key": "014F", "mappings": {"default": {"default": "o con breve"}}}, {"key": "0151", "mappings": {"default": {"default": "o con doble acento agudo"}}}, {"key": "0155", "mappings": {"default": {"default": "r con agudo"}}}, {"key": "0157", "mappings": {"default": {"default": "r con cedilla"}}}, {"key": "0159", "mappings": {"default": {"default": "r con carón"}}}, {"key": "015B", "mappings": {"default": {"default": "s con agudo"}}}, {"key": "015D", "mappings": {"default": {"default": "s con acento circunflejo"}}}, {"key": "015F", "mappings": {"default": {"default": "s con cedilla"}}}, {"key": "0161", "mappings": {"default": {"default": "s con carón"}}}, {"key": "0163", "mappings": {"default": {"default": "t con cedilla"}}}, {"key": "0165", "mappings": {"default": {"default": "t con carón"}}}, {"key": "0169", "mappings": {"default": {"default": "u con tilde"}}}, {"key": "016B", "mappings": {"default": {"default": "u barra"}}}, {"key": "016D", "mappings": {"default": {"default": "u con breve"}}}, {"key": "016F", "mappings": {"default": {"default": "u con anillo arriba"}}}, {"key": "0171", "mappings": {"default": {"default": "u con doble acento agudo"}}}, {"key": "0173", "mappings": {"default": {"default": "u con ogonek"}}}, {"key": "0175", "mappings": {"default": {"default": "w con circunflejo"}}}, {"key": "0177", "mappings": {"default": {"default": "y con circunflejo"}}}, {"key": "017A", "mappings": {"default": {"default": "z con agudo"}}}, {"key": "017C", "mappings": {"default": {"default": "z con punto"}}}, {"key": "017E", "mappings": {"default": {"default": "z con carón"}}}, {"key": "01CE", "mappings": {"default": {"default": "a con acento"}}}, {"key": "01D0", "mappings": {"default": {"default": "i con acento"}}}, {"key": "01D2", "mappings": {"default": {"default": "o con acento"}}}, {"key": "01D4", "mappings": {"default": {"default": "u con acento"}}}, {"key": "01E7", "mappings": {"default": {"default": "g con acento"}}}, {"key": "01E9", "mappings": {"default": {"default": "k con acento"}}}, {"key": "01EB", "mappings": {"default": {"default": "o con pecíolo"}}}, {"key": "01F0", "mappings": {"default": {"default": "j con acento"}}}, {"key": "01F5", "mappings": {"default": {"default": "g con agudo"}}}, {"key": "01F9", "mappings": {"default": {"default": "n con grave"}}}, {"key": "0201", "mappings": {"default": {"default": "a con doble tumba"}}}, {"key": "0203", "mappings": {"default": {"default": "a con letras invertidas"}}}, {"key": "0205", "mappings": {"default": {"default": "e con doble tumba"}}}, {"key": "0207", "mappings": {"default": {"default": "e con letras invertidas"}}}, {"key": "0209", "mappings": {"default": {"default": "i con doble tumba"}}}, {"key": "020B", "mappings": {"default": {"default": "i con Breve invertida"}}}, {"key": "020D", "mappings": {"default": {"default": "o con doble tumba"}}}, {"key": "020F", "mappings": {"default": {"default": "o con letras invertidas"}}}, {"key": "0211", "mappings": {"default": {"default": "r con doble sepulcro"}}}, {"key": "0213", "mappings": {"default": {"default": "r con letras invertidas"}}}, {"key": "0215", "mappings": {"default": {"default": "u con doble tumba"}}}, {"key": "0217", "mappings": {"default": {"default": "u con letras invertidas"}}}, {"key": "0219", "mappings": {"default": {"default": "s con coma debajo"}}}, {"key": "021B", "mappings": {"default": {"default": "t con coma debajo"}}}, {"key": "021F", "mappings": {"default": {"default": "h con caron"}}}, {"key": "0227", "mappings": {"default": {"default": "con un punto arriba"}}}, {"key": "0229", "mappings": {"default": {"default": "e con cedilla"}}}, {"key": "022F", "mappings": {"default": {"default": "o con punto arriba"}}}, {"key": "0233", "mappings": {"default": {"default": "y con Macron"}}}, {"key": "0237", "mappings": {"default": {"default": "s in punto j"}}}, {"key": "1E01", "mappings": {"default": {"default": "a con anillo debajo"}}}, {"key": "1E03", "mappings": {"default": {"default": "b con punto arriba"}}}, {"key": "1E05", "mappings": {"default": {"default": "b con punto debajo"}}}, {"key": "1E07", "mappings": {"default": {"default": "b con línea debajo"}}}, {"key": "1E0B", "mappings": {"default": {"default": "d con punto arriba"}}}, {"key": "1E0D", "mappings": {"default": {"default": "d con punto debajo"}}}, {"key": "1E0F", "mappings": {"default": {"default": "d con línea debajo"}}}, {"key": "1E11", "mappings": {"default": {"default": "d con cedilla"}}}, {"key": "1E13", "mappings": {"default": {"default": "d con acento circunflejo debajo"}}}, {"key": "1E19", "mappings": {"default": {"default": "e con acento circunflejo debajo"}}}, {"key": "1E1B", "mappings": {"default": {"default": "e con tilde debajo"}}}, {"key": "1E1F", "mappings": {"default": {"default": "f con punto arriba"}}}, {"key": "1E21", "mappings": {"default": {"default": "g con macrón"}}}, {"key": "1E23", "mappings": {"default": {"default": "h con punto arriba"}}}, {"key": "1E25", "mappings": {"default": {"default": "h con punto debajo"}}}, {"key": "1E27", "mappings": {"default": {"default": "h con diéresis"}}}, {"key": "1E29", "mappings": {"default": {"default": "h con cedilla"}}}, {"key": "1E2B", "mappings": {"default": {"default": "h con breve debajo"}}}, {"key": "1E2D", "mappings": {"default": {"default": "i con tilde debajo"}}}, {"key": "1E31", "mappings": {"default": {"default": "k con agudo"}}}, {"key": "1E33", "mappings": {"default": {"default": "k con punto debajo"}}}, {"key": "1E35", "mappings": {"default": {"default": "k con línea debajo"}}}, {"key": "1E37", "mappings": {"default": {"default": "l con punto debajo"}}}, {"key": "1E3B", "mappings": {"default": {"default": "l con línea debajo"}}}, {"key": "1E3D", "mappings": {"default": {"default": "l con acento circunflejo debajo"}}}, {"key": "1E3F", "mappings": {"default": {"default": "m con agudo"}}}, {"key": "1E41", "mappings": {"default": {"default": "m con punto arriba"}}}, {"key": "1E43", "mappings": {"default": {"default": "m con punto debajo"}}}, {"key": "1E45", "mappings": {"default": {"default": "n con punto arriba"}}}, {"key": "1E47", "mappings": {"default": {"default": "n con punto debajo"}}}, {"key": "1E49", "mappings": {"default": {"default": "n con línea debajo"}}}, {"key": "1E4B", "mappings": {"default": {"default": "n con acento circunflejo debajo"}}}, {"key": "1E55", "mappings": {"default": {"default": "p con agudo"}}}, {"key": "1E57", "mappings": {"default": {"default": "p con punto arriba"}}}, {"key": "1E59", "mappings": {"default": {"default": "r con punto arriba"}}}, {"key": "1E5B", "mappings": {"default": {"default": "r con punto debajo"}}}, {"key": "1E5F", "mappings": {"default": {"default": "r con línea debajo"}}}, {"key": "1E61", "mappings": {"default": {"default": "s con punto arriba"}}}, {"key": "1E63", "mappings": {"default": {"default": "s con punto debajo"}}}, {"key": "1E6B", "mappings": {"default": {"default": "t con punto arriba"}}}, {"key": "1E6D", "mappings": {"default": {"default": "t con punto debajo"}}}, {"key": "1E6F", "mappings": {"default": {"default": "t con línea debajo"}}}, {"key": "1E71", "mappings": {"default": {"default": "t con acento circunflejo debajo"}}}, {"key": "1E73", "mappings": {"default": {"default": "u con diéresis debajo"}}}, {"key": "1E75", "mappings": {"default": {"default": "u con tilde debajo"}}}, {"key": "1E77", "mappings": {"default": {"default": "u con acento circunflejo debajo"}}}, {"key": "1E7D", "mappings": {"default": {"default": "v con tilde"}}}, {"key": "1E7F", "mappings": {"default": {"default": "v con punto debajo"}}}, {"key": "1E81", "mappings": {"default": {"default": "w con grave"}}}, {"key": "1E83", "mappings": {"default": {"default": "w con agudo"}}}, {"key": "1E85", "mappings": {"default": {"default": "w con diéresis"}}}, {"key": "1E87", "mappings": {"default": {"default": "w con punto arriba"}}}, {"key": "1E89", "mappings": {"default": {"default": "w con punto debajo"}}}, {"key": "1E8B", "mappings": {"default": {"default": "x con punto arriba"}}}, {"key": "1E8D", "mappings": {"default": {"default": "x con diéresis"}}}, {"key": "1E8F", "mappings": {"default": {"default": "y con punto arriba"}}}, {"key": "1E91", "mappings": {"default": {"default": "z con acento circunflejo"}}}, {"key": "1E93", "mappings": {"default": {"default": "z con punto debajo"}}}, {"key": "1E95", "mappings": {"default": {"default": "z con línea debajo"}}}, {"key": "1E96", "mappings": {"default": {"default": "h con línea debajo"}}}, {"key": "1E97", "mappings": {"default": {"default": "t con diéresis"}}}, {"key": "1E98", "mappings": {"default": {"default": "w con anillo arriba"}}}, {"key": "1E99", "mappings": {"default": {"default": "y con anillo arriba"}}}, {"key": "1E9A", "mappings": {"default": {"default": "a con medio derecho de anillo"}}}, {"key": "1EA1", "mappings": {"default": {"default": "a con punto debajo"}}}, {"key": "1EA3", "mappings": {"default": {"default": "a con gancho arriba"}}}, {"key": "1EB9", "mappings": {"default": {"default": "e con punto debajo"}}}, {"key": "1EBB", "mappings": {"default": {"default": "e con gancho arriba"}}}, {"key": "1EBD", "mappings": {"default": {"default": "e con tilde"}}}, {"key": "1EC9", "mappings": {"default": {"default": "i con gancho arriba"}}}, {"key": "1ECB", "mappings": {"default": {"default": "i con punto debajo"}}}, {"key": "1ECD", "mappings": {"default": {"default": "o con punto debajo"}}}, {"key": "1ECF", "mappings": {"default": {"default": "o con gancho arriba"}}}, {"key": "1EE5", "mappings": {"default": {"default": "u con punto debajo"}}}, {"key": "1EE7", "mappings": {"default": {"default": "u con gancho arriba"}}}, {"key": "1EF3", "mappings": {"default": {"default": "y con grave"}}}, {"key": "1EF5", "mappings": {"default": {"default": "y con punto debajo"}}}, {"key": "1EF7", "mappings": {"default": {"default": "y con gancho arriba"}}}, {"key": "1EF9", "mappings": {"default": {"default": "y con tilde"}}}], "es/symbols/latin-rest.min": [{"locale": "es"}, {"key": "210E", "mappings": {"default": {"physics": "constante de planck"}}}, {"key": "0363", "mappings": {"default": {"default": "combinando a"}}}, {"key": "0364", "mappings": {"default": {"default": "combinando e"}}}, {"key": "0365", "mappings": {"default": {"default": "combinando i"}}}, {"key": "0366", "mappings": {"default": {"default": "combinando o"}}}, {"key": "0367", "mappings": {"default": {"default": "combinando u"}}}, {"key": "0368", "mappings": {"default": {"default": "combinando c"}}}, {"key": "0369", "mappings": {"default": {"default": "combinando d"}}}, {"key": "036A", "mappings": {"default": {"default": "combinando h"}}}, {"key": "036B", "mappings": {"default": {"default": "combinando m"}}}, {"key": "036C", "mappings": {"default": {"default": "combinando r"}}}, {"key": "036D", "mappings": {"default": {"default": "combinando t"}}}, {"key": "036E", "mappings": {"default": {"default": "combinando v"}}}, {"key": "036F", "mappings": {"default": {"default": "combinando x"}}}, {"key": "1D62", "mappings": {"default": {"default": "subíndice i"}}}, {"key": "1D63", "mappings": {"default": {"default": "subíndice r"}}}, {"key": "1D64", "mappings": {"default": {"default": "subíndice u"}}}, {"key": "1D65", "mappings": {"default": {"default": "subíndice latino letra pequeña V"}}}, {"key": "1DCA", "mappings": {"default": {"default": "combinando r abajo"}}}, {"key": "1DD3", "mappings": {"default": {"default": "combinando letra minúscula en latín, aplanado, abierto arriba"}}}, {"key": "1DD4", "mappings": {"default": {"default": "combinando ae"}}}, {"key": "1DD5", "mappings": {"default": {"default": "combinando ao"}}}, {"key": "1DD6", "mappings": {"default": {"default": "combinando av"}}}, {"key": "1DD7", "mappings": {"default": {"default": "combinando Letra Pequeña Latina C Cedilla"}}}, {"key": "1DD8", "mappings": {"default": {"default": "combinando insular d"}}}, {"key": "1DD9", "mappings": {"default": {"default": "combinando la eth"}}}, {"key": "1DDA", "mappings": {"default": {"default": "combinando g"}}}, {"key": "1DDB", "mappings": {"default": {"default": "combinando pequeña mayúscula G"}}}, {"key": "1DDC", "mappings": {"default": {"default": "combinando k"}}}, {"key": "1DDD", "mappings": {"default": {"default": "combinando l"}}}, {"key": "1DDE", "mappings": {"default": {"default": "combinando pequeña mayúscula L"}}}, {"key": "1DDF", "mappings": {"default": {"default": "combinando pequeña mayúscula M"}}}, {"key": "1DE0", "mappings": {"default": {"default": "combinando n"}}}, {"key": "1DE1", "mappings": {"default": {"default": "combinando pequeña mayúscula N"}}}, {"key": "1DE2", "mappings": {"default": {"default": "combinando pequeña mayúscula R"}}}, {"key": "1DE3", "mappings": {"default": {"default": "combinando pequeña r rotunda"}}}, {"key": "1DE4", "mappings": {"default": {"default": "combinando s"}}}, {"key": "1DE5", "mappings": {"default": {"default": "combinando larga s"}}}, {"key": "1DE6", "mappings": {"default": {"default": "combinando z"}}}, {"key": "2071", "mappings": {"default": {"default": "superíndice i"}}}, {"key": "207F", "mappings": {"default": {"default": "superíndice n"}}}, {"key": "2090", "mappings": {"default": {"default": "subíndice a"}}}, {"key": "2091", "mappings": {"default": {"default": "subíndice e"}}}, {"key": "2092", "mappings": {"default": {"default": "subíndice o"}}}, {"key": "2093", "mappings": {"default": {"default": "subíndice x"}}}, {"key": "2094", "mappings": {"default": {"default": "subíndice schwa"}}}, {"key": "2095", "mappings": {"default": {"default": "subíndice h"}}}, {"key": "2096", "mappings": {"default": {"default": "subíndice k"}}}, {"key": "2097", "mappings": {"default": {"default": "subíndice l"}}}, {"key": "2098", "mappings": {"default": {"default": "subíndice m"}}}, {"key": "2099", "mappings": {"default": {"default": "subíndice n"}}}, {"key": "209A", "mappings": {"default": {"default": "subíndice p"}}}, {"key": "209B", "mappings": {"default": {"default": "subíndice s"}}}, {"key": "209C", "mappings": {"default": {"default": "subíndice t"}}}, {"key": "2C7C", "mappings": {"default": {"default": "subíndice j"}}}, {"key": "1F12A", "mappings": {"default": {"default": "concha de tortuga con corchetes mayúscula S"}}}, {"key": "1F12B", "mappings": {"default": {"default": "círculo itálico mayús<PERSON> C"}}}, {"key": "1F12C", "mappings": {"default": {"default": "círculo it<PERSON><PERSON> R"}}}, {"key": "1F18A", "mappings": {"default": {"default": "cruzada negativa cuadrada Mayúscula P"}}}], "es/symbols/latin-upper-double-accent.min": [{"locale": "es"}, {"key": "01D5", "mappings": {"default": {"default": "mayúscula U con diéresis y macron"}}}, {"key": "01D7", "mappings": {"default": {"default": "mayúscula U con diéresis y acento agudo"}}}, {"key": "01D9", "mappings": {"default": {"default": "mayúscula U con diéresis y acento"}}}, {"key": "01DB", "mappings": {"default": {"default": "mayúscula U con diéresis y grave"}}}, {"key": "01DE", "mappings": {"default": {"default": "mayúscula A con diéresis y macron"}}}, {"key": "01E0", "mappings": {"default": {"default": "mayúscula A con punto arriba y macron"}}}, {"key": "01EC", "mappings": {"default": {"default": "mayúscula O con pecíolo y macron"}}}, {"key": "01FA", "mappings": {"default": {"default": "mayúscula A con ring above y acento agudo"}}}, {"key": "022A", "mappings": {"default": {"default": "mayúscula O con diaéresis y macron"}}}, {"key": "022C", "mappings": {"default": {"default": "may<PERSON><PERSON> O con tilde y macron"}}}, {"key": "0230", "mappings": {"default": {"default": "mayúscula O con dot above y macron"}}}, {"key": "1E08", "mappings": {"default": {"default": "mayúscula C con cedilla y agudo"}}}, {"key": "1E14", "mappings": {"default": {"default": "may<PERSON>cula E con macrón y grave"}}}, {"key": "1E16", "mappings": {"default": {"default": "mayúscula E con macrón y agudo"}}}, {"key": "1E1C", "mappings": {"default": {"default": "mayúscula E with cedilla and breve"}}}, {"key": "1E2E", "mappings": {"default": {"default": "mayúscula I con diéresis y agudo"}}}, {"key": "1E38", "mappings": {"default": {"default": "mayúscula L con punto debajo y macrón"}}}, {"key": "1E4C", "mappings": {"default": {"default": "may<PERSON><PERSON> O con tilde y acute"}}}, {"key": "1E4E", "mappings": {"default": {"default": "may<PERSON>cula O con tilde y diéresis"}}}, {"key": "1E50", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> O con macrón y grave"}}}, {"key": "1E52", "mappings": {"default": {"default": "may<PERSON>cula O con macrón y agudo"}}}, {"key": "1E5C", "mappings": {"default": {"default": "mayúscula R con punto debajo y macrón"}}}, {"key": "1E64", "mappings": {"default": {"default": "mayúscula S con agudo y punto arriba"}}}, {"key": "1E66", "mappings": {"default": {"default": "mayúscula S con carón y punto arriba"}}}, {"key": "1E68", "mappings": {"default": {"default": "mayúscula S con punto debajo y punto arriba"}}}, {"key": "1E78", "mappings": {"default": {"default": "mayúscula U con tilde y agudo"}}}, {"key": "1E7A", "mappings": {"default": {"default": "mayúscula U con macrón y diéresis"}}}, {"key": "1EA4", "mappings": {"default": {"default": "mayúscula A con acento circunflejo y agudo"}}}, {"key": "1EA6", "mappings": {"default": {"default": "mayúscula A con acento circunflejo y grave"}}}, {"key": "1EA8", "mappings": {"default": {"default": "mayúscula A con acento circunflejo y gancho arriba"}}}, {"key": "1EAA", "mappings": {"default": {"default": "mayúscula A con acento circunflejo y tilde"}}}, {"key": "1EAC", "mappings": {"default": {"default": "mayúscula A con acento circunflejo y punto debajo"}}}, {"key": "1EAE", "mappings": {"default": {"default": "mayúscula A con breve y agudo"}}}, {"key": "1EB0", "mappings": {"default": {"default": "mayúscula A con breve y grave"}}}, {"key": "1EB2", "mappings": {"default": {"default": "mayúscula A con breve y gancho arriba"}}}, {"key": "1EB4", "mappings": {"default": {"default": "mayúscula A con breve y tilde"}}}, {"key": "1EB6", "mappings": {"default": {"default": "mayúscula A con breve y punto debajo"}}}, {"key": "1EBE", "mappings": {"default": {"default": "mayúscula E con acento circunflejo y agudo"}}}, {"key": "1EC0", "mappings": {"default": {"default": "mayúscula E con acento circunflejo y grave"}}}, {"key": "1EC2", "mappings": {"default": {"default": "mayúscula E con acento circunflejo y gancho arriba"}}}, {"key": "1EC4", "mappings": {"default": {"default": "mayúscula E con acento circunflejo y tilde"}}}, {"key": "1EC6", "mappings": {"default": {"default": "mayúscula E con acento circunflejo y punto debajo"}}}, {"key": "1ED0", "mappings": {"default": {"default": "mayúscula O con acento circunflejo y agudo"}}}, {"key": "1ED2", "mappings": {"default": {"default": "may<PERSON>cula O con acento circunflejo y grave"}}}, {"key": "1ED4", "mappings": {"default": {"default": "mayúscula O con acento circunflejo y gancho arriba"}}}, {"key": "1ED6", "mappings": {"default": {"default": "may<PERSON>cula O con acento circunflejo y tilde"}}}, {"key": "1ED8", "mappings": {"default": {"default": "mayúscula O con acento circunflejo y punto debajo"}}}, {"key": "1EDA", "mappings": {"default": {"default": "mayúscula O with horn and acute"}}}, {"key": "1EDC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> with horn and grave"}}}, {"key": "1EDE", "mappings": {"default": {"default": "may<PERSON><PERSON> O with horn and hook above"}}}, {"key": "1EE0", "mappings": {"default": {"default": "may<PERSON><PERSON> O with horn and tilde"}}}, {"key": "1EE2", "mappings": {"default": {"default": "mayúscula O con cuerno y punto debajo"}}}, {"key": "1EE8", "mappings": {"default": {"default": "mayúscula U con cuerno y agudo"}}}, {"key": "1EEA", "mappings": {"default": {"default": "mayúscula U con cuerno y grave"}}}, {"key": "1EEC", "mappings": {"default": {"default": "mayúscula U con cuerno y gancho arriba"}}}, {"key": "1EEE", "mappings": {"default": {"default": "mayúscula U con cuerno y tilde"}}}, {"key": "1EF0", "mappings": {"default": {"default": "mayúscula U con cuerno y punto debajo"}}}], "es/symbols/latin-upper-single-accent.min": [{"locale": "es"}, {"key": "00C0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> A grave"}}}, {"key": "00C1", "mappings": {"default": {"default": "mayúscula A aguda"}}}, {"key": "00C2", "mappings": {"default": {"default": "mayúscula A acento circunflejo"}}}, {"key": "00C3", "mappings": {"default": {"default": "may<PERSON><PERSON> A con tilde"}}}, {"key": "00C4", "mappings": {"default": {"default": "mayúscula A diéresis"}}}, {"key": "00C5", "mappings": {"default": {"default": "may<PERSON>cula A con anillo"}}}, {"key": "00C7", "mappings": {"default": {"default": "mayúscula C cedilla"}}}, {"key": "00C8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "00C9", "mappings": {"default": {"default": "mayúscula E aguda"}}}, {"key": "00CA", "mappings": {"default": {"default": "mayúscula E acento circunflejo"}}}, {"key": "00CB", "mappings": {"default": {"default": "mayúscula E diéresis"}}}, {"key": "00CC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> I grave"}}}, {"key": "00CD", "mappings": {"default": {"default": "may<PERSON>cula I aguda"}}}, {"key": "00CE", "mappings": {"default": {"default": "mayúscula I acento circumflejo"}}}, {"key": "00CF", "mappings": {"default": {"default": "mayúscula I diéresis"}}}, {"key": "00D1", "mappings": {"default": {"default": "mayúscula Ñ"}}}, {"key": "00D2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "00D3", "mappings": {"default": {"default": "mayúscula O aguda"}}}, {"key": "00D4", "mappings": {"default": {"default": "may<PERSON>cula O acento circunflejo"}}}, {"key": "00D5", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> O con tilde"}}}, {"key": "00D6", "mappings": {"default": {"default": "mayúscula O diéresis"}}}, {"key": "00D9", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> U grave"}}}, {"key": "00DA", "mappings": {"default": {"default": "mayúscula U aguda"}}}, {"key": "00DB", "mappings": {"default": {"default": "mayúscula U acento circunflejo"}}}, {"key": "00DC", "mappings": {"default": {"default": "mayúscula U diéresis"}}}, {"key": "00DD", "mappings": {"default": {"default": "mayúscula Y aguda"}}}, {"key": "0100", "mappings": {"default": {"default": "mayúscula A barra"}}}, {"key": "0102", "mappings": {"default": {"default": "mayúscula A con breve"}}}, {"key": "0104", "mappings": {"default": {"default": "mayúscula A con ogonek"}}}, {"key": "0106", "mappings": {"default": {"default": "mayúscula C con agudo"}}}, {"key": "0108", "mappings": {"default": {"default": "mayúscula C con acento circunflejo"}}}, {"key": "010A", "mappings": {"default": {"default": "mayúscula C con punto arriba"}}}, {"key": "010C", "mappings": {"default": {"default": "mayúscula C con carón"}}}, {"key": "010E", "mappings": {"default": {"default": "mayúscula D con carón"}}}, {"key": "0112", "mappings": {"default": {"default": "may<PERSON>cula E barra"}}}, {"key": "0114", "mappings": {"default": {"default": "mayúscula E con breve"}}}, {"key": "0116", "mappings": {"default": {"default": "mayúscula E con punto arriba"}}}, {"key": "0118", "mappings": {"default": {"default": "mayúscula E con ogonek"}}}, {"key": "011A", "mappings": {"default": {"default": "mayúscula E con carón"}}}, {"key": "011C", "mappings": {"default": {"default": "mayúscula G con acento circunflejo"}}}, {"key": "011E", "mappings": {"default": {"default": "mayúscula G con breve"}}}, {"key": "0120", "mappings": {"default": {"default": "mayúscula G con punto arriba"}}}, {"key": "0122", "mappings": {"default": {"default": "mayúscula G con cedilla"}}}, {"key": "0124", "mappings": {"default": {"default": "mayúscula H con acento circunflejo"}}}, {"key": "0128", "mappings": {"default": {"default": "may<PERSON><PERSON> I con tilde"}}}, {"key": "012A", "mappings": {"default": {"default": "may<PERSON>cula I barra"}}}, {"key": "012C", "mappings": {"default": {"default": "mayúscula I con breve"}}}, {"key": "012E", "mappings": {"default": {"default": "may<PERSON><PERSON> I con ogonek"}}}, {"key": "0130", "mappings": {"default": {"default": "mayúscula I con punto arriba"}}}, {"key": "0134", "mappings": {"default": {"default": "mayúscula J con acento circunflejo"}}}, {"key": "0136", "mappings": {"default": {"default": "mayúscula K con cedilla"}}}, {"key": "0139", "mappings": {"default": {"default": "may<PERSON>cula L con agudo"}}}, {"key": "013B", "mappings": {"default": {"default": "mayúscula L con cedilla"}}}, {"key": "013D", "mappings": {"default": {"default": "mayúscula L con carón"}}}, {"key": "013F", "mappings": {"default": {"default": "mayúscula L con punto mediano"}}}, {"key": "0143", "mappings": {"default": {"default": "may<PERSON>cula N con agudo"}}}, {"key": "0145", "mappings": {"default": {"default": "mayúscula N con cedilla"}}}, {"key": "0147", "mappings": {"default": {"default": "mayúscula n con carón"}}}, {"key": "014C", "mappings": {"default": {"default": "may<PERSON><PERSON> O barra"}}}, {"key": "014E", "mappings": {"default": {"default": "mayúscula O con breve"}}}, {"key": "0150", "mappings": {"default": {"default": "may<PERSON><PERSON> O con doble acento agudo "}}}, {"key": "0154", "mappings": {"default": {"default": "may<PERSON><PERSON> R con agudo"}}}, {"key": "0156", "mappings": {"default": {"default": "mayúscula R con cedilla"}}}, {"key": "0158", "mappings": {"default": {"default": "mayúscula R con carón"}}}, {"key": "015A", "mappings": {"default": {"default": "may<PERSON><PERSON> S con agudo"}}}, {"key": "015C", "mappings": {"default": {"default": "mayúscula S con acento circunflejo"}}}, {"key": "015E", "mappings": {"default": {"default": "mayúscula S con cedilla"}}}, {"key": "0160", "mappings": {"default": {"default": "mayúscula S con carón"}}}, {"key": "0162", "mappings": {"default": {"default": "mayúscula T con cedilla"}}}, {"key": "0164", "mappings": {"default": {"default": "mayúscula T con carón"}}}, {"key": "0168", "mappings": {"default": {"default": "mayúscula U con tilde"}}}, {"key": "016A", "mappings": {"default": {"default": "mayúscula U barra"}}}, {"key": "016C", "mappings": {"default": {"default": "mayúscula U con breve"}}}, {"key": "016E", "mappings": {"default": {"default": "mayúscula U con anillo arriba"}}}, {"key": "0170", "mappings": {"default": {"default": "mayúscula U con doble acento agudo"}}}, {"key": "0172", "mappings": {"default": {"default": "mayúscula U con ogonek"}}}, {"key": "0174", "mappings": {"default": {"default": "may<PERSON>cula W con acento circunflejo"}}}, {"key": "0176", "mappings": {"default": {"default": "mayúscula Y con circunflejo"}}}, {"key": "0178", "mappings": {"default": {"default": "mayúscula Y diéresis"}}}, {"key": "0179", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> con agudo"}}}, {"key": "017B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> Z con punto"}}}, {"key": "017D", "mappings": {"default": {"default": "may<PERSON>cula Z con carón"}}}, {"key": "01CD", "mappings": {"default": {"default": "mayúscula A con acento"}}}, {"key": "01CF", "mappings": {"default": {"default": "may<PERSON>cula I con acento"}}}, {"key": "01D1", "mappings": {"default": {"default": "may<PERSON><PERSON> O con acento"}}}, {"key": "01D3", "mappings": {"default": {"default": "mayúscula U con acento"}}}, {"key": "01E6", "mappings": {"default": {"default": "mayúscula G con acento"}}}, {"key": "01E8", "mappings": {"default": {"default": "mayúscula K con acento"}}}, {"key": "01EA", "mappings": {"default": {"default": "mayúscula O con pecíolo"}}}, {"key": "01F4", "mappings": {"default": {"default": "mayúscula G con acento agudo"}}}, {"key": "01F8", "mappings": {"default": {"default": "may<PERSON><PERSON> N con grave"}}}, {"key": "0200", "mappings": {"default": {"default": "may<PERSON><PERSON> con doble tumba"}}}, {"key": "0202", "mappings": {"default": {"default": "mayúscula a con breve invertido"}}}, {"key": "0204", "mappings": {"default": {"default": "may<PERSON><PERSON> E con doble tumba"}}}, {"key": "0206", "mappings": {"default": {"default": "mayúscula E con Breve invertido"}}}, {"key": "0208", "mappings": {"default": {"default": "may<PERSON><PERSON> I con doble tumba"}}}, {"key": "020A", "mappings": {"default": {"default": "mayúscula I con Breve invertido"}}}, {"key": "020C", "mappings": {"default": {"default": "may<PERSON><PERSON> O con doble tumba"}}}, {"key": "020E", "mappings": {"default": {"default": "mayúscula O con Breve invertido"}}}, {"key": "0210", "mappings": {"default": {"default": "may<PERSON><PERSON> R con doble tumba"}}}, {"key": "0212", "mappings": {"default": {"default": "mayúscula R con breve invertido"}}}, {"key": "0214", "mappings": {"default": {"default": "may<PERSON><PERSON> U con doble tumba"}}}, {"key": "0216", "mappings": {"default": {"default": "mayúscula U con breve invertido"}}}, {"key": "0218", "mappings": {"default": {"default": "mayúscula S con coma debajo"}}}, {"key": "021A", "mappings": {"default": {"default": "mayúscula T con coma debajo"}}}, {"key": "021E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> con Car<PERSON>"}}}, {"key": "0226", "mappings": {"default": {"default": "may<PERSON>cula con un punto arriba"}}}, {"key": "0228", "mappings": {"default": {"default": "mayúscula E con cedilla"}}}, {"key": "022E", "mappings": {"default": {"default": "mayúscula O con punto arriba"}}}, {"key": "0232", "mappings": {"default": {"default": "Mayúscula Y con Macron"}}}, {"key": "1E00", "mappings": {"default": {"default": "mayúscula A con anillo debajo"}}}, {"key": "1E02", "mappings": {"default": {"default": "mayúscula B con punto arriba"}}}, {"key": "1E04", "mappings": {"default": {"default": "mayúscula B con punto debajo"}}}, {"key": "1E06", "mappings": {"default": {"default": "mayúscula B con línea debajo"}}}, {"key": "1E0A", "mappings": {"default": {"default": "mayúscula D con punto arriba"}}}, {"key": "1E0C", "mappings": {"default": {"default": "mayúscula D con punto debajo"}}}, {"key": "1E0E", "mappings": {"default": {"default": "mayúscula D con línea debajo"}}}, {"key": "1E10", "mappings": {"default": {"default": "mayúscula D con cedilla"}}}, {"key": "1E12", "mappings": {"default": {"default": "mayúscula D con acento circunflejo debajo"}}}, {"key": "1E18", "mappings": {"default": {"default": "mayúscula E con acento circunflejo debajo"}}}, {"key": "1E1A", "mappings": {"default": {"default": "may<PERSON><PERSON> E con tilde debajo"}}}, {"key": "1E1E", "mappings": {"default": {"default": "mayúscula F con punto arriba"}}}, {"key": "1E20", "mappings": {"default": {"default": "may<PERSON><PERSON> G con macrón"}}}, {"key": "1E22", "mappings": {"default": {"default": "mayúscula H con punto arriba"}}}, {"key": "1E24", "mappings": {"default": {"default": "mayúscula H con punto debajo"}}}, {"key": "1E26", "mappings": {"default": {"default": "mayúscula H con diéresis"}}}, {"key": "1E28", "mappings": {"default": {"default": "mayúscula H con cedilla"}}}, {"key": "1E2A", "mappings": {"default": {"default": "mayúscula H con breve debajo"}}}, {"key": "1E2C", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> I con tilde debajo"}}}, {"key": "1E30", "mappings": {"default": {"default": "may<PERSON><PERSON> K con agudo"}}}, {"key": "1E32", "mappings": {"default": {"default": "mayúscula K con punto debajo"}}}, {"key": "1E34", "mappings": {"default": {"default": "mayúscula K con línea debajo"}}}, {"key": "1E36", "mappings": {"default": {"default": "mayúscula L con punto debajo"}}}, {"key": "1E3A", "mappings": {"default": {"default": "mayúscula L con línea debajo"}}}, {"key": "1E3C", "mappings": {"default": {"default": "mayúscula L con acento circunflejo debajo"}}}, {"key": "1E3E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> con agudo"}}}, {"key": "1E40", "mappings": {"default": {"default": "mayúscula M con punto arriba"}}}, {"key": "1E42", "mappings": {"default": {"default": "may<PERSON>cula M con punto debajo"}}}, {"key": "1E44", "mappings": {"default": {"default": "mayúscula N con punto arriba"}}}, {"key": "1E46", "mappings": {"default": {"default": "mayúscula N con punto debajo"}}}, {"key": "1E48", "mappings": {"default": {"default": "mayúscula N con línea debajo"}}}, {"key": "1E4A", "mappings": {"default": {"default": "mayúscula N con acento circunflejo debajo"}}}, {"key": "1E54", "mappings": {"default": {"default": "may<PERSON><PERSON> P con agudo"}}}, {"key": "1E56", "mappings": {"default": {"default": "mayúscula P con punto arriba"}}}, {"key": "1E58", "mappings": {"default": {"default": "mayúscula R con punto arriba"}}}, {"key": "1E5A", "mappings": {"default": {"default": "mayúscula R con punto debajo"}}}, {"key": "1E5E", "mappings": {"default": {"default": "mayúscula R con línea debajo"}}}, {"key": "1E60", "mappings": {"default": {"default": "mayúscula S con punto arriba"}}}, {"key": "1E62", "mappings": {"default": {"default": "mayúscula S con punto debajo"}}}, {"key": "1E6A", "mappings": {"default": {"default": "mayúscula T con punto arriba"}}}, {"key": "1E6C", "mappings": {"default": {"default": "mayúscula T con punto debajo"}}}, {"key": "1E6E", "mappings": {"default": {"default": "mayúscula T con línea debajo"}}}, {"key": "1E70", "mappings": {"default": {"default": "mayúscula T con acento circunflejo debajo"}}}, {"key": "1E72", "mappings": {"default": {"default": "mayúscula U con diéresis debajo"}}}, {"key": "1E74", "mappings": {"default": {"default": "mayúscula U con tilde debajo"}}}, {"key": "1E76", "mappings": {"default": {"default": "mayúscula U con acento circunflejo debajo"}}}, {"key": "1E7C", "mappings": {"default": {"default": "may<PERSON><PERSON> V con tilde"}}}, {"key": "1E7E", "mappings": {"default": {"default": "mayúscula V con punto debajo"}}}, {"key": "1E80", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> W con grave"}}}, {"key": "1E82", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> con agudo"}}}, {"key": "1E84", "mappings": {"default": {"default": "mayúscula W con diéresis"}}}, {"key": "1E86", "mappings": {"default": {"default": "mayúscula W con punto arriba"}}}, {"key": "1E88", "mappings": {"default": {"default": "mayúscula W con punto debajo"}}}, {"key": "1E8A", "mappings": {"default": {"default": "mayúscula X con punto arriba"}}}, {"key": "1E8C", "mappings": {"default": {"default": "mayúscula X con diéresis"}}}, {"key": "1E8E", "mappings": {"default": {"default": "mayúscula Y con punto arriba"}}}, {"key": "1E90", "mappings": {"default": {"default": "may<PERSON>cula Z con acento circunflejo"}}}, {"key": "1E92", "mappings": {"default": {"default": "may<PERSON>cula Z con punto debajo"}}}, {"key": "1E94", "mappings": {"default": {"default": "mayúscula Z with line below"}}}, {"key": "1EA0", "mappings": {"default": {"default": "mayúscula A con punto debajo"}}}, {"key": "1EA2", "mappings": {"default": {"default": "mayúscula A con gancho arriba"}}}, {"key": "1EB8", "mappings": {"default": {"default": "mayúscula E con punto debajo"}}}, {"key": "1EBA", "mappings": {"default": {"default": "mayúscula E con gancho arriba"}}}, {"key": "1EBC", "mappings": {"default": {"default": "may<PERSON><PERSON> E con tilde"}}}, {"key": "1EC8", "mappings": {"default": {"default": "may<PERSON>cula I con gancho arriba"}}}, {"key": "1ECA", "mappings": {"default": {"default": "mayúscula I con punto debajo"}}}, {"key": "1ECC", "mappings": {"default": {"default": "may<PERSON>cula O con punto debajo"}}}, {"key": "1ECE", "mappings": {"default": {"default": "may<PERSON>cula O con gancho arriba"}}}, {"key": "1EE4", "mappings": {"default": {"default": "mayúscula U con punto debajo"}}}, {"key": "1EE6", "mappings": {"default": {"default": "mayúscula U con gancho arriba"}}}, {"key": "1EF2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> Y con grave"}}}, {"key": "1EF4", "mappings": {"default": {"default": "mayúscula Y con punto debajo"}}}, {"key": "1EF6", "mappings": {"default": {"default": "mayúscula Y con gancho arriba"}}}, {"key": "1EF8", "mappings": {"default": {"default": "may<PERSON><PERSON> Y con tilde"}}}], "es/symbols/math_angles.min": [{"locale": "es"}, {"key": "22BE", "mappings": {"default": {"default": "ángulo recto con arco"}}}, {"key": "237C", "mappings": {"default": {"default": "ángulo recto con flecha zigzag hacia abajo"}}}, {"key": "27C0", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "299B", "mappings": {"default": {"default": "Ángulo medido de apertura a la izquierda"}}}, {"key": "299C", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> de ángulo recto con cuadrado"}}}, {"key": "299D", "mappings": {"default": {"default": "ángulo recto medido con punto"}}}, {"key": "299E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON> con <PERSON>"}}}, {"key": "299F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "29A0", "mappings": {"default": {"default": "Ángulo esférico de apertura a la izquierda"}}}, {"key": "29A1", "mappings": {"default": {"default": "Apertura de ángulo esférico"}}}, {"key": "29A2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "29A3", "mappings": {"default": {"default": "Ángulo invertido"}}}, {"key": "29A4", "mappings": {"default": {"default": "ángulo con barra inferior"}}}, {"key": "29A5", "mappings": {"default": {"default": "ángulo inverso con barra inferior"}}}, {"key": "29A6", "mappings": {"default": {"default": "ángulo oblicuo hacia arriba"}}}, {"key": "29A7", "mappings": {"default": {"default": "ángulo oblicuo hacia abajo"}}}, {"key": "29A8", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing up and to the right"}}}, {"key": "29A9", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing up and to the left"}}}, {"key": "29AA", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing down and to the right"}}}, {"key": "29AB", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing down and to the left"}}}, {"key": "29AC", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing right and up"}}}, {"key": "29AD", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing left and up"}}}, {"key": "29AE", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing right and down"}}}, {"key": "29AF", "mappings": {"default": {"default": "measured angle with open arm ending in arrow pointing left and down"}}}], "es/symbols/math_arrows.min": [{"locale": "es"}, {"key": "2190", "mappings": {"default": {"default": "flecha i<PERSON>a"}}}, {"key": "2191", "mappings": {"default": {"default": "flecha arriba"}}}, {"key": "2192", "mappings": {"default": {"default": "flecha derecha", "defaultMP": "flecha"}}}, {"key": "2193", "mappings": {"default": {"default": "flecha abajo"}}}, {"key": "2194", "mappings": {"default": {"default": "flecha izquierda y derecha"}}}, {"key": "2195", "mappings": {"default": {"default": "flecha arriba y abajo"}}}, {"key": "2196", "mappings": {"default": {"default": "flecha a noroeste"}}}, {"key": "2197", "mappings": {"default": {"default": "flecha a nordeste"}}}, {"key": "2198", "mappings": {"default": {"default": "flecha a sureste"}}}, {"key": "2199", "mappings": {"default": {"default": "flecha a suroeste"}}}, {"key": "219A", "mappings": {"default": {"default": "flecha izquierda tachada"}}}, {"key": "219B", "mappings": {"default": {"default": "flecha tachada"}}}, {"key": "219C", "mappings": {"default": {"default": "flecha ondulada i<PERSON>a"}}}, {"key": "219D", "mappings": {"default": {"default": "flecha ondulada"}}}, {"key": "219E", "mappings": {"default": {"default": "flecha izquierda con doble punta"}}}, {"key": "219F", "mappings": {"default": {"default": "flecha con doble punta hacia arriba"}}}, {"key": "21A0", "mappings": {"default": {"default": "flecha derechaa con doble punta"}}}, {"key": "21A1", "mappings": {"default": {"default": "flecha con doble punta hacia abajo"}}}, {"key": "21A2", "mappings": {"default": {"default": "flecha izquierda con cola"}}}, {"key": "21A3", "mappings": {"default": {"default": "flecha derecha con cola"}}}, {"key": "21A4", "mappings": {"default": {"default": "flecha izqui<PERSON>a de <PERSON>ra"}}}, {"key": "21A5", "mappings": {"default": {"default": "barra con flecha"}}}, {"key": "21A6", "mappings": {"default": {"default": "flecha derecha de barra"}}}, {"key": "21A7", "mappings": {"default": {"default": "flecha con barra"}}}, {"key": "21A8", "mappings": {"default": {"default": "flecha arriba y abajo con base"}}}, {"key": "21A9", "mappings": {"default": {"default": "flecha izquierda con gancho"}}}, {"key": "21AA", "mappings": {"default": {"default": "flecha con gancho"}}}, {"key": "21AB", "mappings": {"default": {"default": "flecha izquierda con lazo"}}}, {"key": "21AC", "mappings": {"default": {"default": "flecha con lazo"}}}, {"key": "21AD", "mappings": {"default": {"default": "flecha ondulada izquierda y derecha"}}}, {"key": "21AE", "mappings": {"default": {"default": "flecha izquierda y derecha tachada"}}}, {"key": "21AF", "mappings": {"default": {"default": "flecha zigzag abajo"}}}, {"key": "21B0", "mappings": {"default": {"default": "flecha arriba con punta izquierda"}}}, {"key": "21B1", "mappings": {"default": {"default": "flecha arriba con punta derecha"}}}, {"key": "21B2", "mappings": {"default": {"default": "flecha abajo con punta izquierda"}}}, {"key": "21B3", "mappings": {"default": {"default": "flecha abajo con punta derecha"}}}, {"key": "21B4", "mappings": {"default": {"default": "flecha derecha desde esquina inferior"}}}, {"key": "21B5", "mappings": {"default": {"default": "flecha hacia abajo desde esquina izquierda"}}}, {"key": "21B6", "mappings": {"default": {"default": "flecha semicircular superior en sentido antihorario"}}}, {"key": "21B7", "mappings": {"default": {"default": "flecha semicircular superior en sentido horario"}}}, {"key": "21B8", "mappings": {"default": {"default": "flecha nor<PERSON>te a barra larga"}}}, {"key": "21B9", "mappings": {"default": {"default": "flecha izquierda a barra arriba de flecha derecha a barra"}}}, {"key": "21BA", "mappings": {"default": {"default": "flecha circular abierta en sentido antihorario"}}}, {"key": "21BB", "mappings": {"default": {"default": "flecha circular abierta en sentido horario"}}}, {"key": "21C4", "mappings": {"default": {"default": "flecha derecha arriba de flecha izquierda"}}}, {"key": "21C5", "mappings": {"default": {"default": "flecha arriba a la izquierda de flecha abajo"}}}, {"key": "21C6", "mappings": {"default": {"default": "flecha izquierda arriba de flecha derecha"}}}, {"key": "21C7", "mappings": {"default": {"default": "dos flechas hacia la izquierda"}}}, {"key": "21C8", "mappings": {"default": {"default": "dos flechas hacia arriba"}}}, {"key": "21C9", "mappings": {"default": {"default": "dos flechas"}}}, {"key": "21CA", "mappings": {"default": {"default": "dos flechas hacia abajo"}}}, {"key": "21CD", "mappings": {"default": {"default": "flecha doble izqui<PERSON>a tachada"}}}, {"key": "21CE", "mappings": {"default": {"default": "flecha doble izquierda y derecha tachada"}}}, {"key": "21CF", "mappings": {"default": {"default": "flecha doble tachada"}}}, {"key": "21D0", "mappings": {"default": {"default": "flecha doble hacia la izquierda"}}}, {"key": "21D1", "mappings": {"default": {"default": "flecha doble hacia arriba"}}}, {"key": "21D2", "mappings": {"default": {"default": "flecha doble"}}}, {"key": "21D3", "mappings": {"default": {"default": "flecha doble hacia abajo"}}}, {"key": "21D4", "mappings": {"default": {"default": "flecha doble izquierda-derecha"}}}, {"key": "21D5", "mappings": {"default": {"default": "flecha doble vertical"}}}, {"key": "21D6", "mappings": {"default": {"default": "flecha doble hacia noroeste"}}}, {"key": "21D7", "mappings": {"default": {"default": "flecha doble hacia nordeste"}}}, {"key": "21D8", "mappings": {"default": {"default": "flecha doble hacia sudeste"}}}, {"key": "21D9", "mappings": {"default": {"default": "flecha doble hacia sudoeste"}}}, {"key": "21DA", "mappings": {"default": {"default": "flecha triple izquierda"}}}, {"key": "21DB", "mappings": {"default": {"default": "flecha triple"}}}, {"key": "21DC", "mappings": {"default": {"default": "flecha ondulada i<PERSON>a"}}}, {"key": "21DD", "mappings": {"default": {"default": "flecha ondulada"}}}, {"key": "21DE", "mappings": {"default": {"default": "flecha hacia arriba con doble tachado"}}}, {"key": "21DF", "mappings": {"default": {"default": "flecha hacia abajo con doble tachado"}}}, {"key": "21E0", "mappings": {"default": {"default": "flecha de puntos hacia la izquierda"}}}, {"key": "21E1", "mappings": {"default": {"default": "flecha de puntos hacia arriba"}}}, {"key": "21E2", "mappings": {"default": {"default": "flecha de puntos"}}}, {"key": "21E3", "mappings": {"default": {"default": "flecha de puntos hacia abajo"}}}, {"key": "21E4", "mappings": {"default": {"default": "flecha izquierda a barra"}}}, {"key": "21E5", "mappings": {"default": {"default": "flecha derecha a barra"}}}, {"key": "21E6", "mappings": {"default": {"default": "flecha vacía hacia la izquierda"}}}, {"key": "21E7", "mappings": {"default": {"default": "flecha vacía hacia arriba"}}}, {"key": "21E8", "mappings": {"default": {"default": "flecha vacía"}}}, {"key": "21E9", "mappings": {"default": {"default": "flecha vacía hacia abajo"}}}, {"key": "21EA", "mappings": {"default": {"default": "flecha vacía hacia arriba desde barra"}}}, {"key": "21EB", "mappings": {"default": {"default": "flecha blanca hacia arriba en el pedestal"}}}, {"key": "21EC", "mappings": {"default": {"default": "flecha blanca hacia arriba sobre pedestal con barra horizontal"}}}, {"key": "21ED", "mappings": {"default": {"default": "flecha blanca hacia arriba sobre pedestal con barra vertical"}}}, {"key": "21EE", "mappings": {"default": {"default": "flecha doble hacia arriba blanca"}}}, {"key": "21EF", "mappings": {"default": {"default": "flecha doble hacia arriba blanca en pedestal"}}}, {"key": "21F0", "mappings": {"default": {"default": "flecha blanca hacia la derecha de la pared"}}}, {"key": "21F1", "mappings": {"default": {"default": "flecha del noroeste a la esquina"}}}, {"key": "21F2", "mappings": {"default": {"default": "flecha del sudeste a la esquina"}}}, {"key": "21F3", "mappings": {"default": {"default": "arriba abajo flecha blanca"}}}, {"key": "21F4", "mappings": {"default": {"default": "flecha derecha con círculo pequeño"}}}, {"key": "21F5", "mappings": {"default": {"default": "upwards arrow to the right of downwards arrow"}}}, {"key": "21F6", "mappings": {"default": {"default": "tres flechas a la derecha"}}}, {"key": "21F7", "mappings": {"default": {"default": "flecha hacia la izquierda con trazo vertical"}}}, {"key": "21F8", "mappings": {"default": {"default": "flecha hacia la derecha con trazo vertical"}}}, {"key": "21F9", "mappings": {"default": {"default": "flecha izquierda derecha con trazo vertical"}}}, {"key": "21FA", "mappings": {"default": {"default": "flecha hacia la izquierda con doble trazo vertical"}}}, {"key": "21FB", "mappings": {"default": {"default": "flecha hacia la derecha con doble trazo vertical"}}}, {"key": "21FC", "mappings": {"default": {"default": "flecha izquierda derecha con doble trazo vertical"}}}, {"key": "21FD", "mappings": {"default": {"default": "flecha izquierda con punta vacía"}}}, {"key": "21FE", "mappings": {"default": {"default": "flecha izquierda con punta vacía"}}}, {"key": "21FF", "mappings": {"default": {"default": "flecha izquierda derecha con punta vacía"}}}, {"key": "2301", "mappings": {"default": {"default": "flecha electrica"}}}, {"key": "2303", "mappings": {"default": {"default": "flecha arriba"}}}, {"key": "2304", "mappings": {"default": {"default": "flecha hacia abajo"}}}, {"key": "2324", "mappings": {"default": {"default": "flecha arriba entre dos barras horizontales"}}}, {"key": "238B", "mappings": {"default": {"default": "círculo roto con la flecha del noroeste"}}}, {"key": "2794", "mappings": {"default": {"default": "flecha hacia la derecha con cabeza ancha"}}}, {"key": "2798", "mappings": {"default": {"default": "flecha <PERSON> pesada"}}}, {"key": "2799", "mappings": {"default": {"default": "flecha hacia la derecha pesada"}}}, {"key": "279A", "mappings": {"default": {"default": "flecha pesada del noreste"}}}, {"key": "279B", "mappings": {"default": {"default": "punto de dibujo Flecha hacia la derecha"}}}, {"key": "279C", "mappings": {"default": {"default": "flecha hacia la derecha con punta redonda redonda"}}}, {"key": "279D", "mappings": {"default": {"default": "flecha hacia la derecha con forma de triángulo"}}}, {"key": "279E", "mappings": {"default": {"default": "flecha hacia la derecha con forma de triángulo pesado"}}}, {"key": "279F", "mappings": {"default": {"default": "triángulo punteado hacia la flecha hacia la derecha"}}}, {"key": "27A0", "mappings": {"default": {"default": "flecha hacia la derecha con forma de triángulo de trazo pesado"}}}, {"key": "27A1", "mappings": {"default": {"default": "flecha hacia la derecha negra"}}}, {"key": "27A2", "mappings": {"default": {"default": "punta de flecha tridimensional hacia la derecha hacia arriba"}}}, {"key": "27A3", "mappings": {"default": {"default": "punta de flecha hacia la derecha con iluminación inferior en tres dimensiones"}}}, {"key": "27A4", "mappings": {"default": {"default": "punta de flecha negra hacia la derecha"}}}, {"key": "27A5", "mappings": {"default": {"default": "pesado negro curvado hacia abajo y hacia la derecha flecha"}}}, {"key": "27A6", "mappings": {"default": {"default": "pesado negro curvado hacia arriba y hacia la derecha flecha"}}}, {"key": "27A7", "mappings": {"default": {"default": "squat Black Arrow hacia la derecha"}}}, {"key": "27A8", "mappings": {"default": {"default": "flecha hacia la derecha de color negro cóncava puntiaguda pesada"}}}, {"key": "27A9", "mappings": {"default": {"default": "sombra derecha flecha blanca hacia la derecha"}}}, {"key": "27AA", "mappings": {"default": {"default": "sombra hacia la izquierda blanca a la izquierda"}}}, {"key": "27AB", "mappings": {"default": {"default": "back-Tilted <PERSON><PERSON> White Right Arrow"}}}, {"key": "27AC", "mappings": {"default": {"default": "flecha hacia la derecha blanca sombreada inclinada hacia delante"}}}, {"key": "27AD", "mappings": {"default": {"default": "flecha hacia la derecha pesada inferior, sombreada a la derecha, blanca"}}}, {"key": "27AE", "mappings": {"default": {"default": "flecha hacia la derecha blanca superior derecha sombreada pesada"}}}, {"key": "27AF", "mappings": {"default": {"default": "con muesca, flecha derecha hacia abajo, a la derecha, hacia la derecha"}}}, {"key": "27B1", "mappings": {"default": {"default": "con muescas, flecha derecha hacia arriba, a la derecha, hacia la derecha"}}}, {"key": "27B2", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> pesado blanco flecha hacia la derecha"}}}, {"key": "27B3", "mappings": {"default": {"default": "flecha hacia la derecha con plumas blancas"}}}, {"key": "27B4", "mappings": {"default": {"default": "flecha suroriental de plumas negras"}}}, {"key": "27B5", "mappings": {"default": {"default": "flecha hacia la derecha con plumas negras"}}}, {"key": "27B6", "mappings": {"default": {"default": "flecha del noreste con plumas negras"}}}, {"key": "27B7", "mappings": {"default": {"default": "flecha suroriental con plumas negras pesadas"}}}, {"key": "27B8", "mappings": {"default": {"default": "flecha derecha pesada con plumas negras"}}}, {"key": "27B9", "mappings": {"default": {"default": "flecha del noreste con plumas negras pesadas"}}}, {"key": "27BA", "mappings": {"default": {"default": "lágrima-flecha de flecha hacia la derecha"}}}, {"key": "27BB", "mappings": {"default": {"default": "flecha hacia la derecha con forma de lágrima pesada"}}}, {"key": "27BC", "mappings": {"default": {"default": "flecha hacia la derecha de cola de cuña"}}}, {"key": "27BD", "mappings": {"default": {"default": "flecha hacia la derecha de cola de cuña pesada"}}}, {"key": "27BE", "mappings": {"default": {"default": "open-Outlined Outward Arrow"}}}, {"key": "27F0", "mappings": {"default": {"default": "flecha cuádr<PERSON>le hacia arriba"}}}, {"key": "27F1", "mappings": {"default": {"default": "flecha cu<PERSON><PERSON><PERSON>le hacia abajo"}}}, {"key": "27F2", "mappings": {"default": {"default": "flecha hacia la izquierda desde círculo"}}}, {"key": "27F3", "mappings": {"default": {"default": "flecha hacia la derecha desde círculo"}}}, {"key": "27F4", "mappings": {"default": {"default": "flecha con más en círculo"}}}, {"key": "27F5", "mappings": {"default": {"default": "flecha larga hacia la izquierda"}}}, {"key": "27F6", "mappings": {"default": {"default": "flecha larga"}}}, {"key": "27F7", "mappings": {"default": {"default": "flecha larga hacia izquierda y derecha"}}}, {"key": "27F8", "mappings": {"default": {"default": "doble flecha larga hacia la izquierda"}}}, {"key": "27F9", "mappings": {"default": {"default": "doble flecha larga"}}}, {"key": "27FA", "mappings": {"default": {"default": "doble flecha larga hacia izquierda y derecha"}}}, {"key": "27FB", "mappings": {"default": {"default": "flecha larga hacia la izquierda, desde barra"}}}, {"key": "27FC", "mappings": {"default": {"default": "flecha larga desde barra"}}}, {"key": "27FD", "mappings": {"default": {"default": "doble flecha larga hacia la izquierda desde barra"}}}, {"key": "27FE", "mappings": {"default": {"default": "doble flecha larga desde barra"}}}, {"key": "27FF", "mappings": {"default": {"default": "flecha larga en zigzag"}}}, {"key": "2900", "mappings": {"default": {"default": "flecha de dos cabezas hacia la derecha con trazo vertical"}}}, {"key": "2901", "mappings": {"default": {"default": "flecha de dos cabezas hacia la derecha con doble movimiento vertical"}}}, {"key": "2902", "mappings": {"default": {"default": "doble flecha hacia la izquierda con trazo vertical"}}}, {"key": "2903", "mappings": {"default": {"default": "doble flecha hacia la derecha con trazo vertical"}}}, {"key": "2904", "mappings": {"default": {"default": "izquierda Derecha Doble Flecha con Trazo Vertical"}}}, {"key": "2905", "mappings": {"default": {"default": "rightwards two headed arrow from bar"}}}, {"key": "2906", "mappings": {"default": {"default": "doble flecha hacia la izquierda de la barra"}}}, {"key": "2907", "mappings": {"default": {"default": "flecha doble hacia la derecha de la barra"}}}, {"key": "2908", "mappings": {"default": {"default": "flecha hacia abajo con trazo horizontal"}}}, {"key": "2909", "mappings": {"default": {"default": "flecha hacia arriba con trazo horizontal"}}}, {"key": "290A", "mappings": {"default": {"default": "flecha triple hacia arriba"}}}, {"key": "290B", "mappings": {"default": {"default": "triple flecha hacia abajo"}}}, {"key": "290C", "mappings": {"default": {"default": "leftwards double dash arrow"}}}, {"key": "290D", "mappings": {"default": {"default": "rightwards double dash arrow"}}}, {"key": "290E", "mappings": {"default": {"default": "leftwards triple dash arrow"}}}, {"key": "290F", "mappings": {"default": {"default": "rightwards triple dash arrow"}}}, {"key": "2910", "mappings": {"default": {"default": "rightwards two headed triple dash arrow"}}}, {"key": "2911", "mappings": {"default": {"default": "rightwards arrow with dotted stem"}}}, {"key": "2912", "mappings": {"default": {"default": "upwards arrow to bar"}}}, {"key": "2913", "mappings": {"default": {"default": "downwards arrow to bar"}}}, {"key": "2914", "mappings": {"default": {"default": "flecha hacia la derecha con cola con trazo vertical"}}}, {"key": "2915", "mappings": {"default": {"default": "flecha hacia la derecha con la cola con doble trazo vertical"}}}, {"key": "2916", "mappings": {"default": {"default": "rightwards two headed arrow with tail"}}}, {"key": "2917", "mappings": {"default": {"default": "flecha de dos cabezas hacia la derecha con cola con trazo vertical"}}}, {"key": "2918", "mappings": {"default": {"default": "flecha de dos cabezas hacia la derecha con cola con doble movimiento vertical"}}}, {"key": "2919", "mappings": {"default": {"default": "cola de flecha hacia la izquierda"}}}, {"key": "291A", "mappings": {"default": {"default": "flecha hacia la derecha"}}}, {"key": "291B", "mappings": {"default": {"default": "cola de doble flecha hacia la izquierda"}}}, {"key": "291C", "mappings": {"default": {"default": "cola de doble flecha hacia la derecha"}}}, {"key": "291D", "mappings": {"default": {"default": "flecha hacia diamante relleno a la izquierda"}}}, {"key": "291E", "mappings": {"default": {"default": "flecha hacia diamante a la derecha"}}}, {"key": "291F", "mappings": {"default": {"default": "flecha desde barra hacia diamante a la izquierda"}}}, {"key": "2920", "mappings": {"default": {"default": "flecha desde barra hacia diamante a la derecha"}}}, {"key": "2921", "mappings": {"default": {"default": "flechas del noroeste y sureste"}}}, {"key": "2922", "mappings": {"default": {"default": "flecha noreste y sudoeste"}}}, {"key": "2923", "mappings": {"default": {"default": "flecha a noroeste con garfio"}}}, {"key": "2924", "mappings": {"default": {"default": "flecha a nordeste con garfio"}}}, {"key": "2925", "mappings": {"default": {"default": "flecha a sureste con garfio"}}}, {"key": "2926", "mappings": {"default": {"default": "flecha a suroeste con garfio"}}}, {"key": "2927", "mappings": {"default": {"default": "flechas a noroeste y nordeste"}}}, {"key": "2928", "mappings": {"default": {"default": "flechas a nordeste y sudeste"}}}, {"key": "2929", "mappings": {"default": {"default": "flechas a suddeste y sudoeste"}}}, {"key": "292A", "mappings": {"default": {"default": "flechas a sudoeste y noroeste"}}}, {"key": "292D", "mappings": {"default": {"default": "flecha <PERSON>o <PERSON>"}}}, {"key": "292E", "mappings": {"default": {"default": "north East Arrow Crossing South East Arrow"}}}, {"key": "292F", "mappings": {"default": {"default": "caída en diagonal que cruza la flecha noreste"}}}, {"key": "2930", "mappings": {"default": {"default": "creciente Cruce de la Cruz del Sureste de la Flecha"}}}, {"key": "2931", "mappings": {"default": {"default": "north East Arrow Crossing North West Arrow"}}}, {"key": "2932", "mappings": {"default": {"default": "north West Arrow Crossing North East Arrow"}}}, {"key": "2933", "mappings": {"default": {"default": "flecha ondulada"}}}, {"key": "2934", "mappings": {"default": {"default": "flecha que apunta hacia la derecha y luego curva hacia arriba"}}}, {"key": "2935", "mappings": {"default": {"default": "flecha hacia la derecha que gira hacia abajo"}}}, {"key": "2936", "mappings": {"default": {"default": "flecha hacia abajo que gira hacia la izquierda"}}}, {"key": "2937", "mappings": {"default": {"default": "flecha hacia abajo que gira hacia la derecha"}}}, {"key": "2938", "mappings": {"default": {"default": "flecha semicircular a la derecha en sentido horario"}}}, {"key": "2939", "mappings": {"default": {"default": "flecha semicircular a la izquierda en sentido antihorario"}}}, {"key": "293A", "mappings": {"default": {"default": "arco superior flecha hacia la izquierda"}}}, {"key": "293B", "mappings": {"default": {"default": "arco inferior flecha hacia la izquierda"}}}, {"key": "293C", "mappings": {"default": {"default": "giro negativo"}}}, {"key": "293D", "mappings": {"default": {"default": "giro positivo"}}}, {"key": "293E", "mappings": {"default": {"default": "flecha hacia la derecha semicircular inferior derecha"}}}, {"key": "293F", "mappings": {"default": {"default": "flecha inferior izquierda semicircular en sentido antihorario"}}}, {"key": "2940", "mappings": {"default": {"default": "círculo cerrado en sentido contrario a las agujas del reloj"}}}, {"key": "2941", "mappings": {"default": {"default": "círculo cerrado en el sentido de las agujas del reloj"}}}, {"key": "2942", "mappings": {"default": {"default": "flecha hacia la derecha sobre la flecha corta hacia la izquierda"}}}, {"key": "2943", "mappings": {"default": {"default": "flecha hacia la izquierda sobre la flecha corta hacia la derecha"}}}, {"key": "2944", "mappings": {"default": {"default": "flecha corta hacia la derecha arriba flecha hacia la izquierda"}}}, {"key": "2945", "mappings": {"default": {"default": "flecha con más suscrito"}}}, {"key": "2946", "mappings": {"default": {"default": "flecha hacia la izquierda con más abajo"}}}, {"key": "2947", "mappings": {"default": {"default": "flecha hacia la derecha a través de X"}}}, {"key": "2948", "mappings": {"default": {"default": "flecha hacia la izquierda a través de círculo"}}}, {"key": "2949", "mappings": {"default": {"default": "dos cabezas de flecha hacia arriba desde círculo"}}}, {"key": "2970", "mappings": {"default": {"default": "round implies"}}}, {"key": "2971", "mappings": {"default": {"default": "flecha con igual"}}}, {"key": "2972", "mappings": {"default": {"default": "flecha con tilde"}}}, {"key": "2973", "mappings": {"default": {"default": "tilde con flecha hacia la izquierda"}}}, {"key": "2974", "mappings": {"default": {"default": "tilde con flecha"}}}, {"key": "2975", "mappings": {"default": {"default": "flecha con casi igual a"}}}, {"key": "2976", "mappings": {"default": {"default": "flecha hacia la izquierda con menor que"}}}, {"key": "2977", "mappings": {"default": {"default": "flecha hacia la izquierda a través de menos de"}}}, {"key": "2978", "mappings": {"default": {"default": "flecha con mayor que"}}}, {"key": "2979", "mappings": {"default": {"default": "flecha hacia la izquierda con incluido"}}}, {"key": "297A", "mappings": {"default": {"default": "flecha hacia la izquierda a través del subconjunto"}}}, {"key": "297B", "mappings": {"default": {"default": "flecha hacia la izquierda con contiene"}}}, {"key": "29B3", "mappings": {"default": {"default": "conjunto vacío con flecha"}}}, {"key": "29B4", "mappings": {"default": {"default": "conjunto vacío con flecha inversa"}}}, {"key": "29BD", "mappings": {"default": {"default": "flecha arriba a través del círculo"}}}, {"key": "29EA", "mappings": {"default": {"default": "diamante negro con flecha hacia abajo"}}}, {"key": "29EC", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> blanco con flecha hacia abajo"}}}, {"key": "29ED", "mappings": {"default": {"default": "círculo negro con flecha hacia abajo"}}}, {"key": "2A17", "mappings": {"default": {"default": "integral con flecha hacia la izquierda con garfio"}}}, {"key": "2B00", "mappings": {"default": {"default": "flecha blanca del noreste"}}}, {"key": "2B01", "mappings": {"default": {"default": "flecha blanca del noroeste"}}}, {"key": "2B02", "mappings": {"default": {"default": "flecha blanca del sureste"}}}, {"key": "2B03", "mappings": {"default": {"default": "sudoeste flecha blanca"}}}, {"key": "2B04", "mappings": {"default": {"default": "izquierda derecha flecha blanca"}}}, {"key": "2B05", "mappings": {"default": {"default": "flecha negra hacia la izquierda"}}}, {"key": "2B06", "mappings": {"default": {"default": "flecha negra hacia arriba"}}}, {"key": "2B07", "mappings": {"default": {"default": "flecha negra hacia abajo"}}}, {"key": "2B08", "mappings": {"default": {"default": "flecha negra del noreste"}}}, {"key": "2B09", "mappings": {"default": {"default": "flecha negra del noroeste"}}}, {"key": "2B0A", "mappings": {"default": {"default": "flecha negra sur este"}}}, {"key": "2B0B", "mappings": {"default": {"default": "sudoeste flecha negra"}}}, {"key": "2B0C", "mappings": {"default": {"default": "izquierda derecha flecha negra"}}}, {"key": "2B0D", "mappings": {"default": {"default": "arriba abajo flecha negra"}}}, {"key": "2B0E", "mappings": {"default": {"default": "flecha hacia la derecha con la punta hacia abajo"}}}, {"key": "2B0F", "mappings": {"default": {"default": "flecha hacia la derecha con la punta hacia arriba"}}}, {"key": "2B10", "mappings": {"default": {"default": "flecha hacia la izquierda con la punta hacia abajo"}}}, {"key": "2B11", "mappings": {"default": {"default": "flecha hacia la izquierda con la punta hacia arriba"}}}, {"key": "2B30", "mappings": {"default": {"default": "flecha izquierda con círculo pequeño"}}}, {"key": "2B31", "mappings": {"default": {"default": "tres flechas a la izquierda"}}}, {"key": "2B32", "mappings": {"default": {"default": "flecha izquierda con un círculo más"}}}, {"key": "2B33", "mappings": {"default": {"default": "flecha de flecha larga hacia la izquierda"}}}, {"key": "2B34", "mappings": {"default": {"default": "flecha de dos cabezas hacia la izquierda con trazo vertical"}}}, {"key": "2B35", "mappings": {"default": {"default": "flecha de dos cabezas hacia la izquierda con doble movimiento vertical"}}}, {"key": "2B36", "mappings": {"default": {"default": "flecha de dos cabezas hacia la izquierda de la barra"}}}, {"key": "2B37", "mappings": {"default": {"default": "flecha triple de dos cabezas hacia la izquierda"}}}, {"key": "2B38", "mappings": {"default": {"default": "flecha hacia la izquierda con tallo punteado"}}}, {"key": "2B39", "mappings": {"default": {"default": "flecha hacia la izquierda con cola con trazo vertical"}}}, {"key": "2B3A", "mappings": {"default": {"default": "flecha hacia la izquierda con cola con doble trazo vertical"}}}, {"key": "2B3B", "mappings": {"default": {"default": "flecha de dos cabezas hacia la izquierda con cola"}}}, {"key": "2B3C", "mappings": {"default": {"default": "flecha de dos cabezas hacia la izquierda con cola con trazo vertical"}}}, {"key": "2B3D", "mappings": {"default": {"default": "flecha de dos cabezas hacia la izquierda con cola con doble movimiento vertical"}}}, {"key": "2B3E", "mappings": {"default": {"default": "flecha hacia la izquierda a través de X"}}}, {"key": "2B3F", "mappings": {"default": {"default": "ola flecha apuntando directamente a la izquierda"}}}, {"key": "2B40", "mappings": {"default": {"default": "signo de igual flecha arriba a la izquierda"}}}, {"key": "2B41", "mappings": {"default": {"default": "operador de tilde inverso sobre la flecha hacia la izquierda"}}}, {"key": "2B42", "mappings": {"default": {"default": "flecha hacia la izquierda sobre el reverso casi igual a"}}}, {"key": "2B43", "mappings": {"default": {"default": "flecha hacia la derecha a través de mayor-que"}}}, {"key": "2B44", "mappings": {"default": {"default": "flecha hacia la derecha a través de Superset"}}}, {"key": "2B45", "mappings": {"default": {"default": "flecha cuádruple hacia la izquierda"}}}, {"key": "2B46", "mappings": {"default": {"default": "flecha cuádruple hacia la derecha"}}}, {"key": "2B47", "mappings": {"default": {"default": "operador de tilde inverso sobre la flecha hacia la derecha"}}}, {"key": "2B48", "mappings": {"default": {"default": "flecha hacia la derecha sobre el reverso casi igual a"}}}, {"key": "2B49", "mappings": {"default": {"default": "operador de tilde sobre la flecha hacia la izquierda"}}}, {"key": "2B4A", "mappings": {"default": {"default": "flecha hacia la izquierda por encima de casi igual a"}}}, {"key": "2B4B", "mappings": {"default": {"default": "flecha hacia la izquierda sobre el operador de tilde inverso"}}}, {"key": "2B4C", "mappings": {"default": {"default": "flecha hacia la derecha sobre el operador de tilde inverso"}}}, {"key": "FFE9", "mappings": {"default": {"default": "flecha hacia la izquierda de medio ancho"}}}, {"key": "FFEA", "mappings": {"default": {"default": "flecha ascendente de medio ancho"}}}, {"key": "FFEB", "mappings": {"default": {"default": "flecha de medio ancho hacia la derecha"}}}, {"key": "FFEC", "mappings": {"default": {"default": "flecha hacia abajo de medio ancho"}}}], "es/symbols/math_characters.min": [{"locale": "es"}, {"key": "2113", "mappings": {"default": {"default": "script l"}}}, {"key": "2118", "mappings": {"default": {"default": "p <PERSON> <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "213C", "mappings": {"default": {"default": "negrita de pizarra pi"}}}, {"key": "213D", "mappings": {"default": {"default": "negrita de pizarra gamma"}}}, {"key": "213E", "mappings": {"default": {"default": "negrita de pizarra mayúscula Gamma"}}}, {"key": "213F", "mappings": {"default": {"default": "negrita de pizarra may<PERSON> Pi"}}}, {"key": "2140", "mappings": {"default": {"default": "negrita de pizarra suma de n-ary"}}}, {"key": "2145", "mappings": {"default": {"default": "mayúscula D"}}}, {"key": "2146", "mappings": {"default": {"default": "d"}}}, {"key": "2147", "mappings": {"default": {"default": "e"}}}, {"key": "2148", "mappings": {"default": {"default": "i"}}}, {"key": "2149", "mappings": {"default": {"default": "negrita de pizarra cursiva j"}}}, {"key": "1D6A4", "mappings": {"default": {"default": "cursiva sin punto i"}}}, {"key": "1D6A5", "mappings": {"default": {"default": "cursiva sin punto j"}}}], "es/symbols/math_delimiters.min": [{"locale": "es"}, {"key": "0028", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>", "defaultMP": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "0029", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> derecho", "defaultMP": "c<PERSON><PERSON>"}}}, {"key": "005B", "mappings": {"default": {"default": "corchete izquierdo", "defaultMP": "abre corchetes"}}}, {"key": "005D", "mappings": {"default": {"default": "corchete derecho", "defaultMP": "cierra corchetes"}}}, {"key": "007B", "mappings": {"default": {"default": "llave iz<PERSON>a", "defaultMP": "abre llaves"}}}, {"key": "007D", "mappings": {"default": {"default": "llaves derecha", "defaultMP": "cierra ll<PERSON>"}}}, {"key": "2045", "mappings": {"default": {"default": "soporte cuadrado izquierdo con pluma"}}}, {"key": "2046", "mappings": {"default": {"default": "soporte cuadrado derecho con pluma"}}}, {"key": "2308", "mappings": {"default": {"default": "esquina superior izquierda"}}}, {"key": "2309", "mappings": {"default": {"default": "esquina superior derecha"}}}, {"key": "230A", "mappings": {"default": {"default": "esquina inferior izquierda"}}}, {"key": "230B", "mappings": {"default": {"default": "esquina inferior derecha"}}}, {"key": "230C", "mappings": {"default": {"default": "corte inferior derecho"}}}, {"key": "230D", "mappings": {"default": {"default": "corte inferior izquierdo"}}}, {"key": "230E", "mappings": {"default": {"default": "corte superior derecho "}}}, {"key": "230F", "mappings": {"default": {"default": "corte superior izquierdo"}}}, {"key": "231C", "mappings": {"default": {"default": "ángulo superior izquierdo"}}}, {"key": "231D", "mappings": {"default": {"default": "ángulo superior derecho"}}}, {"key": "231E", "mappings": {"default": {"default": "ángulo inferior izquierdo"}}}, {"key": "231F", "mappings": {"default": {"default": "ángulo inferior derecho"}}}, {"key": "2320", "mappings": {"default": {"default": "mitad superior de integral"}}}, {"key": "2321", "mappings": {"default": {"default": "mitad inferior de integral"}}}, {"key": "2329", "mappings": {"default": {"default": "paréntesis angular i<PERSON>o"}}}, {"key": "232A", "mappings": {"default": {"default": "paréntesis angular derecho"}}}, {"key": "239B", "mappings": {"default": {"default": "parén<PERSON>is i<PERSON>qui<PERSON>o gan<PERSON> superior"}}}, {"key": "239C", "mappings": {"default": {"default": "extensión del paréntesis izquierdo"}}}, {"key": "239D", "mappings": {"default": {"default": "paréntesis inferior gancho inferior"}}}, {"key": "239E", "mappings": {"default": {"default": "paréntesis derecho gancho superior"}}}, {"key": "239F", "mappings": {"default": {"default": "extensión del paréntesis derecho"}}}, {"key": "23A0", "mappings": {"default": {"default": "paréntesis inferior gancho inferior"}}}, {"key": "23A1", "mappings": {"default": {"default": "esquina cuadrada izquierda esquina superior"}}}, {"key": "23A2", "mappings": {"default": {"default": "extensión del soporte cuadrado izquierdo"}}}, {"key": "23A3", "mappings": {"default": {"default": "esquina cuadrada izquierda esquina inferior"}}}, {"key": "23A4", "mappings": {"default": {"default": "esquina cuadrada derecha esquina superior"}}}, {"key": "23A5", "mappings": {"default": {"default": "extensión de soporte cuadrado derecho"}}}, {"key": "23A6", "mappings": {"default": {"default": "esquina cuadrada derecha esquina inferior"}}}, {"key": "23A7", "mappings": {"default": {"default": "gancho superior rizado izquierdo"}}}, {"key": "23A8", "mappings": {"default": {"default": "pedazo medio rizado i<PERSON>o"}}}, {"key": "23A9", "mappings": {"default": {"default": "soporte izquierdo rizado gancho <PERSON>"}}}, {"key": "23AA", "mappings": {"default": {"default": "extensión de soporte rizado"}}}, {"key": "23AB", "mappings": {"default": {"default": "gancho superior rizado derecho"}}}, {"key": "23AC", "mappings": {"default": {"default": "pedazo medio de soporte rizado derecho"}}}, {"key": "23AD", "mappings": {"default": {"default": "abrazadera inferior derecha rizado"}}}, {"key": "23AE", "mappings": {"default": {"default": "extensión integral"}}}, {"key": "23AF", "mappings": {"default": {"default": "extensión de línea horizontal"}}}, {"key": "23B0", "mappings": {"default": {"default": "izquierda arriba o derecho debajo sección llave"}}}, {"key": "23B1", "mappings": {"default": {"default": "derecha arriba o izquierdo debajo sección llave"}}}, {"key": "23B2", "mappings": {"default": {"default": "suma superior"}}}, {"key": "23B3", "mappings": {"default": {"default": "parte inferior de la suma"}}}, {"key": "23B4", "mappings": {"default": {"default": "corchete superior"}}}, {"key": "23B5", "mappings": {"default": {"default": "corchete inferior"}}}, {"key": "23B6", "mappings": {"default": {"default": "soporte cuadrado inferior sobre soporte cuadrado superior"}}}, {"key": "23B7", "mappings": {"default": {"default": "parte inferior del símbolo radical"}}}, {"key": "23B8", "mappings": {"default": {"default": "línea de caja vertical izquierda"}}}, {"key": "23B9", "mappings": {"default": {"default": "línea de caja vertical derecha"}}}, {"key": "23DC", "mappings": {"default": {"default": "paréntesis superior"}}}, {"key": "23DD", "mappings": {"default": {"default": "paréntesis inferior"}}}, {"key": "23DE", "mappings": {"default": {"default": "corchete rizado superior"}}}, {"key": "23DF", "mappings": {"default": {"default": "corchete rizado inferior"}}}, {"key": "23E0", "mappings": {"default": {"default": "corchete superior en tortuga"}}}, {"key": "23E1", "mappings": {"default": {"default": "corchete inferior en tortuga"}}}, {"key": "2768", "mappings": {"default": {"default": "ornamento de paréntesis izquierdo medio"}}}, {"key": "2769", "mappings": {"default": {"default": "adorno de paréntesis medio derecho"}}}, {"key": "276A", "mappings": {"default": {"default": "adorno paréntesis izquierdo aplanado medio"}}}, {"key": "276B", "mappings": {"default": {"default": "adorno paréntesis derecho medio aplanado"}}}, {"key": "276C", "mappings": {"default": {"default": "adorno de soporte de ángulo medio apuntando hacia la izquierda"}}}, {"key": "276D", "mappings": {"default": {"default": "adorno de soporte de ángulo medio apuntando hacia la derecha"}}}, {"key": "276E", "mappings": {"default": {"default": "ornamento de comillas en el ángulo que apunta hacia la izquierda"}}}, {"key": "276F", "mappings": {"default": {"default": "ornamento de comillas en ángulo recto que apunta hacia la derecha"}}}, {"key": "2770", "mappings": {"default": {"default": "ornamento pesado del soporte del ángulo que señala a la izquierda"}}}, {"key": "2771", "mappings": {"default": {"default": "ornamento pesado del soporte del ángulo que señala hacia la derecha"}}}, {"key": "2772", "mappings": {"default": {"default": "ornamento de soporte de concha de tortuga izquierda ligera"}}}, {"key": "2773", "mappings": {"default": {"default": "ornamento de corchete derecho tortuga derecha"}}}, {"key": "2774", "mappings": {"default": {"default": "ornamento de soporte rizado izquierdo medio"}}}, {"key": "2775", "mappings": {"default": {"default": "ornamento de soporte rizado derecho medio"}}}, {"key": "27C5", "mappings": {"default": {"default": "símbolo delimitador izquierdo de bolsa de s-formó"}}}, {"key": "27C6", "mappings": {"default": {"default": "símbolo delimitador derecho de bolsa de s-formó"}}}, {"key": "27E6", "mappings": {"default": {"default": "corchete vacío <PERSON>"}}}, {"key": "27E7", "mappings": {"default": {"default": "corchete vacío derecho"}}}, {"key": "27E8", "mappings": {"default": {"default": "corchete agudo <PERSON>"}}}, {"key": "27E9", "mappings": {"default": {"default": "corchete agudo derecho"}}}, {"key": "27EA", "mappings": {"default": {"default": "doble corchete agudo <PERSON>"}}}, {"key": "27EB", "mappings": {"default": {"default": "doble corchete agudo derecho"}}}, {"key": "27EC", "mappings": {"default": {"default": "corchete de tortuga vacío <PERSON>"}}}, {"key": "27ED", "mappings": {"default": {"default": "corchete de tortuga vacío derecho "}}}, {"key": "27EE", "mappings": {"default": {"default": "paréntesis plano izquierdo"}}}, {"key": "27EF", "mappings": {"default": {"default": "paréntesis plano derecho"}}}, {"key": "2983", "mappings": {"default": {"default": "soporte rizado blanco <PERSON>"}}}, {"key": "2984", "mappings": {"default": {"default": "derecha rizado blanco soporte"}}}, {"key": "2985", "mappings": {"default": {"default": "left white paren"}}}, {"key": "2986", "mappings": {"default": {"default": "right white paren"}}}, {"key": "2987", "mappings": {"default": {"default": "notación Z izquierda soporte de imagen"}}}, {"key": "2988", "mappings": {"default": {"default": "corchete de imagen derecha de notación Z"}}}, {"key": "2989", "mappings": {"default": {"default": "z notation left binding bracket"}}}, {"key": "298A", "mappings": {"default": {"default": "z notation right binding bracket"}}}, {"key": "298B", "mappings": {"default": {"default": "left bracket with underbar"}}}, {"key": "298C", "mappings": {"default": {"default": "right bracket with underbar"}}}, {"key": "298D", "mappings": {"default": {"default": "left bracket with tick in top corner"}}}, {"key": "298E", "mappings": {"default": {"default": "right bracket with tick in bottom corner"}}}, {"key": "298F", "mappings": {"default": {"default": "left bracket with tick in bottom corner"}}}, {"key": "2990", "mappings": {"default": {"default": "right bracket with tick in top corner"}}}, {"key": "2991", "mappings": {"default": {"default": "left angle bracket with dot"}}}, {"key": "2992", "mappings": {"default": {"default": "right angle bracket with dot"}}}, {"key": "2993", "mappings": {"default": {"default": "left arc less than bracket"}}}, {"key": "2994", "mappings": {"default": {"default": "right arc greater than bracket"}}}, {"key": "2995", "mappings": {"default": {"default": "double left arc greater than bracket"}}}, {"key": "2996", "mappings": {"default": {"default": "double right arc less than bracket"}}}, {"key": "2997", "mappings": {"default": {"default": "soporte de concha de tortuga negra izquierda"}}}, {"key": "2998", "mappings": {"default": {"default": "abrazadera de concha de tortuga negra derecha"}}}, {"key": "29D8", "mappings": {"default": {"default": "valla izquierda ondulada"}}}, {"key": "29D9", "mappings": {"default": {"default": "valla derecha ondulada"}}}, {"key": "29DA", "mappings": {"default": {"default": "left double wiggly fence"}}}, {"key": "29DB", "mappings": {"default": {"default": "right double wiggly fence"}}}, {"key": "29FC", "mappings": {"default": {"default": "abrazadera de ángulo curvo a la izquierda"}}}, {"key": "29FD", "mappings": {"default": {"default": "abrazadera de ángulo curvo orientada hacia la derecha"}}}, {"key": "2E22", "mappings": {"default": {"default": "soporte superior izquierdo superior"}}}, {"key": "2E23", "mappings": {"default": {"default": "medio soporte superior derecho"}}}, {"key": "2E24", "mappings": {"default": {"default": "abrazadera inferior izquierda"}}}, {"key": "2E25", "mappings": {"default": {"default": "medio soporte inferior derecho"}}}, {"key": "2E26", "mappings": {"default": {"default": "soporte U lateral izquierdo"}}}, {"key": "2E27", "mappings": {"default": {"default": "lado derecho U soporte"}}}, {"key": "2E28", "mappings": {"default": {"default": "doble par<PERSON><PERSON><PERSON>"}}}, {"key": "2E29", "mappings": {"default": {"default": "par<PERSON><PERSON>is doble derecho"}}}, {"key": "3008", "mappings": {"default": {"default": "soporte de ángulo i<PERSON>"}}}, {"key": "3009", "mappings": {"default": {"default": "soporte de ángulo recto"}}}, {"key": "300A", "mappings": {"default": {"default": "par<PERSON><PERSON><PERSON> angular doble"}}}, {"key": "300B", "mappings": {"default": {"default": "parén<PERSON>is angular doble derecho", "defaultMP": "cierra par<PERSON><PERSON>is angular doble"}}}, {"key": "300C", "mappings": {"default": {"default": "soporte de la esquina izquierda"}}}, {"key": "300D", "mappings": {"default": {"default": "soporte de esquina derecha"}}}, {"key": "300E", "mappings": {"default": {"default": "soporte de esquina blanco iz<PERSON>o"}}}, {"key": "300F", "mappings": {"default": {"default": "soporte de esquina blanco derecho"}}}, {"key": "3010", "mappings": {"default": {"default": "soporte Lenticular Izquierdo Negro"}}}, {"key": "3011", "mappings": {"default": {"default": "soporte Lenticular Negro Derecho"}}}, {"key": "3014", "mappings": {"default": {"default": "left tortoise shell bracket"}}}, {"key": "3015", "mappings": {"default": {"default": "right tortoise shell bracket"}}}, {"key": "3016", "mappings": {"default": {"default": "soporte lenticular blanco i<PERSON>o"}}}, {"key": "3017", "mappings": {"default": {"default": "soporte lenticular blanco derecho"}}}, {"key": "3018", "mappings": {"default": {"default": "soporte de caparazón de tortuga blanca izquierda"}}}, {"key": "3019", "mappings": {"default": {"default": "soporte de concha de tortuga blanca derecha"}}}, {"key": "301A", "mappings": {"default": {"default": "corchete vacío"}}}, {"key": "301B", "mappings": {"default": {"default": "corchete vacío derecho", "defaultMP": "cierra corchete vacío"}}}, {"key": "301D", "mappings": {"default": {"default": "comilla doble invertida"}}}, {"key": "301E", "mappings": {"default": {"default": "segunda"}}}, {"key": "301F", "mappings": {"default": {"default": "comillas bajas dobles"}}}, {"key": "FD3E", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON> adornado"}}}, {"key": "FD3F", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> derecho adornado"}}}, {"key": "FE17", "mappings": {"default": {"default": "formulario de presentación para soporte lenticular blanco izquierdo vertical"}}}, {"key": "FE18", "mappings": {"default": {"default": "formulario de presentación para soporte lenticular blanco vertical derecho"}}}, {"key": "FE35", "mappings": {"default": {"default": "paréntesis superior"}}}, {"key": "FE36", "mappings": {"default": {"default": "paréntesis inferior"}}}, {"key": "FE37", "mappings": {"default": {"default": "llave superior"}}}, {"key": "FE38", "mappings": {"default": {"default": "llave inferior"}}}, {"key": "FE39", "mappings": {"default": {"default": "formulario de presentación para el soporte vertical de concha de tortuga izquierda"}}}, {"key": "FE3A", "mappings": {"default": {"default": "formulario de presentación para el soporte vertical de concha de tortuga derecha"}}}, {"key": "FE3B", "mappings": {"default": {"default": "formulario de presentación para el soporte lenticular negro izquierdo vertical"}}}, {"key": "FE3C", "mappings": {"default": {"default": "formulario de presentación para el soporte lenticular negro derecho vertical"}}}, {"key": "FE3D", "mappings": {"default": {"default": "formulario de presentación para soporte vertical doble ángulo izquierdo"}}}, {"key": "FE3E", "mappings": {"default": {"default": "formulario de presentación para el soporte de ángulo recto doble vertical"}}}, {"key": "FE3F", "mappings": {"default": {"default": "paréntesis angular superior"}}}, {"key": "FE40", "mappings": {"default": {"default": "paréntesis angular inferior"}}}, {"key": "FE41", "mappings": {"default": {"default": "formulario de presentación para soporte de esquina vertical izquierda"}}}, {"key": "FE42", "mappings": {"default": {"default": "formulario de presentación para soporte de esquina vertical derecha"}}}, {"key": "FE43", "mappings": {"default": {"default": "formulario de presentación para soporte de esquina blanco vertical izquierdo"}}}, {"key": "FE44", "mappings": {"default": {"default": "formulario de presentación para soporte de esquina vertical derecha blanca"}}}, {"key": "FE47", "mappings": {"default": {"default": "formulario de presentación para soporte cuadrado vertical izquierdo"}}}, {"key": "FE48", "mappings": {"default": {"default": "formulario de presentación para soporte cuadrado vertical derecho"}}}, {"key": "FE59", "mappings": {"default": {"default": "pequeño parén<PERSON>"}}}, {"key": "FE5A", "mappings": {"default": {"default": "parén<PERSON>is derecho pequeño"}}}, {"key": "FE5B", "mappings": {"default": {"default": "corchete Rizado I<PERSON>o Pequeño"}}}, {"key": "FE5C", "mappings": {"default": {"default": "corchete derecho rizado pequeño"}}}, {"key": "FE5D", "mappings": {"default": {"default": "soporte de concha pequeña tortuga izquierda"}}}, {"key": "FE5E", "mappings": {"default": {"default": "pequeño corchete derecho de concha de tortuga"}}}, {"key": "FF08", "mappings": {"default": {"default": "par<PERSON><PERSON><PERSON><PERSON> de ancho completo"}}}, {"key": "FF09", "mappings": {"default": {"default": "parén<PERSON>is derecho de ancho completo"}}}, {"key": "FF3B", "mappings": {"default": {"default": "soporte cuadrado izquierdo de ancho completo"}}}, {"key": "FF3D", "mappings": {"default": {"default": "soporte cuadrado derecho de ancho completo"}}}, {"key": "FF5B", "mappings": {"default": {"default": "corchete rizado izqui<PERSON>o de ancho completo"}}}, {"key": "FF5D", "mappings": {"default": {"default": "corchete derecho de ancho completo"}}}, {"key": "FF5F", "mappings": {"default": {"default": "parén<PERSON>is blanco i<PERSON> de ancho completo"}}}, {"key": "FF60", "mappings": {"default": {"default": "paréntesis blanco derecho de ancho completo"}}}, {"key": "FF62", "mappings": {"default": {"default": "corchete de esquina izquierda de medio ancho"}}}, {"key": "FF63", "mappings": {"default": {"default": "soporte de esquina derecha de medio ancho"}}}], "es/symbols/math_geometry.min": [{"locale": "es"}, {"key": "2500", "mappings": {"default": {"default": "delimitador horizontal"}}}, {"key": "2501", "mappings": {"default": {"default": "dibujos de cajas pesadas horizontales"}}}, {"key": "2502", "mappings": {"default": {"default": "delimitador vertical"}}}, {"key": "2503", "mappings": {"default": {"default": "dibujos de cajas Pesado Vertical"}}}, {"key": "2504", "mappings": {"default": {"default": "dibujos de cajas Light Triple Dash Horizontal"}}}, {"key": "2505", "mappings": {"default": {"default": "dibujos de cajas Heavy Triple Dash Horizontal"}}}, {"key": "2506", "mappings": {"default": {"default": "dibujos de cajas Light Triple Dash Vertical"}}}, {"key": "2507", "mappings": {"default": {"default": "dibujos de cajas Heavy Triple Dash Vertical"}}}, {"key": "2508", "mappings": {"default": {"default": "caja Dibujos Ligero Cuadruple Dash Horizontal"}}}, {"key": "2509", "mappings": {"default": {"default": "dibujos de cajas Heavy Quadruple Dash Horizontal"}}}, {"key": "250A", "mappings": {"default": {"default": "caja Dibujos Luz Cuadruple Tablero Vertical"}}}, {"key": "250B", "mappings": {"default": {"default": "dibujos de cajas Heavy Quadruple Dash Vertical"}}}, {"key": "250C", "mappings": {"default": {"default": "esquina inferior derecha"}}}, {"key": "250D", "mappings": {"default": {"default": "dibujos de cajas Abajo Ligero y Derecho Pesado"}}}, {"key": "250E", "mappings": {"default": {"default": "dibujos de cajas abajo pesados ​​y rectos ligeros"}}}, {"key": "250F", "mappings": {"default": {"default": "dibujos de cajas pesados ​​abajo y derecha"}}}, {"key": "2510", "mappings": {"default": {"default": "esquina inferior izquierda"}}}, {"key": "2511", "mappings": {"default": {"default": "dibujos de cajas abajo ligeros y pesados ​​a la izquierda"}}}, {"key": "2512", "mappings": {"default": {"default": "dibujos de cajas abajo pesados ​​y luz izquierda"}}}, {"key": "2513", "mappings": {"default": {"default": "dibujos de cajas pesados ​​hacia abajo y hacia la izquierda"}}}, {"key": "2514", "mappings": {"default": {"default": "esquina superior derecha"}}}, {"key": "2515", "mappings": {"default": {"default": "dibujos de cajas hasta ligero y derecho pesado"}}}, {"key": "2516", "mappings": {"default": {"default": "dibujos de cajas hasta Pesadas y Ligeras"}}}, {"key": "2517", "mappings": {"default": {"default": "dibujos de cajas pesados ​​y derechos"}}}, {"key": "2518", "mappings": {"default": {"default": "esquina superior izquierda"}}}, {"key": "2519", "mappings": {"default": {"default": "dibujos de cajas hasta livianos y pesados ​​a la izquierda"}}}, {"key": "251A", "mappings": {"default": {"default": "dibujos de cajas hasta pesado y luz izquierda"}}}, {"key": "251B", "mappings": {"default": {"default": "dibujos de cajas pesados ​​hacia arriba y hacia la izquierda"}}}, {"key": "251C", "mappings": {"default": {"default": "delimitador izquierdo y separador horizontal"}}}, {"key": "251D", "mappings": {"default": {"default": "cuadros de caja Vertical ligero y derecho pesado"}}}, {"key": "251E", "mappings": {"default": {"default": "dibujos de cajas hasta pesados ​​y rectos hacia abajo"}}}, {"key": "251F", "mappings": {"default": {"default": "dibujos de cajas abajo pesados ​​y rectos arriba ligeros"}}}, {"key": "2520", "mappings": {"default": {"default": "dibujos de cajas verticales pesadas y rectas ligeras"}}}, {"key": "2521", "mappings": {"default": {"default": "dibujos de cajas abajo ligeros y rectos arriba pesados"}}}, {"key": "2522", "mappings": {"default": {"default": "dibujos de cajas para arriba livianos y rectos para abajo pesados"}}}, {"key": "2523", "mappings": {"default": {"default": "dibujos de cajas pesadas verticales y derechas"}}}, {"key": "2524", "mappings": {"default": {"default": "delimitador derecho y separador horizontal"}}}, {"key": "2525", "mappings": {"default": {"default": "cuadros de caja Vertical ligero y izquierdo pesado"}}}, {"key": "2526", "mappings": {"default": {"default": "dibujos de cajas para arriba Pesado y Izquierda Abajo Luz"}}}, {"key": "2527", "mappings": {"default": {"default": "dibujos de cajas abajo pesados ​​y luz izquierda arriba"}}}, {"key": "2528", "mappings": {"default": {"default": "dibujos de cajas verticales pesadas y luz izquierda"}}}, {"key": "2529", "mappings": {"default": {"default": "dibujos de cajas abajo ligeros e izquierdos arriba pesados"}}}, {"key": "252A", "mappings": {"default": {"default": "dibujos de cajas para arriba Ligero y Izquierda Abajo Pesado"}}}, {"key": "252B", "mappings": {"default": {"default": "dibujos de cajas pesadas verticales e izquierdas"}}}, {"key": "252C", "mappings": {"default": {"default": "delimitador superior y separador vertical"}}}, {"key": "252D", "mappings": {"default": {"default": "dibujos de cajas izquierda pesados ​​y derecha abajo luz"}}}, {"key": "252E", "mappings": {"default": {"default": "dibujos de cajas, derecha, pesada y izquierda abajo, luz"}}}, {"key": "252F", "mappings": {"default": {"default": "dibujos de cajas abajo ligeros y horizontales pesados"}}}, {"key": "2530", "mappings": {"default": {"default": "dibujos de cajas de luz pesada y horizontal"}}}, {"key": "2531", "mappings": {"default": {"default": "dibujos de la caja de luz derecha y izquierda abajo pesado"}}}, {"key": "2532", "mappings": {"default": {"default": "cuadros de caja izquierda ligera y derecha abajo pesada"}}}, {"key": "2533", "mappings": {"default": {"default": "dibujos de cajas pesados ​​y horizontales"}}}, {"key": "2534", "mappings": {"default": {"default": "delimitador inferior y separador vertical"}}}, {"key": "2535", "mappings": {"default": {"default": "dibujos de cajas izquierda pesada y derecha arriba ligera"}}}, {"key": "2536", "mappings": {"default": {"default": "dibujos de caja derecha pesada y luz izquierda arriba"}}}, {"key": "2537", "mappings": {"default": {"default": "dibujos de cajas hasta ligero y horizontal pesado"}}}, {"key": "2538", "mappings": {"default": {"default": "dibujos de cajas hasta luz pesada y horizontal"}}}, {"key": "2539", "mappings": {"default": {"default": "dibujos de cajas a la derecha, livianos y a la izquierda, pesados"}}}, {"key": "253A", "mappings": {"default": {"default": "dibujos de cajas Izquierda Ligera y Derecha Arriba Pesada"}}}, {"key": "253B", "mappings": {"default": {"default": "dibujos de cajas pesados ​​y horizontales"}}}, {"key": "253C", "mappings": {"default": {"default": "cruce de separadores sencillos"}}}, {"key": "253D", "mappings": {"default": {"default": "dibujos de cajas Izquierda Pesada y Derecha Vertical Luz"}}}, {"key": "253E", "mappings": {"default": {"default": "dibujos de caja derecha pesada y luz vertical izquierda"}}}, {"key": "253F", "mappings": {"default": {"default": "dibujos de cajas Vertical Ligera y Horizontal Pesada"}}}, {"key": "2540", "mappings": {"default": {"default": "dibujos de cajas para arriba pesados ​​y abajo luz horizontal"}}}, {"key": "2541", "mappings": {"default": {"default": "dibujos de cajas hacia abajo pesados ​​y hacia arriba luz horizontal"}}}, {"key": "2542", "mappings": {"default": {"default": "dibujos de cajas verticales pesadas y horizontales ligeras"}}}, {"key": "2543", "mappings": {"default": {"default": "dibujos de cajas Izquierda Arriba Pesada y Derecha abajo Luz"}}}, {"key": "2544", "mappings": {"default": {"default": "dibujos de caja derecha arriba pesada y izquierda abajo luz"}}}, {"key": "2545", "mappings": {"default": {"default": "dibujos de cajas, iz<PERSON><PERSON>a abajo, pesada y derecha arriba, ligera"}}}, {"key": "2546", "mappings": {"default": {"default": "dibujos de la caja derecha abajo pesada y izquierda arriba ligera"}}}, {"key": "2547", "mappings": {"default": {"default": "dibujos de cajas abajo ligeros y ascendentes horizontales pesados"}}}, {"key": "2548", "mappings": {"default": {"default": "dibujos de cajas para arriba Ligero y para abajo horizontal pesado"}}}, {"key": "2549", "mappings": {"default": {"default": "dibujos de cajas a la derecha, ligeros y a la izquierda, verticales, pesados"}}}, {"key": "254A", "mappings": {"default": {"default": "dibujos de cajas Izquierda Ligera y Derecha Vertical Pesada"}}}, {"key": "254B", "mappings": {"default": {"default": "dibujos de cajas pesadas verticales y horizontales"}}}, {"key": "254C", "mappings": {"default": {"default": "dibujos de cajas Light Double Dash Horizontal"}}}, {"key": "254D", "mappings": {"default": {"default": "dibujos de cajas Heavy Double Dash Horizontal"}}}, {"key": "254E", "mappings": {"default": {"default": "dibujos de cajas Light Double Dash Vertical"}}}, {"key": "254F", "mappings": {"default": {"default": "dibujos de cajas Heavy Double Dash Vertical"}}}, {"key": "2550", "mappings": {"default": {"default": "delimitador horizontal doble"}}}, {"key": "2551", "mappings": {"default": {"default": "delimitador vertical doble"}}}, {"key": "2552", "mappings": {"default": {"default": "esquina inferior derecha, vertical doble"}}}, {"key": "2553", "mappings": {"default": {"default": "esquina inferior derecha, horizontal doble"}}}, {"key": "2554", "mappings": {"default": {"default": "esquina inferior derecha doble"}}}, {"key": "2555", "mappings": {"default": {"default": "esquina inferior izquierda, vertical doble"}}}, {"key": "2556", "mappings": {"default": {"default": "esquina inferior izquierda, horizontal doble"}}}, {"key": "2557", "mappings": {"default": {"default": "esquina inferior izquierda doble"}}}, {"key": "2558", "mappings": {"default": {"default": "esquina superior derecha, vertical doble"}}}, {"key": "2559", "mappings": {"default": {"default": "esquina superior derecha, horizontal doble"}}}, {"key": "255A", "mappings": {"default": {"default": "esquina superior derecha doble"}}}, {"key": "255B", "mappings": {"default": {"default": "esquina superior izquierda, vertical doble"}}}, {"key": "255C", "mappings": {"default": {"default": "esquina superior izquierda, horizontal doble"}}}, {"key": "255D", "mappings": {"default": {"default": "esquina superior izquierda doble"}}}, {"key": "255E", "mappings": {"default": {"default": "delimitador izquierdo con separador doble"}}}, {"key": "255F", "mappings": {"default": {"default": "delimitador izquierdo doble con separador horizontal"}}}, {"key": "2560", "mappings": {"default": {"default": "delimitador izquierdo y separador horizontal dobles"}}}, {"key": "2561", "mappings": {"default": {"default": "delimitador derecho con separador doble"}}}, {"key": "2562", "mappings": {"default": {"default": "delimitador derecho doble con separador horizontal"}}}, {"key": "2563", "mappings": {"default": {"default": "delimitador derecho y separador horizontal dobles"}}}, {"key": "2564", "mappings": {"default": {"default": "delimitador superior doble con separador vertical"}}}, {"key": "2565", "mappings": {"default": {"default": "delimitador superior con separador vertical doble"}}}, {"key": "2566", "mappings": {"default": {"default": "delimitador superior y separador vertical dobles"}}}, {"key": "2567", "mappings": {"default": {"default": "delimitador inferior doble con separador vertical"}}}, {"key": "2568", "mappings": {"default": {"default": "delimitador inferior con separador vertical doble"}}}, {"key": "2569", "mappings": {"default": {"default": "delimitador inferior y separador vertical dobles"}}}, {"key": "256A", "mappings": {"default": {"default": "cruce de separadores, vertical sencillo y horizontal doble"}}}, {"key": "256B", "mappings": {"default": {"default": "cruce de separadores, vertical doble y horizontal sencillo"}}}, {"key": "256C", "mappings": {"default": {"default": "cruce de separadores dobles"}}}, {"key": "256D", "mappings": {"default": {"default": "dibujos de cajas de luz arco abajo y derecha"}}}, {"key": "256E", "mappings": {"default": {"default": "dibujos de cajas de luz arco hacia abajo y hacia la izquierda"}}}, {"key": "256F", "mappings": {"default": {"default": "dibujos de cajas de luz arco arriba y a la izquierda"}}}, {"key": "2570", "mappings": {"default": {"default": "caja de dibujos de luz arco arriba y derecha"}}}, {"key": "2571", "mappings": {"default": {"default": "dibujos de cajas Luz Diagonal Superior derecha a inferior izquierda"}}}, {"key": "2572", "mappings": {"default": {"default": "dibujos de cajas Luz Diagonal Superior izquierda a inferior derecha"}}}, {"key": "2573", "mappings": {"default": {"default": "dibujos de cajas de luz diagonal cruz"}}}, {"key": "2574", "mappings": {"default": {"default": "dibujos de cajas luz izquierda"}}}, {"key": "2575", "mappings": {"default": {"default": "dibujos de cajas se iluminan"}}}, {"key": "2576", "mappings": {"default": {"default": "dibujos de cajas a la derecha"}}}, {"key": "2577", "mappings": {"default": {"default": "dibujos de cajas de luz hacia abajo"}}}, {"key": "2578", "mappings": {"default": {"default": "dibujos de cajas pesados ​​a la izquierda"}}}, {"key": "2579", "mappings": {"default": {"default": "dibujos de cajas pesados"}}}, {"key": "257A", "mappings": {"default": {"default": "caja de dibujos pesado derecho"}}}, {"key": "257B", "mappings": {"default": {"default": "dibujos de cajas pesados"}}}, {"key": "257C", "mappings": {"default": {"default": "dibujos de cajas Ligero Izquierda y Pesada Derecha"}}}, {"key": "257D", "mappings": {"default": {"default": "dibujos de cajas iluminados y pesados"}}}, {"key": "257E", "mappings": {"default": {"default": "dibujos de cajas Pesado Izquierda y Luz Derecha"}}}, {"key": "257F", "mappings": {"default": {"default": "dibujos de cajas pesados ​​y ligeros"}}}, {"key": "2580", "mappings": {"default": {"default": "bloque mitad superior"}}}, {"key": "2581", "mappings": {"default": {"default": "bajar un octavo bloque"}}}, {"key": "2582", "mappings": {"default": {"default": "bloque de un cuarto inferior"}}}, {"key": "2583", "mappings": {"default": {"default": "bloque de tres octavos más bajo"}}}, {"key": "2584", "mappings": {"default": {"default": "bloque mitad inferior"}}}, {"key": "2585", "mappings": {"default": {"default": "bloque inferior de cinco octavos"}}}, {"key": "2586", "mappings": {"default": {"default": "bloque de tres cuartos más bajo"}}}, {"key": "2587", "mappings": {"default": {"default": "lower Seven Eighths Block"}}}, {"key": "2588", "mappings": {"default": {"default": "bloque"}}}, {"key": "2589", "mappings": {"default": {"default": "bloque de siete octavos a la izquierda"}}}, {"key": "258A", "mappings": {"default": {"default": "bloque de tres cuartos a la izquierda"}}}, {"key": "258B", "mappings": {"default": {"default": "bloque de cinco octavos a la izquierda"}}}, {"key": "258C", "mappings": {"default": {"default": "media cuadra izquierda"}}}, {"key": "258D", "mappings": {"default": {"default": "bloque de tres octavos a la izquierda"}}}, {"key": "258E", "mappings": {"default": {"default": "bloque de un cuarto a la izquierda"}}}, {"key": "258F", "mappings": {"default": {"default": "izquierda un octavo bloque"}}}, {"key": "2590", "mappings": {"default": {"default": "media cuadra derecha"}}}, {"key": "2591", "mappings": {"default": {"default": "sombra ligera"}}}, {"key": "2592", "mappings": {"default": {"default": "sombra media"}}}, {"key": "2593", "mappings": {"default": {"default": "sombra intensa"}}}, {"key": "2594", "mappings": {"default": {"default": "upper One Eighth Block"}}}, {"key": "2595", "mappings": {"default": {"default": "a la derecha un octavo bloque"}}}, {"key": "2596", "mappings": {"default": {"default": "cuadrante inferior izquierdo"}}}, {"key": "2597", "mappings": {"default": {"default": "cuadrante inferior derecho"}}}, {"key": "2598", "mappings": {"default": {"default": "cuadrante superior izquierdo"}}}, {"key": "2599", "mappings": {"default": {"default": "cuadrante superior izquierdo e inferior izquierdo e inferior derecho"}}}, {"key": "259A", "mappings": {"default": {"default": "cuadrante superior izquierdo e inferior derecho"}}}, {"key": "259B", "mappings": {"default": {"default": "cuadrante superior izquierdo y superior derecho e inferior izquierdo"}}}, {"key": "259C", "mappings": {"default": {"default": "cuadrante superior izquierdo y superior derecho e inferior derecho"}}}, {"key": "259D", "mappings": {"default": {"default": "cuadrante superior derecho"}}}, {"key": "259E", "mappings": {"default": {"default": "cuadrante superior derecho e inferior izquierdo"}}}, {"key": "259F", "mappings": {"default": {"default": "cuadrante superior derecho e inferior izquierdo e inferior derecho"}}}, {"key": "25A0", "mappings": {"default": {"default": "cuadrado relleno"}}}, {"key": "25A1", "mappings": {"default": {"default": "cuadrado"}}}, {"key": "25A2", "mappings": {"default": {"default": "cuadrado blanco con esquinas redondeadas"}}}, {"key": "25A3", "mappings": {"default": {"default": "cuadrado blanco que contiene cuadrado pequeño negro"}}}, {"key": "25A4", "mappings": {"default": {"default": "cuadrado con relleno horizontal"}}}, {"key": "25A5", "mappings": {"default": {"default": "cuadrado con relleno vertical"}}}, {"key": "25A6", "mappings": {"default": {"default": "cuadrado con relleno de rayado ortogonal"}}}, {"key": "25A7", "mappings": {"default": {"default": "cuadrado con la esquina superior izquierda a la derecha inferior"}}}, {"key": "25A8", "mappings": {"default": {"default": "cuadrado con la parte superior derecha hacia abajo izquierda Relleno"}}}, {"key": "25A9", "mappings": {"default": {"default": "cuadrado con relleno de rayado diagonal"}}}, {"key": "25AA", "mappings": {"default": {"default": "cuadrado relleno"}}}, {"key": "25AB", "mappings": {"default": {"default": "cuadrado"}}}, {"key": "25AC", "mappings": {"default": {"default": "rectángulo negro"}}}, {"key": "25AD", "mappings": {"default": {"default": "rect<PERSON><PERSON><PERSON>"}}}, {"key": "25AE", "mappings": {"default": {"default": "rectán<PERSON>lo relleno vertical"}}}, {"key": "25AF", "mappings": {"default": {"default": "rectángulo vertical"}}}, {"key": "25B0", "mappings": {"default": {"default": "paralelogramo negro"}}}, {"key": "25B1", "mappings": {"default": {"default": "paralelogramo"}}}, {"key": "25B2", "mappings": {"default": {"default": "tri<PERSON><PERSON><PERSON> re<PERSON>o"}}}, {"key": "25B3", "mappings": {"default": {"default": "trián<PERSON><PERSON>"}}}, {"key": "25B4", "mappings": {"default": {"default": "tri<PERSON><PERSON><PERSON> re<PERSON>o"}}}, {"key": "25B5", "mappings": {"default": {"default": "trián<PERSON><PERSON>"}}}, {"key": "25B6", "mappings": {"default": {"default": "trián<PERSON><PERSON> re<PERSON>o se<PERSON> derecha"}}}, {"key": "25B7", "mappings": {"default": {"default": "trián<PERSON><PERSON> se<PERSON> derecha"}}}, {"key": "25B8", "mappings": {"default": {"default": "trián<PERSON><PERSON> re<PERSON>o se<PERSON> derecha"}}}, {"key": "25B9", "mappings": {"default": {"default": "trián<PERSON><PERSON> se<PERSON> derecha"}}}, {"key": "25BA", "mappings": {"default": {"default": "indicador de apuntar hacia la derecha negro"}}}, {"key": "25BB", "mappings": {"default": {"default": "puntero de apuntar hacia la derecha blanco"}}}, {"key": "25BC", "mappings": {"default": {"default": "trián<PERSON><PERSON> re<PERSON>o se<PERSON> a<PERSON>jo"}}}, {"key": "25BD", "mappings": {"default": {"default": "trián<PERSON><PERSON> a<PERSON>"}}}, {"key": "25BE", "mappings": {"default": {"default": "trián<PERSON><PERSON> re<PERSON>o se<PERSON> a<PERSON>jo"}}}, {"key": "25BF", "mappings": {"default": {"default": "trián<PERSON><PERSON> a<PERSON>"}}}, {"key": "25C0", "mappings": {"default": {"default": "trián<PERSON>lo re<PERSON>o se<PERSON>"}}}, {"key": "25C1", "mappings": {"default": {"default": "trián<PERSON><PERSON>"}}}, {"key": "25C2", "mappings": {"default": {"default": "trián<PERSON>lo re<PERSON>o se<PERSON>"}}}, {"key": "25C3", "mappings": {"default": {"default": "trián<PERSON><PERSON>"}}}, {"key": "25C4", "mappings": {"default": {"default": "puntero relleno se<PERSON>"}}}, {"key": "25C5", "mappings": {"default": {"default": "puntero señala izquierda"}}}, {"key": "25C6", "mappings": {"default": {"default": "di<PERSON><PERSON> re<PERSON>o"}}}, {"key": "25C7", "mappings": {"default": {"default": "diamante"}}}, {"key": "25C8", "mappings": {"default": {"default": "diamante vacío contiene diamante relleno"}}}, {"key": "25C9", "mappings": {"default": {"default": "ojo de pez"}}}, {"key": "25CA", "mappings": {"default": {"default": "rombo"}}}, {"key": "25CB", "mappings": {"default": {"default": "c<PERSON><PERSON>ulo v<PERSON>"}}}, {"key": "25CC", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "25CD", "mappings": {"default": {"default": "cí<PERSON>ulo con relleno vertical"}}}, {"key": "25CE", "mappings": {"default": {"default": "blanco"}}}, {"key": "25CF", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "25D0", "mappings": {"default": {"default": "círculo con mitad izquierda rellena"}}}, {"key": "25D1", "mappings": {"default": {"default": "cí<PERSON>ulo con mitad derecha rellena"}}}, {"key": "25D2", "mappings": {"default": {"default": "círculo con mitad inferior rellena"}}}, {"key": "25D3", "mappings": {"default": {"default": "círculo con mitad superior rellena"}}}, {"key": "25D4", "mappings": {"default": {"default": "círculo con cuadrante superior derecho relleno"}}}, {"key": "25D5", "mappings": {"default": {"default": "círculo con cuadrante superior izquierdo relleno"}}}, {"key": "25D6", "mappings": {"default": {"default": "semicí<PERSON><PERSON>"}}}, {"key": "25D7", "mappings": {"default": {"default": "semicí<PERSON>ulo derecho relleno"}}}, {"key": "25D8", "mappings": {"default": {"default": "bullet inversa"}}}, {"key": "25D9", "mappings": {"default": {"default": "círculo vacío in<PERSON>o"}}}, {"key": "25DA", "mappings": {"default": {"default": "semicírculo superior vacío inverso"}}}, {"key": "25DB", "mappings": {"default": {"default": "semicírculo superior vacío inverso"}}}, {"key": "25DC", "mappings": {"default": {"default": "arco superior izquierda"}}}, {"key": "25DD", "mappings": {"default": {"default": "arco superior derecha"}}}, {"key": "25DE", "mappings": {"default": {"default": "arco inferior derecha"}}}, {"key": "25DF", "mappings": {"default": {"default": "arco inferior izquierda"}}}, {"key": "25E0", "mappings": {"default": {"default": "semicírculo superior"}}}, {"key": "25E1", "mappings": {"default": {"default": "semicírculo inferior"}}}, {"key": "25E2", "mappings": {"default": {"default": "triángulo inferior derecha relleno"}}}, {"key": "25E3", "mappings": {"default": {"default": "triángulo inferior izquierda relleno"}}}, {"key": "25E4", "mappings": {"default": {"default": "triángulo superior izquierda relleno"}}}, {"key": "25E5", "mappings": {"default": {"default": "triángulo superior derecha relleno"}}}, {"key": "25E6", "mappings": {"default": {"default": "composición"}}}, {"key": "25E7", "mappings": {"default": {"default": "cuadrado con mitad izquierda rellena"}}}, {"key": "25E8", "mappings": {"default": {"default": "cuadrado con mitad derecha rellena"}}}, {"key": "25E9", "mappings": {"default": {"default": "cuadrado con mitad superior izquierda rellena"}}}, {"key": "25EA", "mappings": {"default": {"default": "cuadrado con mitad inferior derecha rellena"}}}, {"key": "25EB", "mappings": {"default": {"default": "cuadrado con bisectriz"}}}, {"key": "25EC", "mappings": {"default": {"default": "triángulo vacío con punto"}}}, {"key": "25ED", "mappings": {"default": {"default": "triángulo apuntando arriba con mitad izquierda rellena"}}}, {"key": "25EE", "mappings": {"default": {"default": "triángulo apuntando arriba con mitad derecha rellena"}}}, {"key": "25EF", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> grande"}}}, {"key": "25F0", "mappings": {"default": {"default": "cuadrado blanco con cuadrante superior izquierdo"}}}, {"key": "25F1", "mappings": {"default": {"default": "cuadrado blanco con cuadrante inferior izquierdo"}}}, {"key": "25F2", "mappings": {"default": {"default": "cuadrado blanco con cuadrante inferior derecho"}}}, {"key": "25F3", "mappings": {"default": {"default": "cuadrado blanco con cuadrante superior derecho"}}}, {"key": "25F4", "mappings": {"default": {"default": "círculo blanco con cuadrante superior izquierdo"}}}, {"key": "25F5", "mappings": {"default": {"default": "círculo blanco con cuadrante inferior izquierdo"}}}, {"key": "25F6", "mappings": {"default": {"default": "círculo blanco con cuadrante inferior derecho"}}}, {"key": "25F7", "mappings": {"default": {"default": "círculo blanco con cuadrante superior derecho"}}}, {"key": "25F8", "mappings": {"default": {"default": "triángulo superior izquierda"}}}, {"key": "25F9", "mappings": {"default": {"default": "triángulo superior derecha"}}}, {"key": "25FA", "mappings": {"default": {"default": "triángulo inferior izquierda"}}}, {"key": "25FB", "mappings": {"default": {"default": "cuadrado mediano vacío"}}}, {"key": "25FC", "mappings": {"default": {"default": "cuadrado mediano relleno"}}}, {"key": "25FD", "mappings": {"default": {"default": "cuadrado mediano vacío"}}}, {"key": "25FE", "mappings": {"default": {"default": "cuadrado mediano relleno"}}}, {"key": "25FF", "mappings": {"default": {"default": "triángulo inferior derecha"}}}, {"key": "2B12", "mappings": {"default": {"default": "cuadrado con mitad superior negra"}}}, {"key": "2B13", "mappings": {"default": {"default": "cuadrado con mitad inferior negra"}}}, {"key": "2B14", "mappings": {"default": {"default": "cuadrado con mitad derecha superior diagonal negra"}}}, {"key": "2B15", "mappings": {"default": {"default": "cuadrado con mitad diagonal izquierda inferior negra"}}}, {"key": "2B16", "mappings": {"default": {"default": "diamante con <PERSON><PERSON>"}}}, {"key": "2B17", "mappings": {"default": {"default": "diamante con mitad derecha negra"}}}, {"key": "2B18", "mappings": {"default": {"default": "diamante con mitad superior negra"}}}, {"key": "2B19", "mappings": {"default": {"default": "diamante con mitad inferior negra"}}}, {"key": "2B1A", "mappings": {"default": {"default": "cuadrado punteado"}}}, {"key": "2B1B", "mappings": {"default": {"default": "cuadrado grande negro"}}}, {"key": "2B1C", "mappings": {"default": {"default": "gran Plaza Blanca"}}}, {"key": "2B1D", "mappings": {"default": {"default": "cuadrado muy pequeño negro"}}}, {"key": "2B1E", "mappings": {"default": {"default": "cuadrado muy pequeño blanco"}}}, {"key": "2B1F", "mappings": {"default": {"default": "pentágono negro"}}}, {"key": "2B20", "mappings": {"default": {"default": "pentá<PERSON><PERSON> blanco"}}}, {"key": "2B21", "mappings": {"default": {"default": "hexágono blanco"}}}, {"key": "2B22", "mappings": {"default": {"default": "hexágono negro"}}}, {"key": "2B23", "mappings": {"default": {"default": "hexagonal negro horizontal"}}}, {"key": "2B24", "mappings": {"default": {"default": "circulo grande negro"}}}, {"key": "2B25", "mappings": {"default": {"default": "diaman<PERSON>"}}}, {"key": "2B26", "mappings": {"default": {"default": "diamante blanco medio"}}}, {"key": "2B27", "mappings": {"default": {"default": "lozenge Mediano Negro"}}}, {"key": "2B28", "mappings": {"default": {"default": "pastilla blanca mediana"}}}, {"key": "2B29", "mappings": {"default": {"default": "pequeño diamante negro"}}}, {"key": "2B2A", "mappings": {"default": {"default": "lozenge pequeño negro"}}}, {"key": "2B2B", "mappings": {"default": {"default": "lozenge pequeño blanco"}}}, {"key": "2B2C", "mappings": {"default": {"default": "elipse horizontal negro"}}}, {"key": "2B2D", "mappings": {"default": {"default": "elipse horizontal blanco"}}}, {"key": "2B2E", "mappings": {"default": {"default": "elipse Vertical Negro"}}}, {"key": "2B2F", "mappings": {"default": {"default": "elipse Vert<PERSON> Blanco"}}}, {"key": "2B50", "mappings": {"default": {"default": "estrella media vacía"}}}, {"key": "2B51", "mappings": {"default": {"default": "estrella pequeña rellena"}}}, {"key": "2B52", "mappings": {"default": {"default": "estrella pequeña vacía"}}}, {"key": "2B53", "mappings": {"default": {"default": "pentágono negro que señala hacia la derecha"}}}, {"key": "2B54", "mappings": {"default": {"default": "pentágono blanco que señala hacia la derecha"}}}, {"key": "2B55", "mappings": {"default": {"default": "círculo grande pesado"}}}, {"key": "2B56", "mappings": {"default": {"default": "óvalo pesado con interior oval"}}}, {"key": "2B57", "mappings": {"default": {"default": "círculo pesado con círculo dentro"}}}, {"key": "2B58", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>ado"}}}, {"key": "2B59", "mappings": {"default": {"default": "saltire en círculo pesado"}}}], "es/symbols/math_harpoons.min": [{"locale": "es"}, {"key": "21BC", "mappings": {"default": {"default": "flecha izquierda con arpón arriba"}}}, {"key": "21BD", "mappings": {"default": {"default": "flecha izquierda con arpón debajo"}}}, {"key": "21BE", "mappings": {"default": {"default": "flecha arriba con arpón derecho"}}}, {"key": "21BF", "mappings": {"default": {"default": "flecha arriba con arpón izquierdo"}}}, {"key": "21C0", "mappings": {"default": {"default": "flecha derecha con arpón arriba"}}}, {"key": "21C1", "mappings": {"default": {"default": "flecha derecha con arpón debajo"}}}, {"key": "21C2", "mappings": {"default": {"default": "flecha abajo con arpón derecho"}}}, {"key": "21C3", "mappings": {"default": {"default": "flecha abajo con arpón izquierdo"}}}, {"key": "21CB", "mappings": {"default": {"default": "arpón izquierdo arriba de arpón derecho"}}}, {"key": "21CC", "mappings": {"default": {"default": "arpón derecho arriba de arpón izquierdo"}}}, {"key": "294A", "mappings": {"default": {"default": "barra izquierda arriba barra derecha abajo arpón"}}}, {"key": "294B", "mappings": {"default": {"default": "barra izquierda abajo barra derecha arriba arpón"}}}, {"key": "294C", "mappings": {"default": {"default": "up barb right down barb left harpoon"}}}, {"key": "294D", "mappings": {"default": {"default": "arriba barb izquierda abajo barb derecha arpón"}}}, {"key": "294E", "mappings": {"default": {"default": "barra izquierda arriba barra derecha arriba arpón"}}}, {"key": "294F", "mappings": {"default": {"default": "arpón a la derecha hacia arriba y abajo"}}}, {"key": "2950", "mappings": {"default": {"default": "barra izquierda abajo barra derecha abajo arpón"}}}, {"key": "2951", "mappings": {"default": {"default": "arpón a la izquierda hacia arriba y abajo"}}}, {"key": "2952", "mappings": {"default": {"default": "arpón a la izquierda con púa hasta la barra"}}}, {"key": "2953", "mappings": {"default": {"default": "arpón a la derecha con púa hasta la barra"}}}, {"key": "2954", "mappings": {"default": {"default": "arpón ascendente con púa derecha a barra"}}}, {"key": "2955", "mappings": {"default": {"default": "arpón hacia abajo con púa derecha a barra"}}}, {"key": "2956", "mappings": {"default": {"default": "arpón a la izquierda con púas abajo a la barra"}}}, {"key": "2957", "mappings": {"default": {"default": "arpón a la derecha con púa abajo a la barra"}}}, {"key": "2958", "mappings": {"default": {"default": "arpón ascendente con púa izquierda a barra"}}}, {"key": "2959", "mappings": {"default": {"default": "arpón hacia abajo con la barra izquierda a la barra"}}}, {"key": "295A", "mappings": {"default": {"default": "arpón a la izquierda con púa arriba de la barra"}}}, {"key": "295B", "mappings": {"default": {"default": "arpón a la derecha con púa arriba del bar"}}}, {"key": "295C", "mappings": {"default": {"default": "arpón ascendente con púa derecha de barra"}}}, {"key": "295D", "mappings": {"default": {"default": "arpón hacia abajo con púa derecha de barra"}}}, {"key": "295E", "mappings": {"default": {"default": "arpón a la izquierda con púa abajo de barra"}}}, {"key": "295F", "mappings": {"default": {"default": "arpón a la derecha con púa abajo de barra"}}}, {"key": "2960", "mappings": {"default": {"default": "arpón ascendente con púa izquierda de barra"}}}, {"key": "2961", "mappings": {"default": {"default": "arpón hacia abajo con barra izquierda de barra"}}}, {"key": "2962", "mappings": {"default": {"default": "arpón a la izquierda con púas arriba arriba arpón a la izquierda con púas abajo"}}}, {"key": "2963", "mappings": {"default": {"default": "arpón ascendente con púa izquierda al lado arpón ascendente con púa derecha"}}}, {"key": "2964", "mappings": {"default": {"default": "arpón a la derecha con púas arriba arriba arpón a la derecha con púas abajo"}}}, {"key": "2965", "mappings": {"default": {"default": "arpón hacia abajo con lengüeta a la izquierda al lado arpón hacia abajo con lengüeta a la derecha"}}}, {"key": "2966", "mappings": {"default": {"default": "arpón a la izquierda con púa arriba arriba arpón a la derecha con púa arriba"}}}, {"key": "2967", "mappings": {"default": {"default": "arpón a la izquierda con púa abajo arriba arpón a la derecha con púa abajo"}}}, {"key": "2968", "mappings": {"default": {"default": "arpón a la derecha con púas arriba arriba arpón a la izquierda con púas arriba"}}}, {"key": "2969", "mappings": {"default": {"default": "arpón a la derecha con púa abajo arriba arpón a la izquierda con púa abajo"}}}, {"key": "296A", "mappings": {"default": {"default": "barra con arpón hacia la izquierda"}}}, {"key": "296B", "mappings": {"default": {"default": "arpón hacia la izquierda con barra"}}}, {"key": "296C", "mappings": {"default": {"default": "barra con arpón hacia la derecha"}}}, {"key": "296D", "mappings": {"default": {"default": "arpón hacia la derecha con barra"}}}, {"key": "296E", "mappings": {"default": {"default": "arpón ascendente con púa izquierda al lado arpón hacia abajo con púa derecha"}}}, {"key": "296F", "mappings": {"default": {"default": "arpón hacia abajo con lengüeta a la izquierda al lado arpón hacia arriba con pica a la derecha"}}}, {"key": "297C", "mappings": {"default": {"default": "cola de pescado izquierda"}}}, {"key": "297D", "mappings": {"default": {"default": "cola de pescado derecha"}}}, {"key": "297E", "mappings": {"default": {"default": "barra con doble gancho"}}}, {"key": "297F", "mappings": {"default": {"default": "cola de pez abajo"}}}], "es/symbols/math_non_characters.min": [{"locale": "es"}, {"key": "210F", "mappings": {"default": {"default": "h barra"}}}, {"key": "2114", "mappings": {"default": {"default": "L B símbolo de barra"}}}, {"key": "2116", "mappings": {"default": {"default": "signo de número"}}}, {"key": "2117", "mappings": {"default": {"default": "derechos de autor de sonido"}}}, {"key": "211E", "mappings": {"default": {"default": "receta"}}}, {"key": "211F", "mappings": {"default": {"default": "respuesta"}}}, {"key": "2120", "mappings": {"default": {"default": "marca de servicio"}}}, {"key": "2121", "mappings": {"default": {"default": "señal de telefono"}}}, {"key": "2122", "mappings": {"default": {"default": "registrado"}}}, {"key": "2123", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "2125", "mappings": {"default": {"default": "onza"}}}, {"key": "2126", "mappings": {"default": {"default": "ohmio"}}}, {"key": "2127", "mappings": {"default": {"default": "ohmio invertido"}}}, {"key": "212A", "mappings": {"default": {"default": "grados kelvin"}}}, {"key": "212B", "mappings": {"default": {"default": "angstrom"}}}, {"key": "212E", "mappings": {"default": {"default": "Sí<PERSON>lo estimado"}}}, {"key": "2132", "mappings": {"default": {"default": "mayúscula F invertida"}}}, {"key": "2139", "mappings": {"default": {"default": "fuente de información"}}}, {"key": "213A", "mappings": {"default": {"default": "capital rotado Q"}}}, {"key": "213B", "mappings": {"default": {"default": "signo de fax"}}}, {"key": "2141", "mappings": {"default": {"default": "convertido sans serif may<PERSON> G"}}}, {"key": "2142", "mappings": {"default": {"default": "convertido sans serif mayúscula L"}}}, {"key": "2143", "mappings": {"default": {"default": "invertido sans serif mayúscula L"}}}, {"key": "2144", "mappings": {"default": {"default": "convertido en sans serif may<PERSON> Y"}}}], "es/symbols/math_symbols.min": [{"locale": "es"}, {"key": "0021", "mappings": {"default": {"default": "factorial"}}}, {"key": "0022", "mappings": {"default": {"default": "comillas"}}}, {"key": "0023", "mappings": {"default": {"default": "al<PERSON><PERSON><PERSON>", "defaultMP": "signo de número"}}}, {"key": "0024", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "0025", "mappings": {"default": {"default": "porciento"}}}, {"key": "0026", "mappings": {"default": {"default": "ampersand"}}}, {"key": "0027", "mappings": {"default": {"default": "prima", "defaultMP": "<PERSON><PERSON>ós<PERSON><PERSON>"}}}, {"key": "002A", "mappings": {"default": {"default": "por"}}}, {"key": "002B", "mappings": {"default": {"default": "más"}}}, {"key": "002C", "mappings": {"default": {"default": "coma"}}}, {"key": "002D", "mappings": {"default": {"default": "menos"}}}, {"key": "002E", "mappings": {"default": {"default": "punto"}}}, {"key": "002F", "mappings": {"default": {"default": "barra oblicua"}}}, {"key": "003A", "mappings": {"default": {"default": "dos puntos"}}}, {"key": "003B", "mappings": {"default": {"default": "punto y coma"}}}, {"key": "003C", "mappings": {"default": {"default": "menor que"}}}, {"key": "003D", "mappings": {"default": {"default": "igual"}}}, {"key": "003E", "mappings": {"default": {"default": "mayor que"}}}, {"key": "003F", "mappings": {"default": {"default": "interrogación derecho", "defaultMP": "cierra interrogación"}}}, {"key": "0040", "mappings": {"default": {"default": "arroba"}}}, {"key": "005C", "mappings": {"default": {"default": "barra inversa"}}}, {"key": "005E", "mappings": {"default": {"default": "circunflejo"}}}, {"key": "005F", "mappings": {"default": {"default": "subrayado"}}}, {"key": "0060", "mappings": {"default": {"default": "acento grave"}}}, {"key": "007C", "mappings": {"default": {"default": "barra vertical"}}}, {"key": "007E", "mappings": {"default": {"default": "tilde"}}}, {"key": "00A1", "mappings": {"default": {"default": "admiración izquierdo", "defaultMP": "abre admiración"}}}, {"key": "00A2", "mappings": {"default": {"default": "centavo"}}}, {"key": "00A3", "mappings": {"default": {"default": "libra esterlina"}}}, {"key": "00A4", "mappings": {"default": {"default": "moneda"}}}, {"key": "00A5", "mappings": {"default": {"default": "yen"}}}, {"key": "00A6", "mappings": {"default": {"default": "barra vertical partida"}}}, {"key": "00A7", "mappings": {"default": {"default": "sección"}}}, {"key": "00A8", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "00A9", "mappings": {"default": {"default": "copyright"}}}, {"key": "00AA", "mappings": {"default": {"default": "ordinal femenino"}}}, {"key": "00AB", "mappings": {"default": {"default": "comillas angulares izquierda", "defaultMP": "abre comillas angulares"}}}, {"key": "00AC", "mappings": {"default": {"default": "negación"}}}, {"key": "00AE", "mappings": {"default": {"default": "registrado"}}}, {"key": "00AF", "mappings": {"default": {"default": "barra"}}}, {"key": "00B0", "mappings": {"default": {"default": "grado"}}}, {"key": "00B1", "mappings": {"default": {"default": "más menos"}}}, {"key": "00B4", "mappings": {"default": {"default": "acento agudo"}}}, {"key": "00B5", "mappings": {"default": {"default": "micro"}}}, {"key": "00B6", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "00B7", "mappings": {"default": {"default": "punto medio"}}}, {"key": "00B8", "mappings": {"default": {"default": "cedilla"}}}, {"key": "00BA", "mappings": {"default": {"default": "ordinal masculino"}}}, {"key": "00BB", "mappings": {"default": {"default": "comillas angulares derecha", "defaultMP": "cierra comillas angulares"}}}, {"key": "00BF", "mappings": {"default": {"default": "interrogación izquierdo", "defaultMP": "abre interrogación"}}}, {"key": "00D7", "mappings": {"default": {"default": "por"}}}, {"key": "00F7", "mappings": {"default": {"default": "dividido"}}}, {"key": "02B9", "mappings": {"default": {"default": "prima"}}}, {"key": "02BA", "mappings": {"default": {"default": "dos prima"}}}, {"key": "02D8", "mappings": {"default": {"default": "breve"}}}, {"key": "02D9", "mappings": {"default": {"default": "punto en superescrito"}}}, {"key": "02DA", "mappings": {"default": {"default": "anillo en superescrito"}}}, {"key": "02DB", "mappings": {"default": {"default": "ogonek"}}}, {"key": "02DC", "mappings": {"default": {"default": "tilde"}}}, {"key": "02DD", "mappings": {"default": {"default": "doble acento agudo"}}}, {"key": "2010", "mappings": {"default": {"default": "menos"}}}, {"key": "2011", "mappings": {"default": {"default": "guión no rompible"}}}, {"key": "2012", "mappings": {"default": {"default": "figura Dash"}}}, {"key": "2013", "mappings": {"default": {"default": "en raya"}}}, {"key": "2014", "mappings": {"default": {"default": "em raya"}}}, {"key": "2015", "mappings": {"default": {"default": "barra horizontal"}}}, {"key": "2016", "mappings": {"default": {"default": "doble barra vertical"}}}, {"key": "2017", "mappings": {"default": {"default": "línea baja doble"}}}, {"key": "2018", "mappings": {"default": {"default": "comilla izquierda"}}}, {"key": "2019", "mappings": {"default": {"default": "comilla derecha"}}}, {"key": "201A", "mappings": {"default": {"default": "comilla bajo 9"}}}, {"key": "201B", "mappings": {"default": {"default": "marca de comillas simple con inversión alta-9"}}}, {"key": "201C", "mappings": {"default": {"default": "doble comilla"}}}, {"key": "201D", "mappings": {"default": {"default": "doble comilla derecha"}}}, {"key": "201E", "mappings": {"default": {"default": "doble comilla bajo 9"}}}, {"key": "201F", "mappings": {"default": {"default": "doble alto-invertido-9 comillas"}}}, {"key": "2020", "mappings": {"default": {"default": "daga"}}}, {"key": "2021", "mappings": {"default": {"default": "doble daga"}}}, {"key": "2022", "mappings": {"default": {"default": "bala"}}}, {"key": "2023", "mappings": {"default": {"default": "bala triangular"}}}, {"key": "2024", "mappings": {"default": {"default": "líder de un punto"}}}, {"key": "2025", "mappings": {"default": {"default": "dos puntos líder"}}}, {"key": "2026", "mappings": {"default": {"default": "puntos suspensivos"}}}, {"key": "2027", "mappings": {"default": {"default": "punto de separación"}}}, {"key": "2030", "mappings": {"default": {"default": "tanto por millar"}}}, {"key": "2031", "mappings": {"default": {"default": "tanto por diez mil"}}}, {"key": "2032", "mappings": {"default": {"default": "prima"}}}, {"key": "2033", "mappings": {"default": {"default": "dos prima"}}}, {"key": "2034", "mappings": {"default": {"default": "tres prima"}}}, {"key": "2035", "mappings": {"default": {"default": "acento grave"}}}, {"key": "2036", "mappings": {"default": {"default": "doble acento grave"}}}, {"key": "2037", "mappings": {"default": {"default": "prima invertido"}}}, {"key": "2038", "mappings": {"default": {"default": "signo de intercalación"}}}, {"key": "2039", "mappings": {"default": {"default": "comillas izquierda", "defaultMP": "abre comillas"}}}, {"key": "203A", "mappings": {"default": {"default": "comillas derecha", "defaultMP": "cierra comillas"}}}, {"key": "203B", "mappings": {"default": {"default": "marca de referencia"}}}, {"key": "203C", "mappings": {"default": {"default": "doble signo de exclamación"}}}, {"key": "203D", "mappings": {"default": {"default": "interrobang"}}}, {"key": "203E", "mappings": {"default": {"default": "barra superior"}}}, {"key": "203F", "mappings": {"default": {"default": "undertie"}}}, {"key": "2040", "mappings": {"default": {"default": "ligadura"}}}, {"key": "2041", "mappings": {"default": {"default": "punto de inserción"}}}, {"key": "2042", "mappings": {"default": {"default": "asterismo"}}}, {"key": "2043", "mappings": {"default": {"default": "g<PERSON><PERSON> bala"}}}, {"key": "2044", "mappings": {"default": {"default": "barra de fracción"}}}, {"key": "2047", "mappings": {"default": {"default": "doble signo de interrogación"}}}, {"key": "2048", "mappings": {"default": {"default": "pregunta Signo de exclamación"}}}, {"key": "2049", "mappings": {"default": {"default": "signo de interrogación de exclamación"}}}, {"key": "204B", "mappings": {"default": {"default": "signo de Pilcrow invertido"}}}, {"key": "204C", "mappings": {"default": {"default": "bala negra hacia la izquierda"}}}, {"key": "204D", "mappings": {"default": {"default": "bala negra hacia la derecha"}}}, {"key": "204E", "mappings": {"default": {"default": "bajo asterisco"}}}, {"key": "204F", "mappings": {"default": {"default": "punto y coma invertido"}}}, {"key": "2050", "mappings": {"default": {"default": "cierre superior"}}}, {"key": "2051", "mappings": {"default": {"default": "dos asteriscos alineados verticalmente"}}}, {"key": "2052", "mappings": {"default": {"default": "signo de menos comercial"}}}, {"key": "2053", "mappings": {"default": {"default": "swung Dash"}}}, {"key": "2054", "mappings": {"default": {"default": "ropa interior invertida"}}}, {"key": "2055", "mappings": {"default": {"default": "signo de puntuación de la flor"}}}, {"key": "2056", "mappings": {"default": {"default": "puntuación de tres puntos"}}}, {"key": "2057", "mappings": {"default": {"default": "cuatro prima"}}}, {"key": "2058", "mappings": {"default": {"default": "puntuación de cuatro puntos"}}}, {"key": "2059", "mappings": {"default": {"default": "puntuación de cinco puntos"}}}, {"key": "205A", "mappings": {"default": {"default": "puntuación de dos puntos"}}}, {"key": "205B", "mappings": {"default": {"default": "marca de cuatro puntos"}}}, {"key": "205C", "mappings": {"default": {"default": "cruz punt<PERSON>a"}}}, {"key": "205D", "mappings": {"default": {"default": "tricolon"}}}, {"key": "205E", "mappings": {"default": {"default": "cuatro puntos verticales"}}}, {"key": "207A", "mappings": {"default": {"default": "superscript Plus Sign"}}}, {"key": "207B", "mappings": {"default": {"default": "superín<PERSON> menos"}}}, {"key": "207C", "mappings": {"default": {"default": "superscript Equals Sign"}}}, {"key": "207D", "mappings": {"default": {"default": "paréntesis izquierdo superíndice"}}}, {"key": "207E", "mappings": {"default": {"default": "paréntesis derecho superíndice"}}}, {"key": "208A", "mappings": {"default": {"default": "signo de subíndice más"}}}, {"key": "208B", "mappings": {"default": {"default": "subín<PERSON> menos"}}}, {"key": "208C", "mappings": {"default": {"default": "signo de subíndice es igual"}}}, {"key": "208D", "mappings": {"default": {"default": "subíndice par<PERSON>"}}}, {"key": "208E", "mappings": {"default": {"default": "subíndice de paréntesis derecho"}}}, {"key": "214A", "mappings": {"default": {"default": "l<PERSON><PERSON> de propiedad"}}}, {"key": "214B", "mappings": {"default": {"default": "ampersand convertido"}}}, {"key": "214C", "mappings": {"default": {"default": "por signo"}}}, {"key": "214D", "mappings": {"default": {"default": "aktieselskab"}}}, {"key": "214E", "mappings": {"default": {"default": "torneado pequeño f"}}}, {"key": "2200", "mappings": {"default": {"default": "para todo"}}}, {"key": "2201", "mappings": {"default": {"default": "complementario"}}}, {"key": "2203", "mappings": {"default": {"default": "existe"}}}, {"key": "2204", "mappings": {"default": {"default": "no existe"}}}, {"key": "2205", "mappings": {"default": {"default": "conjunto vacío"}}}, {"key": "2206", "mappings": {"default": {"default": "incremento"}}}, {"key": "2208", "mappings": {"default": {"default": "perteneciente a"}}}, {"key": "2209", "mappings": {"default": {"default": "no perteneciente a"}}}, {"key": "220A", "mappings": {"default": {"default": "perteneciente a"}}}, {"key": "220B", "mappings": {"default": {"default": "comprende a"}}}, {"key": "220C", "mappings": {"default": {"default": "no comprende a"}}}, {"key": "220D", "mappings": {"default": {"default": "comprende a"}}}, {"key": "220E", "mappings": {"default": {"default": "fin de demostración"}}}, {"key": "220F", "mappings": {"default": {"default": "producto"}}}, {"key": "2210", "mappings": {"default": {"default": "coproducto"}}}, {"key": "2211", "mappings": {"default": {"default": "sumatorio"}}}, {"key": "2212", "mappings": {"default": {"default": "menos"}}}, {"key": "2213", "mappings": {"default": {"default": "menos más"}}}, {"key": "2214", "mappings": {"default": {"default": "punto más"}}}, {"key": "2215", "mappings": {"default": {"default": "barra de división"}}}, {"key": "2216", "mappings": {"default": {"default": "menos"}}}, {"key": "2217", "mappings": {"default": {"default": "asterisco"}}}, {"key": "2218", "mappings": {"default": {"default": "composición"}}}, {"key": "2219", "mappings": {"default": {"default": "bala"}}}, {"key": "221A", "mappings": {"default": {"default": "raíz"}}}, {"key": "221B", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "221C", "mappings": {"default": {"default": "ra<PERSON><PERSON> cuarta"}}}, {"key": "221D", "mappings": {"default": {"default": "proporcional a"}}}, {"key": "221E", "mappings": {"default": {"default": "infinito"}}}, {"key": "221F", "mappings": {"default": {"default": "ángulo recto"}}}, {"key": "2220", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2221", "mappings": {"default": {"default": "á<PERSON><PERSON> medido"}}}, {"key": "2222", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2223", "mappings": {"default": {"default": "divide a"}}}, {"key": "2224", "mappings": {"default": {"default": "no divide a"}}}, {"key": "2225", "mappings": {"default": {"default": "paralela a"}}}, {"key": "2226", "mappings": {"default": {"default": "no paralela a"}}}, {"key": "2227", "mappings": {"default": {"default": "y"}}}, {"key": "2228", "mappings": {"default": {"default": "o"}}}, {"key": "2229", "mappings": {"default": {"default": "intersección"}}}, {"key": "222A", "mappings": {"default": {"default": "unión"}}}, {"key": "222B", "mappings": {"default": {"default": "integral"}}}, {"key": "222C", "mappings": {"default": {"default": "integral doble"}}}, {"key": "222D", "mappings": {"default": {"default": "integral triple"}}}, {"key": "222E", "mappings": {"default": {"default": "integral de contorno"}}}, {"key": "222F", "mappings": {"default": {"default": "integral de superficie"}}}, {"key": "2230", "mappings": {"default": {"default": "integral de volumen"}}}, {"key": "2231", "mappings": {"default": {"default": "integral de contorno en sentido horario"}}}, {"key": "2232", "mappings": {"default": {"default": "integral de contorno en sentido horario"}}}, {"key": "2233", "mappings": {"default": {"default": "integral de contorno en sentido antihorario"}}}, {"key": "2234", "mappings": {"default": {"default": "por lo tanto"}}}, {"key": "2235", "mappings": {"default": {"default": "porque"}}}, {"key": "2236", "mappings": {"default": {"default": "razón"}}}, {"key": "2237", "mappings": {"default": {"default": "proporción"}}}, {"key": "2238", "mappings": {"default": {"default": "punto menos"}}}, {"key": "2239", "mappings": {"default": {"default": "exceso"}}}, {"key": "223A", "mappings": {"default": {"default": "proporción geométrica"}}}, {"key": "223B", "mappings": {"default": {"default": "ho<PERSON>cia"}}}, {"key": "223C", "mappings": {"default": {"default": "tilde"}}}, {"key": "223D", "mappings": {"default": {"default": "tilde invertido"}}}, {"key": "223E", "mappings": {"default": {"default": "s horizontal invertida"}}}, {"key": "223F", "mappings": {"default": {"default": "sinusoide"}}}, {"key": "2240", "mappings": {"default": {"default": "producto de guirnalda"}}}, {"key": "2241", "mappings": {"default": {"default": "no tilde"}}}, {"key": "2242", "mappings": {"default": {"default": "menos tilde"}}}, {"key": "2243", "mappings": {"default": {"default": "asintóticamente igual a"}}}, {"key": "2244", "mappings": {"default": {"default": "no asintóticamente igual a"}}}, {"key": "2245", "mappings": {"default": {"default": "aproximadamente igual a"}}}, {"key": "2246", "mappings": {"default": {"default": "aproximado pero no igual a"}}}, {"key": "2247", "mappings": {"default": {"default": "no aproximado a"}}}, {"key": "2248", "mappings": {"default": {"default": "a<PERSON>roxi<PERSON><PERSON>", "defaultMP": "casi igual a"}}}, {"key": "2249", "mappings": {"default": {"default": "no es casi igual a"}}}, {"key": "224A", "mappings": {"default": {"default": "igual o casi igual a"}}}, {"key": "224B", "mappings": {"default": {"default": "triple tilde"}}}, {"key": "224C", "mappings": {"default": {"default": "todo igual a"}}}, {"key": "224D", "mappings": {"default": {"default": "equivalente a"}}}, {"key": "224E", "mappings": {"default": {"default": "geométricamente equivalente a"}}}, {"key": "224F", "mappings": {"default": {"default": "diferencia entre"}}}, {"key": "2250", "mappings": {"default": {"default": "se acerca al límite"}}}, {"key": "2251", "mappings": {"default": {"default": "geométricamente igual a"}}}, {"key": "2252", "mappings": {"default": {"default": "aproximadamente igual a o imagen de"}}}, {"key": "2253", "mappings": {"default": {"default": "imagen de o aproximadamente igual a"}}}, {"key": "2254", "mappings": {"default": {"default": "dos puntos igual"}}}, {"key": "2255", "mappings": {"default": {"default": "igual dos puntos"}}}, {"key": "2256", "mappings": {"default": {"default": "igual incluyendo anillo"}}}, {"key": "2257", "mappings": {"default": {"default": "igual anillo a"}}}, {"key": "2258", "mappings": {"default": {"default": "corresponde a"}}}, {"key": "2259", "mappings": {"default": {"default": "estima a"}}}, {"key": "225A", "mappings": {"default": {"default": "equiangular con"}}}, {"key": "225B", "mappings": {"default": {"default": "igual con estrella"}}}, {"key": "225C", "mappings": {"default": {"default": "igual con delta"}}}, {"key": "225D", "mappings": {"default": {"default": "igual por definición a"}}}, {"key": "225E", "mappings": {"default": {"default": "medido por"}}}, {"key": "225F", "mappings": {"default": {"default": "igual con interrogante"}}}, {"key": "2260", "mappings": {"default": {"default": "no es igual a", "defaultMP": "distinto de"}}}, {"key": "2261", "mappings": {"default": {"default": "idéntico a"}}}, {"key": "2262", "mappings": {"default": {"default": "no es idéntico a"}}}, {"key": "2263", "mappings": {"default": {"default": "estrictamente equivalente a"}}}, {"key": "2264", "mappings": {"default": {"default": "menor o igual que"}}}, {"key": "2265", "mappings": {"default": {"default": "mayor o igual que"}}}, {"key": "2266", "mappings": {"default": {"default": "menor o igual que"}}}, {"key": "2267", "mappings": {"default": {"default": "mayor o igual que"}}}, {"key": "2268", "mappings": {"default": {"default": "estrictamente menor que"}}}, {"key": "2269", "mappings": {"default": {"default": "estrictamente mayor que"}}}, {"key": "226A", "mappings": {"default": {"default": "mucho menor que"}}}, {"key": "226B", "mappings": {"default": {"default": "mucho mayor que"}}}, {"key": "226C", "mappings": {"default": {"default": "entre"}}}, {"key": "226D", "mappings": {"default": {"default": "no equivalente a"}}}, {"key": "226E", "mappings": {"default": {"default": "no menor que"}}}, {"key": "226F", "mappings": {"default": {"default": "no mayor que"}}}, {"key": "2270", "mappings": {"default": {"default": "no menor ni igual a"}}}, {"key": "2271", "mappings": {"default": {"default": "no mayor ni igual a"}}}, {"key": "2272", "mappings": {"default": {"default": "menor o equivalente a"}}}, {"key": "2273", "mappings": {"default": {"default": "mayor o equivalente a"}}}, {"key": "2274", "mappings": {"default": {"default": "no menor ni equivalente a"}}}, {"key": "2275", "mappings": {"default": {"default": "no mayor ni equivalente a"}}}, {"key": "2276", "mappings": {"default": {"default": "menor o mayor que"}}}, {"key": "2277", "mappings": {"default": {"default": "mayor o menor que"}}}, {"key": "2278", "mappings": {"default": {"default": "no menor ni mayor que"}}}, {"key": "2279", "mappings": {"default": {"default": "no mayor ni menor que"}}}, {"key": "227A", "mappings": {"default": {"default": "precede a"}}}, {"key": "227B", "mappings": {"default": {"default": "sigue a"}}}, {"key": "227C", "mappings": {"default": {"default": "precede o es igual a"}}}, {"key": "227D", "mappings": {"default": {"default": "sigue o es igual a"}}}, {"key": "227E", "mappings": {"default": {"default": "precede o es equivalente a"}}}, {"key": "227F", "mappings": {"default": {"default": "sigue o es equivalente a"}}}, {"key": "2280", "mappings": {"default": {"default": "no precede a"}}}, {"key": "2281", "mappings": {"default": {"default": "no sigue a"}}}, {"key": "2282", "mappings": {"default": {"default": "incluido en"}}}, {"key": "2283", "mappings": {"default": {"default": "contiene a"}}}, {"key": "2284", "mappings": {"default": {"default": "no incluido en"}}}, {"key": "2285", "mappings": {"default": {"default": "no contiene a"}}}, {"key": "2286", "mappings": {"default": {"default": "incluido o igual a"}}}, {"key": "2287", "mappings": {"default": {"default": "contiene o es igual a"}}}, {"key": "2288", "mappings": {"default": {"default": "no incluido ni igual a"}}}, {"key": "2289", "mappings": {"default": {"default": "no contiene ni es igual a"}}}, {"key": "228A", "mappings": {"default": {"default": "incluido estrictamente en"}}}, {"key": "228B", "mappings": {"default": {"default": "contiene estrictamente a"}}}, {"key": "228C", "mappings": {"default": {"default": "familia de conjuntos"}}}, {"key": "228D", "mappings": {"default": {"default": "producto de familia de conjuntos"}}}, {"key": "228E", "mappings": {"default": {"default": "unión de familia de conjuntos"}}}, {"key": "228F", "mappings": {"default": {"default": "imagen cuadrada de"}}}, {"key": "2290", "mappings": {"default": {"default": "original cuadrado de"}}}, {"key": "2291", "mappings": {"default": {"default": "imagen cuadrada de o igual a"}}}, {"key": "2292", "mappings": {"default": {"default": "original cuadrado de o igual a"}}}, {"key": "2293", "mappings": {"default": {"default": "intersección cuadrada"}}}, {"key": "2294", "mappings": {"default": {"default": "unión cuadrada"}}}, {"key": "2295", "mappings": {"default": {"default": "más en círculo"}}}, {"key": "2296", "mappings": {"default": {"default": "menos en círculo"}}}, {"key": "2297", "mappings": {"default": {"default": "por en círculo"}}}, {"key": "2298", "mappings": {"default": {"default": "barra en círculo"}}}, {"key": "2299", "mappings": {"default": {"default": "punto en círculo"}}}, {"key": "229A", "mappings": {"default": {"default": "anillo en cí<PERSON>ulo"}}}, {"key": "229B", "mappings": {"default": {"default": "asterisco en círculo"}}}, {"key": "229C", "mappings": {"default": {"default": "igual en círculo"}}}, {"key": "229D", "mappings": {"default": {"default": "menos en círculo"}}}, {"key": "229E", "mappings": {"default": {"default": "más en cuadrado"}}}, {"key": "229F", "mappings": {"default": {"default": "menos en cuadrado"}}}, {"key": "22A0", "mappings": {"default": {"default": "veces en cuadrado"}}}, {"key": "22A1", "mappings": {"default": {"default": "punto en cuadrado"}}}, {"key": "22A2", "mappings": {"default": {"default": "t horizontal hacia la izquierda"}}}, {"key": "22A3", "mappings": {"default": {"default": "t horizontal hacia la derecha"}}}, {"key": "22A4", "mappings": {"default": {"default": "perpendicular"}}}, {"key": "22A5", "mappings": {"default": {"default": "perpendicular invertida"}}}, {"key": "22A6", "mappings": {"default": {"default": "afirmación"}}}, {"key": "22A7", "mappings": {"default": {"default": "modela"}}}, {"key": "22A8", "mappings": {"default": {"default": "verdadero"}}}, {"key": "22A9", "mappings": {"default": {"default": "obliga"}}}, {"key": "22AA", "mappings": {"default": {"default": "triple barra vertical torniquete derecho"}}}, {"key": "22AB", "mappings": {"default": {"default": "doble barra vertical doble torniquete derecho"}}}, {"key": "22AC", "mappings": {"default": {"default": "no prueba"}}}, {"key": "22AD", "mappings": {"default": {"default": "falso"}}}, {"key": "22AE", "mappings": {"default": {"default": "no obliga"}}}, {"key": "22AF", "mappings": {"default": {"default": "doble barra vertical doble torniquete derecho negada"}}}, {"key": "22B0", "mappings": {"default": {"default": "precede respecto de"}}}, {"key": "22B1", "mappings": {"default": {"default": "sigue respecto de"}}}, {"key": "22B2", "mappings": {"default": {"default": "subgrupo normal de"}}}, {"key": "22B3", "mappings": {"default": {"default": "contiene como subgrupo normal a"}}}, {"key": "22B4", "mappings": {"default": {"default": "subgrupo normal o es igual a"}}}, {"key": "22B5", "mappings": {"default": {"default": "contiene como subgrupo normal o es igual a"}}}, {"key": "22B6", "mappings": {"default": {"default": "original de"}}}, {"key": "22B7", "mappings": {"default": {"default": "imagen de"}}}, {"key": "22B8", "mappings": {"default": {"default": "multifunción"}}}, {"key": "22B9", "mappings": {"default": {"default": "matriz hermitiana conjugada"}}}, {"key": "22BA", "mappings": {"default": {"default": "interpola"}}}, {"key": "22BB", "mappings": {"default": {"default": "o excluyente"}}}, {"key": "22BC", "mappings": {"default": {"default": "no y"}}}, {"key": "22BD", "mappings": {"default": {"default": "no o"}}}, {"key": "22BF", "mappings": {"default": {"default": "trián<PERSON>lo rectán<PERSON>lo"}}}, {"key": "22C0", "mappings": {"default": {"default": "y"}}}, {"key": "22C1", "mappings": {"default": {"default": "o"}}}, {"key": "22C2", "mappings": {"default": {"default": "intersección"}}}, {"key": "22C3", "mappings": {"default": {"default": "unión"}}}, {"key": "22C4", "mappings": {"default": {"default": "operador diamante"}}}, {"key": "22C5", "mappings": {"default": {"default": "punto"}}}, {"key": "22C6", "mappings": {"default": {"default": "estrella"}}}, {"key": "22C7", "mappings": {"default": {"default": "dividido por"}}}, {"key": "22C8", "mappings": {"default": {"default": "moño"}}}, {"key": "22C9", "mappings": {"default": {"default": "factor normal izquierdo producto semidirecto"}}}, {"key": "22CA", "mappings": {"default": {"default": "factor normal derecho producto semidirecto"}}}, {"key": "22CB", "mappings": {"default": {"default": "producto semidirecto izquierdo"}}}, {"key": "22CC", "mappings": {"default": {"default": "producto semidirecto derecho"}}}, {"key": "22CD", "mappings": {"default": {"default": "tilde invertida igual a"}}}, {"key": "22CE", "mappings": {"default": {"default": "o rizada"}}}, {"key": "22CF", "mappings": {"default": {"default": "y rizada"}}}, {"key": "22D0", "mappings": {"default": {"default": "doble incluido"}}}, {"key": "22D1", "mappings": {"default": {"default": "doble contiene"}}}, {"key": "22D2", "mappings": {"default": {"default": "doble intersección"}}}, {"key": "22D3", "mappings": {"default": {"default": "doble unión"}}}, {"key": "22D4", "mappings": {"default": {"default": "<PERSON><PERSON>o"}}}, {"key": "22D5", "mappings": {"default": {"default": "paralela o igual a"}}}, {"key": "22D6", "mappings": {"default": {"default": "menor que con punto"}}}, {"key": "22D7", "mappings": {"default": {"default": "mayor que con punto"}}}, {"key": "22D8", "mappings": {"default": {"default": "mucho menor que"}}}, {"key": "22D9", "mappings": {"default": {"default": "mucho mayor que"}}}, {"key": "22DA", "mappings": {"default": {"default": "menor igual o mayor que"}}}, {"key": "22DB", "mappings": {"default": {"default": "mayor igual o menor que"}}}, {"key": "22DC", "mappings": {"default": {"default": "menor o igual que"}}}, {"key": "22DD", "mappings": {"default": {"default": "mayor o igual que"}}}, {"key": "22DE", "mappings": {"default": {"default": "precede o es igual a"}}}, {"key": "22DF", "mappings": {"default": {"default": "sigue o es igual a"}}}, {"key": "22E0", "mappings": {"default": {"default": "no precede ni es igual a"}}}, {"key": "22E1", "mappings": {"default": {"default": "no sigue ni es igual a"}}}, {"key": "22E2", "mappings": {"default": {"default": "no es imagen cuadrada ni igual a"}}}, {"key": "22E3", "mappings": {"default": {"default": "no es original cuadrada ni igual a"}}}, {"key": "22E4", "mappings": {"default": {"default": "imagen cuadrada estricta"}}}, {"key": "22E5", "mappings": {"default": {"default": "original cuadrada estricta"}}}, {"key": "22E6", "mappings": {"default": {"default": "menor que no equivalente a"}}}, {"key": "22E7", "mappings": {"default": {"default": "mayor que no equivalente a"}}}, {"key": "22E8", "mappings": {"default": {"default": "precede no equivalente a"}}}, {"key": "22E9", "mappings": {"default": {"default": "sigue no equivalente a"}}}, {"key": "22EA", "mappings": {"default": {"default": "no subgrupo normal de"}}}, {"key": "22EB", "mappings": {"default": {"default": "no contiene como subgrupo normal a"}}}, {"key": "22EC", "mappings": {"default": {"default": "no es subgrupo normal ni igual a"}}}, {"key": "22ED", "mappings": {"default": {"default": "no contiene como subgrupo normal ni es igual a"}}}, {"key": "22EE", "mappings": {"default": {"default": "puntos suspensivos verticales"}}}, {"key": "22EF", "mappings": {"default": {"default": "puntos suspensivos altos"}}}, {"key": "22F0", "mappings": {"default": {"default": "puntos suspensivos diagonales subiendo"}}}, {"key": "22F1", "mappings": {"default": {"default": "puntos suspensivos diagonales bajando"}}}, {"key": "22F2", "mappings": {"default": {"default": "pertenece con trazo horizontal"}}}, {"key": "22F3", "mappings": {"default": {"default": "pertenece con barra vertical al fin de raya horizontal"}}}, {"key": "22F4", "mappings": {"default": {"default": "pertenece en pequeño con trazo vertical"}}}, {"key": "22F5", "mappings": {"default": {"default": "pertenece con punto arriba"}}}, {"key": "22F6", "mappings": {"default": {"default": "pertenece con barra arriba"}}}, {"key": "22F7", "mappings": {"default": {"default": "perteneciente subrayado"}}}, {"key": "22F8", "mappings": {"default": {"default": "perteneciente subrayado"}}}, {"key": "22F9", "mappings": {"default": {"default": "pertenece con dos trazos horizontales"}}}, {"key": "22FA", "mappings": {"default": {"default": "contiene o es igual a"}}}, {"key": "22FB", "mappings": {"default": {"default": "contiene con marca"}}}, {"key": "22FC", "mappings": {"default": {"default": "contiene con marca"}}}, {"key": "22FD", "mappings": {"default": {"default": "contiene con barra"}}}, {"key": "22FE", "mappings": {"default": {"default": "perteneciente con barra"}}}, {"key": "22FF", "mappings": {"default": {"default": "z anotación asociación de bolsa"}}}, {"key": "2300", "mappings": {"default": {"default": "diámetro"}}}, {"key": "2302", "mappings": {"default": {"default": "casa"}}}, {"key": "2305", "mappings": {"default": {"default": "en proyectividad con"}}}, {"key": "2306", "mappings": {"default": {"default": "en perspectividad con"}}}, {"key": "2307", "mappings": {"default": {"default": "l<PERSON>ea ondulada"}}}, {"key": "2310", "mappings": {"default": {"default": "no invertido"}}}, {"key": "2311", "mappings": {"default": {"default": "pastillas cuadradas"}}}, {"key": "2312", "mappings": {"default": {"default": "arco"}}}, {"key": "2313", "mappings": {"default": {"default": "segmento"}}}, {"key": "2314", "mappings": {"default": {"default": "sector"}}}, {"key": "2795", "mappings": {"default": {"default": "signo más pesado"}}}, {"key": "2796", "mappings": {"default": {"default": "signo menos pesado"}}}, {"key": "2797", "mappings": {"default": {"default": "signo de la división pesada"}}}, {"key": "27B0", "mappings": {"default": {"default": "bucle rizado"}}}, {"key": "27BF", "mappings": {"default": {"default": "doble bucle rizado"}}}, {"key": "27C1", "mappings": {"default": {"default": "trián<PERSON>lo encerrando triángulo"}}}, {"key": "27C2", "mappings": {"default": {"default": "perpendicular"}}}, {"key": "27C3", "mappings": {"default": {"default": "incluido en"}}}, {"key": "27C4", "mappings": {"default": {"default": "contiene a"}}}, {"key": "27C7", "mappings": {"default": {"default": "punto en círculo"}}}, {"key": "27C8", "mappings": {"default": {"default": "atrás tajo antes de subconjunto"}}}, {"key": "27C9", "mappings": {"default": {"default": "sobreconjunto antes de tajo"}}}, {"key": "27CA", "mappings": {"default": {"default": "barra vertical con golpe horizontal"}}}, {"key": "27CB", "mappings": {"default": {"default": "diagonal mate<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "27CC", "mappings": {"default": {"default": "división larga"}}}, {"key": "27CD", "mappings": {"default": {"default": "diagonal descendente <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "27CE", "mappings": {"default": {"default": "cuadrado Lógico Y"}}}, {"key": "27CF", "mappings": {"default": {"default": "cuadrado Lógico O"}}}, {"key": "27D0", "mappings": {"default": {"default": "diamante blanco con punto centrado"}}}, {"key": "27D1", "mappings": {"default": {"default": "y con punto interior"}}}, {"key": "27D2", "mappings": {"default": {"default": "elemento de apertura hacia arriba"}}}, {"key": "27D3", "mappings": {"default": {"default": "ángulo inferior derecho con punto"}}}, {"key": "27D4", "mappings": {"default": {"default": "ángulo inferior izquierdo con punto"}}}, {"key": "27D5", "mappings": {"default": {"default": "iz<PERSON>erdo exterior une"}}}, {"key": "27D6", "mappings": {"default": {"default": "derecho exterior une"}}}, {"key": "27D7", "mappings": {"default": {"default": "lleno exterior une"}}}, {"key": "27D8", "mappings": {"default": {"default": "perpendicular inversa"}}}, {"key": "27D9", "mappings": {"default": {"default": "perpendicular"}}}, {"key": "27DA", "mappings": {"default": {"default": "izquierdo y derecho doble torniquete"}}}, {"key": "27DB", "mappings": {"default": {"default": "doble T horizontal"}}}, {"key": "27DC", "mappings": {"default": {"default": "multifunción por la izquierda"}}}, {"key": "27DD", "mappings": {"default": {"default": "t horizontal derecha larga"}}}, {"key": "27DE", "mappings": {"default": {"default": "t horizontal izquierda larga"}}}, {"key": "27DF", "mappings": {"default": {"default": "perpendicular inversa con círculo arriba"}}}, {"key": "27E0", "mappings": {"default": {"default": "pastilla dividida por regla horizontal"}}}, {"key": "27E1", "mappings": {"default": {"default": "diamante cóncavo vacío"}}}, {"key": "27E2", "mappings": {"default": {"default": "diamante cóncavo con tictac hacia izquierda"}}}, {"key": "27E3", "mappings": {"default": {"default": "diamante cóncavo con tictac hacia derecha"}}}, {"key": "27E4", "mappings": {"default": {"default": "cuadrado con tictac hacia izquierda"}}}, {"key": "27E5", "mappings": {"default": {"default": "cuadrado con tictac hacia derecha"}}}, {"key": "292B", "mappings": {"default": {"default": "cruce en diagonal <PERSON><PERSON><PERSON> en diagonal <PERSON><PERSON><PERSON><PERSON> en diagonal"}}}, {"key": "292C", "mappings": {"default": {"default": "diagonal descendente <PERSON><PERSON><PERSON> diagonal ascendente"}}}, {"key": "2980", "mappings": {"default": {"default": "delimitador de barra vertical triple"}}}, {"key": "2981", "mappings": {"default": {"default": "punto de notación Z"}}}, {"key": "2982", "mappings": {"default": {"default": "tipo de notación Z Colon"}}}, {"key": "2999", "mappings": {"default": {"default": "valla punteada"}}}, {"key": "299A", "mappings": {"default": {"default": "zigzag en vertical"}}}, {"key": "29B0", "mappings": {"default": {"default": "conjunto vacío invertido"}}}, {"key": "29B1", "mappings": {"default": {"default": "conjunto vacío con barra"}}}, {"key": "29B2", "mappings": {"default": {"default": "conjunto vacío con cí<PERSON>ulo"}}}, {"key": "29B5", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> con barra"}}}, {"key": "29B6", "mappings": {"default": {"default": "barra en círculo"}}}, {"key": "29B7", "mappings": {"default": {"default": "paralelas en círculo"}}}, {"key": "29B8", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> inverso solidus"}}}, {"key": "29B9", "mappings": {"default": {"default": "perpendicular en círculo"}}}, {"key": "29BA", "mappings": {"default": {"default": "círculo dividido por barra horizontal y mitad superior dividido por barra vertical"}}}, {"key": "29BB", "mappings": {"default": {"default": "x superimpresa en círculo"}}}, {"key": "29BC", "mappings": {"default": {"default": "circled anticlockwise rotated division"}}}, {"key": "29BE", "mappings": {"default": {"default": "bullet vacía en círculo"}}}, {"key": "29BF", "mappings": {"default": {"default": "bullet en círculo"}}}, {"key": "29C0", "mappings": {"default": {"default": "menor que en círculo"}}}, {"key": "29C1", "mappings": {"default": {"default": "mayor que en c<PERSON><PERSON>ulo"}}}, {"key": "29C2", "mappings": {"default": {"default": "círculo con círculo a la derecha"}}}, {"key": "29C3", "mappings": {"default": {"default": "circle with two horizontal strokes to the right"}}}, {"key": "29C4", "mappings": {"default": {"default": "cuadrado con diagonal ascendente"}}}, {"key": "29C5", "mappings": {"default": {"default": "cuadrado con diagonal descendente"}}}, {"key": "29C6", "mappings": {"default": {"default": "as<PERSON><PERSON> cuadrado"}}}, {"key": "29C7", "mappings": {"default": {"default": "pequeño cí<PERSON>ulo cu<PERSON>"}}}, {"key": "29C8", "mappings": {"default": {"default": "cuadrado cuadrado"}}}, {"key": "29C9", "mappings": {"default": {"default": "dos cuadrados unidos"}}}, {"key": "29CA", "mappings": {"default": {"default": "triángulo con punto arriba"}}}, {"key": "29CB", "mappings": {"default": {"default": "triángulo con barra inferior"}}}, {"key": "29CC", "mappings": {"default": {"default": "s en triangulo"}}}, {"key": "29CD", "mappings": {"default": {"default": "triangle with serifs at bottom"}}}, {"key": "29CE", "mappings": {"default": {"default": "triángulo hacia la derecha sobre triángulo hacia la izquierda"}}}, {"key": "29CF", "mappings": {"default": {"default": "subgrupo normal o igual a"}}}, {"key": "29D0", "mappings": {"default": {"default": "contiene como subgrupo normal o es igual a"}}}, {"key": "29D1", "mappings": {"default": {"default": "pajarita Con Mi<PERSON>egra"}}}, {"key": "29D2", "mappings": {"default": {"default": "pajarita Con Mi<PERSON>"}}}, {"key": "29D3", "mappings": {"default": {"default": "pajarita <PERSON>"}}}, {"key": "29D4", "mappings": {"default": {"default": "tiempos con Mitad Izquierda Negra"}}}, {"key": "29D5", "mappings": {"default": {"default": "tiempos con la mitad derecha negra"}}}, {"key": "29D6", "mappings": {"default": {"default": "reloj de arena blanco"}}}, {"key": "29D7", "mappings": {"default": {"default": "reloj de arena negro"}}}, {"key": "29DC", "mappings": {"default": {"default": "infinito incompleto"}}}, {"key": "29DD", "mappings": {"default": {"default": "empate sobre el infinito"}}}, {"key": "29DE", "mappings": {"default": {"default": "no infinito"}}}, {"key": "29DF", "mappings": {"default": {"default": "multimapa de doble extremo"}}}, {"key": "29E0", "mappings": {"default": {"default": "cuadrado con contorno contorneado"}}}, {"key": "29E1", "mappings": {"default": {"default": "aumenta como"}}}, {"key": "29E2", "mappings": {"default": {"default": "shuffle Product"}}}, {"key": "29E3", "mappings": {"default": {"default": "igual tachado doble "}}}, {"key": "29E4", "mappings": {"default": {"default": "igual con tilde, con doble tachado"}}}, {"key": "29E5", "mappings": {"default": {"default": "idéntico a con doble tachado"}}}, {"key": "29E6", "mappings": {"default": {"default": "g<PERSON><PERSON>"}}}, {"key": "29E7", "mappings": {"default": {"default": "termodinamica"}}}, {"key": "29E8", "mappings": {"default": {"default": "triángulo que apunta hacia abajo con la mitad izquierda negra"}}}, {"key": "29E9", "mappings": {"default": {"default": "triángulo que apunta hacia abajo con la mitad derecha negra"}}}, {"key": "29EB", "mappings": {"default": {"default": "rombo relleno"}}}, {"key": "29EE", "mappings": {"default": {"default": "plaza blanca con error"}}}, {"key": "29EF", "mappings": {"default": {"default": "error en la Plaza Negra"}}}, {"key": "29F0", "mappings": {"default": {"default": "error White Barred Diamond"}}}, {"key": "29F1", "mappings": {"default": {"default": "error Black Barred Diamond"}}}, {"key": "29F2", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> blanco con error"}}}, {"key": "29F3", "mappings": {"default": {"default": "círculo negro con barras de error"}}}, {"key": "29F4", "mappings": {"default": {"default": "rule delayed"}}}, {"key": "29F5", "mappings": {"default": {"default": "operador de Solidus Inverso"}}}, {"key": "29F6", "mappings": {"default": {"default": "solidus with overbar"}}}, {"key": "29F7", "mappings": {"default": {"default": "solidus inverso con trazo horizontal"}}}, {"key": "29F8", "mappings": {"default": {"default": "sólido grande"}}}, {"key": "29F9", "mappings": {"default": {"default": "sólido inverso grande"}}}, {"key": "29FA", "mappings": {"default": {"default": "doble Plus"}}}, {"key": "29FB", "mappings": {"default": {"default": "triple Plus"}}}, {"key": "29FE", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "29FF", "mappings": {"default": {"default": "miny"}}}, {"key": "2A00", "mappings": {"default": {"default": "operador de puntos circulares N-Ary"}}}, {"key": "2A01", "mappings": {"default": {"default": "operador N-Ary Circled Plus"}}}, {"key": "2A02", "mappings": {"default": {"default": "operador de los tiempos en círculo de N-Ary"}}}, {"key": "2A03", "mappings": {"default": {"default": "operador de la unión N-Ary con punto"}}}, {"key": "2A04", "mappings": {"default": {"default": "n-Ary Union Operator con Plus"}}}, {"key": "2A05", "mappings": {"default": {"default": "operador de intersecciones cuadradas N-Ary"}}}, {"key": "2A06", "mappings": {"default": {"default": "n-Ary Square Union Operator"}}}, {"key": "2A07", "mappings": {"default": {"default": "dos lógico y operador"}}}, {"key": "2A08", "mappings": {"default": {"default": "dos lógico u operador"}}}, {"key": "2A09", "mappings": {"default": {"default": "operador de N-Ary Times"}}}, {"key": "2A0A", "mappings": {"default": {"default": "modulo dos sumas"}}}, {"key": "2A0B", "mappings": {"default": {"default": "suma con Integral"}}}, {"key": "2A0C", "mappings": {"default": {"default": "operador integral cuádruple"}}}, {"key": "2A0D", "mappings": {"default": {"default": "finite part integral"}}}, {"key": "2A0E", "mappings": {"default": {"default": "integral con Doble Trazo"}}}, {"key": "2A0F", "mappings": {"default": {"default": "promedio Integral con Slash"}}}, {"key": "2A10", "mappings": {"default": {"default": "circulation function"}}}, {"key": "2A11", "mappings": {"default": {"default": "integral en sentido antihorario"}}}, {"key": "2A12", "mappings": {"default": {"default": "integral lineal para rectángulo en polo"}}}, {"key": "2A13", "mappings": {"default": {"default": "integral lineal para semicírculo en polo"}}}, {"key": "2A14", "mappings": {"default": {"default": "integral lineal excluyendo el polo"}}}, {"key": "2A15", "mappings": {"default": {"default": "integral en torno de un punto operador"}}}, {"key": "2A16", "mappings": {"default": {"default": "operador integral para cuaterniones"}}}, {"key": "2A18", "mappings": {"default": {"default": "integral con Times Sign"}}}, {"key": "2A19", "mappings": {"default": {"default": "integral con Intersección"}}}, {"key": "2A1A", "mappings": {"default": {"default": "integral con union"}}}, {"key": "2A1B", "mappings": {"default": {"default": "integral con Overbar"}}}, {"key": "2A1C", "mappings": {"default": {"default": "integral con barra inferior"}}}, {"key": "2A1D", "mappings": {"default": {"default": "unirse"}}}, {"key": "2A1E", "mappings": {"default": {"default": "operador de triángulo izquierdo grande"}}}, {"key": "2A1F", "mappings": {"default": {"default": "composición de esquemas de notación Z"}}}, {"key": "2A20", "mappings": {"default": {"default": "esquema de notación Z"}}}, {"key": "2A21", "mappings": {"default": {"default": "proyección del esquema de notación Z"}}}, {"key": "2A22", "mappings": {"default": {"default": "más con círculo"}}}, {"key": "2A23", "mappings": {"default": {"default": "más con ángulo"}}}, {"key": "2A24", "mappings": {"default": {"default": "tilde con más suscrito"}}}, {"key": "2A25", "mappings": {"default": {"default": "más con punto inferior"}}}, {"key": "2A26", "mappings": {"default": {"default": "tilde con más"}}}, {"key": "2A27", "mappings": {"default": {"default": "más con 2 suscrito"}}}, {"key": "2A28", "mappings": {"default": {"default": "signo más con triángulo negro"}}}, {"key": "2A29", "mappings": {"default": {"default": "menos con coma"}}}, {"key": "2A2A", "mappings": {"default": {"default": "menos con punto inferior"}}}, {"key": "2A2B", "mappings": {"default": {"default": "signo menos con puntos que caen"}}}, {"key": "2A2C", "mappings": {"default": {"default": "signo menos con puntos crecientes"}}}, {"key": "2A2D", "mappings": {"default": {"default": "más en semicírculo i<PERSON>"}}}, {"key": "2A2E", "mappings": {"default": {"default": "más en semicírculo derecho"}}}, {"key": "2A2F", "mappings": {"default": {"default": "producto vectorial"}}}, {"key": "2A30", "mappings": {"default": {"default": "por con punto"}}}, {"key": "2A31", "mappings": {"default": {"default": "por con subrayado"}}}, {"key": "2A32", "mappings": {"default": {"default": "producto semidirecto con fondo cerrado"}}}, {"key": "2A33", "mappings": {"default": {"default": "smash product"}}}, {"key": "2A34", "mappings": {"default": {"default": "por en semicírculo i<PERSON>"}}}, {"key": "2A35", "mappings": {"default": {"default": "por en semicírculo derecho"}}}, {"key": "2A36", "mappings": {"default": {"default": "por en círculo con ángulo"}}}, {"key": "2A37", "mappings": {"default": {"default": "por en anillo"}}}, {"key": "2A38", "mappings": {"default": {"default": "división en círculo"}}}, {"key": "2A39", "mappings": {"default": {"default": "más en triángulo"}}}, {"key": "2A3A", "mappings": {"default": {"default": "menos en triángulo"}}}, {"key": "2A3B", "mappings": {"default": {"default": "por en triángulo"}}}, {"key": "2A3C", "mappings": {"default": {"default": "producto interior"}}}, {"key": "2A3D", "mappings": {"default": {"default": "producto interior derecho"}}}, {"key": "2A3E", "mappings": {"default": {"default": "notación Z Composición Relacional"}}}, {"key": "2A3F", "mappings": {"default": {"default": "coproducto"}}}, {"key": "2A40", "mappings": {"default": {"default": "intersección con punto"}}}, {"key": "2A41", "mappings": {"default": {"default": "unión con signo de menos"}}}, {"key": "2A42", "mappings": {"default": {"default": "unión con barra"}}}, {"key": "2A43", "mappings": {"default": {"default": "intersección con barra"}}}, {"key": "2A44", "mappings": {"default": {"default": "intersección con and"}}}, {"key": "2A45", "mappings": {"default": {"default": "unión con or"}}}, {"key": "2A46", "mappings": {"default": {"default": "unión sobre intersección"}}}, {"key": "2A47", "mappings": {"default": {"default": "intersección sobre unión"}}}, {"key": "2A48", "mappings": {"default": {"default": "unión sobre barra, sobre intersección"}}}, {"key": "2A49", "mappings": {"default": {"default": "intersección sobre barra, sobre unión "}}}, {"key": "2A4A", "mappings": {"default": {"default": "unión y unión"}}}, {"key": "2A4B", "mappings": {"default": {"default": "intersección e intersección"}}}, {"key": "2A4C", "mappings": {"default": {"default": "unión cerrada"}}}, {"key": "2A4D", "mappings": {"default": {"default": "intersección cerrada"}}}, {"key": "2A4E", "mappings": {"default": {"default": "intersección cuadrada doble"}}}, {"key": "2A4F", "mappings": {"default": {"default": "unión cuadrada doble"}}}, {"key": "2A50", "mappings": {"default": {"default": "unión cerrada con por"}}}, {"key": "2A51", "mappings": {"default": {"default": "lógico y con punto arriba."}}}, {"key": "2A52", "mappings": {"default": {"default": "lógico o con punto arriba"}}}, {"key": "2A53", "mappings": {"default": {"default": "y"}}}, {"key": "2A54", "mappings": {"default": {"default": "o"}}}, {"key": "2A55", "mappings": {"default": {"default": "two intersecting logical and"}}}, {"key": "2A56", "mappings": {"default": {"default": "two intersecting logical or"}}}, {"key": "2A57", "mappings": {"default": {"default": "sloping large or"}}}, {"key": "2A58", "mappings": {"default": {"default": "sloping large and"}}}, {"key": "2A59", "mappings": {"default": {"default": "lógica o superpuesta, lógica y"}}}, {"key": "2A5A", "mappings": {"default": {"default": "logical and with middle stem"}}}, {"key": "2A5B", "mappings": {"default": {"default": "logical or with middle stem"}}}, {"key": "2A5C", "mappings": {"default": {"default": "logical and with horizontal dash"}}}, {"key": "2A5D", "mappings": {"default": {"default": "logical or with horizontal dash"}}}, {"key": "2A5E", "mappings": {"default": {"default": "lógica y con doble Overbar"}}}, {"key": "2A5F", "mappings": {"default": {"default": "logical and with underbar"}}}, {"key": "2A60", "mappings": {"default": {"default": "lógica y con doble barra inferior."}}}, {"key": "2A61", "mappings": {"default": {"default": "pequeño V<PERSON> con <PERSON>"}}}, {"key": "2A62", "mappings": {"default": {"default": "lógica o con doble barra."}}}, {"key": "2A63", "mappings": {"default": {"default": "lógica o con doble barra inferior"}}}, {"key": "2A64", "mappings": {"default": {"default": "notación Z Dominio Antirestricción"}}}, {"key": "2A65", "mappings": {"default": {"default": "rango de notación Z Antirestricción"}}}, {"key": "2A66", "mappings": {"default": {"default": "igual con punto suscrito"}}}, {"key": "2A67", "mappings": {"default": {"default": "idéntico con punto arriba"}}}, {"key": "2A68", "mappings": {"default": {"default": "triple barra horizontal con doble movimiento vertical"}}}, {"key": "2A69", "mappings": {"default": {"default": "triple barra horizontal con triple movimiento vertical"}}}, {"key": "2A6A", "mappings": {"default": {"default": "tilde con punto"}}}, {"key": "2A6B", "mappings": {"default": {"default": "operador de tilde con puntos crecientes"}}}, {"key": "2A6C", "mappings": {"default": {"default": "similares menos similares"}}}, {"key": "2A6D", "mappings": {"default": {"default": "congruente con punto"}}}, {"key": "2A6E", "mappings": {"default": {"default": "igual a Asterisco"}}}, {"key": "2A6F", "mappings": {"default": {"default": "casi igual con ángulo"}}}, {"key": "2A70", "mappings": {"default": {"default": "aproximadamente igual o igual a"}}}, {"key": "2A71", "mappings": {"default": {"default": "igual con más suscrito"}}}, {"key": "2A72", "mappings": {"default": {"default": "igual con más"}}}, {"key": "2A73", "mappings": {"default": {"default": "igual con operador tilde"}}}, {"key": "2A74", "mappings": {"default": {"default": "igual con dos puntos"}}}, {"key": "2A75", "mappings": {"default": {"default": "igual doble"}}}, {"key": "2A76", "mappings": {"default": {"default": "tres signos de igualdad consecutivos"}}}, {"key": "2A77", "mappings": {"default": {"default": "igual entre cuatro puntos"}}}, {"key": "2A78", "mappings": {"default": {"default": "equivalente con cuatro puntos"}}}, {"key": "2A79", "mappings": {"default": {"default": "menor que con círculo interior"}}}, {"key": "2A7A", "mappings": {"default": {"default": "mayor que con c<PERSON><PERSON><PERSON> interior"}}}, {"key": "2A7B", "mappings": {"default": {"default": "menor que con interrogante"}}}, {"key": "2A7C", "mappings": {"default": {"default": "mayor que con interrogante"}}}, {"key": "2A7D", "mappings": {"default": {"default": "estrictamente menor que"}}}, {"key": "2A7E", "mappings": {"default": {"default": "estrictamente mayor que"}}}, {"key": "2A7F", "mappings": {"default": {"default": "estrictamente menor que con punto interior"}}}, {"key": "2A80", "mappings": {"default": {"default": "estrictamente mayor que con punto interior"}}}, {"key": "2A81", "mappings": {"default": {"default": "estrictamente menor que con punto"}}}, {"key": "2A82", "mappings": {"default": {"default": "estrictamente mayor que con punto"}}}, {"key": "2A83", "mappings": {"default": {"default": "estrictamente menor que con punto"}}}, {"key": "2A84", "mappings": {"default": {"default": "estrictamente mayor que con punto"}}}, {"key": "2A85", "mappings": {"default": {"default": "menor que o aproximado"}}}, {"key": "2A86", "mappings": {"default": {"default": "mayor que o aproximado"}}}, {"key": "2A87", "mappings": {"default": {"default": "menor que una línea no es igual a"}}}, {"key": "2A88", "mappings": {"default": {"default": "mayores y una sola línea no son iguales a"}}}, {"key": "2A89", "mappings": {"default": {"default": "menor que, pero no aproximado a"}}}, {"key": "2A8A", "mappings": {"default": {"default": "mayor que, pero no aproximado a"}}}, {"key": "2A8B", "mappings": {"default": {"default": "menos que arriba de la doble línea igual por encima de mayor que"}}}, {"key": "2A8C", "mappings": {"default": {"default": "más que arriba de la línea doble igual sobre menos que"}}}, {"key": "2A8D", "mappings": {"default": {"default": "menor, similar o igual a"}}}, {"key": "2A8E", "mappings": {"default": {"default": "mayor, similar o igual a"}}}, {"key": "2A8F", "mappings": {"default": {"default": "menor, similar o mayor que"}}}, {"key": "2A90", "mappings": {"default": {"default": "mayor, similar o menor que"}}}, {"key": "2A91", "mappings": {"default": {"default": "menor, mayor o igual a"}}}, {"key": "2A92", "mappings": {"default": {"default": "mayor, menor o igual a"}}}, {"key": "2A93", "mappings": {"default": {"default": "estrictamente menor o mayor que"}}}, {"key": "2A94", "mappings": {"default": {"default": "estrictamente mayor o menor que"}}}, {"key": "2A95", "mappings": {"default": {"default": "inclinado igual o menor que"}}}, {"key": "2A96", "mappings": {"default": {"default": "inclinado igual o mayor que"}}}, {"key": "2A97", "mappings": {"default": {"default": "estrictamente menor que con punto interior"}}}, {"key": "2A98", "mappings": {"default": {"default": "estrictamente mayor que con punto interior"}}}, {"key": "2A99", "mappings": {"default": {"default": "menor o igual a"}}}, {"key": "2A9A", "mappings": {"default": {"default": "mayor o igual a"}}}, {"key": "2A9B", "mappings": {"default": {"default": "doble línea inclinada igual o menor que"}}}, {"key": "2A9C", "mappings": {"default": {"default": "doble línea inclinada igual o mayor que"}}}, {"key": "2A9D", "mappings": {"default": {"default": "equivalente o menor que"}}}, {"key": "2A9E", "mappings": {"default": {"default": "mayor o similar a"}}}, {"key": "2A9F", "mappings": {"default": {"default": "similar, menor o igual a"}}}, {"key": "2AA0", "mappings": {"default": {"default": "similar, mayor o igual a"}}}, {"key": "2AA1", "mappings": {"default": {"default": "mucho menor que"}}}, {"key": "2AA2", "mappings": {"default": {"default": "mucho mayor que"}}}, {"key": "2AA3", "mappings": {"default": {"default": "doble anidada menor que con barra inferior"}}}, {"key": "2AA4", "mappings": {"default": {"default": "menor que, mayor que"}}}, {"key": "2AA5", "mappings": {"default": {"default": "mayor o menor que"}}}, {"key": "2AA6", "mappings": {"default": {"default": "menor que cerrado con curva"}}}, {"key": "2AA7", "mappings": {"default": {"default": "mayor que cerrado con curva"}}}, {"key": "2AA8", "mappings": {"default": {"default": "estrictamente menor que cerrado con curva"}}}, {"key": "2AA9", "mappings": {"default": {"default": "estrictamente mayor que cerrado con curva"}}}, {"key": "2AAA", "mappings": {"default": {"default": "más reducido que"}}}, {"key": "2AAB", "mappings": {"default": {"default": "más amplio que"}}}, {"key": "2AAC", "mappings": {"default": {"default": "más reducido o igual a"}}}, {"key": "2AAD", "mappings": {"default": {"default": "más amplio o igual a"}}}, {"key": "2AAE", "mappings": {"default": {"default": "diferencia entre"}}}, {"key": "2AAF", "mappings": {"default": {"default": "precede o es igual a"}}}, {"key": "2AB0", "mappings": {"default": {"default": "sucede por encima de la línea de igual signo"}}}, {"key": "2AB1", "mappings": {"default": {"default": "precede por encima de una línea no es igual a"}}}, {"key": "2AB2", "mappings": {"default": {"default": "tiene éxito por encima de una línea no es igual a"}}}, {"key": "2AB3", "mappings": {"default": {"default": "precede arriba del signo igual"}}}, {"key": "2AB4", "mappings": {"default": {"default": "sucede por encima del signo igual"}}}, {"key": "2AB5", "mappings": {"default": {"default": "precede estrictamente a"}}}, {"key": "2AB6", "mappings": {"default": {"default": "sigue estrictamente a"}}}, {"key": "2AB7", "mappings": {"default": {"default": "precede por encima de casi igual a"}}}, {"key": "2AB8", "mappings": {"default": {"default": "tiene éxito por encima de casi igual a"}}}, {"key": "2AB9", "mappings": {"default": {"default": "precede arriba no es igual a"}}}, {"key": "2ABA", "mappings": {"default": {"default": "tiene éxito por encima de no casi igual a"}}}, {"key": "2ABB", "mappings": {"default": {"default": "precede mucho antes"}}}, {"key": "2ABC", "mappings": {"default": {"default": "sigue mucho después"}}}, {"key": "2ABD", "mappings": {"default": {"default": "subrelación de"}}}, {"key": "2ABE", "mappings": {"default": {"default": "contiene como subrelación a"}}}, {"key": "2ABF", "mappings": {"default": {"default": "incluido con más suscrito"}}}, {"key": "2AC0", "mappings": {"default": {"default": "contiene con más suscrito"}}}, {"key": "2AC1", "mappings": {"default": {"default": "incluido con por suscrito"}}}, {"key": "2AC2", "mappings": {"default": {"default": "contiene con por suscrito"}}}, {"key": "2AC3", "mappings": {"default": {"default": "incluido con punto o es igual a"}}}, {"key": "2AC4", "mappings": {"default": {"default": "contiene con punto o es igual a"}}}, {"key": "2AC5", "mappings": {"default": {"default": "subconjunto de signo igual arriba"}}}, {"key": "2AC6", "mappings": {"default": {"default": "superconjunto de signo igual arriba"}}}, {"key": "2AC7", "mappings": {"default": {"default": "incluido aproximadamente en"}}}, {"key": "2AC8", "mappings": {"default": {"default": "contiene aproximadamente a"}}}, {"key": "2AC9", "mappings": {"default": {"default": "subconjunto de Arriba Casi Igual a"}}}, {"key": "2ACA", "mappings": {"default": {"default": "superconjunto de arriba casi igual a"}}}, {"key": "2ACB", "mappings": {"default": {"default": "subconjunto de lo anterior no es igual a"}}}, {"key": "2ACC", "mappings": {"default": {"default": "superconjunto de arriba no es igual a"}}}, {"key": "2ACD", "mappings": {"default": {"default": "operador de caja abierta a la izquierda cuadrada"}}}, {"key": "2ACE", "mappings": {"default": {"default": "operador de caja abierta a la derecha cuadrada"}}}, {"key": "2ACF", "mappings": {"default": {"default": "incluido con cierre en"}}}, {"key": "2AD0", "mappings": {"default": {"default": "contiene al cierre del subconjunto"}}}, {"key": "2AD1", "mappings": {"default": {"default": "incluido el cierre o es igual a"}}}, {"key": "2AD2", "mappings": {"default": {"default": "contiene al cierre del subconjunto o es igual a"}}}, {"key": "2AD3", "mappings": {"default": {"default": "está incluido o contiene a"}}}, {"key": "2AD4", "mappings": {"default": {"default": "contiene o está incluido en"}}}, {"key": "2AD5", "mappings": {"default": {"default": "incluido doblemente en"}}}, {"key": "2AD6", "mappings": {"default": {"default": "contiene doblemente a"}}}, {"key": "2AD7", "mappings": {"default": {"default": "contiene o está incluido en"}}}, {"key": "2AD8", "mappings": {"default": {"default": "contiene o está incluido en"}}}, {"key": "2AD9", "mappings": {"default": {"default": "perteneciente dirigido hacia abajo"}}}, {"key": "2ADA", "mappings": {"default": {"default": "pitchfork with tee top"}}}, {"key": "2ADB", "mappings": {"default": {"default": "transversal intersection"}}}, {"key": "2ADC", "mappings": {"default": {"default": "bifurcación"}}}, {"key": "2ADD", "mappings": {"default": {"default": "nonforking"}}}, {"key": "2ADE", "mappings": {"default": {"default": "tachu<PERSON> Izquierda Corta"}}}, {"key": "2ADF", "mappings": {"default": {"default": "tachuela corta hacia abajo"}}}, {"key": "2AE0", "mappings": {"default": {"default": "short Up Tack"}}}, {"key": "2AE1", "mappings": {"default": {"default": "perpendicular con s"}}}, {"key": "2AE2", "mappings": {"default": {"default": "barra vertical Torniquete a la derecha"}}}, {"key": "2AE3", "mappings": {"default": {"default": "barra vertical doble Tor<PERSON><PERSON>e i<PERSON>o"}}}, {"key": "2AE4", "mappings": {"default": {"default": "double left turnstile vertical bar"}}}, {"key": "2AE5", "mappings": {"default": {"default": "doble barra vertical doble torniquete iz<PERSON>erdo"}}}, {"key": "2AE6", "mappings": {"default": {"default": "long dash from left member of double vertical"}}}, {"key": "2AE7", "mappings": {"default": {"default": "short down tack with overbar"}}}, {"key": "2AE8", "mappings": {"default": {"default": "barra con <PERSON>"}}}, {"key": "2AE9", "mappings": {"default": {"default": "short up tack above short down tack"}}}, {"key": "2AEA", "mappings": {"default": {"default": "tachuela doble"}}}, {"key": "2AEB", "mappings": {"default": {"default": "double up tack"}}}, {"key": "2AEC", "mappings": {"default": {"default": "double stroke not sign"}}}, {"key": "2AED", "mappings": {"default": {"default": "reversed double stroke not sign"}}}, {"key": "2AEE", "mappings": {"default": {"default": "does not divide with reversed negation slash"}}}, {"key": "2AEF", "mappings": {"default": {"default": "barra vertical con círculo"}}}, {"key": "2AF0", "mappings": {"default": {"default": "barra con círculo inferior"}}}, {"key": "2AF1", "mappings": {"default": {"default": "down tack with circle below"}}}, {"key": "2AF2", "mappings": {"default": {"default": "paralela con marca"}}}, {"key": "2AF3", "mappings": {"default": {"default": "parale<PERSON> con tilde"}}}, {"key": "2AF4", "mappings": {"default": {"default": "relación binaria de barra vertical triple"}}}, {"key": "2AF5", "mappings": {"default": {"default": "barra vertical triple con trazo horizontal"}}}, {"key": "2AF6", "mappings": {"default": {"default": "operador <PERSON>"}}}, {"key": "2AF7", "mappings": {"default": {"default": "triple anidada menos que"}}}, {"key": "2AF8", "mappings": {"default": {"default": "triple anidado mayor que"}}}, {"key": "2AF9", "mappings": {"default": {"default": "doble línea inclinada menor o igual a"}}}, {"key": "2AFA", "mappings": {"default": {"default": "doble línea inclinada mayor o igual a"}}}, {"key": "2AFB", "mappings": {"default": {"default": "relación Binaria Triple Sólida"}}}, {"key": "2AFC", "mappings": {"default": {"default": "operador de barra vertical triple grande"}}}, {"key": "2AFD", "mappings": {"default": {"default": "operador <PERSON><PERSON>"}}}, {"key": "2AFE", "mappings": {"default": {"default": "barra vertical blanca"}}}, {"key": "2AFF", "mappings": {"default": {"default": "barra vertical blanca N-Ary"}}}, {"key": "301C", "mappings": {"default": {"default": "wave Dash"}}}, {"key": "FE10", "mappings": {"default": {"default": "formulario de presentación para coma vertical"}}}, {"key": "FE13", "mappings": {"default": {"default": "formulario de presentación para colon vertical"}}}, {"key": "FE14", "mappings": {"default": {"default": "formulario de presentación para punto y coma vertical"}}}, {"key": "FE15", "mappings": {"default": {"default": "formulario de presentación para el signo de exclamación vertical"}}}, {"key": "FE16", "mappings": {"default": {"default": "formulario de presentación para el signo de interrogación vertical"}}}, {"key": "FE19", "mappings": {"default": {"default": "formulario de presentación para elipsis horizontal vertical"}}}, {"key": "FE30", "mappings": {"default": {"default": "formulario de presentación para líder vertical de dos puntos"}}}, {"key": "FE31", "mappings": {"default": {"default": "formulario de presentación para Vertical Em Dash"}}}, {"key": "FE32", "mappings": {"default": {"default": "formulario de presentación para Vertical En Dash"}}}, {"key": "FE33", "mappings": {"default": {"default": "formulario de presentación para línea vertical baja"}}}, {"key": "FE34", "mappings": {"default": {"default": "formulario de presentación para línea baja ondulada vertical"}}}, {"key": "FE45", "mappings": {"default": {"default": "punto de sésamo"}}}, {"key": "FE46", "mappings": {"default": {"default": "punto de sésamo blanco"}}}, {"key": "FE49", "mappings": {"default": {"default": "línea discontinua"}}}, {"key": "FE4A", "mappings": {"default": {"default": "centinela Overline"}}}, {"key": "FE4B", "mappings": {"default": {"default": "ondulado Overline"}}}, {"key": "FE4C", "mappings": {"default": {"default": "doble <PERSON><PERSON><PERSON><PERSON>"}}}, {"key": "FE4D", "mappings": {"default": {"default": "línea baja discontinua"}}}, {"key": "FE4E", "mappings": {"default": {"default": "línea baja de línea central"}}}, {"key": "FE4F", "mappings": {"default": {"default": "línea baja ondulada"}}}, {"key": "FE50", "mappings": {"default": {"default": "comas pequeñas"}}}, {"key": "FE52", "mappings": {"default": {"default": "parada completa pequeña"}}}, {"key": "FE54", "mappings": {"default": {"default": "pequeño punto y coma"}}}, {"key": "FE55", "mappings": {"default": {"default": "pequeño colon"}}}, {"key": "FE56", "mappings": {"default": {"default": "pequeño signo de interrogación"}}}, {"key": "FE57", "mappings": {"default": {"default": "pequeño signo de exclamación"}}}, {"key": "FE58", "mappings": {"default": {"default": "pequeño Em Dash"}}}, {"key": "FE5F", "mappings": {"default": {"default": "signo de número pequeño"}}}, {"key": "FE60", "mappings": {"default": {"default": "ampersand pequeño"}}}, {"key": "FE61", "mappings": {"default": {"default": "pequeño asterisco"}}}, {"key": "FE62", "mappings": {"default": {"default": "signo más pequeño"}}}, {"key": "FE63", "mappings": {"default": {"default": "pequeño guión-menos"}}}, {"key": "FE64", "mappings": {"default": {"default": "signo menor que pequeño"}}}, {"key": "FE65", "mappings": {"default": {"default": "signo mayor que pequeño"}}}, {"key": "FE66", "mappings": {"default": {"default": "signo pequeño igual"}}}, {"key": "FE68", "mappings": {"default": {"default": "división entera"}}}, {"key": "FE69", "mappings": {"default": {"default": "pequeño signo de d<PERSON>"}}}, {"key": "FE6A", "mappings": {"default": {"default": "signo de porcentaje pequeño"}}}, {"key": "FE6B", "mappings": {"default": {"default": "pequeño comercial en"}}}, {"key": "FF01", "mappings": {"default": {"default": "signo de exclamación de ancho completo"}}}, {"key": "FF02", "mappings": {"default": {"default": "comillas de ancho completo"}}}, {"key": "FF03", "mappings": {"default": {"default": "número de signo de ancho completo"}}}, {"key": "FF04", "mappings": {"default": {"default": "signo de d<PERSON><PERSON> de ancho completo"}}}, {"key": "FF05", "mappings": {"default": {"default": "signo de porcentaje de ancho completo"}}}, {"key": "FF06", "mappings": {"default": {"default": "ampersand de ancho completo"}}}, {"key": "FF07", "mappings": {"default": {"default": "apóstrofe de ancho completo"}}}, {"key": "FF0A", "mappings": {"default": {"default": "asterisco de ancho completo"}}}, {"key": "FF0B", "mappings": {"default": {"default": "signo de ancho completo"}}}, {"key": "FF0C", "mappings": {"default": {"default": "coma de ancho completo"}}}, {"key": "FF0D", "mappings": {"default": {"default": "ancho de ancho completo"}}}, {"key": "FF0E", "mappings": {"default": {"default": "fullwidth Full Stop"}}}, {"key": "FF0F", "mappings": {"default": {"default": "s<PERSON><PERSON><PERSON> de ancho completo"}}}, {"key": "FF1A", "mappings": {"default": {"default": "ancho completo de colon"}}}, {"key": "FF1B", "mappings": {"default": {"default": "punto y coma de ancho completo"}}}, {"key": "FF1C", "mappings": {"default": {"default": "ancho completo menos de la señal"}}}, {"key": "FF1D", "mappings": {"default": {"default": "signo de igual ancho completo"}}}, {"key": "FF1E", "mappings": {"default": {"default": "ancho completo mayor que el signo"}}}, {"key": "FF1F", "mappings": {"default": {"default": "signo de interrogación de ancho completo"}}}, {"key": "FF20", "mappings": {"default": {"default": "comercial de ancho completo en"}}}, {"key": "FF3C", "mappings": {"default": {"default": "s<PERSON>lido inverso de ancho completo"}}}, {"key": "FF3E", "mappings": {"default": {"default": "acento circunflejo de ancho completo"}}}, {"key": "FF3F", "mappings": {"default": {"default": "línea baja de ancho completo"}}}, {"key": "FF40", "mappings": {"default": {"default": "acento grave de ancho completo"}}}, {"key": "FF5C", "mappings": {"default": {"default": "barra vertical"}}}, {"key": "FF5E", "mappings": {"default": {"default": "tilde de ancho completo"}}}, {"key": "FFE0", "mappings": {"default": {"default": "signo de centavo de ancho completo"}}}, {"key": "FFE1", "mappings": {"default": {"default": "signo de libra de ancho completo"}}}, {"key": "FFE2", "mappings": {"default": {"default": "fullwidth no firmar"}}}, {"key": "FFE3", "mappings": {"default": {"default": "ancho completo macron"}}}, {"key": "FFE4", "mappings": {"default": {"default": "barra rota de ancho completo"}}}, {"key": "FFE5", "mappings": {"default": {"default": "signo de yen de ancho completo"}}}, {"key": "FFE6", "mappings": {"default": {"default": "signo de ancho completo ganado"}}}, {"key": "FFE8", "mappings": {"default": {"default": "formas de ancho medio luz vertical"}}}, {"key": "FFED", "mappings": {"default": {"default": "medio ancho cuadrado negro"}}}, {"key": "FFEE", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> blanco de medio ancho"}}}], "es/symbols/math_whitespace.min": [{"locale": "es"}, {"key": "0020", "mappings": {"default": {"default": "espacio"}}}, {"key": "00A0", "mappings": {"default": {"default": " "}}}, {"key": "00AD", "mappings": {"default": {"default": "gui<PERSON> corto"}}}, {"key": "2000", "mappings": {"default": {"default": "en quad"}}}, {"key": "2001", "mappings": {"default": {"default": "em Quad"}}}, {"key": "2002", "mappings": {"default": {"default": ""}}}, {"key": "2003", "mappings": {"default": {"default": ""}}}, {"key": "2004", "mappings": {"default": {"default": ""}}}, {"key": "2005", "mappings": {"default": {"default": ""}}}, {"key": "2006", "mappings": {"default": {"default": "espacio Six-Per-Em"}}}, {"key": "2007", "mappings": {"default": {"default": ""}}}, {"key": "2008", "mappings": {"default": {"default": ""}}}, {"key": "2009", "mappings": {"default": {"default": ""}}}, {"key": "200A", "mappings": {"default": {"default": ""}}}, {"key": "200B", "mappings": {"default": {"default": ""}}}, {"key": "200C", "mappings": {"default": {"default": "ancho de cero no ensamblador"}}}, {"key": "200D", "mappings": {"default": {"default": "ancho de cero ensamblador"}}}, {"key": "200E", "mappings": {"default": {"default": "marca de izquierda a derecha"}}}, {"key": "200F", "mappings": {"default": {"default": "marca de derecha a izquierda"}}}, {"key": "2028", "mappings": {"default": {"default": "separador de linea"}}}, {"key": "2029", "mappings": {"default": {"default": "separador de párrafo"}}}, {"key": "202A", "mappings": {"default": {"default": "incrustación de izquierda a derecha"}}}, {"key": "202B", "mappings": {"default": {"default": "incrustación de derecha a izquierda"}}}, {"key": "202C", "mappings": {"default": {"default": "formato direccional pop"}}}, {"key": "202D", "mappings": {"default": {"default": "anulación de izquierda a derecha"}}}, {"key": "202E", "mappings": {"default": {"default": "anulación de derecha a izquierda"}}}, {"key": "202F", "mappings": {"default": {"default": "espacio estrecho sin descanso"}}}, {"key": "205F", "mappings": {"default": {"default": ""}}}, {"key": "2060", "mappings": {"default": {"default": ""}}}, {"key": "2061", "mappings": {"default": {"default": "de"}}}, {"key": "2062", "mappings": {"default": {"default": "por"}}}, {"key": "2063", "mappings": {"default": {"default": "coma"}}}, {"key": "2064", "mappings": {"default": {"default": "más"}}}, {"key": "206A", "mappings": {"default": {"default": "inhibir el intercambio simétrico"}}}, {"key": "206B", "mappings": {"default": {"default": "activar el intercambio simétrico"}}}, {"key": "206E", "mappings": {"default": {"default": "formas de dígitos nacionales"}}}, {"key": "206F", "mappings": {"default": {"default": "formas de dígitos nominales"}}}, {"key": "FEFF", "mappings": {"default": {"default": ""}}}, {"key": "FFF9", "mappings": {"default": {"default": "ancla de anotación interlinear"}}}, {"key": "FFFA", "mappings": {"default": {"default": "separador de anotación interlinear"}}}, {"key": "FFFB", "mappings": {"default": {"default": "terminador de anotación interlinear"}}}], "es/symbols/other_stars.min": [{"locale": "es"}, {"key": "23E8", "mappings": {"default": {"default": "símbolo exponente decimal"}}}, {"key": "2605", "mappings": {"default": {"default": "estrella rellena"}}}, {"key": "2606", "mappings": {"default": {"default": "estrella vacía"}}}, {"key": "26AA", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> blanco medio"}}}, {"key": "26AB", "mappings": {"default": {"default": "círculo negro medio"}}}, {"key": "2705", "mappings": {"default": {"default": "marca de verificación pesada blanca"}}}, {"key": "2713", "mappings": {"default": {"default": "check mark"}}}, {"key": "2714", "mappings": {"default": {"default": "marca de verificación pesada"}}}, {"key": "2715", "mappings": {"default": {"default": "multiplicación x"}}}, {"key": "2716", "mappings": {"default": {"default": "multiplicación pesada X"}}}, {"key": "2717", "mappings": {"default": {"default": "ballot x"}}}, {"key": "2718", "mappings": {"default": {"default": "boleta pesada X"}}}, {"key": "271B", "mappings": {"default": {"default": "centro abierto de la cruz"}}}, {"key": "271C", "mappings": {"default": {"default": "pesado centro abierto cruz"}}}, {"key": "2720", "mappings": {"default": {"default": "cruz de Malta"}}}, {"key": "2721", "mappings": {"default": {"default": "estrella de David"}}}, {"key": "2722", "mappings": {"default": {"default": "cuatro asterisco de lágrima"}}}, {"key": "2723", "mappings": {"default": {"default": "cuatro asterisco de globo"}}}, {"key": "2724", "mappings": {"default": {"default": "pesado Cuatro asterisco impulsado por globo"}}}, {"key": "2725", "mappings": {"default": {"default": "cuatro asterisco de club-spoked"}}}, {"key": "2726", "mappings": {"default": {"default": "estrella de cuatro puntas negra"}}}, {"key": "2727", "mappings": {"default": {"default": "estrella blanca de cuatro puntas"}}}, {"key": "2728", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "2729", "mappings": {"default": {"default": "estrés subrayado estrella blanca"}}}, {"key": "272A", "mappings": {"default": {"default": "estrella blanca circulada"}}}, {"key": "272B", "mappings": {"default": {"default": "centro Abierto Estrella Negra"}}}, {"key": "272C", "mappings": {"default": {"default": "centro negro estrella blanca"}}}, {"key": "272D", "mappings": {"default": {"default": "contorneado negro estrella"}}}, {"key": "272E", "mappings": {"default": {"default": "estrella negra contorneada pesada"}}}, {"key": "272F", "mappings": {"default": {"default": "pinwheel Star"}}}, {"key": "2730", "mappings": {"default": {"default": "estrella blanca sombreada"}}}, {"key": "2731", "mappings": {"default": {"default": "asterisco pesado"}}}, {"key": "2732", "mappings": {"default": {"default": "centro abierto de asterisco"}}}, {"key": "2733", "mappings": {"default": {"default": "ocho asterisco hablado"}}}, {"key": "2734", "mappings": {"default": {"default": "ocho estrellas negras punt<PERSON>"}}}, {"key": "2735", "mappings": {"default": {"default": "ocho estrellas de molinete en punta"}}}, {"key": "2736", "mappings": {"default": {"default": "estrella rellena de seis puntas"}}}, {"key": "2739", "mappings": {"default": {"default": "doce estrellas negras punt<PERSON>"}}}, {"key": "273A", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON><PERSON> asteriscos en punta"}}}, {"key": "273B", "mappings": {"default": {"default": "asterisco con efecto de lágrima"}}}, {"key": "273C", "mappings": {"default": {"default": "centro abierto con asterisco con forma de lágrima"}}}, {"key": "273D", "mappings": {"default": {"default": "heavy teardrop spoked asterisk"}}}, {"key": "273E", "mappings": {"default": {"default": "seis pétalos de florette en blanco y negro"}}}, {"key": "273F", "mappings": {"default": {"default": "florette negro"}}}, {"key": "2740", "mappings": {"default": {"default": "florette blanco"}}}, {"key": "2741", "mappings": {"default": {"default": "<PERSON><PERSON> Outlined Black Florette"}}}, {"key": "2742", "mappings": {"default": {"default": "círculo abierto centro ocho estrellas señaladas"}}}, {"key": "2743", "mappings": {"default": {"default": "asterisco pesado del molinillo de viento"}}}, {"key": "2744", "mappings": {"default": {"default": "copo de nieve"}}}, {"key": "2745", "mappings": {"default": {"default": "copo de nieve trifolio apretado"}}}, {"key": "2746", "mappings": {"default": {"default": "copo de nieve pesado de Chevron"}}}, {"key": "2747", "mappings": {"default": {"default": "brillar"}}}, {"key": "2748", "mappings": {"default": {"default": "chispa pesada"}}}, {"key": "2749", "mappings": {"default": {"default": "asterisco de globo"}}}, {"key": "274A", "mappings": {"default": {"default": "ocho asterisco de hélice con efecto de lágrima"}}}, {"key": "274B", "mappings": {"default": {"default": "heavy eight asterisk de hélice en forma de lágrima"}}}, {"key": "274C", "mappings": {"default": {"default": "marca de la cruz"}}}, {"key": "274D", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON> blanco sombreado"}}}], "es/units/area.min": [{"locale": "es"}, {"key": "sq", "mappings": {"default": {"default": "cuadrado"}}}, {"key": "sq inch", "mappings": {"default": {"default": "pulgada cuadrada", "plural": "pulgadas cuadrada"}}}, {"key": "sq rd", "mappings": {"default": {"default": "rod cuadrado", "plural": "rods cuadrado"}}}, {"key": "sq ft", "mappings": {"default": {"default": "pie cuadrado", "plural": "pies cuadrado"}}}, {"key": "sq yd", "mappings": {"default": {"default": "yarda cuadrada", "plural": "yardas cuadrada"}}}, {"key": "sq mi", "mappings": {"default": {"default": "milla cuadrada"}}}, {"key": "acr", "mappings": {"default": {"default": "acre"}}}, {"key": "ha", "mappings": {"default": {"default": "hectarea"}}}], "es/units/currency.min": [{"locale": "es"}, {"key": "$", "mappings": {"default": {"default": "<PERSON><PERSON><PERSON>"}}}, {"key": "£", "mappings": {"default": {"default": "penique"}}}, {"key": "¥", "mappings": {"default": {"default": "yen"}}}, {"key": "€", "mappings": {"default": {"default": "euro"}}}, {"key": "₡", "mappings": {"default": {"default": "colón"}}}, {"key": "₢", "mappings": {"default": {"default": "c<PERSON><PERSON><PERSON>"}}}, {"key": "₣", "mappings": {"default": {"default": "franco"}}}, {"key": "₤", "mappings": {"default": {"default": "lira"}}}, {"key": "₥", "mappings": {"default": {"default": "mills"}}}, {"key": "₦", "mappings": {"default": {"default": "naira"}}}, {"key": "₧", "mappings": {"default": {"default": "peseta"}}}, {"key": "₨", "mappings": {"default": {"default": "rupia"}}}, {"key": "₩", "mappings": {"default": {"default": "won"}}}, {"key": "₪", "mappings": {"default": {"default": "siclo"}}}, {"key": "₫", "mappings": {"default": {"default": "dong"}}}, {"key": "₭", "mappings": {"default": {"default": "kip"}}}, {"key": "₮", "mappings": {"default": {"default": "<PERSON><PERSON>"}}}, {"key": "₯", "mappings": {"default": {"plural": "dracmas", "default": "dracma"}}}, {"key": "₰", "mappings": {"default": {"plural": "peniques alemanes", "default": "penique alemán"}}}, {"key": "₱", "mappings": {"default": {"default": "peso"}}}, {"key": "₲", "mappings": {"default": {"default": "guaraní", "plural": "guaran<PERSON><PERSON>"}}}, {"key": "₳", "mappings": {"default": {"default": "austral"}}}, {"key": "₴", "mappings": {"default": {"default": "hryvnias"}}}, {"key": "₵", "mappings": {"default": {"default": "cedis"}}}, {"key": "₸", "mappings": {"default": {"default": "tenge", "plural": "tenge"}}}, {"key": "₺", "mappings": {"default": {"default": "lira turca", "plural": "liras turca"}}}, {"key": "元", "mappings": {"default": {"default": "yuan"}}}, {"key": "¢", "mappings": {"default": {"default": "centésimo"}}}], "es/units/energy.min": [{"locale": "es"}, {"key": "W", "mappings": {"default": {"default": "vatio"}}}, {"key": "kwh", "mappings": {"default": {"default": "kilovatio hora"}}}, {"key": "J", "mappings": {"default": {"default": "Joule"}}}, {"key": "N", "mappings": {"default": {"default": "<PERSON>"}}}, {"key": "A", "mappings": {"default": {"default": "Ampere"}}}, {"key": "V", "mappings": {"default": {"default": "Voltio"}}}, {"key": "ohm", "mappings": {"default": {"default": "ohmio"}}}, {"key": "Ω", "mappings": {"default": {"default": "ohmio"}}}], "es/units/length.min": [{"locale": "es"}, {"key": "m", "mappings": {"default": {"default": "metro"}}}, {"key": "ft", "mappings": {"default": {"default": "pies"}}}, {"key": "in", "mappings": {"default": {"default": "pulgada"}}}, {"key": "mi", "mappings": {"default": {"default": "milla"}}}, {"key": "yd", "mappings": {"default": {"default": "yarda"}}}, {"key": "link", "mappings": {"default": {"default": "link"}}}, {"key": "rod", "mappings": {"default": {"default": "rod"}}}, {"key": "chain", "mappings": {"default": {"default": "cadena"}}}, {"key": "furlong", "mappings": {"default": {"default": "furlong"}}}, {"key": "n.m.", "mappings": {"default": {"default": "milla náutica", "plural": "millas náuticas"}}}], "es/units/memory.min": [{"locale": "es"}, {"key": "b", "mappings": {"default": {"default": "bit"}}}, {"key": "B", "mappings": {"default": {"default": "byte"}}}, {"key": "KB", "mappings": {"default": {"default": "kilobyte"}}}], "es/units/other.min": [{"locale": "es"}, {"key": "doz", "mappings": {"default": {"default": "docena"}}}], "es/units/speed.min": [{"locale": "es"}, {"key": "kt", "mappings": {"default": {"default": "nudo"}}}, {"key": "mph", "mappings": {"default": {"default": "milla por hora", "plural": "millas por hora"}}}, {"key": "rpm", "mappings": {"default": {"default": "revoluciones por minuto"}}}, {"key": "kmh", "mappings": {"default": {"plural": "kilómetros por hora", "default": "kilómetro por hora"}}}], "es/units/temperature.min": [{"locale": "es"}, {"key": "F", "mappings": {"default": {"default": "grado Fahrenheit", "plural": "grados Fahrenheit"}}}, {"key": "C", "mappings": {"default": {"default": "centígrado"}}}, {"key": "K", "mappings": {"default": {"default": "<PERSON><PERSON>", "plural": "<PERSON><PERSON>"}}}], "es/units/time.min": [{"locale": "es"}, {"key": "s", "mappings": {"default": {"default": "segundo"}}}, {"key": "″", "mappings": {"default": {"default": "segundo"}}}, {"key": "min", "mappings": {"default": {"default": "minuto"}}}, {"key": "°", "mappings": {"default": {"default": "grado"}}}, {"key": "h", "mappings": {"default": {"default": "hora"}}}], "es/units/volume.min": [{"locale": "es"}, {"key": "bbl", "mappings": {"default": {"default": "barrile"}}}, {"key": "cu", "mappings": {"default": {"default": "cú<PERSON><PERSON>"}}}, {"key": "fl. oz.", "mappings": {"default": {"default": "onza líquida", "plural": "onzas líquidas"}}}, {"key": "gal", "mappings": {"default": {"default": "galone"}}}, {"key": "pt", "mappings": {"default": {"default": "pinta"}}}, {"key": "qt", "mappings": {"default": {"default": "quart"}}}, {"key": "tbsp", "mappings": {"default": {"default": "cu<PERSON>ra"}}}, {"key": "tsp", "mappings": {"default": {"default": "cu<PERSON><PERSON>"}}}, {"key": "cc", "mappings": {"default": {"default": "centímetro cúbico", "plural": "centímetros cú<PERSON>os"}}}, {"key": "l", "mappings": {"default": {"default": "litro"}}}, {"key": "cu inch", "mappings": {"default": {"default": "pulgada cú<PERSON>a", "plural": "pulga<PERSON> c<PERSON>"}}}, {"key": "cu ft", "mappings": {"default": {"default": "pie cúbico", "plural": "pies cúbicos"}}}, {"key": "cu yd", "mappings": {"default": {"default": "<PERSON>a c<PERSON>", "plural": "yardas cúbicas"}}}, {"key": "fluid dram", "mappings": {"default": {"default": "dracma líquida", "plural": "dracma lí<PERSON>"}}}, {"key": "cup", "mappings": {"default": {"default": "taza"}}}], "es/units/weight.min": [{"locale": "es"}, {"key": "lb", "mappings": {"default": {"default": "libra"}}}, {"key": "LT", "mappings": {"default": {"default": "libra imperiale", "plural": "libras imperiales"}}}, {"key": "oz", "mappings": {"default": {"default": "onza"}}}, {"key": "mcg", "mappings": {"default": {"default": "microgramo"}}}, {"key": "gr", "mappings": {"default": {"default": "gramo"}}}, {"key": "g", "mappings": {"default": {"default": "gramo"}}}, {"key": "t", "mappings": {"default": {"default": "<PERSON>lada"}}}, {"key": "dram", "mappings": {"default": {"default": "dracma"}}}, {"key": "st", "mappings": {"default": {"default": "stone", "plural": "stones"}}}, {"key": "qtr", "mappings": {"default": {"default": "cuarto corto"}}}, {"key": "cwt", "mappings": {"default": {"default": "centena"}}}], "es/rules/mathspeak_spanish.min": {"locale": "es", "domain": "mathspeak", "modality": "speech", "inherits": "base", "rules": [["Ignore", "text"], ["Ignore", "vulgar-fraction"], ["Ignore", "subscript-simple"], ["Ignore", "prime-subscript-simple"], ["Ignore", "fraction-sbrief"], ["SpecializedRule", "fraction-brief", "brief", "sbrief"], ["Ignore", "unit"], ["Ignore", "unit-combine"], ["Precondition", "unit-singular", "default", "self::identifier[@role=\"unit\"]"], ["Precondition", "unit-plural", "default", "self::identifier[@role=\"unit\"]", "not(contains(@grammar, \"singular\"))"], ["Precondition", "reciprocal", "default", "self::superscript[@role=\"unit\"]", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "count(preceding-sibling::*)=0 or preceding-sibling::*[@role!=\"unit\"]"], ["Precondition", "per", "default", "self::superscript[@role=\"unit\"]", "name(children/*[1])=\"identifier\"", "name(children/*[2])=\"prefixop\"", "children/*[2][@role=\"negative\"]", "children/*[2]/children/*[1][text()=1]", "preceding-sibling::*[@role=\"unit\"]"], ["Precondition", "unit-combine-mult", "default", "self::infixop", "@role=\"multiplication\" or @role=\"implicit\"", "children/*[@role=\"unit\"]"], ["Precondition", "unit-combine-singular", "default", "self::infixop[@role=\"unit\"]", "name(children/*[1])!=\"number\"", "contains(@grammar, \"singular\")", "count(children/*)>1"], ["Precondition", "unit-combine-singular-first", "default", "self::infixop[@role=\"unit\"]", "name(children/*[1])=\"number\"", "children/*[1][text()=1]"], ["Precondition", "unit-combine-singular-final", "default", "self::infixop[@role=\"unit\"]", "name(children/*[1])=\"number\"", "children/*[1][text()=1]", "count(children/*)=2"], ["Precondition", "unit-divide", "default", "self::fraction[@role=\"unit\"]"]]}, "es/rules/mathspeak_spanish_actions.min": {"locale": "es", "domain": "mathspeak", "modality": "speech", "kind": "actions", "rules": [["Action", "collapsed", "[n] . (engine:modality=summary, grammar:?collapsed); [t] \"plegado\""], ["Action", "blank-cell-empty", "[t] \"espacio\""], ["Action", "blank-line-empty", "[t] \"espacio\""], ["Action", "font", "[t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "mixed-number", "[n] children/*[1]; [t] \"más\"; [n] children/*[2]"], ["Action", "number-with-chars", "[t] \"número\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-with-chars-brief", "[t] \"núm\"; [m] CQFspaceoutNumber (grammar:protected)"], ["Action", "number-as-upper-word", "[t] \"mayúscula\"; [t] CSFspaceoutText"], ["Action", "number-baseline", "[t] \"línea base\"; [n] . (grammar:baseline)"], ["Action", "number-baseline-brief", "[t] \"base\"; [n] text()"], ["Action", "number-baseline-font", "[t] \"línea base\"; [t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "number-baseline-font-brief", "[t] \"base\"; [t] @font (grammar:localFont); [n] . (grammar:ignoreFont=@font)"], ["Action", "negative-number", "[t] \"menos\"; [n] children/*[1]"], ["Action", "negative", "[t] \"menos\"; [n] children/*[1]"], ["Action", "division", "[n] children/*[1]; [t] \"dividido\"; [n] children/*[2]"], ["Action", "subtraction", "[m] children/* (separator:\"menos\")"], ["Action", "fences-neutral", "[t] \"empezar valor absoluto\"; [n] children/*[1]; [t] \"finalizar valor absoluto\""], ["Action", "fences-neutral-sbrief", "[t] \"valor absoluto\"; [n] children/*[1]; [t] \"finalizar valor absoluto\""], ["Action", "fences-metric", "[t] \"empezar métrica\"; [n] children/*[1]; [t] \"finalizar métrica\""], ["Action", "fences-metric-sbrief", "[t] \"métrica\"; [n] children/*[1]; [t] \"finalizar métrica\""], ["Action", "empty-set", "[t] \"conjunto vacío\""], ["Action", "fences-set", "[t] \"empezar llave\"; [n] children/*[1]; [t] \"finalizar llave\""], ["Action", "fences-set-sbrief", "[t] \"llave\"; [n] children/*[1]; [t] \"finalizar llave\""], ["Action", "text", "[n] text() (grammar:noTranslateText)"], ["Action", "factorial", "[t] \"factorial\""], ["Action", "minus", "[t] \"menos\""], ["Action", "continued-fraction-outer", "[t] \"fracción continua\"; [n] children/*[1]; [t] \"entre\"; [n] children/*[2]"], ["Action", "continued-fraction-outer-brief", "[t] \"frac continua\"; [n] children/*[1]; [t] \"entre\"; [n] children/*[2]"], ["Action", "continued-fraction-inner", "[t] \"empezar fracción\"; [n] children/*[1]; [t] \"entre\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-brief", "[t] \"empezar frac\"; [n] children/*[1]; [t] \"entre\"; [n] children/*[2]"], ["Action", "continued-fraction-inner-sbrief", "[t] \"frac\"; [n] children/*[1]; [t] \"entre\"; [n] children/*[2]"], ["Action", "integral", "[n] children/*[1]; [t] \"definida\"; [t] \"subíndice\"; [n] children/*[2]; [t] \"superíndice\"; [n] children/*[3]; [t] \"línea base\""], ["Action", "integral-brief", "[n] children/*[1]; [t] \"Sub\"; [n] children/*[2]; [t] \"Sup\"; [n] children/*[3]; [t] \"Base\""], ["Action", "square", "[n] children/*[1]; [t] \"al cuadrado\""], ["Action", "cube", "[n] children/*[1]; [t] \"al cubo\""], ["Action", "prime", "[n] children/*[1]; [n] children/*[2]"], ["Action", "counted-prime", "[t] count(children/*) (grammar:numbers2alpha); [t] \"prima\""], ["Action", "counted-prime-multichar", "[t] string-length(text()) (grammar:numbers2alpha); [t] \"prima\""], ["Action", "overscore", "[t] \"modificando superior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "overscore-brief", "[t] \"mod superior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-overscore", "[t] \"modificando superior superior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-overscore-brief", "[t] \"mod superior superior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "underscore", "[t] \"modificando inferior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "underscore-brief", "[t] \"mod inferior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-underscore", "[t] \"modificando inferior inferior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "double-underscore-brief", "[t] \"mod inferior inferior\"; [n] children/*[1]; [t] \"con\"; [n] children/*[2]"], ["Action", "overbar", "[n] children/*[1]; [t] \"barra\""], ["Action", "underbar", "[n] children/*[1]; [t] \"subbarra\""], ["Action", "overtilde", "[n] children/*[1]; [t] \"tilde\""], ["Action", "undertilde", "[n] children/*[1]; [t] \"subtilde\""], ["Action", "matrix", "[t] \"empezar matriz\"; [t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar matriz\""], ["Action", "matrix-sbrief", "[t] \"matriz\"; [t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFwordCounter, context:\" \"); [t] \"finalizar matriz\""], ["Action", "matrix-row", "[m] children/* (ctxtFunc:CTFwordCounter, context:\"columna\", pause:200)"], ["Action", "row-with-label", "[t] \"con etiqueta\"; [n] content/*[1]; [t] \"finalizar etiqueta\" (pause:200); [m] children/* (ctxtFunc:CTFwordCounter, context:\"columna\")"], ["Action", "row-with-label-brief", "[t] \"etiqueta\"; [n] content/*[1]; [m] children/* (ctxtFunc:CTFwordCounter, context:\"columna\")"], ["Action", "row-with-text-label", "[t] \"etiqueta\"; [t] CSFRemoveParens; [m] children/* (ctxtFunc:CTFwordCounter, context:\"columna\")"], ["Action", "empty-row", "[t] \"espacio\""], ["Action", "empty-cell", "[t] \"espacio\" (pause:300)"], ["Action", "determinant", "[t] \"empezar determinante\"; [t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar determinante\""], ["Action", "determinant-sbrief", "[t] \"determinante\"; [t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar determinante\""], ["Action", "determinant-simple", "[t] \"empezar determinante\"; [t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila\", grammar:simpleDet); [t] \"finalizar determinante\""], ["Action", "determinant-simple-sbrief", "[t] \"determinante\"; [t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila\", grammar:simpleDet); [t] \"finalizar determinante\""], ["Action", "layout", "[t] \"empezar esquema\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar esquema\""], ["Action", "layout-sbrief", "[t] \"esquema\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar esquema\""], ["Action", "binomial", "[t] \"empezar binomial\"; [n] children/*[1]/children/*[1]; [t] \"en\"; [n] children/*[2]/children/*[1]; [t] \"finalizar binomial\""], ["Action", "binomial-sbrief", "[t] \"binomial\"; [n] children/*[1]/children/*[1]; [t] \"en\"; [n] children/*[2]/children/*[1]; [t] \"finalizar binomial\""], ["Action", "cases", "[t] \"empezar esquema\"; [n] content/*[1]; [t] \"alargada\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar esquema\""], ["Action", "cases-sbrief", "[t] \"esquema\"; [n] content/*[1]; [t] \"alargada\"; [m] children/* (ctxtFunc:CTFwordCounter, context:\"fila \"); [t] \"finalizar esquema\""], ["Action", "line-with-label", "[t] \"con etiqueta\"; [n] content/*[1]; [t] \"finalizar etiqueta\" (pause:200); [m] children/*"], ["Action", "line-with-label-brief", "[t] \"etiqueta\"; [n] content/*[1] (pause:200); [m] children/*"], ["Action", "line-with-text-label", "[t] \"etiqueta\"; [t] CSFRemoveParens; [m] children/*"], ["Action", "empty-line", "[t] \"espacio\""], ["Action", "empty-line-with-label", "[t] \"con etiqueta\"; [n] content/*[1]; [t] \"finalizar etiqueta\" (pause:200); [t] \"espacio\""], ["Action", "empty-line-with-label-brief", "[t] \"etiqueta\"; [n] content/*[1] (pause:200); [t] \"espacio\""], ["Action", "enclose", "[t] \"empezar rodear\"; [t] @role (grammar:localEnclose); [n] children/*[1]; [t] \"finalizar rodear\""], ["Action", "leftbar", "[t] \"barra vertical\"; [n] children/*[1]"], ["Action", "rightbar", "[n] children/*[1]; [t] \"barra vertical\""], ["Action", "crossout", "[t] \"tachado\"; [n] children/*[1]; [t] \"finalizar tachado\""], ["Action", "cancel", "[t] \"tachado\"; [n] children/*[1]/children/*[1]; [t] \"con\"; [n] children/*[2]; [t] \"finalizar tachado\""], ["Action", "cancel-reverse", "[t] \"tachado\"; [n] children/*[2]/children/*[1]; [t] \"con\"; [n] children/*[1]; [t] \"finalizar tachado\""], ["Action", "unit-singular", "[t] text() (grammar:annotation=\"unit\":translate)"], ["Action", "unit-plural", "[t] text() (grammar:annotation=\"unit\":translate:plural)"], ["Action", "unit-square", "[n] children/*[1]; [t] \"cuadrado\""], ["Action", "unit-cubic", "[n] children/*[1]; [t] \"cúbico\""], ["Action", "reciprocal", "[t] \"rec<PERSON><PERSON><PERSON><PERSON>\"; [n] children/*[1]"], ["Action", "per", "[t] \"por\"; [n] children/*[1]"], ["Action", "unit-combine", "[m] children/* (sepFunc:CTFunitMultipliers)"], ["Action", "unit-combine-mult", "[m] children/* (sepFunc:CTFunitMultipliers)"], ["Action", "unit-combine-singular", "[n] children/*[1]; [t] \"por\"; [m] children/*[position()>1] (sepFunc:CTFunitMultipliers, grammar:!singular)"], ["Action", "unit-combine-singular-first", "[n] children/*[1]; [n] children/*[2] (grammar:singular); [t] \"por\"; [m] children/*[position()>2] (sepFunc:CTFunitMultipliers)"], ["Action", "unit-combine-singular-final", "[n] children/*[1]; [n] children/*[2] (grammar:singular)"], ["Action", "unit-divide", "[n] children/*[1]; [t] \"por\"; [n] children/*[2] (grammar:singular)"]]}, "es/rules/prefix_spanish.min": {"locale": "es", "inherits": "base", "modality": "prefix", "domain": "default", "rules": []}, "es/rules/prefix_spanish_actions.min": {"locale": "es", "modality": "prefix", "domain": "default", "kind": "actions", "rules": [["Action", "numerator", "[t] \"numerador\" (pause:200)"], ["Action", "denominator", "[t] \"denominador\" (pause:200)"], ["Action", "base", "[t] \"base\" (pause:200)"], ["Action", "exponent", "[t] \"exponente\" (pause:200)"], ["Action", "subscript", "[t] \"subíndice\" (pause:200)"], ["Action", "overscript", "[t] \"sobreíndice\" (pause:200)"], ["Action", "underscript", "[t] \"bajoíndice\" (pause:200)"], ["Action", "radicand", "[t] \"radicand\" (pause:200)"], ["Action", "index", "[t] \"índice\" (pause:200)"], ["Action", "leftsub", "[t] \"subíndice i<PERSON>o\" (pause:200)"], ["Action", "leftsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"subíndice i<PERSON>o\" (pause:200)"], ["Action", "leftsuper", "[t] \"superíndice i<PERSON>o\" (pause:200)"], ["Action", "leftsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"superíndice i<PERSON>\" (pause:200)"], ["Action", "rightsub", "[t] \"subíndice derecho\" (pause:200)"], ["Action", "rightsub-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"subíndice derecho\" (pause:200)"], ["Action", "<PERSON><PERSON>r", "[t] \"superíndice derecho\" (pause:200)"], ["Action", "rightsuper-counted", "[t] CSFordinalPosition (grammar:gender=\"m\"); [t] \"superíndice derecho\" (pause:200)"], ["Action", "choice", "[t] \"cantidad de elección\" (pause:200)"], ["Action", "select", "[t] \"cantidad de selección\" (pause:200)"], ["Action", "row", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"fila\" (pause:200)"], ["Action", "cell", "[n] ../..; [t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"columna\" (pause:200)"], ["Action", "cell-simple", "[t] CSFordinalPosition (grammar:gender=\"f\"); [t] \"columna\" (pause:200)"]]}, "es/rules/summary_spanish.min": {"locale": "es", "modality": "summary", "inherits": "base", "rules": []}, "es/rules/summary_spanish_actions.min": {"locale": "es", "modality": "summary", "kind": "actions", "rules": [["Action", "abstr-identifier-long", "[t] \"identificador largo\""], ["Action", "abstr-identifier", "[t] \"identificador\""], ["Action", "abstr-number-long", "[t] \"número largo\""], ["Action", "abstr-number", "[t] \"número\""], ["Action", "abstr-mixed-number-long", "[t] \"número largo mixto\""], ["Action", "abstr-mixed-number", "[t] \"número mixto\""], ["Action", "abstr-text", "[t] \"texto\""], ["Action", "abstr-function", "[t] \"expresión funcional\""], ["Action", "abstr-function-brief", "[t] \"función\""], ["Action", "abstr-lim", "[t] \"función de límite\""], ["Action", "abstr-lim-brief", "[t] \"límite\""], ["Action", "abstr-fraction", "[t] \"fracción\""], ["Action", "abstr-fraction-brief", "[t] \"frac\""], ["Action", "abstr-continued-fraction", "[t] \"fracción continua\""], ["Action", "abstr-continued-fraction-brief", "[t] \"frac continua\""], ["Action", "abstr-sqrt", "[t] \"raíz cuadrada\""], ["Action", "abstr-sqrt-nested", "[t] \"raíz cuadrada anidada\""], ["Action", "abstr-root-end", "[t] \"raíz del índice\"; [n] children/*[1] (engine:modality=speech); [t] \"finalizar índice\""], ["Action", "abstr-root", "[t] \"raíz del índice\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-brief", "[t] \"raíz\""], ["Action", "abstr-root-nested-end", "[t] \"raíz anidada del índice\"; [n] children/*[1] (engine:modality=speech); [t] \"finalizar índice\""], ["Action", "abstr-root-nested", "[t] \"raíz anidada del índice\"; [n] children/*[1] (engine:modality=speech)"], ["Action", "abstr-root-nested-brief", "[t] \"raíz anidada\""], ["Action", "abstr-superscript", "[t] \"potencia\""], ["Action", "abstr-subscript", "[t] \"subíndice\""], ["Action", "abstr-subsup", "[t] \"potencia con subíndice\""], ["Action", "abstr-infixop", "[t] @role (grammar:localRole); [t] \"con\"; [t] count(./children/*); [t] \"elementos\""], ["Action", "abstr-infixop-var", "[t] @role (grammar:localRole); [t] \"con una cantidad variable de elementos\""], ["Action", "abstr-infixop-brief", "[t] @role (grammar:localRole)"], ["Action", "abstr-addition", "[t] \"suma con\"; [t] count(./children/*); [t] \"sumandos\""], ["Action", "abstr-addition-brief", "[t] \"suma\""], ["Action", "abstr-addition-var", "[t] \"suma con número variable de sumandos\""], ["Action", "abstr-multiplication", "[t] \"producto con\"; [t] count(./children/*); [t] \"factores\""], ["Action", "abstr-multiplication-brief", "[t] \"producto\""], ["Action", "abstr-multiplication-var", "[t] \"producto con una cantidad variable de factores\""], ["Action", "abstr-vector", "[t] \"vector de dimensión\"; [t] count(./children/*)"], ["Action", "abstr-vector-brief", "[t] \"vector\""], ["Action", "abstr-vector-var", "[t] \"vector de dimensión n\""], ["Action", "abstr-binomial", "[t] \"binomio\""], ["Action", "abstr-determinant", "[t] \"determinante de dimensión\"; [t] count(./children/*)"], ["Action", "abstr-determinant-brief", "[t] \"determinante\""], ["Action", "abstr-determinant-var", "[t] \"determinante de dimensión n\""], ["Action", "abstr-squarematrix", "[t] \"matriz cuadrada de dimensión\"; [t] count(./children/*)"], ["Action", "abstr-squarematrix-brief", "[t] \"matriz cuadrada\""], ["Action", "abstr-rowvector", "[t] \"vector fila de dimensión\"; [t] count(./children/row/children/*)"], ["Action", "abstr-rowvector-brief", "[t] \"vector fila\""], ["Action", "abstr-rowvector-var", "[t] \"vector fila de dimensión n\""], ["Action", "abstr-matrix", "[t] count(children/*); [t] \"por\"; [t] count(children/*[1]/children/*); [t] \"matriz\""], ["Action", "abstr-matrix-brief", "[t] \"matriz\""], ["Action", "abstr-matrix-var", "[t] \"matriz de dimensión n por m\""], ["Action", "abstr-cases", "[t] \"declaración de caso\"; [t] \"con\"; [t] count(children/*); [t] \"casos\""], ["Action", "abstr-cases-brief", "[t] \"declaración de caso\""], ["Action", "abstr-cases-var", "[t] \"declaración de caso con número variable de casos\""], ["Action", "abstr-punctuated", "[t] \"lista separada por\"; [n] content/*[1] (grammar:plural); [t] \"de longitud\"; [t] count(children/*) - count(content/*)"], ["Action", "abstr-punctuated-brief", "[t] \"lista separada por\"; [n] content/*[1] (grammar:plural)"], ["Action", "abstr-punctuated-var", "[t] \"lista separada por\"; [n] content/*[1] (grammar:plural); [t] \"de longitud variable\""], ["Action", "abstr-bigop", "[n] content/*[1]"], ["Action", "abstr-integral", "[t] \"integral\""], ["Action", "abstr-relation", "[t] @role (grammar:localRole)"], ["Action", "abstr-relation-seq", "[t] \"secuencia de\"; [t] @role (grammar:localRole); [t] \"con\"; [t] count(./children/*); [t] \"elementos\""], ["Action", "abstr-relation-seq-brief", "[t] \"secuencia de\"; [t] @role (grammar:localRole)"], ["Action", "abstr-relation-var", "[t] \"secuencia de\"; [t] @role (grammar:localRole); [t] \"con una cantidad variable de elementos\""], ["Action", "abstr-multirel", "[t] \"secuencia de relación\"; [t] \"con\"; [t] count(./children/*); [t] \"elementos\""], ["Action", "abstr-multirel-brief", "[t] \"secuencia de relación\""], ["Action", "abstr-multirel-var", "[t] \"secuencia de relación con número variable de elementos\""], ["Action", "abstr-table", "[t] \"mesa con\"; [t] count(children/*); [t] \"filas y\"; [t] count(children/*[1]/children/*); [t] \"columnas\""], ["Action", "abstr-line", "[t] \"en\"; [t] @role (grammar:localRole)"], ["Action", "abstr-row", "[t] \"en\"; [t] @role (grammar:localRole); [t] count(preceding-sibling::..); [t] \"con\"; [t] count(children/*); [t] \"columnas\""], ["Action", "abstr-cell", "[t] \"en\"; [t] @role (grammar:localRole)"]]}}