# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an IoT device detection data management system built with Vue 2.6.12 and Element UI 2.15.6. The system provides functionality for managing device detection data, Excel template design, device-template binding, and data import/export.

## Key Features

- Detection data management (upload, view, manage)
- Excel template designer with drag-and-drop field placement
- Multi-sheet Excel template support
- Device template binding management
- Data import/export in Excel format

## Technology Stack

- Frontend Framework: Vue 2.6.12
- UI Component Library: Element UI 2.15.6
- Routing: Vue Router 3.4.9
- State Management: Vuex 3.6.0
- HTTP Client: Axios 0.21.0
- Excel Processing: XLSX 0.18.5
- Build Tool: Vue CLI 4.4.6

## Common Development Commands

### Development
```bash
npm run dev
```
Starts the development server on port 9981 with hot reloading.

### Building
```bash
npm run build:prod
```
Builds the application for production environment.

```bash
npm run build:stage
```
Builds the application for staging environment.

### Linting
```bash
npm run lint
```
Runs ESLint on the codebase to check for code style issues.

## Project Structure

```
src/
├── api/               # API interface definitions
│   ├── analysis/      # Device analysis related APIs
│   └── detection/     # Detection data related APIs
├── assets/            # Static assets and styles
├── components/        # Reusable components
│   └── detection/     # Detection-specific components
├── layout/            # Page layout components
├── router/            # Vue Router configuration
├── store/             # Vuex state management
├── utils/             # Utility functions
└── views/             # Page components
    └── detection/     # Detection data management pages
```

## Key Components

### ExcelStyleTemplateDesigner
The core component for designing Excel templates with a drag-and-drop interface. Located at `src/components/detection/ExcelStyleTemplateDesigner.vue`.

Key features:
- Multi-sheet support
- Cell merging functionality
- Field library with drag-and-drop placement
- Real-time preview of template design
- Context menu for cell operations

### API Integration
API calls are handled through axios with interceptors defined in `src/utils/request.js`. All API endpoints are defined in the `src/api/` directory.

## Development Environment

- Node.js >= 12.0.0
- Development server runs on http://localhost:9981
- API requests are proxied to:
  - Main service: http://127.0.0.1:9335
  - IoT service: http://127.0.0.1:9335

## Code Architecture

The application follows a standard Vue.js architecture with:
1. Component-based UI structure
2. Centralized state management through Vuex
3. Route-based code splitting
4. API layer abstraction
5. Utility function modules

Key architectural patterns:
- Components are organized by feature (detection, device, etc.)
- API calls are centralized in the `api` directory
- Utility functions are in the `utils` directory
- State management follows Vuex patterns with modules

后端目录在/home/<USER>/nl-mes/logicture-nl/logictrue-iot
蓝色渐变色 linear-gradient(180deg, #124B9A, #0D438D)