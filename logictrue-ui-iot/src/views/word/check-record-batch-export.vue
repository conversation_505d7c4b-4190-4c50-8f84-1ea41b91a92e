<template>
  <div class="batch-export">
    <div class="export-header">
      <h2>检验记录批量导出</h2>
      <div class="header-actions">
        <div class="input-section">
          <label>车辆ID列表（用逗号分隔）:</label>
          <textarea
            v-model="carIdInput"
            placeholder="请输入车辆ID，多个ID用逗号分隔，例如：0822,0823,0824"
            class="car-id-input"
            rows="3"
          ></textarea>
          <button @click="parseCarIds" class="parse-btn">解析ID</button>
        </div>
        <div class="action-buttons">
          <button @click="exportBatch" class="export-btn" :disabled="!hasValidCarIds || isExporting">
            {{ isExporting ? '导出中...' : '批量导出' }}
          </button>
          <button @click="clearAll" class="clear-btn">清空</button>
        </div>
      </div>
    </div>

    <!-- 车辆ID列表显示 -->
    <div v-if="carIds.length > 0" class="car-id-list">
      <h3>待导出车辆列表 ({{ carIds.length }}个)</h3>
      <div class="car-id-tags">
        <div
          v-for="(carId, index) in carIds"
          :key="index"
          class="car-id-tag"
        >
          <span>{{ carId }}</span>
          <button @click="removeCarId(index)" class="remove-btn">×</button>
        </div>
      </div>
    </div>

    <!-- 导出配置 -->
    <div class="export-config">
      <h3>导出配置</h3>
      <div class="config-form">
        <div class="form-item">
          <label>文档标题:</label>
          <input
            v-model="exportConfig.title"
            type="text"
            placeholder="检验记录表_批量导出"
            class="config-input"
          >
        </div>
        <div class="form-item">
          <label>页面方向:</label>
          <select v-model="exportConfig.pageOrientation" class="config-select">
            <option value="LANDSCAPE">横向</option>
            <option value="PORTRAIT">纵向</option>
          </select>
        </div>
        <div class="form-item">
          <label>
            <input
              v-model="exportConfig.includeCheckRecords"
              type="checkbox"
            >
            包含检验记录数据
          </label>
        </div>
        <div class="form-item">
          <label>
            <input
              v-model="exportConfig.addPageNumbers"
              type="checkbox"
            >
            添加页码
          </label>
        </div>
        <div class="form-item">
          <label>
            <input
              v-model="exportConfig.addExportTime"
              type="checkbox"
            >
            添加导出时间
          </label>
        </div>
      </div>
    </div>

    <!-- 导出进度 -->
    <div v-if="isExporting" class="export-progress">
      <div class="progress-info">
        <h3>导出进度</h3>
        <p>{{ exportStatus }}</p>
      </div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: exportProgress + '%' }"></div>
      </div>
      <p class="progress-text">{{ exportProgress }}%</p>
    </div>

    <!-- 操作结果提示 -->
    <div v-if="operationResult" class="operation-result" :class="operationResult.success ? 'success' : 'error'">
      {{ operationResult.message }}
    </div>
  </div>
</template>

<script>
import { exportCheckRecordBatch } from '@/api/word/export'

export default {
  name: 'CheckRecordBatchExport',
  data() {
    return {
      // 车辆ID输入
      carIdInput: '',
      
      // 解析后的车辆ID列表
      carIds: [],
      
      // 导出配置
      exportConfig: {
        title: '检验记录表_批量导出',
        pageOrientation: 'LANDSCAPE',
        includeCheckRecords: true,
        addPageNumbers: true,
        addExportTime: true
      },
      
      // 导出状态
      isExporting: false,
      exportProgress: 0,
      exportStatus: '',
      
      // 操作结果
      operationResult: null
    }
  },
  computed: {
    hasValidCarIds() {
      return this.carIds.length > 0
    }
  },
  methods: {
    /**
     * 解析车辆ID
     */
    parseCarIds() {
      if (!this.carIdInput.trim()) {
        this.showOperationResult(false, '请输入车辆ID')
        return
      }
      
      // 解析车辆ID，支持逗号、分号、换行符分隔
      const ids = this.carIdInput
        .split(/[,;，；\n\r]/)
        .map(id => id.trim())
        .filter(id => id.length > 0)
      
      // 去重
      const uniqueIds = [...new Set(ids)]
      
      this.carIds = uniqueIds
      this.showOperationResult(true, `成功解析 ${uniqueIds.length} 个车辆ID`)
    },
    
    /**
     * 移除车辆ID
     */
    removeCarId(index) {
      this.carIds.splice(index, 1)
    },
    
    /**
     * 清空所有数据
     */
    clearAll() {
      this.carIdInput = ''
      this.carIds = []
      this.exportProgress = 0
      this.exportStatus = ''
      this.operationResult = null
    },
    
    /**
     * 批量导出
     */
    async exportBatch() {
      if (!this.hasValidCarIds) {
        this.showOperationResult(false, '请先添加车辆ID')
        return
      }
      
      try {
        this.isExporting = true
        this.exportProgress = 0
        this.exportStatus = '正在准备导出数据...'
        
        // 模拟进度更新
        const progressInterval = setInterval(() => {
          if (this.exportProgress < 90) {
            this.exportProgress += Math.random() * 10
            this.exportStatus = `正在处理车辆数据... (${Math.floor(this.exportProgress)}%)`
          }
        }, 500)
        
        // 准备导出请求数据
        const exportRequest = {
          carIds: this.carIds,
          title: this.exportConfig.title,
          includeCheckRecords: this.exportConfig.includeCheckRecords,
          pageOrientation: this.exportConfig.pageOrientation,
          exportConfig: {
            addPageNumbers: this.exportConfig.addPageNumbers,
            addExportTime: this.exportConfig.addExportTime,
            includeEmptyRows: false,
            mergeSameProcess: true,
            maxRowsPerPage: 50
          }
        }
        
        // 调用后端导出API
        const response = await exportCheckRecordBatch(exportRequest)
        
        clearInterval(progressInterval)
        this.exportProgress = 100
        this.exportStatus = '导出完成，正在下载文件...'
        
        // 处理文件下载
        this.downloadWordFile(response, this.exportConfig.title)
        
        this.showOperationResult(true, '批量导出成功')
        
      } catch (error) {
        console.error('批量导出失败:', error)
        this.showOperationResult(false, '批量导出失败：' + error.message)
      } finally {
        this.isExporting = false
        this.exportProgress = 0
        this.exportStatus = ''
      }
    },
    
    /**
     * 下载Word文件
     */
    downloadWordFile(response, fileName) {
      try {
        // 检查响应
        if (!response || !response.data) {
          throw new Error('服务器返回数据为空')
        }

        if (response.status !== 200) {
          throw new Error(`服务器响应错误: ${response.status}`)
        }

        const dataSize = response.data.size || response.data.byteLength || response.data.length
        if (!dataSize || dataSize === 0) {
          throw new Error('返回的文件数据为空')
        }

        // 创建下载链接
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })
        
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        
        // 生成文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
        link.download = `${fileName}_${timestamp}.docx`
        
        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        
        // 清理URL对象
        window.URL.revokeObjectURL(url)
        
        console.log('Word文件下载成功:', link.download)
        
      } catch (error) {
        console.error('下载Word文件失败:', error)
        throw error
      }
    },
    
    /**
     * 显示操作结果
     */
    showOperationResult(success, message) {
      this.operationResult = { success, message }
      setTimeout(() => {
        this.operationResult = null
      }, 3000)
    }
  }
}
</script>

<style scoped>
.batch-export {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.export-header {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.export-header h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.input-section {
  flex: 1;
}

.input-section label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.car-id-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
  margin-bottom: 10px;
}

.car-id-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.parse-btn, .export-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.parse-btn {
  background: #007bff;
  color: white;
}

.parse-btn:hover {
  background: #0056b3;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-direction: column;
}

.export-btn {
  background: #28a745;
  color: white;
}

.export-btn:hover:not(:disabled) {
  background: #218838;
}

.export-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
}

/* 车辆ID列表 */
.car-id-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.car-id-list h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.car-id-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.car-id-tag {
  display: flex;
  align-items: center;
  background: #e9ecef;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 14px;
}

.car-id-tag span {
  margin-right: 5px;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-btn:hover {
  background: #c82333;
}

/* 导出配置 */
.export-config {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.export-config h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.config-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item label {
  font-weight: 500;
  color: #333;
  white-space: nowrap;
}

.config-input, .config-select {
  flex: 1;
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.config-input:focus, .config-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 导出进度 */
.export-progress {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-info h3 {
  margin: 0 0 10px 0;
  color: #333;
}

.progress-info p {
  margin: 0 0 15px 0;
  color: #666;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-weight: 500;
  color: #333;
  margin: 0;
}

/* 操作结果提示 */
.operation-result {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.operation-result.success {
  background: #28a745;
}

.operation-result.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-actions {
    flex-direction: column;
  }
  
  .config-form {
    grid-template-columns: 1fr;
  }
  
  .form-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-item label {
    margin-bottom: 5px;
  }
}
</style>
