<template>
  <div class="check-record-manage">
    <div class="page-header">
      <h2>检验记录数据管理</h2>
      <div class="header-actions">
        <button @click="showAddDialog" class="add-btn">
          <i class="el-icon-plus"></i>
          新增记录
        </button>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-form">
        <div class="form-item">
          <label>车辆ID:</label>
          <input
            v-model="searchForm.carId"
            type="text"
            placeholder="请输入车辆ID"
            class="search-input"
            @keyup.enter="handleSearch"
          >
        </div>
        <div class="form-item">
          <label>检查工序:</label>
          <input
            v-model="searchForm.checkName"
            type="text"
            placeholder="请输入检查工序名称"
            class="search-input"
            @keyup.enter="handleSearch"
          >
        </div>
        <div class="form-actions">
          <button @click="handleSearch" class="search-btn">查询</button>
          <button @click="handleReset" class="reset-btn">重置</button>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <div class="table-header">
        <div class="table-info">
          <span>共 {{ pagination.total }} 条记录</span>
        </div>
      </div>

      <div class="data-table">
        <table>
          <thead>
            <tr>
              <th>序号</th>
              <th>检查工序名称</th>
              <th>检查项目及技术条件</th>
              <th>实际检查结果</th>
              <th>完工月</th>
              <th>完工日</th>
              <th>操作员</th>
              <th>班组长</th>
              <th>检验员</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(record, index) in recordList" :key="record.id || index">
              <td>{{ (pagination.current - 1) * pagination.size + index + 1 }}</td>
              <td>{{ record.check_name }}</td>
              <td>{{ record.check_content }}</td>
              <td>{{ record.result }}</td>
              <td>{{ record.month_str }}</td>
              <td>{{ record.day_str }}</td>
              <td>{{ record.check_user_name }}</td>
              <td>{{ record.bzz }}</td>
              <td>{{ record.jyy }}</td>
              <td class="action-column">
                <button @click="editRecord(record)" class="edit-btn">编辑</button>
                <button @click="deleteRecord(record)" class="delete-btn">删除</button>
              </td>
            </tr>
            <tr v-if="recordList.length === 0">
              <td colspan="10" class="no-data">暂无数据</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <button
          @click="changePage(pagination.current - 1)"
          :disabled="pagination.current <= 1"
          class="page-btn"
        >
          上一页
        </button>
        <span class="page-info">
          第 {{ pagination.current }} 页 / 共 {{ pagination.pages }} 页
        </span>
        <button
          @click="changePage(pagination.current + 1)"
          :disabled="pagination.current >= pagination.pages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <div v-if="showDialog" class="dialog-overlay" @click="closeDialog">
      <div class="dialog-content" @click.stop>
        <div class="dialog-header">
          <h3>{{ isEdit ? '编辑记录' : '新增记录' }}</h3>
          <button @click="closeDialog" class="close-btn">×</button>
        </div>

        <div class="dialog-body">
          <div class="form-grid">
            <div class="form-item">
              <label>车辆ID <span class="required">*</span>:</label>
              <input
                v-model="formData.car_id"
                type="text"
                placeholder="请输入车辆ID"
                class="form-input"
                :disabled="isEdit"
              >
            </div>

            <div class="form-item">
              <label>检查工序名称 <span class="required">*</span>:</label>
              <input
                v-model="formData.check_name"
                type="text"
                placeholder="请输入检查工序名称"
                class="form-input"
              >
            </div>

            <div class="form-item full-width">
              <label>检查项目及技术条件 <span class="required">*</span>:</label>
              <textarea
                v-model="formData.check_content"
                placeholder="请输入检查项目及技术条件"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>

            <div class="form-item">
              <label>实际检查结果:</label>
              <input
                v-model="formData.result"
                type="text"
                placeholder="请输入实际检查结果"
                class="form-input"
              >
            </div>

            <div class="form-item">
              <label>完工月:</label>
              <input
                v-model="formData.month_str"
                type="number"
                placeholder="月份"
                class="form-input"
                min="1"
                max="12"
              >
            </div>

            <div class="form-item">
              <label>完工日:</label>
              <input
                v-model="formData.day_str"
                type="number"
                placeholder="日期"
                class="form-input"
                min="1"
                max="31"
              >
            </div>

            <div class="form-item">
              <label>操作员:</label>
              <input
                v-model="formData.check_user_name"
                type="text"
                placeholder="请输入操作员姓名"
                class="form-input"
              >
            </div>

            <div class="form-item">
              <label>班组长:</label>
              <input
                v-model="formData.bzz"
                type="text"
                placeholder="请输入班组长姓名"
                class="form-input"
              >
            </div>

            <div class="form-item">
              <label>检验员:</label>
              <input
                v-model="formData.jyy"
                type="text"
                placeholder="请输入检验员姓名"
                class="form-input"
              >
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <button @click="closeDialog" class="cancel-btn">取消</button>
          <button @click="saveRecord" class="confirm-btn" :disabled="saving">
            {{ saving ? '保存中...' : '保存' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 操作结果提示 -->
    <div v-if="message" class="message" :class="message.type">
      {{ message.text }}
    </div>
  </div>
</template>

<script>
import { getCheckRecord, saveOrUpdateCheckRecord, deleteCheckRecord } from '@/api/word/checkRecord'

export default {
  name: 'CheckRecordManage',
  data() {
    return {
      // 搜索表单
      searchForm: {
        carId: '',
        checkName: ''
      },

      // 记录列表
      recordList: [],

      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 0
      },

      // 弹窗相关
      showDialog: false,
      isEdit: false,
      saving: false,

      // 表单数据
      formData: {
        car_id: '',
        check_name: '',
        check_content: '',
        result: '',
        month_str: null,
        day_str: null,
        check_user_name: '',
        bzz: '',
        jyy: ''
      },

      // 消息提示
      message: null
    }
  },
  mounted() {
    this.handleSearch()
  },
  methods: {
    /**
     * 搜索记录
     */
    async handleSearch() {
      try {
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.size,
          carId: this.searchForm.carId,
          checkName: this.searchForm.checkName
        }

        const response = await getCheckRecord(params)
        if (response.code === 200 || response.code === 1) {
          const data = response.data
          this.recordList = data.list || []
          this.pagination.total = data.total || 0
          this.pagination.pages = data.pages || 0
          this.pagination.current = data.current || 1
        } else {
          this.showMessage('error', response.msg || '查询失败')
        }
      } catch (error) {
        console.error('查询检验记录失败:', error)
        this.showMessage('error', '查询检验记录失败')
      }
    },

    /**
     * 重置搜索
     */
    handleReset() {
      this.searchForm.carId = ''
      this.searchForm.checkName = ''
      this.pagination.current = 1
      this.handleSearch()
    },

    /**
     * 切换页码
     */
    changePage(page) {
      if (page >= 1 && page <= this.pagination.pages) {
        this.pagination.current = page
        this.handleSearch()
      }
    },

    /**
     * 显示新增弹窗
     */
    showAddDialog() {
      this.isEdit = false
      this.resetFormData()
      this.showDialog = true
    },

    /**
     * 编辑记录
     */
    editRecord(record) {
      this.isEdit = true
      this.formData = {
        id: record.id,
        car_id: record.car_id || '',
        check_name: record.check_name || '',
        check_content: record.check_content || '',
        result: record.result || '',
        month_str: record.month_str,
        day_str: record.day_str,
        check_user_name: record.check_user_name || '',
        bzz: record.bzz || '',
        jyy: record.jyy || ''
      }
      this.showDialog = true
    },

    /**
     * 删除记录
     */
    async deleteRecord(record) {
      if (!confirm(`确定要删除这条检验记录吗？`)) {
        return
      }

      try {
        const response = await deleteCheckRecord(record.id)
        if (response.code === 200) {
          this.showMessage('success', '删除成功')
          this.handleSearch()
        } else {
          this.showMessage('error', response.msg || '删除失败')
        }
      } catch (error) {
        console.error('删除记录失败:', error)
        this.showMessage('error', '删除记录失败')
      }
    },

    /**
     * 保存记录
     */
    async saveRecord() {
      // 表单验证
      if (!this.validateForm()) {
        return
      }

      this.saving = true
      try {
        const response = await saveOrUpdateCheckRecord(this.formData)
        if (response.code === 200 || response.code === 1) {
          this.showMessage('success', this.isEdit ? '更新成功' : '新增成功')
          this.closeDialog()
          this.handleSearch()
        } else {
          this.showMessage('error', response.msg || '保存失败')
        }
      } catch (error) {
        console.error('保存记录失败:', error)
        this.showMessage('error', '保存记录失败')
      } finally {
        this.saving = false
      }
    },

    /**
     * 表单验证
     */
    validateForm() {
      if (!this.formData.car_id.trim()) {
        this.showMessage('error', '请输入车辆ID')
        return false
      }
      if (!this.formData.check_name.trim()) {
        this.showMessage('error', '请输入检查工序名称')
        return false
      }
      if (!this.formData.check_content.trim()) {
        this.showMessage('error', '请输入检查项目及技术条件')
        return false
      }
      return true
    },

    /**
     * 关闭弹窗
     */
    closeDialog() {
      this.showDialog = false
      this.resetFormData()
      this.saving = false
    },

    /**
     * 重置表单数据
     */
    resetFormData() {
      this.formData = {
        car_id: '',
        check_name: '',
        check_content: '',
        result: '',
        month_str: null,
        day_str: null,
        check_user_name: '',
        bzz: '',
        jyy: ''
      }
    },

    /**
     * 显示消息提示
     */
    showMessage(type, text) {
      this.message = { type, text }
      setTimeout(() => {
        this.message = null
      }, 3000)
    }
  }
}
</script>

<style scoped>
.check-record-manage {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.add-btn:hover {
  background: #0056b3;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item label {
  min-width: 80px;
  font-weight: 500;
  color: #333;
}

.search-input {
  width: 200px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  gap: 10px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-btn {
  background: #007bff;
  color: white;
}

.search-btn:hover {
  background: #0056b3;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover {
  background: #545b62;
}

/* 表格区域 */
.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.table-info {
  color: #666;
  font-size: 14px;
}

.data-table {
  overflow-x: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  min-width: 1000px;
}

.data-table th,
.data-table td {
  padding: 12px 8px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.action-column {
  width: 120px;
}

.edit-btn, .delete-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  margin-right: 5px;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #007bff;
  color: white;
}

.edit-btn:hover {
  background: #0056b3;
}

.delete-btn {
  background: #dc3545;
  color: white;
}

.delete-btn:hover {
  background: #c82333;
}

.no-data {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 40px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 20px;
  padding: 15px 0;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.dialog-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.dialog-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #999;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f8f9fa;
  color: #333;
}

.dialog-body {
  padding: 0 20px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.required {
  color: #dc3545;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-input:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.confirm-btn {
  background: #007bff;
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  background: #0056b3;
}

.confirm-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #545b62;
}

/* 消息提示 */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 3000;
  animation: slideIn 0.3s ease;
}

.message.success {
  background: #28a745;
}

.message.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
    align-items: stretch;
  }

  .form-item {
    flex-direction: column;
    align-items: stretch;
  }

  .form-item label {
    margin-bottom: 5px;
  }

  .search-input {
    width: 100%;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .dialog-content {
    width: 95%;
    margin: 10px;
  }
}
</style>
