<template>
  <div class="check-record-config">
    <div class="config-container">
      <!-- 左侧：数据查询区域 -->
      <div class="left-panel">
        <div class="search-section">
          <h3>检验记录数据查询</h3>
          <div class="search-form">
            <div class="form-item">
              <label>车辆ID:</label>
              <input
                  v-model="searchForm.carId"
                  type="text"
                  placeholder="请输入车辆ID"
                  class="search-input"
                  @keyup.enter="handleSearch"
              >
            </div>
            <div class="form-actions">
              <button @click="handleSearch" class="search-btn">查询</button>
              <button @click="handleReset" class="reset-btn">重置</button>
            </div>
          </div>
        </div>

        <!-- 数据列表 -->
        <div class="data-section">
          <div class="data-header">
            <h4>检验记录列表</h4>
            <div class="pagination-info">
              共 {{ pagination.total }} 条记录
            </div>
          </div>

          <!-- 批量操作按钮 -->
          <div class="batch-actions" v-if="selectedRecords.length > 0">
            <button @click="batchInsert" class="batch-btn batch-insert-btn">
              批量插入 ({{ selectedRecords.length }})
            </button>
            <button @click="batchMergeInsert" class="batch-btn batch-merge-btn">
              批量合并插入 ({{ selectedRecords.length }})
            </button>
          </div>

          <div class="data-table">
            <table>
              <thead>
              <tr>
                <th class="checkbox-column">
                  <input
                      type="checkbox"
                      v-model="selectAll"
                      @change="toggleSelectAll"
                      class="select-all-checkbox"
                  >
                </th>
                <th>检查工序名称</th>
                <th>检查项目及技术条件</th>
                <th>实际检查结果</th>
                <th>完工月</th>
                <th>完工日</th>
                <th>操作员</th>
                <th>班组长</th>
                <th>检验员</th>
                <th>操作</th>
              </tr>
              </thead>
              <tbody>
              <tr v-for="(record, index) in recordList" :key="index" :class="{ 'selected-row': isRecordSelected(record) }">
                <td class="checkbox-column">
                  <input
                      type="checkbox"
                      :checked="isRecordSelected(record)"
                      @change="toggleRecordSelection(record)"
                      class="record-checkbox"
                  >
                </td>
                <td>{{ record.check_name }}</td>
                <td>{{ record.check_content }}</td>
                <td>{{ record.result }}</td>
                <td>{{ record.month_str }}</td>
                <td>{{ record.day_str }}</td>
                <td>{{ record.check_user_name }}</td>
                <td>{{ record.bzz }}</td>
                <td>{{ record.jyy }}</td>
                <td>
                  <button @click="insertRecord(record)" class="insert-btn">插入</button>
                </td>
              </tr>
              </tbody>
            </table>
          </div>

          <!-- 分页 -->
          <div class="pagination">
            <button
                @click="changePage(pagination.current - 1)"
                :disabled="pagination.current <= 1"
                class="page-btn"
            >
              上一页
            </button>
            <span class="page-info">
              第 {{ pagination.current }} 页 / 共 {{ pagination.pages }} 页
            </span>
            <button
                @click="changePage(pagination.current + 1)"
                :disabled="pagination.current >= pagination.pages"
                class="page-btn"
            >
              下一页
            </button>
          </div>
        </div>
      </div>

      <!-- 右侧：表格渲染区域 -->
      <div class="right-panel">
        <div class="table-section">
          <div class="table-header">
            <h3>检验记录表</h3>
            <div class="table-actions">
              <button @click="saveTableData" class="save-btn">保存表格</button>
              <button @click="clearTableData" class="clear-btn">清空表格</button>
            </div>
          </div>

          <!-- 页面管理区域 -->
          <div class="page-management">
            <div class="page-tabs">
              <div
                  v-for="page in pageList"
                  :key="page.pageOrder"
                  :class="['page-tab', { active: currentPageOrder === page.pageOrder }]"
                  @click="switchPage(page.pageOrder)"
              >
                <span class="page-name">{{ page.pageName }}</span>
                <button
                    v-if="pageList.length > 1"
                    @click.stop="deletePage(page.pageOrder)"
                    class="delete-page-btn"
                    title="删除页面"
                >
                  ×
                </button>
              </div>
              <button @click="addNewPage" class="add-page-btn" title="添加新页面">+</button>
            </div>

            <div class="page-actions">
              <div class="page-info">
                <span>当前页面：{{ currentPageOrder }}/{{ pageList.length }}</span>
              </div>
              <div class="page-controls">
                <button
                    @click="renamePage"
                    class="rename-btn"
                    title="重命名页面"
                >
                  重命名
                </button>
                <button
                    @click="sortPages"
                    class="sort-btn"
                    title="页面排序"
                >
                  排序
                </button>
              </div>
            </div>
          </div>

          <!-- 表格容器 -->
          <div class="table-container">
            <TableContainer
                ref="tableContainer"
                :table-width="'100%'"
                :table-height="'600px'"
                :data-rows="tableData"
                @data-inserted="handleDataInserted"
                @table-updated="handleTableUpdated"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 保存结果提示 -->
    <div v-if="saveResult" class="save-result" :class="saveResult.success ? 'success' : 'error'">
      {{ saveResult.message }}
    </div>

    <!-- 页面重命名弹窗 -->
    <div v-if="showRenameDialog" class="dialog-overlay" @click="closeRenameDialog">
      <div class="dialog-content" @click.stop>
        <h3>重命名页面</h3>
        <div class="form-item">
          <label>页面名称：</label>
          <input
              v-model="newPageName"
              type="text"
              class="rename-input"
              @keyup.enter="confirmRename"
              ref="renameInput"
          >
        </div>
        <div class="dialog-actions">
          <button @click="confirmRename" class="confirm-btn">确定</button>
          <button @click="closeRenameDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>

    <!-- 页面排序弹窗 -->
    <div v-if="showSortDialog" class="dialog-overlay" @click="closeSortDialog">
      <div class="dialog-content" @click.stop>
        <h3>页面排序</h3>
        <div class="sort-list">
          <div
              v-for="(page, index) in sortablePages"
              :key="page.pageOrder"
              class="sort-item"
              draggable="true"
              @dragstart="handleDragStart(index)"
              @dragover.prevent
              @drop="handleDrop(index)"
          >
            <span class="drag-handle">⋮⋮</span>
            <span class="page-name">{{ page.pageName }}</span>
            <span class="page-order">第{{ page.pageOrder }}页</span>
          </div>
        </div>
        <div class="dialog-actions">
          <button @click="confirmSort" class="confirm-btn">确定</button>
          <button @click="closeSortDialog" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import { getCheckRecord } from '@/api/word/checkRecord'
import {
  saveOrUpdateDesignWord,
  getDesignWordByCarId,
  getDesignWordPagesByCarId,
  getDesignWordByCarIdAndPage,
  setActivePage,
  updateTotalPages
} from '@/api/word/designWord'
import mathFormulaUtils from '@/utils/math-formula-utils'

export default {
  name: 'CheckRecordConfig',
  components: {
    TableContainer
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        carId: '0822'
      },

      // 记录列表
      recordList: [],

      // 多选相关
      selectedRecords: [], // 选中的记录
      selectAll: false, // 全选状态

      // 分页信息
      pagination: {
        current: 1,
        size: 10,
        total: 0,
        pages: 0
      },

      // 表格数据
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 表格配置
      tableConfig: null,

      // 保存结果
      saveResult: null,

      // 当前车辆的设计ID
      currentDesignId: null,

      // 多页相关数据
      pageList: [], // 页面列表
      currentPageOrder: 1, // 当前页面顺序

      // 页面重命名弹窗
      showRenameDialog: false,
      newPageName: '',

      // 页面排序弹窗
      showSortDialog: false,
      sortablePages: [],
      draggedIndex: -1
    }
  },
  mounted() {
    this.initializeTable()
    this.handleSearch()
  },
  methods: {
    /**
     * 初始化表格配置
     */
    initializeTable() {
      // 设置默认表格配置
      this.tableConfig = {
        title: "检验记录表",
        headers: [
          [
            "检查工序\n名称",
            "检 查 项 目 及 技 术 条 件",
            "实 际 检 查 结 果",
            "完工",
            "",
            "操作员",
            "班组长",
            "检验员"
          ],
          [
            "",
            "",
            "",
            "月",
            "日",
            "",
            "",
            ""
          ]
        ],
        headerMerges: [
          {
            startRow: 0,
            startCol: 0,
            endRow: 1,
            endCol: 0,
            content: "检查工序\n名称"
          },
          {
            startRow: 0,
            startCol: 1,
            endRow: 1,
            endCol: 1,
            content: "检 查 项 目 及 技 术 条 件"
          },
          {
            startRow: 0,
            startCol: 2,
            endRow: 1,
            endCol: 2,
            content: "实 际 检 查 结 果"
          },
          {
            startRow: 0,
            startCol: 3,
            endRow: 0,
            endCol: 4,
            content: "完工"
          },
          {
            startRow: 0,
            startCol: 5,
            endRow: 1,
            endCol: 5,
            content: "操作员"
          },
          {
            startRow: 0,
            startCol: 6,
            endRow: 1,
            endCol: 6,
            content: "班组长"
          },
          {
            startRow: 0,
            startCol: 7,
            endRow: 1,
            endCol: 7,
            content: "检验员"
          }
        ],
        headerWidthConfig: {
          columnWidths: [100, 460, 160, 32, 32, 32, 32, 32],
          headerHeights: [35, 35]
        },
        verticalHeadersConfig: [false, false, false, false, false, true, true, true]
      }

      // 应用表格配置
      this.$nextTick(() => {
        this.applyTableConfig()
      })
    },

    /**
     * 应用表格配置
     */
    applyTableConfig() {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer && this.tableConfig) {
        tableContainer.setDynamicHeaderConfig(
            true,
            {
              headers: this.tableConfig.headers,
              merges: this.tableConfig.headerMerges
            },
            this.tableConfig.headerWidthConfig,
            this.tableConfig.verticalHeadersConfig
        )
      }
    },

    /**
     * 搜索记录
     */
    async handleSearch() {
      try {
        const params = {
          pageNum: this.pagination.current,
          pageSize: this.pagination.size,
          carId: this.searchForm.carId
        }

        const response = await getCheckRecord(params)
        if (response.code === 200 || response.code === 1) {
          const data = response.data
          this.recordList = data.list || []
          this.pagination.total = data.total || 0
          this.pagination.pages = data.pages || 0
          this.pagination.current = data.current || 1

          // 清空选中状态
          this.clearSelection()
        } else {
          console.error('查询失败:', response.msg)
          this.showSaveResult(false, response.msg || '查询检验记录失败')
        }

        // 加载对应车辆的页面列表和表格设计
        if (this.searchForm.carId) {
          await this.loadPageList(this.searchForm.carId)
          await this.loadCurrentPageData()
        }

      } catch (error) {
        console.error('查询检验记录失败:', error)
        this.showSaveResult(false, '查询检验记录失败')
      }
    },

    /**
     * 重置搜索
     */
    handleReset() {
      this.searchForm.carId = ''
      this.pagination.current = 1
      this.recordList = []
      this.pagination.total = 0
      this.pagination.pages = 0
      // 清空选中状态
      this.clearSelection()
    },

    /**
     * 切换页码
     */
    changePage(page) {
      if (page >= 1 && page <= this.pagination.pages) {
        this.pagination.current = page
        this.handleSearch() // handleSearch 方法中已经包含了 clearSelection()
      }
    },

    /**
     * 插入记录到表格
     */
    insertRecord(record) {
      try {
        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 构建插入数据
        const insertData = {
          cellRows: [[
            { content: record.check_name, hasMath: false },
            { content: record.check_content, hasMath: false },
            { content: record.result, hasMath: false },
            { content: record.month_str.toString(), hasMath: false },
            { content: record.day_str.toString(), hasMath: false },
            { content: record.check_user_name, hasMath: false },
            { content: record.bzz, hasMath: false },
            { content: record.jyy, hasMath: false }
          ]]
        }

        const options = {
          clearExisting: false,
          validateData: true,
          startRow: this.tableData.length
        }

        const result = tableContainer.insertDataFromJSON(insertData, options)
        if (result.success) {
          this.tableData = tableContainer.dataRows
          this.showSaveResult(true, '数据插入成功')
        } else {
          this.showSaveResult(false, result.message || '数据插入失败')
        }

      } catch (error) {
        console.error('插入记录失败:', error)
        this.showSaveResult(false, '插入记录失败')
      }
    },

    /**
     * 全选/取消全选
     */
    toggleSelectAll() {
      if (this.selectAll) {
        // 全选：将所有记录添加到选中列表
        this.selectedRecords = [...this.recordList]
      } else {
        // 取消全选：清空选中列表
        this.selectedRecords = []
      }
    },

    /**
     * 切换单个记录的选中状态
     */
    toggleRecordSelection(record) {
      const index = this.selectedRecords.findIndex(r => this.isSameRecord(r, record))
      if (index > -1) {
        // 已选中，移除
        this.selectedRecords.splice(index, 1)
      } else {
        // 未选中，添加
        this.selectedRecords.push(record)
      }

      // 更新全选状态
      this.selectAll = this.selectedRecords.length === this.recordList.length
    },

    /**
     * 判断记录是否被选中
     */
    isRecordSelected(record) {
      return this.selectedRecords.some(r => this.isSameRecord(r, record))
    },

    /**
     * 判断两个记录是否相同（基于唯一标识）
     */
    isSameRecord(record1, record2) {
      // 使用多个字段组合作为唯一标识
      return record1.check_name === record2.check_name &&
          record1.check_content === record2.check_content &&
          record1.result === record2.result &&
          record1.month_str === record2.month_str &&
          record1.day_str === record2.day_str
    },

    /**
     * 批量插入记录
     */
    batchInsert() {
      try {
        if (this.selectedRecords.length === 0) {
          this.showSaveResult(false, '请先选择要插入的记录')
          return
        }

        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 按 check_name 分组记录
        const groupedRecords = this.groupRecordsByCheckName(this.selectedRecords)
        const insertRows = []

        // 处理每个分组
        for (const [checkName, records] of Object.entries(groupedRecords)) {
          if (records.length === 1) {
            // 单条记录，直接插入
            const record = records[0]
            insertRows.push([
              { content: record.check_name, hasMath: false },
              { content: record.check_content, hasMath: false },
              { content: record.result, hasMath: false },
              { content: record.month_str.toString(), hasMath: false },
              { content: record.day_str.toString(), hasMath: false },
              { content: record.check_user_name, hasMath: false },
              { content: record.bzz, hasMath: false },
              { content: record.jyy, hasMath: false }
            ])
          } else {
            // 多条记录，合并 check_content
            const firstRecord = records[0]
            const mergedContent = records.map(r => r.check_content).join('\n')

            insertRows.push([
              { content: firstRecord.check_name, hasMath: false },
              { content: mergedContent, hasMath: false },
              { content: firstRecord.result, hasMath: false },
              { content: firstRecord.month_str.toString(), hasMath: false },
              { content: firstRecord.day_str.toString(), hasMath: false },
              { content: firstRecord.check_user_name, hasMath: false },
              { content: firstRecord.bzz, hasMath: false },
              { content: firstRecord.jyy, hasMath: false }
            ])
          }
        }

        // 构建插入数据
        const insertData = {
          cellRows: insertRows
        }

        const options = {
          clearExisting: false,
          validateData: true,
          startRow: this.tableData.length
        }

        const result = tableContainer.insertDataFromJSON(insertData, options)
        if (result.success) {
          this.tableData = tableContainer.dataRows
          this.showSaveResult(true, `批量插入成功，共插入 ${insertRows.length} 行记录`)
          // 清空选中状态
          this.clearSelection()
        } else {
          this.showSaveResult(false, result.message || '批量插入失败')
        }

      } catch (error) {
        console.error('批量插入失败:', error)
        this.showSaveResult(false, '批量插入失败')
      }
    },

    /**
     * 批量合并插入记录
     */
    batchMergeInsert() {
      try {
        if (this.selectedRecords.length === 0) {
          this.showSaveResult(false, '请先选择要插入的记录')
          return
        }

        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 按 check_name 分组记录
        const groupedRecords = this.groupRecordsByCheckName(this.selectedRecords)
        const insertRows = []
        const mergeCells = []
        let currentRowIndex = this.tableData.length

        // 处理每个分组
        for (const [checkName, records] of Object.entries(groupedRecords)) {
          if (records.length === 1) {
            // 单条记录，直接插入
            const record = records[0]
            insertRows.push([
              { content: record.check_name, hasMath: false },
              { content: record.check_content, hasMath: false },
              { content: record.result, hasMath: false },
              { content: record.month_str.toString(), hasMath: false },
              { content: record.day_str.toString(), hasMath: false },
              { content: record.check_user_name, hasMath: false },
              { content: record.bzz, hasMath: false },
              { content: record.jyy, hasMath: false }
            ])
            currentRowIndex++
          } else {
            // 多条记录，需要纵向合并 check_name 列
            const startRowForMerge = currentRowIndex

            // 插入每条记录作为单独的行
            records.forEach((record, index) => {
              insertRows.push([
                { content: index === 0 ? record.check_name : '', hasMath: false }, // 只在第一行显示 check_name
                { content: record.check_content, hasMath: false },
                { content: record.result, hasMath: false },
                { content: record.month_str.toString(), hasMath: false },
                { content: record.day_str.toString(), hasMath: false },
                { content: record.check_user_name, hasMath: false },
                { content: record.bzz, hasMath: false },
                { content: record.jyy, hasMath: false }
              ])
              currentRowIndex++
            })

            // 添加合并单元格配置（check_name 列纵向合并）
            // 注意：mergeCells 中的行索引应该是绝对行索引（包含表头）
            const headerRowCount = this.tableConfig && this.tableConfig.headers ? this.tableConfig.headers.length : 0
            mergeCells.push({
              startRow: startRowForMerge + headerRowCount,
              startCol: 0, // check_name 列
              endRow: currentRowIndex - 1 + headerRowCount,
              endCol: 0,
              content: records[0].check_name
            })
          }
        }

        // 构建插入数据
        const insertData = {
          cellRows: insertRows
        }

        const options = {
          clearExisting: false,
          validateData: true,
          startRow: this.tableData.length,
          mergeCells: mergeCells
        }

        const result = tableContainer.insertDataFromJSON(insertData, options)
        if (result.success) {
          this.tableData = tableContainer.dataRows
          this.showSaveResult(true, `批量合并插入成功，共插入 ${insertRows.length} 行记录`)
          // 清空选中状态
          this.clearSelection()
        } else {
          this.showSaveResult(false, result.message || '批量合并插入失败')
        }

      } catch (error) {
        console.error('批量合并插入失败:', error)
        this.showSaveResult(false, '批量合并插入失败')
      }
    },

    /**
     * 按 check_name 分组记录
     */
    groupRecordsByCheckName(records) {
      const groups = {}
      records.forEach(record => {
        const checkName = record.check_name
        if (!groups[checkName]) {
          groups[checkName] = []
        }
        groups[checkName].push(record)
      })
      return groups
    },

    /**
     * 清空选中状态
     */
    clearSelection() {
      this.selectedRecords = []
      this.selectAll = false
    },

    /**
     * 加载页面列表
     */
    async loadPageList(carId) {
      try {
        const response = await getDesignWordPagesByCarId(carId)
        if (response.code === 200 && response.data) {
          this.pageList = response.data.sort((a, b) => a.pageOrder - b.pageOrder)

          // 如果没有页面，创建默认页面
          if (this.pageList.length === 0) {
            this.pageList = [{
              id: null,
              carId: carId,
              pageName: '第1页',
              pageOrder: 1,
              totalPages: 1,
              isActive: 1
            }]
            this.currentPageOrder = 1
          } else {
            // 找到当前活动页面
            const activePage = this.pageList.find(page => page.isActive === 1)
            this.currentPageOrder = activePage ? activePage.pageOrder : this.pageList[0].pageOrder
          }
        }
      } catch (error) {
        console.error('加载页面列表失败:', error)
        // 创建默认页面
        this.pageList = [{
          id: null,
          carId: carId,
          pageName: '第1页',
          pageOrder: 1,
          totalPages: 1,
          isActive: 1
        }]
        this.currentPageOrder = 1
      }
    },

    /**
     * 加载当前页面数据
     */
    async loadCurrentPageData() {
      if (!this.searchForm.carId || !this.currentPageOrder) {
        return
      }

      try {
        const response = await getDesignWordByCarIdAndPage(this.searchForm.carId, this.currentPageOrder)
        if (response.code === 200 && response.data) {
          const designData = response.data
          this.currentDesignId = designData.id

          // 解析表格配置
          if (designData.tableConfig) {
            this.tableConfig = JSON.parse(designData.tableConfig)
            this.applyTableConfig()
          }

          // 解析表格数据
          if (designData.tableData) {
            const tableData = JSON.parse(designData.tableData)

            // 检查是否是新的完整格式（包含合并信息）
            let cellRowsData = null
            let mergeData = []

            if (tableData && typeof tableData === 'object' && tableData.cellRows) {
              // 新格式：包含完整信息的对象
              cellRowsData = tableData.cellRows
              mergeData = tableData.merges || []
              console.log('加载新格式数据，包含合并信息:', mergeData.length, '个合并单元格')
            } else if (tableData && Array.isArray(tableData)) {
              // 旧格式：直接是cellRows数组
              cellRowsData = tableData
              console.log('加载旧格式数据，无合并信息')
            }

            if (cellRowsData && Array.isArray(cellRowsData)) {
              // 检查数据格式，支持新旧格式
              let processedData = cellRowsData

              // 如果是cellRows格式（包含行高信息），转换为dataRows格式
              if (cellRowsData.length > 0 && cellRowsData[0].length > 0 &&
                  typeof cellRowsData[0][0] === 'object' && cellRowsData[0][0].hasOwnProperty('content')) {
                console.log('加载cellRows格式数据，包含行高信息')
                processedData = cellRowsData.map(row =>
                    row.map(cell => {
                      // 回显时优先展示originContent
                      const displayContent = cell.originContent || cell.content || ''
                      console.log('单元格数据回显:', {
                        原始content: cell.content,
                        originContent: cell.originContent,
                        最终显示: displayContent
                      })
                      return {
                        content: displayContent,
                        originContent: cell.originContent || cell.content || '',
                        isEditing: false,
                        originalContent: displayContent,
                        hasMath: cell.hasMath || false,
                        mathML: cell.mathML || null,
                        width: cell.width,
                        height: cell.height,
                        hasMultipleContent: cell.hasMultipleContent || false,
                        mathMLMap: cell.mathMLMap || null
                      }
                    })
                )
              } else {
                console.log('加载简单格式数据，转换为标准格式')
                // 旧格式，转换为标准格式
                processedData = cellRowsData.map(row =>
                    row.map(cellContent => ({
                      content: cellContent || '',
                      originContent: cellContent || '',
                      isEditing: false,
                      originalContent: cellContent || '',
                      hasMath: false
                    }))
                )
              }

              this.tableData = processedData

              // 使用insertDataFromJSON方法正确应用高度配置和合并信息
              const tableContainer = this.$refs.tableContainer
              if (tableContainer) {
                // 检查是否有高度信息需要应用
                const hasHeightInfo = cellRowsData.length > 0 && cellRowsData[0].length > 0 &&
                    typeof cellRowsData[0][0] === 'object' && cellRowsData[0][0].hasOwnProperty('height')

                if (hasHeightInfo || mergeData.length > 0) {
                  console.log('使用insertDataFromJSON方法应用高度配置和合并信息')
                  // 准备cellRows格式的数据，回显时优先使用originContent
                  const insertData = {
                    cellRows: cellRowsData.map(row =>
                        row.map(cell => {
                          // 回显时优先展示originContent
                          const displayContent = cell.originContent || cell.content || ''
                          return {
                            content: displayContent,
                            originContent: cell.originContent || cell.content || '',
                            hasMath: cell.hasMath || false,
                            mathML: cell.mathML || null,
                            hasMultipleContent: cell.hasMultipleContent || false,
                            mathMLMap: cell.mathMLMap || null,
                            width: cell.width,
                            height: cell.height
                          }
                        })
                    )
                  }

                  // 使用insertDataFromJSON方法插入数据，应用高度配置和合并信息
                  const result = tableContainer.insertDataFromJSON(insertData, {
                    clearExisting: true,
                    validateData: false,
                    mergeCells: mergeData // 应用合并单元格信息
                  })

                  if (result.success) {
                    console.log('高度配置和合并信息应用成功，合并单元格数量:', mergeData.length)
                  } else {
                    console.error('高度配置和合并信息应用失败:', result.message)
                    // 回退到直接设置dataRows
                    tableContainer.dataRows = processedData
                  }
                } else {
                  // 没有高度信息和合并信息，直接设置dataRows
                  tableContainer.dataRows = processedData
                }
              }
            }
          } else {
            // 没有数据，初始化空表格
            this.initializeEmptyTable()
          }
        } else {
          // 没有找到页面数据，初始化空表格
          this.initializeEmptyTable()
        }
      } catch (error) {
        console.error('加载当前页面数据失败:', error)
        this.initializeEmptyTable()
      }
    },

    /**
     * 初始化空表格
     */
    initializeEmptyTable() {
      // 创建完全空的表格（0行数据）
      this.tableData = []

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        // 直接设置为空数组，避免clearAllData自动添加空行
        tableContainer.dataRows = []

        // 清除所有合并单元格
        if (typeof tableContainer.clearAllMerges === 'function') {
          tableContainer.clearAllMerges()
        }

        // 重置内部状态
        if (tableContainer.internalRowHeights) {
          tableContainer.internalRowHeights = {}
        }

        console.log('空表格初始化完成，数据行数:', tableContainer.dataRows.length)
      }
    },


    /**
     * 保存表格数据
     */
    async saveTableData() {
      try {
        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showSaveResult(false, '表格组件未找到')
          return
        }

        // 获取完整的表格数据，包含行高信息和合并信息
        const fullTableData = tableContainer.getDataAsJSON({
          includeEmpty: true,
          includeMergeInfo: true
        })

        // 对表格数据进行转换处理（参考LaTeX转换方法）
        const processedCellRows = await this.processTableDataForSave(fullTableData.cellRows)

        // 构建完整的保存数据结构，包含合并信息
        const completeTableData = {
          cellRows: processedCellRows,
          merges: fullTableData.merges || [], // 保存合并单元格信息
          headerConfig: fullTableData.headerConfig,
          headerWidthConfig: fullTableData.headerWidthConfig,
          metadata: fullTableData.metadata
        }

        // 获取当前页面信息
        const currentPage = this.pageList.find(page => page.pageOrder === this.currentPageOrder)

        // 构建保存数据
        const saveData = {
          id: this.currentDesignId,
          carId: this.searchForm.carId,
          title: this.tableConfig.title || '检验记录表',
          tableConfig: JSON.stringify(this.tableConfig),
          tableData: JSON.stringify(completeTableData), // 保存完整的表格数据，包含合并信息
          status: 1,
          pageName: currentPage ? currentPage.pageName : `第${this.currentPageOrder}页`,
          pageOrder: this.currentPageOrder,
          totalPages: this.pageList.length,
          isActive: 1
        }

        console.log('保存的表格数据:', {
          tableConfig: this.tableConfig,
          originalCellRows: fullTableData.cellRows,
          processedCellRows: processedCellRows,
          merges: fullTableData.merges,
          rowCount: processedCellRows.length,
          pageInfo: {
            pageName: saveData.pageName,
            pageOrder: saveData.pageOrder,
            totalPages: saveData.totalPages
          }
        })

        const response = await saveOrUpdateDesignWord(saveData)
        if (response.code === 200) {
          this.showSaveResult(true, '表格保存成功')
          // 更新当前设计ID
          if (!this.currentDesignId && response.data) {
            this.currentDesignId = response.data
          }

          // 设置为活动页面
          await setActivePage(this.searchForm.carId, this.currentPageOrder)

          // 重新加载页面列表
          await this.loadPageList(this.searchForm.carId)

        } else {
          this.showSaveResult(false, response.msg || '表格保存失败')
        }

      } catch (error) {
        console.error('保存表格数据失败:', error)
        this.showSaveResult(false, '保存表格数据失败')
      }
    },

    /**
     * 切换页面
     */
    async switchPage(pageOrder) {
      if (pageOrder === this.currentPageOrder) {
        return
      }

      this.currentPageOrder = pageOrder
      await this.loadCurrentPageData()

      // 设置为活动页面
      try {
        await setActivePage(this.searchForm.carId, pageOrder)
      } catch (error) {
        console.error('设置活动页面失败:', error)
      }
    },

    /**
     * 添加新页面
     */
    async addNewPage() {
      if (!this.searchForm.carId) {
        this.showSaveResult(false, '请先选择车辆ID')
        return
      }

      const newPageOrder = this.pageList.length + 1
      const newPageName = `第${newPageOrder}页`

      // 添加到页面列表
      this.pageList.push({
        id: null,
        carId: this.searchForm.carId,
        pageName: newPageName,
        pageOrder: newPageOrder,
        totalPages: newPageOrder,
        isActive: 0
      })

      // 更新所有页面的总页数
      try {
        await updateTotalPages(this.searchForm.carId, newPageOrder)
      } catch (error) {
        console.error('更新总页数失败:', error)
      }

      // 切换到新页面
      this.currentPageOrder = newPageOrder
      this.currentDesignId = null
      this.initializeEmptyTable()

      this.showSaveResult(true, `已添加${newPageName}`)
    },

    /**
     * 删除页面
     */
    async deletePage(pageOrder) {
      if (this.pageList.length <= 1) {
        this.showSaveResult(false, '至少需要保留一个页面')
        return
      }

      if (!confirm(`确定要删除第${pageOrder}页吗？`)) {
        return
      }

      try {
        // 找到要删除的页面
        const pageToDelete = this.pageList.find(page => page.pageOrder === pageOrder)
        if (pageToDelete && pageToDelete.id) {
          // 如果页面已保存，从数据库删除
          // 这里需要调用删除接口，暂时跳过
          console.log('需要从数据库删除页面:', pageToDelete.id)
        }

        // 从页面列表中移除
        this.pageList = this.pageList.filter(page => page.pageOrder !== pageOrder)

        // 重新排序页面
        this.pageList.forEach((page, index) => {
          page.pageOrder = index + 1
          page.pageName = `第${index + 1}页`
        })

        // 如果删除的是当前页面，切换到第一页
        if (this.currentPageOrder === pageOrder) {
          this.currentPageOrder = 1
          await this.loadCurrentPageData()
        } else if (this.currentPageOrder > pageOrder) {
          // 如果删除的页面在当前页面之前，调整当前页面序号
          this.currentPageOrder -= 1
        }

        // 更新总页数
        await updateTotalPages(this.searchForm.carId, this.pageList.length)

        this.showSaveResult(true, '页面删除成功')

      } catch (error) {
        console.error('删除页面失败:', error)
        this.showSaveResult(false, '删除页面失败')
      }
    },

    /**
     * 处理表格数据用于保存（参考LaTeX转换方法）
     * @param {Array} cellRows - 原始单元格数据
     * @returns {Promise<Array>} 处理后的单元格数据
     */
    async processTableDataForSave(cellRows) {
      if (!Array.isArray(cellRows)) {
        return cellRows
      }

      console.log('开始处理表格数据进行转换...')
      const processedRows = []

      for (let rowIndex = 0; rowIndex < cellRows.length; rowIndex++) {
        const row = cellRows[rowIndex]
        const processedRow = []

        for (let cellIndex = 0; cellIndex < row.length; cellIndex++) {
          const cell = row[cellIndex]
          const processedCell = { ...cell }

          // 如果单元格有内容，进行转换处理
          if (cell.content && typeof cell.content === 'string') {
            try {
              // 检测是否包含数学公式
              if (mathFormulaUtils.containsMath(cell.content)) {
                console.log(`检测到数学公式内容 [${rowIndex},${cellIndex}]:`, cell.content)

                // 分离公式和普通文本
                const separationResult = mathFormulaUtils.separateFormulaAndText(cell.content)

                if (separationResult.hasFormula) {
                  console.log('分离结果:', separationResult)

                  // 处理每个公式，转换为MathML
                  const mathMLMap = new Map()

                  for (const formula of separationResult.formulas) {
                    try {
                      const mathML = await mathFormulaUtils.latexToMathML(formula.content)
                      if (mathML) {
                        mathMLMap.set(formula.placeholder, mathML)
                        console.log(`公式 ${formula.placeholder} 转换成功`)
                      } else {
                        console.warn(`公式 ${formula.placeholder} 转换失败`)
                      }
                    } catch (error) {
                      console.error(`处理公式 ${formula.placeholder} 失败:`, error)
                    }
                  }

                  // 设置处理结果
                  processedCell.hasMath = true
                  processedCell.hasMultipleContent = separationResult.formulas.length > 1 ||
                      separationResult.processedContent.replace(/__MATH_FORMULA_\d+__/g, '').trim().length > 0
                  processedCell.content = separationResult.processedContent // 包含占位符的内容
                  processedCell.originContent = cell.content // 保存原始内容
                  processedCell.mathMLMap = Object.fromEntries(mathMLMap) // 占位符到MathML的映射
                  processedCell.formulas = separationResult.formulas // 公式信息

                  console.log(`单元格 [${rowIndex},${cellIndex}] 转换完成:`, {
                    content: processedCell.content,
                    mathMLMap: processedCell.mathMLMap,
                    hasMultipleContent: processedCell.hasMultipleContent
                  })
                } else if (mathFormulaUtils.containsMath(cell.content)) {
                  // 纯公式内容的处理（保持原有逻辑兼容性）
                  console.log(`检测到纯公式内容 [${rowIndex},${cellIndex}]:`, cell.content)

                  const mathML = await mathFormulaUtils.latexToMathML(cell.content)

                  if (mathML) {
                    processedCell.hasMath = true
                    processedCell.mathML = mathML
                    processedCell.originContent = cell.content // 保存原始内容
                    console.log(`纯公式 [${rowIndex},${cellIndex}] 转换成功`)
                  } else {
                    console.warn(`纯公式 [${rowIndex},${cellIndex}] 转换失败，保持原有标记`)
                    processedCell.hasMath = true
                    processedCell.originContent = cell.content
                  }
                }
              } else {
                // 普通文本内容，保存原始内容
                processedCell.originContent = cell.content
              }

            } catch (error) {
              console.error(`处理单元格 [${rowIndex},${cellIndex}] 失败:`, error)
              // 出错时保持原始数据
              processedCell.originContent = cell.content
            }
          }

          processedRow.push(processedCell)
        }

        processedRows.push(processedRow)
      }

      console.log('表格数据转换处理完成，处理行数:', processedRows.length)
      return processedRows
    },

    /**
     * 清空表格数据
     */
    clearTableData() {
      if (!confirm('确定要清空表格数据吗？此操作不可撤销。')) {
        return
      }

      // 创建完全空的表格（0行数据）
      this.tableData = []

      const tableContainer = this.$refs.tableContainer
      if (tableContainer) {
        // 直接设置为空数组，避免clearAllData自动添加空行
        tableContainer.dataRows = []

        // 清除所有合并单元格
        if (typeof tableContainer.clearAllMerges === 'function') {
          tableContainer.clearAllMerges()
        }

        // 重置内部状态
        if (tableContainer.internalRowHeights) {
          tableContainer.internalRowHeights = {}
        }

        console.log('表格已清空，数据行数:', tableContainer.dataRows.length)
      }

      this.showSaveResult(true, '表格已清空')
    },

    /**
     * 显示保存结果
     */
    showSaveResult(success, message) {
      this.saveResult = { success, message }
      setTimeout(() => {
        this.saveResult = null
      }, 3000)
    },

    /**
     * 处理数据插入事件
     */
    handleDataInserted(event) {
      console.log('数据插入事件:', event)
    },

    /**
     * 处理表格更新事件
     */
    handleTableUpdated() {
      console.log('表格更新事件')
    },

    /**
     * 重命名页面
     */
    renamePage() {
      const currentPage = this.pageList.find(page => page.pageOrder === this.currentPageOrder)
      this.newPageName = currentPage ? currentPage.pageName : `第${this.currentPageOrder}页`
      this.showRenameDialog = true

      this.$nextTick(() => {
        if (this.$refs.renameInput) {
          this.$refs.renameInput.focus()
          this.$refs.renameInput.select()
        }
      })
    },

    /**
     * 确认重命名
     */
    async confirmRename() {
      if (!this.newPageName.trim()) {
        this.showSaveResult(false, '页面名称不能为空')
        return
      }

      // 更新页面列表中的名称
      const currentPage = this.pageList.find(page => page.pageOrder === this.currentPageOrder)
      if (currentPage) {
        currentPage.pageName = this.newPageName.trim()
        this.showSaveResult(true, '页面重命名成功')
      }

      this.closeRenameDialog()
    },

    /**
     * 关闭重命名弹窗
     */
    closeRenameDialog() {
      this.showRenameDialog = false
      this.newPageName = ''
    },

    /**
     * 页面排序
     */
    sortPages() {
      this.sortablePages = [...this.pageList]
      this.showSortDialog = true
    },

    /**
     * 拖拽开始
     */
    handleDragStart(index) {
      this.draggedIndex = index
    },

    /**
     * 拖拽放置
     */
    handleDrop(index) {
      if (this.draggedIndex === -1 || this.draggedIndex === index) {
        return
      }

      // 交换位置
      const draggedItem = this.sortablePages[this.draggedIndex]
      this.sortablePages.splice(this.draggedIndex, 1)
      this.sortablePages.splice(index, 0, draggedItem)

      // 重新设置页面顺序
      this.sortablePages.forEach((page, idx) => {
        page.pageOrder = idx + 1
        page.pageName = `第${idx + 1}页`
      })

      this.draggedIndex = -1
    },

    /**
     * 确认排序
     */
    async confirmSort() {
      try {
        // 保存排序后的页面列表
        this.pageList = [...this.sortablePages]

        // 批量更新页面顺序到数据库
        const updatePromises = this.pageList.map(page => {
          if (page.id) {
            // 如果页面已存在，更新其顺序
            return saveOrUpdateDesignWord({
              id: page.id,
              carId: page.carId,
              pageName: page.pageName,
              pageOrder: page.pageOrder,
              totalPages: this.pageList.length,
              isActive: page.isActive,
              status: 1
            })
          }
          return Promise.resolve()
        })

        await Promise.all(updatePromises)

        // 调整当前页面顺序
        const oldCurrentPageOrder = this.currentPageOrder
        const newCurrentPage = this.pageList.find(page =>
            page.pageName === `第${oldCurrentPageOrder}页` ||
            page.pageOrder === oldCurrentPageOrder
        )

        if (newCurrentPage) {
          this.currentPageOrder = newCurrentPage.pageOrder
        }

        this.showSaveResult(true, '页面排序成功')
        this.closeSortDialog()

      } catch (error) {
        console.error('保存页面排序失败:', error)
        this.showSaveResult(false, '页面排序保存失败')
      }
    },

    /**
     * 关闭排序弹窗
     */
    closeSortDialog() {
      this.showSortDialog = false
      this.sortablePages = []
      this.draggedIndex = -1
    }
  }
}
</script>

<style scoped>
.check-record-config {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.config-container {
  display: flex;
  gap: 20px;
  height: calc(100vh - 40px);
}

/* 左侧面板 */
.left-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.search-section {
  margin-bottom: 20px;
}

.search-section h3 {
  margin-bottom: 15px;
  color: #333;
  font-size: 18px;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item label {
  min-width: 80px;
  font-weight: 500;
  color: #333;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-actions {
  display: flex;
  gap: 10px;
}

.search-btn, .reset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.search-btn {
  background: #007bff;
  color: white;
}

.search-btn:hover {
  background: #0056b3;
}

.reset-btn {
  background: #6c757d;
  color: white;
}

.reset-btn:hover {
  background: #545b62;
}

/* 数据区域 */
.data-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.data-header h4 {
  margin: 0;
  color: #333;
  font-size: 16px;
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.data-table {
  flex: 1;
  overflow: auto;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.data-table table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.data-table th,
.data-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background: #f8f9fa;
  font-weight: 500;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 1;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.data-table tr.selected-row {
  background: #e3f2fd;
}

.data-table tr.selected-row:hover {
  background: #bbdefb;
}

.checkbox-column {
  width: 40px;
  text-align: center;
}

.select-all-checkbox,
.record-checkbox {
  cursor: pointer;
  transform: scale(1.2);
}

.insert-btn {
  padding: 4px 8px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s ease;
}

.insert-btn:hover {
  background: #218838;
}

/* 批量操作按钮样式 */
.batch-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

.batch-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.batch-insert-btn {
  background: #007bff;
  color: white;
}

.batch-insert-btn:hover {
  background: #0056b3;
}

.batch-merge-btn {
  background: #17a2b8;
  color: white;
}

.batch-merge-btn:hover {
  background: #138496;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 15px;
  padding: 15px 0;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 右侧面板 */
.right-panel {
  flex: 1;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.table-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.table-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.table-actions {
  display: flex;
  gap: 10px;
}

.save-btn, .clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.save-btn {
  background: #28a745;
  color: white;
}

.save-btn:hover {
  background: #218838;
}

.clear-btn {
  background: #dc3545;
  color: white;
}

.clear-btn:hover {
  background: #c82333;
}

.table-container {
  flex: 1;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

/* 页面管理样式 */
.page-management {
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.page-tabs {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.page-tab {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
  transition: all 0.2s ease;
  user-select: none;
}

.page-tab:hover {
  background: #f0f0f0;
  border-color: #007bff;
}

.page-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.page-tab .page-name {
  font-weight: 500;
}

.delete-page-btn {
  background: none;
  border: none;
  color: #dc3545;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.delete-page-btn:hover {
  background: #dc3545;
  color: white;
}

.page-tab.active .delete-page-btn {
  color: rgba(255, 255, 255, 0.8);
}

.page-tab.active .delete-page-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.add-page-btn {
  padding: 6px 10px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
}

.add-page-btn:hover {
  background: #218838;
}

.page-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-info {
  color: #666;
  font-size: 13px;
}

.page-controls {
  display: flex;
  gap: 8px;
}

.rename-btn, .sort-btn {
  padding: 4px 8px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.rename-btn:hover, .sort-btn:hover {
  background: #545b62;
}

/* 保存结果提示 */
.save-result {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-size: 14px;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.save-result.success {
  background: #28a745;
}

.save-result.error {
  background: #dc3545;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 弹窗样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.dialog-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  min-width: 400px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog-content h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
}

.dialog-content .form-item {
  margin-bottom: 15px;
}

.dialog-content .form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.rename-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.rename-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.dialog-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.confirm-btn, .cancel-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.confirm-btn {
  background: #007bff;
  color: white;
}

.confirm-btn:hover {
  background: #0056b3;
}

.cancel-btn {
  background: #6c757d;
  color: white;
}

.cancel-btn:hover {
  background: #545b62;
}

/* 排序列表样式 */
.sort-list {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.sort-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  margin-bottom: 5px;
  cursor: move;
  transition: all 0.2s ease;
}

.sort-item:hover {
  background: #e9ecef;
  border-color: #007bff;
}

.sort-item:last-child {
  margin-bottom: 0;
}

.drag-handle {
  color: #6c757d;
  font-size: 16px;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.sort-item .page-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.sort-item .page-order {
  color: #666;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .config-container {
    flex-direction: column;
    height: auto;
  }

  .left-panel, .right-panel {
    flex: none;
    height: auto;
  }

  .data-table {
    max-height: 400px;
  }

  .table-container {
    height: 500px;
  }
}
</style>
