<template>
  <div class="device-import-container">
    <!-- 文件上传卡片 -->
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">设备数据导入</span>
      </div>

      <div class="upload-section">
        <el-upload
          ref="upload"
          class="upload-demo"
          action="#"
          drag
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".zip"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将ZIP文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">
            只能上传ZIP格式的导出文件，且不超过100MB
          </div>
        </el-upload>

        <div class="upload-actions">
          <el-button type="primary" @click="submitUpload" :loading="uploading">
            <i class="el-icon-upload2"></i>
            上传文件
          </el-button>
          <el-button @click="clearFiles">清空</el-button>
        </div>
      </div>
    </el-card>

    <!-- 验证结果卡片 -->
    <el-card v-if="validationResult" class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span class="card-title">数据验证结果</span>
        <el-tag :type="getValidationStatusType(validationResult.status)" style="float: right;">
          {{ getValidationStatusText(validationResult.status) }}
        </el-tag>
      </div>

      <div v-if="validationResult.status === 1" class="validation-success" style="min-height: 400px;">
        <!-- 导入统计 -->
        <div v-if="validationResult.statistics" class="import-statistics">
          <h4>导入预览</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ validationResult.statistics.totalDeviceCount || 0 }}</div>
                <div class="stat-label">设备数量</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ validationResult.statistics.templateBindingImportCount || 0 }}</div>
                <div class="stat-label">模板绑定</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ formatNumber(validationResult.statistics.basicFieldImportCount) }}</div>
                <div class="stat-label">基础字段</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-value">{{ formatNumber(validationResult.statistics.tableDataRowImportCount) }}</div>
                <div class="stat-label">表格数据行</div>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 导入配置 -->
        <div class="import-config">
          <h4>导入配置</h4>
          <el-form ref="importForm" :model="importForm" :rules="importRules" label-width="150px">
            <el-form-item label="导入模式" prop="importMode">
              <el-radio-group v-model="importForm.importMode">
                <el-radio label="REPLACE">完全替换</el-radio>
                <el-radio label="MERGE">合并导入</el-radio>
              </el-radio-group>
              <div class="form-tip">
                完全替换：删除现有数据后导入；合并导入：保留现有数据，仅导入新数据
              </div>
            </el-form-item>

            <el-form-item label="冲突处理策略" prop="conflictStrategy">
              <el-radio-group v-model="importForm.conflictStrategy">
                <el-radio label="SKIP">跳过冲突项</el-radio>
                <el-radio label="OVERRIDE">覆盖冲突项</el-radio>
                <el-radio label="ERROR">遇到冲突报错</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="覆盖选项">
              <el-checkbox-group v-model="overrideOptions">
                <el-checkbox label="overrideDeviceConfig">设备配置</el-checkbox>
                <el-checkbox label="overrideTemplateBinding">模板绑定</el-checkbox>
                <el-checkbox label="overrideDetectionData">检测数据</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="备注">
              <el-input
                v-model="importForm.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入导入备注"
                maxlength="500"
                show-word-limit
                class="remark-input"
              />
            </el-form-item>

            <el-form-item style="margin-top: 30px;">
              <el-button type="primary" @click="startImport" :loading="importing">
                <i class="el-icon-upload"></i>
                开始导入
              </el-button>
              <el-button @click="resetImportForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <div v-else-if="validationResult.status === 2" class="validation-error">
        <el-alert
          title="验证失败"
          :description="validationResult.errorMessage"
          type="error"
          :closable="false"
          show-icon
        />
      </div>

      <div v-else class="validation-progress">
        <el-progress
          :percentage="validationResult.progress || 0"
          :status="validationResult.status === 2 ? 'exception' : null"
        />
        <div class="progress-text">
          {{ validationResult.currentStep || '验证中...' }}
        </div>
      </div>
    </el-card>

    <!-- 导入进度对话框 -->
    <import-progress-dialog
      :visible.sync="progressVisible"
      :task-id="currentTaskId"
      @close="handleProgressClose"
    />


  </div>
</template>

<script>
import ImportProgressDialog from './components/ImportProgressDialog'
import {
  uploadImportFile,
  validateImportData,
  executeImport,
  cancelDeviceImport
} from '@/api/device/import'

export default {
  name: 'DeviceImport',
  components: {
    ImportProgressDialog
  },
  data() {
    return {
      // 上传相关
      fileList: [],
      uploading: false,

      // 验证结果
      validationResult: null,
      validationPolling: null,

      // 导入表单
      importForm: {
        taskId: null,
        importMode: 'REPLACE',
        conflictStrategy: 'OVERRIDE',
        remark: ''
      },
      overrideOptions: ['overrideDeviceConfig', 'overrideTemplateBinding', 'overrideDetectionData'],
      importRules: {
        importMode: [
          { required: true, message: '请选择导入模式', trigger: 'change' }
        ],
        conflictStrategy: [
          { required: true, message: '请选择冲突处理策略', trigger: 'change' }
        ]
      },

      // 导入状态
      importing: false,
      progressVisible: false,
      currentTaskId: null
    }
  },
  mounted() {
    // 页面初始化
  },
  beforeDestroy() {
    this.stopValidationPolling()
  },
  methods: {
    // 文件上传前检查
    beforeUpload(file) {
      const isZip = file.type === 'application/zip' || file.name.toLowerCase().endsWith('.zip')
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isZip) {
        this.$message.error('只能上传ZIP格式的文件!')
        return false
      }
      if (!isLt100M) {
        this.$message.error('上传文件大小不能超过 100MB!')
        return false
      }
      return true
    },

    // 文件选择变化处理
    handleFileChange(file, fileList) {
      this.fileList = fileList
    },

    // 提交上传
    async submitUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择要上传的文件')
        return
      }

      const file = this.fileList[0].raw || this.fileList[0]
      console.log('准备上传文件:', file.name, '大小:', file.size)

      const formData = new FormData()
      formData.append('file', file)

      this.uploading = true
      try {
        console.log('开始上传文件...')
        const response = await uploadImportFile(formData)
        console.log('上传完成，响应:', response)
        await this.handleUploadSuccess(response, file, this.fileList)
      } catch (error) {
        console.error('上传过程中出错:', error)
        this.handleUploadError(error, file, this.fileList)
      }
    },

    // 上传成功
    async handleUploadSuccess(response, file, fileList) {
      this.uploading = false
      console.log('上传响应:', response)

      if (response.code === 200) {
        this.$message.success('文件上传成功，正在验证数据...')
        this.importForm.taskId = response.data
        console.log('设置任务ID:', this.importForm.taskId)

        // 确保taskId存在后再开始验证轮询
        if (this.importForm.taskId) {
          this.startValidationPolling()
        } else {
          console.error('任务ID为空，无法开始验证')
          this.$message.error('获取任务ID失败，请重新上传文件')
        }
      } else {
        console.error('上传失败响应:', response)
        this.$message.error(response.msg || '文件上传失败')
      }
    },

    // 上传失败
    handleUploadError(err, file, fileList) {
      this.uploading = false
      console.error('上传失败详情:', err)

      let errorMessage = '文件上传失败'
      if (err.response && err.response.data) {
        if (err.response.data.msg) {
          errorMessage = err.response.data.msg
        } else if (err.response.data.message) {
          errorMessage = err.response.data.message
        }
      } else if (err.message) {
        errorMessage = err.message
      }

      this.$message.error(errorMessage)
    },

    // 清空文件
    clearFiles() {
      this.fileList = []
      this.validationResult = null
      this.importForm.taskId = null
      this.stopValidationPolling()
    },

    // 开始验证轮询
    startValidationPolling() {
      console.log('开始验证轮询，任务ID:', this.importForm.taskId)
      this.stopValidationPolling()

      if (!this.importForm.taskId) {
        console.error('任务ID为空，无法开始验证轮询')
        return
      }

      this.validationPolling = setInterval(async () => {
        try {
          console.log('执行验证轮询，任务ID:', this.importForm.taskId)
          const response = await validateImportData(this.importForm.taskId)
          console.log('验证响应:', response)

          if (response.code === 200) {
            this.validationResult = response.data
            console.log('验证结果:', this.validationResult)

            // 如果验证完成（成功或失败），停止轮询
            if (response.data.status === 1 || response.data.status === 2) {
              console.log('验证完成，状态:', response.data.status)
              this.stopValidationPolling()
            }
          } else {
            console.error('验证接口返回错误:', response)
            this.stopValidationPolling()
            this.$message.error(response.msg || '验证失败')
          }
        } catch (error) {
          console.error('验证轮询失败:', error)
          this.stopValidationPolling()
          this.$message.error('验证过程中发生错误: ' + (error.message || error))
        }
      }, 2000) // 每2秒轮询一次
    },

    // 停止验证轮询
    stopValidationPolling() {
      if (this.validationPolling) {
        clearInterval(this.validationPolling)
        this.validationPolling = null
      }
    },

    // 获取验证状态类型
    getValidationStatusType(status) {
      switch (status) {
        case 0: return 'info'    // 验证中
        case 1: return 'success' // 验证成功
        case 2: return 'danger'  // 验证失败
        default: return 'info'
      }
    },

    // 获取验证状态文本
    getValidationStatusText(status) {
      switch (status) {
        case 0: return '验证中'
        case 1: return '验证成功'
        case 2: return '验证失败'
        default: return '未知状态'
      }
    },

    // 格式化数字
    formatNumber(num) {
      if (num === null || num === undefined) return 0
      return num.toLocaleString()
    },

    // 开始导入
    async startImport() {
      if (!this.importForm.taskId) {
        this.$message.error('请先上传文件并完成验证')
        return
      }

      this.$refs.importForm.validate(async (valid) => {
        if (valid) {
          try {
            this.importing = true

            const importRequest = {
              taskId: this.importForm.taskId,
              importMode: this.importForm.importMode,
              conflictStrategy: this.importForm.conflictStrategy,
              overrideDeviceConfig: this.overrideOptions.includes('overrideDeviceConfig'),
              overrideTemplateBinding: this.overrideOptions.includes('overrideTemplateBinding'),
              overrideDetectionData: this.overrideOptions.includes('overrideDetectionData'),
              remark: this.importForm.remark
            }

            const response = await executeImport(importRequest)
            if (response.code === 200) {
              this.$message.success('导入任务已启动')
              this.currentTaskId = this.importForm.taskId
              this.progressVisible = true
            } else {
              this.$message.error(response.msg || '启动导入失败')
            }
          } catch (error) {
            console.error('启动导入失败:', error)
            this.$message.error('启动导入失败')
          } finally {
            this.importing = false
          }
        }
      })
    },

    // 重置导入表单
    resetImportForm() {
      this.importForm = {
        taskId: this.importForm.taskId, // 保留taskId
        importMode: 'REPLACE',
        conflictStrategy: 'OVERRIDE',
        remark: ''
      }
      this.overrideOptions = ['overrideDeviceConfig', 'overrideTemplateBinding', 'overrideDetectionData']
      this.$refs.importForm.clearValidate()
    },

    // 处理进度对话框关闭
    handleProgressClose() {
      this.progressVisible = false
      this.currentTaskId = null
      // 可以选择刷新页面或重置表单
      this.clearFiles()
    }
  }
}
</script>

<style scoped>
.device-import-container {
  padding: 20px;
  height: 100%;
  overflow: auto;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.upload-section {
  text-align: center;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  padding: 30px;
  border-radius: 8px;
  color: white;
}

.upload-section /deep/ .el-upload-dragger {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 2px dashed rgba(255, 255, 255, 0.3) !important;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.upload-section /deep/ .el-upload-dragger:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
}

.upload-section /deep/ .el-upload-dragger .el-icon-upload {
  color: white;
  font-size: 48px;
  margin-bottom: 10px;
}

.upload-section /deep/ .el-upload-dragger .el-upload__text {
  color: white;
  font-size: 16px;
}

.upload-section /deep/ .el-upload-dragger .el-upload__text em {
  color: #90EE90;
  font-weight: bold;
}

::v-deep .upload-section .el-upload__tip {
  color: #d0d9e9;
}

::v-deep .upload-section .el-upload-list__item-name {
  color: #b7bcc5;
}
::v-deep .upload-section .el-upload-list__item-name .el-icon-document {
  color: #b7bcc5;
}

.upload-actions {
  margin-top: 20px;
}

.upload-actions .el-button {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  border: none;
  color: white;
  font-weight: 500;
}

.upload-actions .el-button:hover {
  background: linear-gradient(180deg, #1557A8, #0F4A9B);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.import-config {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.import-statistics {
  margin-bottom: 30px;
  background: linear-gradient(180deg, #124B9A, #0D438D);
  padding: 20px;
  border-radius: 8px;
}

.import-statistics h4,
.import-config h4 {
  margin: 0 0 15px 0;
  color: #ffffff;
  font-weight: 600;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: linear-gradient(135deg, #2a63b1, #164e99);
  border-radius: 8px;
  color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  margin-bottom: 5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

.import-config h4 {
  margin: 0 0 20px 0;
  color: #ffffff;
  font-weight: 600;
}

.form-tip {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 5px;
  font-weight: 500;
}

::v-deep .remark-input .el-input__count {
  background: linear-gradient(180deg, #124B9A, #0D438D);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
}

.validation-success {
  padding: 10px 0;
}

.validation-progress {
  padding: 20px 0;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}
</style>
