<template>
  <div class="import-history-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span class="card-title">导入历史记录</span>
        <div style="float: right;">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-refresh"
            @click="refreshHistory"
          >
            刷新
          </el-button>
          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete"
            @click="cleanupTempFiles"
          >
            清理临时文件
          </el-button>
        </div>
      </div>

      <!-- 搜索条件 -->
      <div class="search-form">
        <el-form :model="searchForm" inline size="small">
          <el-form-item label="任务ID">
            <el-input
              v-model="searchForm.taskId"
              placeholder="请输入任务ID"
              clearable
              style="width: 200px"
            />
          </el-form-item>
          <el-form-item label="导入状态">
            <el-select
              v-model="searchForm.importStatus"
              placeholder="请选择状态"
              clearable
              style="width: 120px"
            >
              <el-option label="进行中" :value="0" />
              <el-option label="成功" :value="1" />
              <el-option label="失败" :value="2" />
              <el-option label="部分成功" :value="3" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">
              搜索
            </el-button>
            <el-button icon="el-icon-refresh" @click="resetSearch">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 历史记录表格 -->
      <el-table
        v-loading="loading"
        :data="historyList"
        border
        stripe
        style="width: 100%"
      >
        <el-table-column prop="taskId" label="任务ID" width="200" show-overflow-tooltip />
        <el-table-column label="导入状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.importStatus)">
              {{ getStatusText(scope.row.importStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
        <el-table-column label="文件大小" width="100" align="center">
          <template slot-scope="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column prop="importedDeviceCount" label="导入设备数" width="120" align="center" />
        <el-table-column prop="importedDetectionDataCount" label="导入数据数" width="120" align="center">
          <template slot-scope="scope">
            {{ formatNumber(scope.row.importedDetectionDataCount) }}
          </template>
        </el-table-column>
        <el-table-column prop="progress" label="进度" width="80" align="center">
          <template slot-scope="scope">
            {{ scope.row.progress || 0 }}%
          </template>
        </el-table-column>
        <el-table-column prop="currentStep" label="当前步骤" min-width="150" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="completeTime" label="完成时间" width="160">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.completeTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.importStatus === 0"
              size="mini"
              type="info"
              @click="viewProgress(scope.row)"
            >
              <i class="el-icon-view"></i>
              查看进度
            </el-button>
            <el-button
              v-if="scope.row.importStatus === 0"
              size="mini"
              type="danger"
              @click="cancelImport(scope.row)"
            >
              <i class="el-icon-close"></i>
              取消
            </el-button>
            <el-button
              size="mini"
              type="info"
              @click="viewDetail(scope.row)"
            >
              <i class="el-icon-info"></i>
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <lt-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="handlePagination"
      />
    </el-card>

    <!-- 进度对话框 -->
    <import-progress-dialog
      :visible.sync="progressVisible"
      :task-id="currentTaskId"
      @close="handleProgressClose"
    />

    <!-- 详情对话框 -->
    <import-detail-dialog
      :visible.sync="detailVisible"
      :import-log="currentImportLog"
    />
  </div>
</template>

<script>
import { getImportHistory, cancelDeviceImport, cleanupTempImportFiles } from '@/api/device/import'
import ImportProgressDialog from '../import/components/ImportProgressDialog'
import ImportDetailDialog from './components/ImportDetailDialog'
import ltPagination from '@/components/lt-pagination/lt-pagination.vue'

export default {
  name: 'ImportHistory',
  components: {
    ImportProgressDialog,
    ImportDetailDialog,
    ltPagination
  },
  data() {
    return {
      // 搜索表单
      searchForm: {
        taskId: '',
        importStatus: null,
        dateRange: []
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        taskId: null,
        importStatus: null,
        startTime: null,
        endTime: null
      },
      // 表格数据
      historyList: [],
      total: 0,
      loading: false,
      // 对话框
      progressVisible: false,
      detailVisible: false,
      currentTaskId: null,
      currentImportLog: null
    }
  },
  mounted() {
    this.getHistoryList()
  },
  methods: {
    // 获取历史记录列表
    async getHistoryList() {
      this.loading = true
      try {
        const response = await getImportHistory(this.queryParams)
        if (response.code === 200) {
          this.historyList = response.data.records || []
          this.total = response.data.total || 0
        } else {
          this.$message.error(response.msg || '获取导入历史失败')
        }
      } catch (error) {
        console.error('获取导入历史失败:', error)
        this.$message.error('获取导入历史失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.queryParams.pageNum = 1
      this.queryParams.taskId = this.searchForm.taskId || null
      this.queryParams.importStatus = this.searchForm.importStatus

      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        this.queryParams.startTime = this.searchForm.dateRange[0]
        this.queryParams.endTime = this.searchForm.dateRange[1]
      } else {
        this.queryParams.startTime = null
        this.queryParams.endTime = null
      }

      this.getHistoryList()
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        taskId: '',
        importStatus: null,
        dateRange: []
      }
      this.queryParams = {
        pageNum: 1,
        pageSize: 20,
        taskId: null,
        importStatus: null,
        startTime: null,
        endTime: null
      }
      this.getHistoryList()
    },

    // 刷新历史
    refreshHistory() {
      this.getHistoryList()
    },

    // 分页处理
    handlePagination({ page, limit }) {
      this.queryParams.pageNum = page
      this.queryParams.pageSize = limit
      this.getHistoryList()
    },

    // 查看进度
    viewProgress(row) {
      this.currentTaskId = row.taskId
      this.progressVisible = true
    },

    // 处理进度对话框关闭
    handleProgressClose() {
      this.progressVisible = false
      this.currentTaskId = null
      this.getHistoryList() // 刷新列表
    },

    // 取消导入
    async cancelImport(row) {
      try {
        await this.$confirm('确定要取消这个导入任务吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await cancelDeviceImport(row.taskId)
        if (response.code === 200) {
          this.$message.success('导入任务已取消')
          this.getHistoryList()
        } else {
          this.$message.error(response.msg || '取消导入任务失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消导入失败:', error)
          this.$message.error('取消导入失败')
        }
      }
    },

    // 查看详情
    viewDetail(row) {
      this.currentImportLog = row
      this.detailVisible = true
    },

    // 清理临时文件
    async cleanupTempFiles() {
      try {
        await this.$confirm('确定要清理临时导入文件吗？（默认清理3天前的文件）', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await cleanupTempImportFiles(3)
        if (response.code === 200) {
          this.$message.success(`清理完成，共清理 ${response.data} 个临时文件`)
          this.getHistoryList()
        } else {
          this.$message.error(response.msg || '清理临时文件失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('清理临时文件失败:', error)
          this.$message.error('清理临时文件失败')
        }
      }
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return `${size.toFixed(2)} ${units[index]}`
    },

    // 格式化数字
    formatNumber(num) {
      if (!num) return '0'
      return num.toLocaleString()
    },

    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        0: 'info',    // 进行中
        1: 'success', // 成功
        2: 'danger',  // 失败
        3: 'warning'  // 部分成功
      }
      return statusMap[status] || 'info'
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '进行中',
        1: '成功',
        2: '失败',
        3: '部分成功'
      }
      return statusMap[status] || '未知'
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return '-'
      return new Date(dateTime).toLocaleString()
    }
  }
}
</script>

<style scoped>
.import-history-container {
  padding: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 4px;
}


</style>
