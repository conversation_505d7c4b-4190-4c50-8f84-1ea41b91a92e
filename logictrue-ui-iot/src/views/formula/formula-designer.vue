<template>
  <div class="formula-designer">
    <div class="designer-header">
      <h2>快速公式设计器</h2>
      <p>设计日常常用的数学公式，自动生成LaTeX格式字符串</p>
    </div>

    <div class="designer-content">
      <!-- 左侧：公式模板库 -->
      <div class="template-panel">
        <div class="panel-header">
          <h3>公式模板库</h3>
        </div>
        <div class="template-categories">
          <div
            v-for="category in templateCategories"
            :key="category.id"
            class="category-item"
            :class="{ active: selectedCategory === category.id }"
            @click="selectCategory(category.id)"
          >
            <i :class="category.icon"></i>
            <span>{{ category.name }}</span>
          </div>
        </div>
        <div class="template-list">
          <div
            v-for="template in filteredTemplates"
            :key="template.id"
            class="template-item"
            @click="selectTemplate(template)"
          >
            <div class="template-preview" v-html="template.preview"></div>
            <div class="template-info">
              <div class="template-name">{{ template.name }}</div>
              <div class="template-desc">{{ template.description }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：公式构建区域 -->
      <div class="builder-panel">
        <div class="panel-header">
          <h3>公式构建器</h3>
          <div class="builder-actions">
            <el-button size="small" @click="clearFormula">清空</el-button>
            <el-button size="small" type="primary" @click="buildFormula">构建公式</el-button>
          </div>
        </div>

        <!-- 公式元素工具栏 -->
        <div class="formula-toolbar">
          <div class="toolbar-group">
            <span class="group-label">基础运算：</span>
            <el-button
              v-for="op in basicOperators"
              :key="op.symbol"
              size="mini"
              @click="addElement(op)"
              :title="op.name"
            >
              {{ op.symbol }}
            </el-button>
          </div>
          <div class="toolbar-group">
            <span class="group-label">函数：</span>
            <el-button
              v-for="func in functions"
              :key="func.latex"
              size="mini"
              @click="addElement(func)"
              :title="func.name"
            >
              {{ func.display }}
            </el-button>
          </div>
          <div class="toolbar-group">
            <span class="group-label">符号：</span>
            <el-button
              v-for="symbol in symbols"
              :key="symbol.latex"
              size="mini"
              @click="addElement(symbol)"
              :title="symbol.name"
            >
              {{ symbol.display }}
            </el-button>
          </div>
        </div>

        <!-- 公式构建区 -->
        <div class="formula-builder">
          <div class="builder-input">
            <el-input
              v-model="formulaInput"
              type="textarea"
              :rows="4"
              placeholder="在此输入或构建您的公式... (支持快捷键: Ctrl+Z撤销, Ctrl+Y重做)"
              @input="updatePreview"
              @keydown="handleKeydown"
              ref="formulaTextarea"
            ></el-input>
            <div class="input-hints">
              <div class="hint-item">
                <span class="hint-key">Ctrl + Z</span>
                <span class="hint-desc">撤销</span>
              </div>
              <div class="hint-item">
                <span class="hint-key">Ctrl + Y</span>
                <span class="hint-desc">重做</span>
              </div>
              <div class="hint-item">
                <span class="hint-key">Tab</span>
                <span class="hint-desc">跳转到下一个占位符</span>
              </div>
            </div>
          </div>

          <!-- 变量定义区 -->
          <div class="variables-section">
            <h4>变量定义</h4>
            <div class="variables-list">
              <div
                v-for="(variable, index) in variables"
                :key="index"
                class="variable-item"
              >
                <el-input
                  v-model="variable.name"
                  placeholder="变量名"
                  size="small"
                  style="width: 80px;"
                  @input="updatePreview"
                ></el-input>
                <span>=</span>
                <el-input
                  v-model="variable.description"
                  placeholder="变量描述"
                  size="small"
                  style="width: 150px;"
                  @input="updatePreview"
                ></el-input>
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="removeVariable(index)"
                ></el-button>
              </div>
              <el-button
                size="small"
                type="primary"
                icon="el-icon-plus"
                @click="addVariable"
              >
                添加变量
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：LaTeX预览和复制区域 -->
      <div class="preview-panel">
        <div class="panel-header">
          <h3>LaTeX预览</h3>
          <div class="copy-actions">
            <el-dropdown @command="handleCopyCommand" trigger="click">
              <el-button
                size="small"
                type="success"
                icon="el-icon-document-copy"
              >
                复制选项<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="latex">复制LaTeX代码</el-dropdown-item>
                <el-dropdown-item command="wrapped">复制包装的LaTeX</el-dropdown-item>
                <el-dropdown-item command="inline">复制内联格式</el-dropdown-item>
                <el-dropdown-item command="display">复制显示格式</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <!-- 公式预览 -->
        <div class="formula-preview">
          <div class="preview-title">公式预览：</div>
          <div
            class="math-preview"
            ref="mathPreview"
            v-html="renderedFormula"
          ></div>
        </div>

        <!-- LaTeX代码 -->
        <div class="latex-code">
          <div class="code-title">LaTeX代码：</div>
          <el-input
            v-model="latexCode"
            type="textarea"
            :rows="6"
            readonly
            class="latex-textarea"
          ></el-input>
        </div>

        <!-- 使用说明 -->
        <div class="usage-info">
          <div class="info-title">使用说明：</div>
          <ul class="info-list">
            <li>选择左侧模板快速开始</li>
            <li>使用中间工具栏添加公式元素</li>
            <li>在构建区直接编辑公式</li>
            <li>右侧实时预览LaTeX效果</li>
            <li>点击"复制LaTeX"获取代码</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FormulaDesigner',
  data() {
    return {
      selectedCategory: 'basic',
      formulaInput: '',
      latexCode: '',
      renderedFormula: '',
      variables: [
        { name: 'x', description: '变量x' },
        { name: 'y', description: '变量y' }
      ],

      // 历史记录
      history: [''],
      historyIndex: 0,
      maxHistorySize: 50,

      // MathJax相关
      mathJaxReady: false,

      // 模板分类
      templateCategories: [
        { id: 'basic', name: '基础运算', icon: 'el-icon-calculator' },
        { id: 'statistics', name: '统计函数', icon: 'el-icon-data-line' },
        { id: 'percentage', name: '百分比', icon: 'el-icon-pie-chart' },
        { id: 'ratio', name: '比率', icon: 'el-icon-scale-to-original' },
        { id: 'advanced', name: '高级函数', icon: 'el-icon-cpu' }
      ],

      // 公式模板
      formulaTemplates: [
        // 基础运算
        {
          id: 'sum',
          category: 'basic',
          name: '求和',
          description: '计算一组数值的总和',
          latex: '\\sum_{i=1}^{n} x_i',
          preview: '∑ᵢ₌₁ⁿ xᵢ',
          variables: ['x_i', 'n']
        },
        {
          id: 'product',
          category: 'basic',
          name: '乘积',
          description: '计算一组数值的乘积',
          latex: '\\prod_{i=1}^{n} x_i',
          preview: '∏ᵢ₌₁ⁿ xᵢ',
          variables: ['x_i', 'n']
        },
        {
          id: 'fraction',
          category: 'basic',
          name: '分数',
          description: '分数表示',
          latex: '\\frac{a}{b}',
          preview: 'a/b',
          variables: ['a', 'b']
        },
        {
          id: 'power',
          category: 'basic',
          name: '幂运算',
          description: '指数运算',
          latex: 'x^{n}',
          preview: 'xⁿ',
          variables: ['x', 'n']
        },
        {
          id: 'sqrt',
          category: 'basic',
          name: '平方根',
          description: '开平方运算',
          latex: '\\sqrt{x}',
          preview: '√x',
          variables: ['x']
        },

        // 统计函数
        {
          id: 'average',
          category: 'statistics',
          name: '平均值',
          description: '计算算术平均值',
          latex: '\\bar{x} = \\frac{1}{n}\\sum_{i=1}^{n} x_i',
          preview: 'x̄ = (1/n)∑ᵢ₌₁ⁿ xᵢ',
          variables: ['x_i', 'n']
        },
        {
          id: 'variance',
          category: 'statistics',
          name: '方差',
          description: '计算样本方差',
          latex: 's^2 = \\frac{1}{n-1}\\sum_{i=1}^{n}(x_i - \\bar{x})^2',
          preview: 's² = (1/(n-1))∑(xᵢ - x̄)²',
          variables: ['x_i', 'n', '\\bar{x}']
        },
        {
          id: 'std_dev',
          category: 'statistics',
          name: '标准差',
          description: '计算标准差',
          latex: 's = \\sqrt{\\frac{1}{n-1}\\sum_{i=1}^{n}(x_i - \\bar{x})^2}',
          preview: 's = √[(1/(n-1))∑(xᵢ - x̄)²]',
          variables: ['x_i', 'n', '\\bar{x}']
        },
        {
          id: 'median',
          category: 'statistics',
          name: '中位数',
          description: '计算中位数',
          latex: 'M = \\begin{cases} x_{\\frac{n+1}{2}} & \\text{if } n \\text{ is odd} \\\\ \\frac{x_{\\frac{n}{2}} + x_{\\frac{n}{2}+1}}{2} & \\text{if } n \\text{ is even} \\end{cases}',
          preview: 'M = 中位数计算',
          variables: ['x_i', 'n']
        },

        // 百分比
        {
          id: 'percentage',
          category: 'percentage',
          name: '百分比',
          description: '计算百分比',
          latex: '\\text{百分比} = \\frac{\\text{部分}}{\\text{总体}} \\times 100\\%',
          preview: '百分比 = (部分/总体) × 100%',
          variables: ['部分', '总体']
        },
        {
          id: 'percentage_change',
          category: 'percentage',
          name: '百分比变化',
          description: '计算百分比变化率',
          latex: '\\text{变化率} = \\frac{\\text{新值} - \\text{旧值}}{\\text{旧值}} \\times 100\\%',
          preview: '变化率 = (新值 - 旧值)/旧值 × 100%',
          variables: ['新值', '旧值']
        },
        {
          id: 'growth_rate',
          category: 'percentage',
          name: '增长率',
          description: '计算增长率',
          latex: '\\text{增长率} = \\frac{V_t - V_0}{V_0} \\times 100\\%',
          preview: '增长率 = (Vₜ - V₀)/V₀ × 100%',
          variables: ['V_t', 'V_0']
        },

        // 比率
        {
          id: 'ratio',
          category: 'ratio',
          name: '比率',
          description: '计算两个量的比率',
          latex: '\\text{比率} = \\frac{A}{B}',
          preview: '比率 = A/B',
          variables: ['A', 'B']
        },
        {
          id: 'proportion',
          category: 'ratio',
          name: '比例',
          description: '四个数的比例关系',
          latex: '\\frac{a}{b} = \\frac{c}{d}',
          preview: 'a/b = c/d',
          variables: ['a', 'b', 'c', 'd']
        },
        {
          id: 'efficiency',
          category: 'ratio',
          name: '效率',
          description: '计算效率',
          latex: '\\text{效率} = \\frac{\\text{输出}}{\\text{输入}} \\times 100\\%',
          preview: '效率 = (输出/输入) × 100%',
          variables: ['输出', '输入']
        },

        // 高级函数
        {
          id: 'derivative',
          category: 'advanced',
          name: '导数',
          description: '函数的导数',
          latex: 'f\'(x) = \\lim_{h \\to 0} \\frac{f(x+h) - f(x)}{h}',
          preview: 'f\'(x) = lim[h→0] [f(x+h) - f(x)]/h',
          variables: ['f(x)', 'h']
        },
        {
          id: 'integral',
          category: 'advanced',
          name: '积分',
          description: '定积分',
          latex: '\\int_{a}^{b} f(x) dx',
          preview: '∫ₐᵇ f(x) dx',
          variables: ['f(x)', 'a', 'b']
        },
        {
          id: 'limit',
          category: 'advanced',
          name: '极限',
          description: '函数极限',
          latex: '\\lim_{x \\to a} f(x) = L',
          preview: 'lim[x→a] f(x) = L',
          variables: ['f(x)', 'a', 'L']
        },
        {
          id: 'matrix',
          category: 'advanced',
          name: '矩阵',
          description: '2x2矩阵',
          latex: '\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}',
          preview: '[a b; c d]',
          variables: ['a', 'b', 'c', 'd']
        }
      ],

      // 基础运算符
      basicOperators: [
        { symbol: '+', latex: '+', name: '加法' },
        { symbol: '-', latex: '-', name: '减法' },
        { symbol: '×', latex: '\\times', name: '乘法' },
        { symbol: '÷', latex: '\\div', name: '除法' },
        { symbol: '=', latex: '=', name: '等于' },
        { symbol: '≠', latex: '\\neq', name: '不等于' },
        { symbol: '<', latex: '<', name: '小于' },
        { symbol: '>', latex: '>', name: '大于' },
        { symbol: '≤', latex: '\\leq', name: '小于等于' },
        { symbol: '≥', latex: '\\geq', name: '大于等于' }
      ],

      // 函数
      functions: [
        { display: 'sin', latex: '\\sin', name: '正弦函数' },
        { display: 'cos', latex: '\\cos', name: '余弦函数' },
        { display: 'tan', latex: '\\tan', name: '正切函数' },
        { display: 'log', latex: '\\log', name: '对数函数' },
        { display: 'ln', latex: '\\ln', name: '自然对数' },
        { display: 'exp', latex: '\\exp', name: '指数函数' },
        { display: 'sqrt', latex: '\\sqrt{}', name: '平方根' },
        { display: 'abs', latex: '\\left|\\right|', name: '绝对值' }
      ],

      // 符号
      symbols: [
        { display: 'π', latex: '\\pi', name: '圆周率' },
        { display: '∞', latex: '\\infty', name: '无穷大' },
        { display: '∑', latex: '\\sum', name: '求和' },
        { display: '∏', latex: '\\prod', name: '乘积' },
        { display: '∫', latex: '\\int', name: '积分' },
        { display: '∂', latex: '\\partial', name: '偏导数' },
        { display: '±', latex: '\\pm', name: '正负号' },
        { display: '∓', latex: '\\mp', name: '负正号' }
      ]
    }
  },

  computed: {
    filteredTemplates() {
      return this.formulaTemplates.filter(template =>
        template.category === this.selectedCategory
      )
    }
  },

  mounted() {
    this.initializeMathJax()
    this.updatePreview()
  },

  methods: {
    selectCategory(categoryId) {
      this.selectedCategory = categoryId
    },

    selectTemplate(template) {
      this.formulaInput = template.latex
      // 根据模板更新变量
      if (template.variables) {
        this.variables = template.variables.map(variable => ({
          name: variable,
          description: `变量 ${variable}`
        }))
      }
      this.updatePreview()
      this.$message.success(`已选择模板：${template.name}`)
    },

    addElement(element) {
      // 在光标位置插入元素
      const textarea = this.$el.querySelector('.formula-builder textarea')
      if (textarea) {
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        const before = this.formulaInput.substring(0, start)
        const after = this.formulaInput.substring(end)

        // 对于特殊元素，添加占位符
        let insertText = element.latex
        if (element.latex.includes('{}')) {
          insertText = element.latex.replace('{}', '{■}')
        } else if (element.latex.includes('\\left|\\right|')) {
          insertText = '\\left|■\\right|'
        }

        this.formulaInput = before + insertText + after

        // 设置新的光标位置
        this.$nextTick(() => {
          const newPosition = start + insertText.length
          textarea.setSelectionRange(newPosition, newPosition)
          textarea.focus()
        })
      } else {
        this.formulaInput += element.latex
      }
      this.updatePreview()
    },

    addVariable() {
      this.variables.push({ name: '', description: '' })
    },

    removeVariable(index) {
      this.variables.splice(index, 1)
      this.updatePreview()
    },

    clearFormula() {
      this.formulaInput = ''
      this.updatePreview()
    },

    buildFormula() {
      if (this.validateFormula()) {
        this.updatePreview()
        this.$message.success('公式构建完成！')
      }
    },

    handleKeydown(event) {
      // 处理快捷键
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'z':
            event.preventDefault()
            this.undo()
            break
          case 'y':
            event.preventDefault()
            this.redo()
            break
        }
      } else if (event.key === 'Tab') {
        event.preventDefault()
        this.jumpToNextPlaceholder()
      }
    },

    addToHistory(value) {
      // 如果当前值与历史记录中的最后一个值相同，则不添加
      if (this.history[this.historyIndex] === value) {
        return
      }

      // 如果不在历史记录的末尾，删除当前位置之后的所有记录
      if (this.historyIndex < this.history.length - 1) {
        this.history = this.history.slice(0, this.historyIndex + 1)
      }

      // 添加新记录
      this.history.push(value)
      this.historyIndex = this.history.length - 1

      // 限制历史记录大小
      if (this.history.length > this.maxHistorySize) {
        this.history = this.history.slice(-this.maxHistorySize)
        this.historyIndex = this.history.length - 1
      }
    },

    undo() {
      if (this.historyIndex > 0) {
        this.historyIndex--
        this.formulaInput = this.history[this.historyIndex]
        this.updatePreview()
      }
    },

    redo() {
      if (this.historyIndex < this.history.length - 1) {
        this.historyIndex++
        this.formulaInput = this.history[this.historyIndex]
        this.updatePreview()
      }
    },

    jumpToNextPlaceholder() {
      const textarea = this.$refs.formulaTextarea.$el.querySelector('textarea')
      if (textarea) {
        const text = this.formulaInput
        const currentPos = textarea.selectionStart
        const placeholderIndex = text.indexOf('■', currentPos)

        if (placeholderIndex !== -1) {
          textarea.setSelectionRange(placeholderIndex, placeholderIndex + 1)
          textarea.focus()
        }
      }
    },

    // 初始化MathJax
    async initializeMathJax() {
      try {
        // 如果MathJax已经存在，直接标记为就绪
        if (window.MathJax && window.MathJax.typesetPromise) {
          this.mathJaxReady = true
          return
        }

        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']],
            processEscapes: true,
            processEnvironments: true
          },
          chtml: {
            fontURL: '/fonts/mathjax'
          },
          startup: {
            ready: () => {
              console.log('MathJax is loaded, but not yet initialized')
              window.MathJax.startup.defaultReady()
              this.mathJaxReady = true
              console.log('MathJax is initialized and ready')
            }
          }
        }

        const mathJaxScript = document.createElement('script')
        mathJaxScript.id = 'MathJax-script'
        mathJaxScript.async = true
        mathJaxScript.src = '/js/mathjax/tex-mml-chtml-mathjax-newcm.js'

        mathJaxScript.onload = () => {
          console.log('MathJax script loaded')
        }

        mathJaxScript.onerror = () => {
          console.error('MathJax script failed to load')
          this.mathJaxReady = false
        }

        document.head.appendChild(mathJaxScript)

      } catch (error) {
        console.error('MathJax初始化失败:', error)
        this.mathJaxReady = false
      }
    },

    updatePreview() {
      // 添加到历史记录
      this.addToHistory(this.formulaInput)

      this.latexCode = this.formulaInput

      // 使用 MathJax 渲染公式
      if (this.formulaInput.trim()) {
        this.renderMathFormula(this.formulaInput)
      } else {
        this.renderedFormula = '<div class="math-empty">请输入公式</div>'
      }
    },

    // 渲染数学公式
    async renderMathFormula(latex) {
      try {
        if (!this.mathJaxReady || !window.MathJax) {
          this.renderedFormula = '<div class="math-loading">MathJax正在加载中...</div>'
          return
        }

        // 创建包含公式的HTML
        const mathHtml = `<div class="math-display">$$${latex}$$</div>`
        this.renderedFormula = mathHtml

        // 等待DOM更新后再渲染MathJax
        await this.$nextTick()

        // 使用MathJax渲染
        const previewElement = this.$refs.mathPreview
        if (previewElement && window.MathJax.typesetPromise) {
          await window.MathJax.typesetPromise([previewElement])
        }
      } catch (error) {
        console.error('MathJax rendering error:', error)
        this.renderedFormula = `<div class="math-error">公式渲染错误: ${error.message}</div>`
      }
    },

    validateFormula() {
      // 简单的LaTeX语法验证
      const latex = this.formulaInput.trim()
      if (!latex) {
        this.$message.error('公式不能为空')
        return false
      }

      // 检查括号匹配
      const openBraces = (latex.match(/\{/g) || []).length
      const closeBraces = (latex.match(/\}/g) || []).length
      if (openBraces !== closeBraces) {
        this.$message.error('公式语法错误: 大括号不匹配')
        return false
      }

      return true
    },

    handleCopyCommand(command) {
      if (!this.latexCode) {
        this.$message.warning('没有可复制的LaTeX代码')
        return
      }

      let textToCopy = ''
      let successMessage = ''

      switch (command) {
        case 'latex':
          textToCopy = this.latexCode
          successMessage = 'LaTeX代码已复制到剪贴板'
          break
        case 'wrapped':
          textToCopy = `$$${this.latexCode}$$`
          successMessage = '包装的LaTeX代码已复制到剪贴板'
          break
        case 'inline':
          textToCopy = `$${this.latexCode}$`
          successMessage = '内联LaTeX代码已复制到剪贴板'
          break
        case 'display':
          textToCopy = `\\[${this.latexCode}\\]`
          successMessage = '显示LaTeX代码已复制到剪贴板'
          break
        default:
          textToCopy = this.latexCode
          successMessage = 'LaTeX代码已复制到剪贴板'
      }

      this.copyToClipboard(textToCopy, successMessage)
    },

    copyLatex() {
      this.handleCopyCommand('latex')
    },

    copyToClipboard(text, successMessage) {
      // 现代浏览器使用 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message.success(successMessage)
          this.showCopyFeedback()
        }).catch(() => {
          this.fallbackCopyTextToClipboard(text, successMessage)
        })
      } else {
        this.fallbackCopyTextToClipboard(text, successMessage)
      }
    },

    fallbackCopyTextToClipboard(text, successMessage) {
      // 降级方案：使用传统的 document.execCommand
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        const successful = document.execCommand('copy')
        if (successful) {
          this.$message.success(successMessage)
          this.showCopyFeedback()
        } else {
          this.$message.error('复制失败，请手动复制')
        }
      } catch (err) {
        console.error('复制失败:', err)
        this.$message.error('复制失败，请手动复制')
      }

      document.body.removeChild(textArea)
    },

    showCopyFeedback() {
      // 显示复制成功的视觉反馈
      const copyButton = this.$el.querySelector('.copy-actions .el-button')
      if (copyButton) {
        copyButton.classList.add('copy-success')
        setTimeout(() => {
          copyButton.classList.remove('copy-success')
        }, 1000)
      }
    }
  }
}
</script>

<style scoped>
.formula-designer {
  padding: 20px;
  background: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.designer-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.designer-header h2 {
  color: #409EFF;
  margin: 0 0 10px 0;
}

.designer-header p {
  color: #666;
  margin: 0;
}

.designer-content {
  display: flex;
  gap: 20px;
  height: calc(100vh - 200px);
}

.template-panel,
.builder-panel,
.preview-panel {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
}

.template-panel {
  width: 300px;
}

.builder-panel {
  flex: 1;
}

.preview-panel {
  width: 350px;
}

.panel-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #333;
}

.copy-actions {
  display: flex;
  gap: 8px;
}

.copy-success {
  animation: copySuccess 1s ease-in-out;
}

@keyframes copySuccess {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); background-color: #67c23a; }
  100% { transform: scale(1); }
}

.template-categories {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 5px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.category-item:hover {
  background: #f0f9ff;
}

.category-item.active {
  background: #409EFF;
  color: white;
}

.category-item i {
  margin-right: 8px;
}

.template-list {
  flex: 1;
  overflow-y: auto;
  padding: 15px;
}

.template-item {
  padding: 12px;
  margin-bottom: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 8px rgba(64,158,255,0.2);
}

.template-preview {
  font-size: 16px;
  text-align: center;
  margin-bottom: 8px;
  padding: 8px;
  background: #f9f9f9;
  border-radius: 4px;
}

.template-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.template-desc {
  font-size: 12px;
  color: #666;
}

.formula-toolbar {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.toolbar-group {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.group-label {
  font-size: 12px;
  color: #666;
  margin-right: 10px;
  min-width: 60px;
}

.formula-builder {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.builder-input {
  margin-bottom: 20px;
}

.input-hints {
  display: flex;
  gap: 15px;
  margin-top: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 12px;
}

.hint-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.hint-key {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-weight: bold;
  color: #495057;
}

.hint-desc {
  color: #6c757d;
}

.variables-section h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.variable-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.formula-preview,
.latex-code,
.usage-info {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.preview-title,
.code-title,
.info-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.math-preview {
  min-height: 80px;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  background: #fafafa;
  overflow-x: auto;
}

/* MathJax 相关样式 */
.math-display {
  text-align: center;
  padding: 15px;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.math-loading {
  text-align: center;
  color: #409EFF;
  font-style: italic;
  padding: 20px;
  animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
  from { opacity: 0.6; }
  to { opacity: 1; }
}

.math-empty {
  text-align: center;
  color: #999;
  font-style: italic;
  padding: 20px;
}

.math-error {
  text-align: center;
  color: #f56c6c;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
}

/* MathJax 容器样式覆盖 */
::v-deep mjx-container {
  display: inline-block !important;
  margin: 0 !important;
}

::v-deep mjx-container[display="true"] {
  display: block !important;
  text-align: center !important;
  margin: 1em 0 !important;
}

/* 确保MathJax在预览区域中正确显示 */
.math-preview ::v-deep mjx-container {
  max-width: 100%;
  overflow-x: auto;
}

.latex-textarea {
  font-family: 'Courier New', monospace;
}

.info-list {
  margin: 0;
  padding-left: 20px;
}

.info-list li {
  margin-bottom: 5px;
  color: #666;
  font-size: 13px;
}
</style>
