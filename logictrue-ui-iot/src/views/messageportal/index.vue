<template>
  <div class="message-portal">
    <el-row>
      <el-col :span="24">
        <MessageCard :bodyHeight="68" :is-title="false">
          <div class="message-top">
            <!-- 头部左侧log 页面说明 -->
            <div class="message-top-left">
              <div>
                <img src="../../assets/images/logo.png" alt=""/>
              </div>
              <div class="message-top-left-title">
                {{ title }}
              </div>
            </div>
            <div class="message-top-right">
              <div>
                {{ times }}
              </div>
              <div class="message-top-left-title">
                {{ userName }}
              </div>
            </div>
          </div>
        </MessageCard>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="11">
        <MessageCard :bodyHeight="300">
          <template>
            <div slot="header" class="clearfix" >
              <span style="width: 100px;">消息简报</span>
              <div class="card-header-box-height" style="float: right; padding: 3px 0px;">
                <div style="margin-right: 20px;">
                  <template v-for="(tag, index) in tagList">
                    <div class="el-badge item el-badge-box" v-if="index < 3" >
                      <span class="el-tag el-tag--success el-tag--plain el-tag--small">{{ tag.label }}</span>
                      <sup class="el-badge__content is-fixed"> {{ tag.num }} </sup>
                    </div>
                  </template>
                  <el-dropdown trigger="click" style="margin-left: 10px;" v-if="tagList && tagList.length > 3">
                    <span class="el-dropdown-link" style="color: #66b1ff">
                      展开<i class="el-icon-caret-bottom el-icon--right"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <template v-for="(tag, index) in tagList">
                        <el-dropdown-item class="clearfix" v-if="index > 2">
                          <div class="el-badge item el-badge-box">
                            <span class="el-tag el-tag--success el-tag--plain el-tag--small">{{ tag.label }}</span>
                            <sup class="el-badge__content is-fixed"> {{ tag.num }} </sup>
                          </div>
                        </el-dropdown-item>
                      </template>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <div class="el-input el-input--small el-input--suffix"
                     style="margin-right: 10px;width: 180px;"
                     @keyup="briefingKeyup"
                >
                  <input type="text" v-model="briefingKeyValue" autocomplete="off" placeholder="请输入搜索内容" class="el-input__inner">
                </div>
                <el-button style="float: right; padding: 3px 0" type="text" @click="openDialog('BriefingDetails')">更多</el-button>
              </div>
            </div>
          </template>
          <div class="briefing-flex">
<!--     el-tag el-tag--${item.type} el-tag--light       -->
            <div
              v-for="(item, index) in briefingTableData"
              :class="` briefing-size`"
            >
              {{ (index + 1) + '. &nbsp;' + item.briefingContent }}
              <span v-if="item.tag" :class="`el-tag el-tag--mini el-tag--light el-tag--${item.type}`">{{item.tag}}</span>
            </div>
          </div>
        </MessageCard>
      </el-col>
      <el-col :span="13">
        <MessageCard :bodyHeight="300">
          <div slot="header" class="clearfix">
            <span>消息列表</span>
            <div class="el-radio-group card-header-box-height" style="float: right; padding: 3px 0">
              <div class="el-input el-input--small el-input--suffix" style="margin-right: 10px;width: 180px;">
                <input type="text" autocomplete="off" placeholder="请输入搜索内容" class="el-input__inner">
              </div>
              <button type="button"
                      class="el-button el-button--primary el-button--mini"
                      style="margin-right: 20px;"
                      @click="openDialog('Subscribe', {})"
              >
                <span>订阅</span>
              </button>
              <el-radio-group v-model="messageRadioValue" size="small">
                <el-radio-button v-for="item in messageRadios" :label="item"></el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <message-table :tableData="tableData" :props="props" :height="240">
            <template v-slot:table-but="{ row }">
              <el-button type="text">已读</el-button>
              <el-button type="text" @click="openDialog('MessageDetails', row, false, '60%')">详情</el-button>
            </template>
          </message-table>
          <lt-pagination total="100" :page-size="pageSize" :page-count="total" :current-page="pageNum" @pagination-change="paginationChange"></lt-pagination>
        </MessageCard>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="15">
        <MessageCard :body-height="160">
          <div slot="header" class="clearfix">
            <span>消息公告</span>
            <div class="card-header-box-height" style="float: right; padding: 3px 0px;">
              <div style="margin-right: 20px;">
                <template v-for="(tag, index) in tagList">
                  <div class="el-badge item el-badge-box" v-if="index < 8" >
                    <span class="el-tag el-tag--success el-tag--plain el-tag--small">{{ tag.label }}</span>
                    <sup class="el-badge__content is-fixed"> {{ tag.num }} </sup>
                  </div>
                </template>
                <el-dropdown trigger="click" style="margin-left: 10px;" v-if="tagList && tagList.length > 8">
                    <span class="el-dropdown-link" style="color: #66b1ff">
                      展开<i class="el-icon-caret-bottom el-icon--right"></i>
                    </span>
                  <el-dropdown-menu slot="dropdown">
                    <template v-for="(tag, index) in tagList">
                      <el-dropdown-item class="clearfix" v-if="index > 7">
                        <div class="el-badge item el-badge-box">
                          <span class="el-tag el-tag--success el-tag--plain el-tag--small">{{ tag.label }}</span>
                          <sup class="el-badge__content is-fixed"> {{ tag.num }} </sup>
                        </div>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <div class="el-input el-input--small el-input--suffix" style="margin-right: 10px;width: 180px;">
                <input type="text" autocomplete="off" placeholder="请输入搜索内容" class="el-input__inner">
              </div>
            </div>
          </div>
          <div class="notice">
            <div class="notice-card" v-for="item in busList" @click="openDialog('BusDetails', item)">
              <div class="title">{{ item.title }}</div>
              <div class="content">{{ item.content }}</div>
            </div>
          </div>
        </MessageCard>
      </el-col>
      <el-col :span="9">
        <MessageCard :body-height="160">
          <div slot="header" class="clearfix">
            <span>快速入口</span>
          </div>
          <div class="entrance">
            <div class="entrance-card" v-for="item in entranceList" @click="goTo(item)">
              <img :src="item.imgUrl ? item.imgUrl : require('./image/快速入口.png')" alt="" width="60px" height="50px" />
              <p>{{ item.name }}</p>
            </div>
          </div>
        </MessageCard>
      </el-col>
    </el-row>
    <el-dialog
      :title="dialogModel[dialogKey] ? dialogModel[dialogKey].title : '标题'"
      :visible="dialogVisible"
      :width="dialogWidth"
      :before-close="() => dialogVisible = false"
      :fullscreen="fullscreen"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      :lock-append-to-body="true"
    >
      <component
        :is="dialogKey"
        :model="model"
      >
      </component>
      <span slot="footer" class="dialog-footer">
        <el-button @click="() => dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import MessageTable from './components/messageTable.vue'
import MessageCard from './components/messageCard.vue'
import dataJs from './js/index'
import LtPagination from "@/components/lt-pagination/lt-pagination.vue";
import BriefingDetails from './briefingDetails.vue'
import BusDetails from './busDetails.vue'
import MessageDetails from './messageDetails.vue'
import Subscribe from './subscribe.vue'

export default {
  name: "index",
  components: {
    LtPagination,
    MessageCard,
    MessageTable,
    BriefingDetails,
    BusDetails,
    MessageDetails,
    Subscribe
  },
  data() {
    return {
      title: '消息门户',
      userName: '逻辑真',
      times: '2024-06-11 10:00:00',
      timer: null,
      briefingTableData: [],
      briefingTableDataCopy: [],
      briefingKeyValue: '',
      messageRadios: ['未读', '已读'],
      messageRadioValue: '未读',
      busList: [],
      entranceList: [],
      props: [],
      tableData: [],
      tagList: [],
      pageSize: 10,
      pageNum: 1,
      total: 10,
      dialogVisible: false,
      dialogKey: '',
      dialogModel: {
        'BriefingDetails': {
          title: "消息简报"
        },
        'BusDetails': {
          title: "消息公告详情",
        },
        'MessageDetails': {
          title: "消息详情",
        },
        'Subscribe': {
          title: "消息订阅",
        }
      },
      model: {},
      dialogWidth: '60%',
      fullscreen: true
    };
  },
  created() {
    this.props = dataJs.notProps;
    this.tableData = dataJs.notTableData;
    this.briefingTableData = dataJs.briefingTableData;
    // 备份用于过滤还原
    this.briefingTableDataCopy = JSON.parse(JSON.stringify(this.briefingTableData));
    this.busList = dataJs.busList;
    this.entranceList = dataJs.entranceList;
    this.tagList = dataJs.tagList;
    // 初始化时间
    this.createTimer();
  },
  methods: {
    briefingKeyup() {
      if (this.briefingKeyValue) {
        this.briefingTableData = [...this.briefingTableDataCopy.filter(item => item.briefingContent.includes(this.briefingKeyValue))];
      } else {
        this.briefingTableData = [...this.briefingTableDataCopy];
      }
    },
    goTo(row) {
      if (row.url) {
        window.open(row.url);
      } else {
        this.$message({
          type: "error",
          message: "数据异常，请联系管理人员进行处理"
        })
      }
    },
    openDialog(is, row, isFull, width) {
      this.dialogVisible = true;
      if (!this.dialogModel[is]) {
        return this.$message({
          type: "error",
          message: "此功能暂未开发，请等待开发。。。"
        })
      }
      this.dialogKey = is;
      this.model = row || {};
      this.dialogWidth = width || '';
      this.fullscreen = isFull === true || isFull === false ? isFull : true;
    },
    paginationChange(page) {
      console.log('当前页:', page);
      // 这里可以添加分页逻辑
    },
    // 定时刷新当前时间
    createTimer() {
      this.times = this.getTime();
      this.timer = setInterval(() => {
        this.times = this.getTime();
      }, 1000);
    },
    // 获取当前时间
    getTime() {
      let date = new Date();
      let Y = date.getFullYear() + '-';
      let M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
      let D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' ';
      let h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
      let m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
      let s = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
      return Y + M + D + h + m + s;
    }
  },
  beforeDestroy() {
    // 页面关闭销毁定时任务
    clearInterval(this.timer);
  }
}
</script>

<style scoped>
@import './css/index.css';

</style>
