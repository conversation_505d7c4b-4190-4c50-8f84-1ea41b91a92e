<template>
  <div class="el-card is-always-shadow margin">
    <div v-if="isTitle" class="el-card__header" style="padding: 10px 10px 5px 10px">
      <slot name="header"></slot>
    </div>
    <div class="el-card__body" :style="`height: ${bodyHeight}px;overflow: auto;${isTitle ? 'padding : 5px 10px;' : 'padding : 2px;'}`">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "messageCard",
  props: {
    bodyHeight: {
      type: Number,
      default: () => 300
    },
    isTitle: {
      type: Boolean,
      default: () => true
    }
  },
  created() {

  },
  mounted() {

  },
  methods: {

  },
}
</script>

<style scoped>
  .margin {
    margin: 5px;
  }
</style>
