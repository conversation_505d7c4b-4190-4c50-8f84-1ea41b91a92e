<template>
  <div>
    <el-row>
      <el-col :span="24">
        <div>
          <span style="margin-right: 20px;">月</span>
          <el-date-picker
            v-model="month"
            format="MM"
            style="width: 100px"
            type="month"
            placeholder="选择月">
          </el-date-picker>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8" v-for="item in dayCardList">
        <MessageCard>
          <div slot="header" class="clearfix" >
            <span>{{ item.day }}</span>
            <div class="card-header-box-height" style="float: right; padding: 3px 0px;">
              <div style="margin-right: 20px;">
                <template v-for="(tag, index) in item.tagList">
                  <div class="el-badge item el-badge-box" v-if="index < 3" >
                    <span class="el-tag el-tag--success el-tag--plain el-tag--small">{{ tag.label }}</span>
                    <sup class="el-badge__content is-fixed"> {{ tag.num }} </sup>
                  </div>
                </template>
                <el-dropdown trigger="click" style="margin-left: 10px;" v-if="item.tagList && item.tagList.length > 3">
                      <span class="el-dropdown-link" style="color: #66b1ff">
                        展开<i class="el-icon-caret-bottom el-icon--right"></i>
                      </span>
                  <el-dropdown-menu slot="dropdown">
                    <template v-for="(tag, index) in item.tagList">
                      <el-dropdown-item class="clearfix" v-if="index > 2">
                        <div class="el-badge item el-badge-box">
                          <span class="el-tag el-tag--success el-tag--plain el-tag--small">{{ tag.label }}</span>
                          <sup class="el-badge__content is-fixed"> {{ tag.num }} </sup>
                        </div>
                      </el-dropdown-item>
                    </template>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>
          <div class="briefing-flex">
            <div
              v-for="(item, index) in item.briefingTableData"
              :class="` briefing-size`"
            >
              {{ (index + 1) + '. &nbsp;' + item.briefingContent }}
              <span v-if="item.tag" :class="`el-tag el-tag--mini el-tag--light el-tag--${item.type}`">{{item.tag}}</span>
            </div>
          </div>
        </MessageCard>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import MessageCard from './components/messageCard.vue'

export default {
  name: "briefingDetails",
  components: {
    MessageCard
  },
  data() {
    return {
      month: '',
      dayCardList: [{
        day: '01',
        briefingTableData: [{
          briefingContent: '简报内容1简报内容1简报内容1',
          type: 'info',
          tag: '重点'
        }, {
          briefingContent: '简报内容2',
          type: 'danger',
          tag: '重点'
        }, {
          briefingContent: '简报内容3',
          type: 'success'
        }, {
          briefingContent: '简报内容4',
          type: 'waring'
        }],
        tagList: [{
          label: '公共',
          value: '1',
          num: 1
        },{
          label: '公共',
          value: '1',
          num: 1
        },{
          label: '公共',
          value: '1',
          num: 1
        },{
          label: '公共',
          value: '1',
          num: 1
        },{
          label: '公共',
          value: '1',
          num: 1
        },{
          label: '公共',
          value: '1',
          num: 1
        }]
      },{
        day: '02',
        briefingTableData: [{
          briefingContent: '简报内容1',
          type: 'info'
        }, {
          briefingContent: '简报内容2',
          type: 'danger'
        }, {
          briefingContent: '简报内容3',
          type: 'success'
        }, {
          briefingContent: '简报内容4',
          type: 'waring'
        }]
      },{
        day: '03',
        briefingTableData: [{
          briefingContent: '简报内容1',
          type: 'info'
        }, {
          briefingContent: '简报内容2',
          type: 'danger'
        }, {
          briefingContent: '简报内容3',
          type: 'success'
        }, {
          briefingContent: '简报内容4',
          type: 'waring'
        }]
      }]
    }
  },
  created() {
    let date = new Date();
    this.month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1);
  },
  methods: {

  }
}
</script>

<style scoped>
@import './css/index.css';

</style>
