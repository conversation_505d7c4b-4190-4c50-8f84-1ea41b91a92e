<template>
  <div>
    <h1 style="text-align: center;"> {{ model.title  }} </h1>
    <div class="content" v-html="model.content"></div>
  </div>
</template>

<script>

export default {
  name: "busDetails",
  props: {
    model: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  methods: {

  }
}
</script>

<style scoped>
  .content {
    text-indent: 25px;
    font-family: '宋体';
    line-height: 24px;
  }
</style>
