<!-- 这是对现有json-table-demo.vue的增强版本，添加了嵌套表格插入功能 -->
<template>
  <div class="json-table-demo">
    <!-- 嵌套表格插入对话框 -->
    <div v-if="showNestedTableDialog" class="nested-table-dialog-overlay" @click="closeNestedTableDialog">
      <div class="nested-table-dialog" @click.stop>
        <div class="dialog-header">
          <h3>插入嵌套表格</h3>
          <button @click="closeNestedTableDialog" class="close-btn">&times;</button>
        </div>

        <div class="dialog-content">
          <div class="dialog-section">
            <h4>导入方式</h4>
            <div class="import-options">
              <label class="radio-option">
                <input type="radio" v-model="importMethod" value="paste">
                粘贴JSON配置
              </label>
              <label class="radio-option">
                <input type="radio" v-model="importMethod" value="designer">
                从设计器导入
              </label>
              <label class="radio-option">
                <input type="radio" v-model="importMethod" value="template">
                使用模板
              </label>
            </div>
          </div>

          <!-- 粘贴JSON配置 -->
          <div v-if="importMethod === 'paste'" class="dialog-section">
            <h4>JSON配置</h4>
            <textarea
              v-model="nestedTableJson"
              placeholder="请粘贴嵌套表格的JSON配置..."
              class="json-input-area"
              rows="8"
            ></textarea>
            <div class="json-actions">
              <button @click="validateNestedTableJson" class="btn btn-info">
                验证JSON
              </button>
              <button @click="formatNestedTableJson" class="btn btn-secondary">
                格式化
              </button>
            </div>
          </div>

          <!-- 从设计器导入 -->
          <div v-if="importMethod === 'designer'" class="dialog-section">
            <h4>设计器配置</h4>
            <p class="info-text">从嵌套表格设计器中导入配置</p>
            <button @click="loadFromDesigner" class="btn btn-primary">
              加载设计器配置
            </button>
            <button @click="openDesigner" class="btn btn-info">
              打开设计器
            </button>
          </div>

          <!-- 使用模板 -->
          <div v-if="importMethod === 'template'" class="dialog-section">
            <h4>选择模板</h4>
            <select v-model="selectedNestedTemplate" class="template-select">
              <option value="">-- 请选择模板 --</option>
              <option v-for="template in nestedTableTemplates" :key="template.id" :value="template.id">
                {{ template.name }}
              </option>
            </select>
            <div v-if="selectedNestedTemplate" class="template-preview">
              <h5>模板预览</h5>
              <div class="template-info">
                <p><strong>名称:</strong> {{ getSelectedTemplate().name }}</p>
                <p><strong>描述:</strong> {{ getSelectedTemplate().description }}</p>
                <p><strong>尺寸:</strong> {{ getSelectedTemplate().columns }}列 × {{ getSelectedTemplate().rows }}行</p>
              </div>
            </div>
          </div>

          <!-- 目标位置选择 -->
          <div class="dialog-section">
            <h4>插入位置</h4>
            <div class="position-config">
              <div class="form-group">
                <label>目标行:</label>
                <input
                  v-model.number="targetPosition.row"
                  type="number"
                  min="0"
                  class="position-input"
                >
              </div>
              <div class="form-group">
                <label>目标列:</label>
                <input
                  v-model.number="targetPosition.col"
                  type="number"
                  min="0"
                  class="position-input"
                >
              </div>
            </div>
            <p class="help-text">将在指定位置的单元格中插入嵌套表格</p>
          </div>

          <!-- 预览区域 -->
          <div v-if="nestedTablePreview" class="dialog-section">
            <h4>预览</h4>
            <div class="nested-preview-container">
              <table class="nested-preview-table">
                <tr v-for="(row, rowIndex) in nestedTablePreview.cellRows" :key="rowIndex">
                  <td v-for="(cell, colIndex) in row" :key="colIndex" class="preview-cell">
                    {{ cell.content }}
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>

        <div class="dialog-footer">
          <button @click="closeNestedTableDialog" class="btn btn-secondary">
            取消
          </button>
          <button @click="insertNestedTable" class="btn btn-primary" :disabled="!canInsertNestedTable">
            插入嵌套表格
          </button>
        </div>
      </div>
    </div>

    <!-- 原有的控制面板内容保持不变，只需要在action-buttons中添加新按钮 -->
    <!-- 这里省略原有内容，实际使用时需要保留所有原有代码 -->

    <!-- 新增的嵌套表格管理区域 -->
    <div class="nested-table-management" v-if="hasNestedTables">
      <h3>嵌套表格管理</h3>
      <div class="nested-table-list">
        <div
          v-for="(nestedInfo, index) in nestedTablesList"
          :key="index"
          class="nested-table-item"
        >
          <div class="nested-info">
            <span class="nested-position">位置: ({{ nestedInfo.row }}, {{ nestedInfo.col }})</span>
            <span class="nested-title">{{ nestedInfo.title }}</span>
            <span class="nested-size">{{ nestedInfo.columns }}×{{ nestedInfo.rows }}</span>
          </div>
          <div class="nested-actions">
            <button @click="editNestedTable(nestedInfo)" class="btn-small btn-info">
              编辑
            </button>
            <button @click="removeNestedTable(nestedInfo)" class="btn-small btn-danger">
              删除
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 这里需要导入原有的所有内容，并添加新的功能
export default {
  name: 'JsonTableDemoEnhanced',
  // 继承原有的所有data、computed、methods等
  data() {
    return {
      // 原有的所有data属性...

      // 新增的嵌套表格相关数据
      showNestedTableDialog: false,
      importMethod: 'paste',
      nestedTableJson: '',
      selectedNestedTemplate: '',
      targetPosition: {
        row: 0,
        col: 0
      },
      nestedTablePreview: null,
      nestedTableTemplates: [
        {
          id: 'basic',
          name: '基础表格',
          description: '3×3基础表格模板',
          columns: 3,
          rows: 3,
          config: {
            enabled: true,
            config: {
              columnWidths: [100, 100, 100],
              rowHeights: [40, 40, 40],
              cellRows: [
                [
                  { content: '项目', originContent: '项目', hasMath: false },
                  { content: '数值', originContent: '数值', hasMath: false },
                  { content: '备注', originContent: '备注', hasMath: false }
                ],
                [
                  { content: '项目1', originContent: '项目1', hasMath: false },
                  { content: '100', originContent: '100', hasMath: false },
                  { content: '正常', originContent: '正常', hasMath: false }
                ],
                [
                  { content: '项目2', originContent: '项目2', hasMath: false },
                  { content: '200', originContent: '200', hasMath: false },
                  { content: '正常', originContent: '正常', hasMath: false }
                ]
              ],
              metadata: {
                title: '基础表格',
                level: 1,
                columns: 3,
                rows: 3
              }
            }
          }
        },
        {
          id: 'inspection',
          name: '检查记录表',
          description: '4×5检查记录表模板',
          columns: 4,
          rows: 5,
          config: {
            enabled: true,
            config: {
              columnWidths: [100, 120, 80, 100],
              rowHeights: [35, 35, 35, 35, 35],
              cellRows: [
                [
                  { content: '检查项', originContent: '检查项', hasMath: false },
                  { content: '标准', originContent: '标准', hasMath: false },
                  { content: '结果', originContent: '结果', hasMath: false },
                  { content: '备注', originContent: '备注', hasMath: false }
                ],
                [
                  { content: '外观', originContent: '外观', hasMath: false },
                  { content: '无划痕', originContent: '无划痕', hasMath: false },
                  { content: '合格', originContent: '合格', hasMath: false },
                  { content: '', originContent: '', hasMath: false }
                ],
                [
                  { content: '尺寸', originContent: '尺寸', hasMath: false },
                  { content: '±0.1mm', originContent: '±0.1mm', hasMath: false },
                  { content: '合格', originContent: '合格', hasMath: false },
                  { content: '', originContent: '', hasMath: false }
                ],
                [
                  { content: '功能', originContent: '功能', hasMath: false },
                  { content: '正常运行', originContent: '正常运行', hasMath: false },
                  { content: '合格', originContent: '合格', hasMath: false },
                  { content: '', originContent: '', hasMath: false }
                ],
                [
                  { content: '包装', originContent: '包装', hasMath: false },
                  { content: '完整无损', originContent: '完整无损', hasMath: false },
                  { content: '合格', originContent: '合格', hasMath: false },
                  { content: '', originContent: '', hasMath: false }
                ]
              ],
              metadata: {
                title: '检查记录表',
                level: 1,
                columns: 4,
                rows: 5
              }
            }
          }
        }
      ]
    }
  },
  computed: {
    // 原有的所有computed属性...

    // 新增的computed属性
    hasNestedTables() {
      return this.nestedTablesList.length > 0
    },

    nestedTablesList() {
      const list = []
      if (this.tableData && Array.isArray(this.tableData)) {
        this.tableData.forEach((row, rowIndex) => {
          if (Array.isArray(row)) {
            row.forEach((cell, colIndex) => {
              if (cell && cell.nestedTable && cell.nestedTable.enabled) {
                list.push({
                  row: rowIndex,
                  col: colIndex,
                  title: cell.nestedTable.config?.metadata?.title || '嵌套表格',
                  columns: cell.nestedTable.config?.metadata?.columns || 0,
                  rows: cell.nestedTable.config?.metadata?.rows || 0,
                  config: cell.nestedTable
                })
              }
            })
          }
        })
      }
      return list
    },

    canInsertNestedTable() {
      return this.nestedTablePreview !== null
    }
  },
  methods: {
    // 原有的所有methods...

    // 新增的嵌套表格相关方法
    showNestedTableInsertDialog() {
      this.showNestedTableDialog = true
      this.importMethod = 'paste'
      this.nestedTableJson = ''
      this.selectedNestedTemplate = ''
      this.targetPosition = { row: 0, col: 0 }
      this.nestedTablePreview = null
    },

    closeNestedTableDialog() {
      this.showNestedTableDialog = false
    },

    // 验证嵌套表格JSON
    validateNestedTableJson() {
      try {
        if (!this.nestedTableJson.trim()) {
          this.showOperationResult('error', 'JSON内容为空')
          return
        }

        const config = JSON.parse(this.nestedTableJson)

        // 验证基本结构
        if (!config.enabled || !config.config) {
          throw new Error('JSON结构不正确，缺少enabled或config字段')
        }

        if (!config.config.cellRows || !Array.isArray(config.config.cellRows)) {
          throw new Error('缺少cellRows字段或格式不正确')
        }

        this.nestedTablePreview = config.config
        this.showOperationResult('success', 'JSON验证通过')
      } catch (error) {
        this.showOperationResult('error', `JSON验证失败: ${error.message}`)
        this.nestedTablePreview = null
      }
    },

    // 格式化嵌套表格JSON
    formatNestedTableJson() {
      try {
        if (!this.nestedTableJson.trim()) {
          this.showOperationResult('error', 'JSON内容为空')
          return
        }

        const config = JSON.parse(this.nestedTableJson)
        this.nestedTableJson = JSON.stringify(config, null, 2)
        this.showOperationResult('success', 'JSON格式化完成')
      } catch (error) {
        this.showOperationResult('error', `JSON格式化失败: ${error.message}`)
      }
    },

    // 从设计器加载配置
    loadFromDesigner() {
      try {
        const designerConfig = localStorage.getItem('nestedTableDesignerConfig')
        if (!designerConfig) {
          this.showOperationResult('error', '未找到设计器配置，请先使用设计器创建配置')
          return
        }

        const config = JSON.parse(designerConfig)
        this.nestedTableJson = JSON.stringify(config, null, 2)
        this.nestedTablePreview = config.config
        this.showOperationResult('success', '设计器配置加载成功')
      } catch (error) {
        this.showOperationResult('error', `加载设计器配置失败: ${error.message}`)
      }
    },

    // 打开设计器
    openDesigner() {
      // 跳转到设计器页面
      this.$router.push('/test/nested-table-designer')
    },

    // 获取选中的模板
    getSelectedTemplate() {
      return this.nestedTableTemplates.find(t => t.id === this.selectedNestedTemplate) || {}
    },

    // 监听模板选择变化
    onTemplateChange() {
      if (this.selectedNestedTemplate) {
        const template = this.getSelectedTemplate()
        this.nestedTablePreview = template.config.config
        this.nestedTableJson = JSON.stringify(template.config, null, 2)
      } else {
        this.nestedTablePreview = null
        this.nestedTableJson = ''
      }
    },

    // 插入嵌套表格
    insertNestedTable() {
      try {
        if (!this.nestedTablePreview) {
          this.showOperationResult('error', '请先配置嵌套表格')
          return
        }

        const tableContainer = this.$refs.tableContainer
        if (!tableContainer) {
          this.showOperationResult('error', '表格组件未找到')
          return
        }

        // 确保目标位置有效
        const targetRow = this.targetPosition.row
        const targetCol = this.targetPosition.col

        if (targetRow < 0 || targetCol < 0) {
          this.showOperationResult('error', '目标位置无效')
          return
        }

        // 确保表格数据存在且目标位置可访问
        if (!this.tableData || !this.tableData[targetRow] || !this.tableData[targetRow][targetCol]) {
          this.showOperationResult('error', '目标位置超出表格范围')
          return
        }

        // 构建完整的嵌套表格配置
        let nestedConfig
        if (this.importMethod === 'template') {
          const template = this.getSelectedTemplate()
          nestedConfig = template.config
        } else {
          nestedConfig = JSON.parse(this.nestedTableJson)
        }

        // 将嵌套表格配置添加到目标单元格
        this.tableData[targetRow][targetCol].nestedTable = nestedConfig
        this.tableData[targetRow][targetCol].content = nestedConfig.config.metadata?.title || '嵌套表格'

        // 触发表格更新
        this.$forceUpdate()

        // 通知TableContainer组件
        if (tableContainer.handleNestedTableCreated) {
          tableContainer.handleNestedTableCreated({
            row: targetRow,
            col: targetCol,
            config: nestedConfig
          })
        }

        this.showOperationResult('success', `嵌套表格已插入到位置 (${targetRow}, ${targetCol})`)
        this.closeNestedTableDialog()

      } catch (error) {
        console.error('插入嵌套表格失败:', error)
        this.showOperationResult('error', `插入失败: ${error.message}`)
      }
    },

    // 编辑嵌套表格
    editNestedTable(nestedInfo) {
      this.targetPosition = { row: nestedInfo.row, col: nestedInfo.col }
      this.nestedTableJson = JSON.stringify(nestedInfo.config, null, 2)
      this.nestedTablePreview = nestedInfo.config.config
      this.importMethod = 'paste'
      this.showNestedTableDialog = true
    },

    // 删除嵌套表格
    removeNestedTable(nestedInfo) {
      if (confirm(`确定要删除位置 (${nestedInfo.row}, ${nestedInfo.col}) 的嵌套表格吗？`)) {
        try {
          const cell = this.tableData[nestedInfo.row][nestedInfo.col]
          if (cell && cell.nestedTable) {
            delete cell.nestedTable
            cell.content = '' // 清空内容

            this.$forceUpdate()
            this.showOperationResult('success', '嵌套表格已删除')
          }
        } catch (error) {
          console.error('删除嵌套表格失败:', error)
          this.showOperationResult('error', `删除失败: ${error.message}`)
        }
      }
    },

    // 显示操作结果（如果原有代码中没有这个方法）
    showOperationResult(type, message) {
      this.operationResult = {
        success: type === 'success',
        message: message
      }

      // 3秒后自动清除结果
      setTimeout(() => {
        this.operationResult = null
      }, 3000)
    }
  },

  watch: {
    // 监听模板选择变化
    selectedNestedTemplate() {
      this.onTemplateChange()
    }
  }
}
</script>

<style scoped>
/* 原有的所有样式保持不变 */

/* 新增的嵌套表格对话框样式 */
.nested-table-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.nested-table-dialog {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.dialog-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #333;
}

.dialog-content {
  padding: 20px;
}

.dialog-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.dialog-section:last-child {
  border-bottom: none;
}

.dialog-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.import-options {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.json-input-area {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  resize: vertical;
}

.json-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.template-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.template-preview {
  margin-top: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 4px;
}

.template-info p {
  margin: 5px 0;
}

.position-config {
  display: flex;
  gap: 20px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.position-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.help-text {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.info-text {
  color: #666;
  margin-bottom: 15px;
}

.nested-preview-container {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: auto;
  max-height: 200px;
}

.nested-preview-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
}

.preview-cell {
  border: 1px solid #ddd;
  padding: 6px 8px;
  text-align: center;
  background: #fafafa;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-primary { background: #007bff; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-danger { background: #dc3545; color: white; }

.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 嵌套表格管理区域样式 */
.nested-table-management {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nested-table-management h3 {
  color: #333;
  margin-bottom: 15px;
}

.nested-table-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.nested-table-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border: 1px solid #eee;
  border-radius: 4px;
  background: #fafafa;
}

.nested-info {
  display: flex;
  gap: 15px;
  align-items: center;
}

.nested-position {
  font-weight: 500;
  color: #666;
}

.nested-title {
  color: #333;
  font-weight: 500;
}

.nested-size {
  color: #999;
  font-size: 12px;
}

.nested-actions {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}
</style>
