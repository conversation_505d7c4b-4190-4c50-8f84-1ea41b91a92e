/**
 * 表格预设数据配置
 * 用于JSON表格演示页面的预设数据管理
 */

export const TABLE_PRESETS = {

    merged: {
        name: '导出数据测试',
        description: '导出数据测试',
        data: {
            title: "检验记录表",
            headers: [
                [
                    "检查工序\n名称",
                    "检 查 项 目 及 技 术 条 件",
                    "实 际 检 查 结 果",
                    "完工",
                    "",
                    "操作员",
                    "班组长",
                    "检验员"
                ],
                [
                    "",
                    "",
                    "",
                    "月",
                    "日",
                    "",
                    "",
                    ""
                ]
            ],
            cellRows: [
                [
                    {content: "脱漆", originContent: "脱漆", hasMath: false},
                    {content: "1.脱漆\n2.冲洗$\\sqrt{x}$", originContent: "1.脱漆\n2.冲洗$\\sqrt{x}$", hasMath: true},
                    {content: "合格", originContent: "合格", hasMath: false},
                    {content: "8", originContent: "8", hasMath: false},
                    {content: "22", originContent: "22", hasMath: false},
                    {content: "张三", originContent: "张三", hasMath: false},
                    {content: "李四", originContent: "李四", hasMath: false},
                    {content: "王五", originContent: "王五", hasMath: false}
                ]
            ],
            merges: [
                {
                    startRow: 2,
                    startCol: 0,
                    endRow: 3,
                    endCol: 0,
                    content: "脱漆"
                }
            ],
            headerMerges: [
                {
                    startRow: 0,
                    startCol: 0,
                    endRow: 1,
                    endCol: 0,
                    content: "检查工序\n名称"
                },
                {
                    startRow: 0,
                    startCol: 1,
                    endRow: 1,
                    endCol: 1,
                    content: "检 查 项 目 及 技 术 条 件"
                },
                {
                    startRow: 0,
                    startCol: 2,
                    endRow: 1,
                    endCol: 2,
                    content: "实 际 检 查 结 果"
                },
                {
                    startRow: 0,
                    startCol: 3,
                    endRow: 0,
                    endCol: 4,
                    content: "完工"
                },
                {
                    startRow: 0,
                    startCol: 5,
                    endRow: 1,
                    endCol: 5,
                    content: "操作员"
                },
                {
                    startRow: 0,
                    startCol: 6,
                    endRow: 1,
                    endCol: 6,
                    content: "班组长"
                },
                {
                    startRow: 0,
                    startCol: 7,
                    endRow: 1,
                    endCol: 7,
                    content: "检验员"
                }
            ],
            headerWidthConfig: {
                columnWidths: [100, 460, 160, 32, 32, 32, 32, 32],
                headerHeights: [35, 35]
            },
            verticalHeadersConfig: [false, false, false, false, false, true, true, true],
            metadata: {
                title: "检验记录表",
                useDynamicHeader: true,
                hasCustomWidth: true,
                totalRows: 1,
                totalColumns: 8,
                headerRows: 2,
                exportTime: new Date().toISOString(),
                hasMergedCells: true,
                hasHeaderMerges: true,
                hasLatexProcessing: true
            }
        }
    },

    complex: {
        name: '复杂示例',
        description: '包含数学公式和复杂合并的示例',
        data: {
            title: "数学公式检验记录表",
            headers: [
                ["检查工序\n名称", "检 查 项 目 及 技 术 条 件", "实 际 检 查 结 果", "完工", "", "操作员", "班组长", "检验员"],
                ["", "", "", "月", "日", "", "", ""]
            ],
            cellRows: [
                [
                    {content: "数学公式测试", hasMath: false},
                    {content: "$E = mc^2$", hasMath: true},
                    {content: "合格", hasMath: false},
                    {content: "12", hasMath: false},
                    {content: "15", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ],
                [
                    {content: "几何计算", hasMath: false},
                    {content: "$\\pi r^2$", hasMath: true},
                    {content: "合格", hasMath: false},
                    {content: "12", hasMath: false},
                    {content: "16", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ],
                [
                    {content: "统计分析", hasMath: false},
                    {content: "$\\sum_{i=1}^n x_i$", hasMath: true},
                    {content: "合格", hasMath: false},
                    {content: "12", hasMath: false},
                    {content: "17", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ],
                [
                    {content: "积分计算", hasMath: false},
                    {content: "$\\int_0^1 x dx = \\frac{1}{2}$", hasMath: true},
                    {content: "合格", hasMath: false},
                    {content: "12", hasMath: false},
                    {content: "18", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ]
            ],
            merges: [
                {
                    startRow: 2,
                    startCol: 3,
                    endRow: 3,
                    endCol: 4,
                    content: "12月17-18日"
                }
            ],
            headerMerges: [
                {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: "检查工序\n名称"},
                {startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: "检 查 项 目 及 技 术 条 件"},
                {startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: "实 际 检 查 结 果"},
                {startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: "完工"},
                {startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: "操作员"},
                {startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: "班组长"},
                {startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: "检验员"}
            ],
            headerWidthConfig: {
                columnWidths: [100, 460, 160, 32, 32, 32, 32, 32],
                headerHeights: [35, 35]
            },
            verticalHeadersConfig: [false, false, false, false, false, true, true, true],
            metadata: {
                title: "数学公式检验记录表",
                useDynamicHeader: true,
                hasCustomWidth: true,
                totalRows: 4,
                totalColumns: 8,
                headerRows: 2,
                exportTime: new Date().toISOString(),
                hasMergedCells: true,
                hasHeaderMerges: true,
                hasLatexProcessing: true
            }
        }
    },

    customHeader: {
        name: '自定义表头',
        description: '包含自定义表头配置的复杂示例',
        data: {
            title: "产品质量检验记录表",
            headers: [
                ['产品名称', '规格型号', '质量检验', '生产信息', '', '操作者', '班组长', '检验员'],
                ['', '', '', '批次号', '日期', '', '', '']
            ],
            cellRows: [
                [
                    {content: "智能手机", hasMath: false},
                    {content: "iPhone 15 Pro", hasMath: false},
                    {content: "外观检查：无划痕", hasMath: false},
                    {content: "A001", hasMath: false},
                    {content: "2024-01-15", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ],
                [
                    {content: "", hasMath: false},
                    {content: "", hasMath: false},
                    {content: "功能测试：正常", hasMath: false},
                    {content: "A001", hasMath: false},
                    {content: "2024-01-15", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ],
                [
                    {content: "平板电脑", hasMath: false},
                    {content: "iPad Air", hasMath: false},
                    {content: "性能测试：$CPU = 95\\%$", hasMath: true},
                    {content: "B002", hasMath: false},
                    {content: "2024-01-16", hasMath: false},
                    {content: "赵六", hasMath: false},
                    {content: "钱七", hasMath: false},
                    {content: "孙八", hasMath: false}
                ]
            ],
            merges: [
                {
                    startRow: 0,
                    startCol: 0,
                    endRow: 1,
                    endCol: 0,
                    content: "智能手机\n（多功能检测）"
                }
            ],
            headerMerges: [
                {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '产品名称'},
                {startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '规格型号'},
                {startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '质量检验'},
                {startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '生产信息'},
                {startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: '操作者'},
                {startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: '班组长'},
                {startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: '检验员'}
            ],
            headerWidthConfig: {
                columnWidths: [180, 150, 120, 100, 100, 90, 90, 90],
                headerHeights: [60, 40]
            },
            verticalHeadersConfig: [false, false, false, false, false, true, true, true],
            metadata: {
                title: "产品质量检验记录表",
                useDynamicHeader: true,
                hasCustomWidth: true,
                totalRows: 3,
                totalColumns: 8,
                headerRows: 2,
                exportTime: new Date().toISOString(),
                hasMergedCells: true,
                hasHeaderMerges: true,
                hasLatexProcessing: true
            }
        }
    },

    // 新增示例数据
    test: {
        name: '测试json表头',
        description: '包含json表头配置的复杂示例',
        data: {
            title: "检验记录表",
            headers: [
                ['产品名称', '生产信息', '', '责任人员', '', ''],
                ['', '批次号', '日期', '检验员', '审核员', '负责人']
            ],
            cellRows: [
                [
                    {content: "智能手机", hasMath: false},
                    {content: "A001", hasMath: false},
                    {content: "2024-01-15", hasMath: false},
                    {content: "张三", hasMath: false},
                    {content: "李四", hasMath: false},
                    {content: "王五", hasMath: false}
                ]
            ],
            merges: [
                {
                    startRow: 0,
                    startCol: 0,
                    endRow: 0,
                    endCol: 0,
                    content: "智能手机\n（多功能检测）"
                }
            ],
            headerMerges: [
                {startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '产品名称'},
                {startRow: 0, startCol: 1, endRow: 0, endCol: 2, content: '生产信息'},
                {startRow: 0, startCol: 3, endRow: 0, endCol: 5, content: '责任人员'}
            ],
            headerWidthConfig: {
                columnWidths: [180, 100, 100, 80, 80, 80],
                headerHeights: [60, 40]
            },
            verticalHeadersConfig: [false, false, false, true, true, true],
            metadata: {
                title: "检验记录表",
                useDynamicHeader: true,
                hasCustomWidth: true,
                totalRows: 1,
                totalColumns: 6,
                headerRows: 2,
                exportTime: new Date().toISOString(),
                hasMergedCells: true,
                hasHeaderMerges: true,
                hasLatexProcessing: true
            }
        }
    },

    // 新增：嵌套表格示例
    nestedTable: {
        name: '嵌套表格示例',
        description: '展示嵌套表格功能的使用示例',
        data: {
            title: "产品检验记录表（含嵌套表格）",
            headers: [
                [
                    "检查工序\n名称",
                    "检 查 项 目 及 技 术 条 件",
                    "实 际 检 查 结 果",
                    "完工",
                    "",
                    "操作员",
                    "班组长",
                    "检验员"
                ],
                [
                    "",
                    "",
                    "",
                    "月",
                    "日",
                    "",
                    "",
                    ""
                ]
            ],
            cellRows: [
                [
                    {content: "外观检查", originContent: "外观检查", hasMath: false},
                    {content: "1.表面无划痕\n2.颜色均匀", originContent: "1.表面无划痕\n2.颜色均匀", hasMath: false},
                    {
                        content: "详细检查结果",
                        originContent: "详细检查结果",
                        hasMath: false,
                        // 嵌套表格配置
                        nestedTable: {
                            enabled: true,
                            config: {
                                columnWidths: [80, 60, 80],
                                rowHeights: [30, 40, 50],
                                cellRows: [
                                    [
                                        {content: "检查项", originContent: "检查项", hasMath: false},
                                        {content: "结果", originContent: "结果", hasMath: false},
                                        {content: "备注", originContent: "备注", hasMath: false}
                                    ],
                                    [
                                        {content: "表面", originContent: "表面", hasMath: false},
                                        {content: "合格", originContent: "合格", hasMath: false},
                                        {content: "无划痕", originContent: "无划痕", hasMath: false}
                                    ],
                                    [
                                        {content: "颜色", originContent: "颜色", hasMath: false},
                                        {content: "合格", originContent: "合格", hasMath: false},
                                        {content: "均匀", originContent: "均匀", hasMath: false}
                                    ]
                                ],
                                metadata: {
                                    title: "外观检查详情",
                                    level: 1,
                                    parentCell: {row: 0, col: 2},
                                    columns: 3,
                                    rows: 3
                                }
                            }
                        }
                    },
                    {content: "8", originContent: "8", hasMath: false},
                    {content: "22", originContent: "22", hasMath: false},
                    {content: "张三", originContent: "张三", hasMath: false},
                    {content: "李四", originContent: "李四", hasMath: false},
                    {content: "王五", originContent: "王五", hasMath: false}
                ],
                [
                    {content: "功能测试", originContent: "功能测试", hasMath: false},
                    {content: "各项功能正常运行", originContent: "各项功能正常运行", hasMath: false},
                    {
                        content: "测试数据",
                        originContent: "测试数据",
                        hasMath: false,
                        // 另一个嵌套表格示例
                        nestedTable: {
                            enabled: true,
                            config: {
                                columnWidths: [90, 80, 60],
                                rowHeights: [50, 40, 30],
                                cellRows: [
                                    [
                                        {content: "测试项目", originContent: "测试项目", hasMath: false},
                                        {content: "测试值", originContent: "测试值", hasMath: false},
                                        {content: "状态", originContent: "状态", hasMath: false}
                                    ],
                                    [
                                        {content: "启动时间", originContent: "启动时间", hasMath: false},
                                        {content: "2.3秒", originContent: "2.3秒", hasMath: false},
                                        {content: "正常", originContent: "正常", hasMath: false}
                                    ],
                                    [
                                        {content: "响应速度", originContent: "响应速度", hasMath: false},
                                        {content: "0.8秒", originContent: "0.8秒", hasMath: false},
                                        {content: "优秀", originContent: "优秀", hasMath: false}
                                    ]
                                ],
                                metadata: {
                                    title: "功能测试数据",
                                    level: 1,
                                    parentCell: {row: 1, col: 2},
                                    columns: 3,
                                    rows: 3
                                }
                            }
                        }
                    },
                    {content: "8", originContent: "8", hasMath: false},
                    {content: "23", originContent: "23", hasMath: false},
                    {content: "赵六", originContent: "赵六", hasMath: false},
                    {content: "钱七", originContent: "钱七", hasMath: false},
                    {content: "孙八", originContent: "孙八", hasMath: false}
                ]
            ],
            merges: [],
            headerMerges: [
                {
                    startRow: 0,
                    startCol: 0,
                    endRow: 1,
                    endCol: 0,
                    content: "检查工序\n名称"
                },
                {
                    startRow: 0,
                    startCol: 1,
                    endRow: 1,
                    endCol: 1,
                    content: "检 查 项 目 及 技 术 条 件"
                },
                {
                    startRow: 0,
                    startCol: 2,
                    endRow: 1,
                    endCol: 2,
                    content: "实 际 检 查 结 果"
                },
                {
                    startRow: 0,
                    startCol: 3,
                    endRow: 0,
                    endCol: 4,
                    content: "完工"
                },
                {
                    startRow: 0,
                    startCol: 5,
                    endRow: 1,
                    endCol: 5,
                    content: "操作员"
                },
                {
                    startRow: 0,
                    startCol: 6,
                    endRow: 1,
                    endCol: 6,
                    content: "班组长"
                },
                {
                    startRow: 0,
                    startCol: 7,
                    endRow: 1,
                    endCol: 7,
                    content: "检验员"
                }
            ],
            headerWidthConfig: {
                columnWidths: [100, 460, 240, 32, 32, 32, 32, 32],
                headerHeights: [35, 35]
            },
            verticalHeadersConfig: [false, false, false, false, false, true, true, true],
            metadata: {
                title: "产品检验记录表（含嵌套表格）",
                useDynamicHeader: true,
                hasCustomWidth: true,
                totalRows: 2,
                totalColumns: 8,
                headerRows: 2,
                exportTime: new Date().toISOString(),
                hasMergedCells: false,
                hasHeaderMerges: true,
                hasLatexProcessing: false,
                hasNestedTables: true,
                nestedLevel: 0,
                maxNestedLevel: 2
            }
        }
    }
}

/**
 * 获取所有预设数据的选项列表
 * @returns {Array} 预设数据选项数组
 */
export function getPresetOptions() {
    return Object.keys(TABLE_PRESETS).map(key => ({
        value: key,
        label: TABLE_PRESETS[key].name,
        description: TABLE_PRESETS[key].description
    }))
}

/**
 * 根据键获取预设数据
 * @param {string} key 预设数据键
 * @returns {Object|null} 预设数据对象
 */
export function getPresetData(key) {
    return TABLE_PRESETS[key]?.data || null
}

/**
 * 添加新的预设数据
 * @param {string} key 预设数据键
 * @param {Object} preset 预设数据对象
 */
export function addPresetData(key, preset) {
    if (!key || !preset) {
        throw new Error('键和预设数据都是必需的')
    }

    if (TABLE_PRESETS[key]) {
        console.warn(`预设数据 "${key}" 已存在，将被覆盖`)
    }

    TABLE_PRESETS[key] = {
        name: preset.name || key,
        description: preset.description || '',
        data: preset.data || {}
    }
}

/**
 * 删除预设数据
 * @param {string} key 预设数据键
 * @returns {boolean} 删除是否成功
 */
export function removePresetData(key) {
    if (TABLE_PRESETS[key]) {
        delete TABLE_PRESETS[key]
        return true
    }
    return false
}

/**
 * 检查预设数据是否存在
 * @param {string} key 预设数据键
 * @returns {boolean} 是否存在
 */
export function hasPresetData(key) {
    return !!TABLE_PRESETS[key]
}

export default {
    TABLE_PRESETS,
    getPresetOptions,
    getPresetData,
    addPresetData,
    removePresetData,
    hasPresetData
}
