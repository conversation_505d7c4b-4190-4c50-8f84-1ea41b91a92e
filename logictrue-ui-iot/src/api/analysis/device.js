import request from '@/utils/request'

/**
 * 获取设备列表
 * @param {Object} query 查询参数
 */
export function getDeviceList(query) {
  return request({
    url: '/iot/device/list',
    method: 'get',
    params: query
  })
}

/**
 * 根据设备编码获取设备信息
 * @param {String} deviceCode 设备编码
 */
export function getDeviceByCode(deviceCode) {
  return request({
    url: `/iot/device/code/${deviceCode}`,
    method: 'get'
  })
}

/**
 * 获取所有设备编码列表
 */
export function getAllDevice() {
  return request({
    url: '/iot/device/getAll',
    method: 'get'
  })
}
