import request from '@/utils/request'

/**
 * Word导出相关API
 */

/**
 * 导出表格到Word文档（支持合并单元格）
 * @param {Object} data 表格数据
 * @param {string} data.title - 文档标题
 * @param {Array} data.headers - 表头数组
 * @param {Array} data.cellRows - 数据行数组（支持公式和宽度配置）
 * @param {Array} data.merges - 合并单元格配置数组
 * @param {Object} data.metadata - 元数据信息
 * @returns {Promise} 返回文件流
 */
export function exportTableToWord(data) {
  console.log('调用Word导出API，数据:', data)

  return request({
    url: '/word/exportNewJsonFormat',
    method: 'post',
    data: data,
    responseType: 'blob', // 重要：设置响应类型为blob以处理文件流
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 60000 // 设置60秒超时，因为Word导出可能需要较长时间
  })
}

/**
 * 导出简单表格到Word文档（兼容旧版本，不含合并单元格）
 * @param {Object} data 表格数据
 * @returns {Promise} 返回文件流
 */
export function exportSimpleTableToWord(data) {
  return request({
    url: '/word/exportTable',
    method: 'post',
    data: data,
    responseType: 'blob',
    timeout: 60000
  })
}

/**
 * 预览Word导出效果（返回HTML预览）
 * @param {Object} data 导出数据
 * @returns {Promise} 返回HTML预览内容
 */
export function previewWordExport(data) {
  console.log('预览Word导出效果，数据:', data)

  return request({
    url: '/word/previewTable',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

/**
 * 获取Word导出模板列表
 * @returns {Promise} 返回模板列表
 */
export function getWordTemplates() {
  return request({
    url: '/word/templates',
    method: 'get'
  })
}

/**
 * 导出检验记录当前页
 * @param {Object} data 导出请求数据
 * @param {string} data.carId - 车辆ID
 * @param {number} data.pageOrder - 页面顺序
 * @param {string} data.title - 文档标题
 * @param {boolean} data.includeCheckRecords - 是否包含检验记录数据
 * @returns {Promise} 返回文件流
 */
export function exportCheckRecordCurrentPage(data) {
  console.log('调用检验记录当前页导出API，数据:', data)

  return request({
    url: '/word/checkRecordExport/exportCurrentPage',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 120000 // 设置2分钟超时，因为后端查询数据可能需要较长时间
  })
}

/**
 * 导出检验记录全部页面
 * @param {Object} data 导出请求数据
 * @param {string} data.carId - 车辆ID
 * @param {string} data.title - 文档标题
 * @param {boolean} data.includeCheckRecords - 是否包含检验记录数据
 * @returns {Promise} 返回文件流
 */
export function exportCheckRecordAllPages(data) {
  console.log('调用检验记录全部页面导出API，数据:', data)

  return request({
    url: '/word/checkRecordExport/exportAllPages',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 180000 // 设置3分钟超时，因为全部页面导出可能需要更长时间
  })
}

/**
 * 批量导出检验记录
 * @param {Object} data 导出请求数据
 * @param {Array} data.carIds - 车辆ID列表
 * @param {string} data.title - 文档标题
 * @param {boolean} data.includeCheckRecords - 是否包含检验记录数据
 * @returns {Promise} 返回文件流
 */
export function exportCheckRecordBatch(data) {
  console.log('调用检验记录批量导出API，数据:', data)

  return request({
    url: '/word/checkRecordExport/exportBatch',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 300000 // 设置5分钟超时，因为批量导出可能需要很长时间
  })
}

/**
 * 使用指定模板导出Word文档
 * @param {Object} params 导出参数
 * @param {string} params.templateId - 模板ID
 * @param {Object} params.data - 数据
 * @returns {Promise} 返回文件流
 */
export function exportWithTemplate(params) {
  console.log('使用模板导出Word，参数:', params)

  return request({
    url: '/word/exportWithTemplate',
    method: 'post',
    data: params,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    },
    timeout: 60000
  })
}


