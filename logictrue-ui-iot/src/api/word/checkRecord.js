import request from '@/utils/request'

// 分页查询检验记录数据
export function getCheckRecord(params) {
  return request({
    url: '/check/get',
    method: 'get',
    params
  })
}

// 保存或更新检验记录数据
export function saveOrUpdateCheckRecord(data) {
  return request({
    url: '/check/saveOrUpdate',
    method: 'post',
    data
  })
}

// 删除检验记录数据
export function deleteCheckRecord(id) {
  return request({
    url: '/check/delete',
    method: 'delete',
    params: { id }
  })
}
