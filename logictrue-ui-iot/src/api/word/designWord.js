import request from '@/utils/request'

// 查询检验记录表设计列表
export function listDesignWord(query) {
  return request({
    url: '/word/designWord/list',
    method: 'get',
    params: query
  })
}

// 分页查询检验记录表设计列表
export function pageDesignWord(params) {
  return request({
    url: '/word/designWord/page',
    method: 'get',
    params
  })
}

// 查询检验记录表设计详细
export function getDesignWord(id) {
  return request({
    url: '/word/designWord/' + id,
    method: 'get'
  })
}

// 根据车辆ID查询检验记录表设计
export function getDesignWordByCarId(carId) {
  return request({
    url: '/word/designWord/byCarId/' + carId,
    method: 'get'
  })
}

// 根据车辆ID获取所有页面
export function getDesignWordPagesByCarId(carId) {
  return request({
    url: '/word/designWord/pages/' + carId,
    method: 'get'
  })
}

// 根据车辆ID和页面顺序获取特定页面
export function getDesignWordByCarIdAndPage(carId, pageOrder) {
  return request({
    url: '/word/designWord/page/' + carId + '/' + pageOrder,
    method: 'get'
  })
}

// 根据车辆ID获取当前活动页面
export function getActiveDesignWordByCarId(carId) {
  return request({
    url: '/word/designWord/activePage/' + carId,
    method: 'get'
  })
}

// 设置活动页面
export function setActivePage(carId, pageOrder) {
  return request({
    url: '/word/designWord/setActivePage',
    method: 'post',
    params: { carId, pageOrder }
  })
}

// 更新总页数
export function updateTotalPages(carId, totalPages) {
  return request({
    url: '/word/designWord/updateTotalPages',
    method: 'post',
    params: { carId, totalPages }
  })
}

// 新增检验记录表设计
export function addDesignWord(data) {
  return request({
    url: '/word/designWord',
    method: 'post',
    data: data
  })
}

// 修改检验记录表设计
export function updateDesignWord(data) {
  return request({
    url: '/word/designWord',
    method: 'put',
    data: data
  })
}

// 保存或更新检验记录表设计
export function saveOrUpdateDesignWord(data) {
  return request({
    url: '/word/designWord/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 删除检验记录表设计
export function delDesignWord(ids) {
  return request({
    url: '/word/designWord/' + ids,
    method: 'delete'
  })
}
