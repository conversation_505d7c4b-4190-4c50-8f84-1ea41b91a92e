import router from './router';
import store from './store';
import {Message} from 'element-ui';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import {getToken} from '@/utils/auth';
import {getConfigKey} from '@/api/system/config';

NProgress.configure({showSpinner: false});

const whiteList = [
  '/eqPowerByDays',
  '/login',
  '/auth-redirect',
  '/bind',
  '/register',
  '/materData',
  '/multiple',
  '/single',
  '/meterStructure',
  '/meterCollection',
  '/factorMatch',
  '/peakSet',
  '/bigscreen',
];

router.beforeEach((to, from, next) => {
  NProgress.start();
  if (getToken()) {
    //console.log("路径："+to.path)
    //console.log(to.query.type)
    to.meta.title && store.dispatch('settings/setTitle', to.meta.title);
    /* has token*/
    let menuType = to.query.type;
    if (to.path === '/login' && menuType) {
      store.dispatch('LogOut').then(() => {
        next({path: '/login?type=' + menuType});
      });
      /*next({ path: '/login?type='+menuType });
      NProgress.done();*/
    } else if (to.path === '/login') {
      next({path: '/'});
      NProgress.done();
    } else {
      if (store.getters.roles.length === 0) {
        // 判断当前用户是否已拉取完user_info信息
        store
          .dispatch('GetInfo')
          .then(() => {
            // 获取配置
            getConfigKey('sys.table.border').then((res) => {
              if (res.msg === 'true') {
                document.documentElement.style.setProperty(
                  '--tableBorder',
                  '1px solid #ebeef5',
                );
              }
            });
            if (sessionStorage.getItem('docTitle')) {
              document.title = sessionStorage.getItem('docTitle')
            } else {
              getConfigKey('sys.index.systemName').then((res) => {
                store.state.app.appTitle = res.msg;
                document.title = res.msg;
              });
            }


            getConfigKey('sys.net.upload_url').then((res) => {
              store.state.app.uploadUrl = res.msg;

              let ipStr = store.state.app.uploadUrl
                .split('//')[1]
              // console.log(process.env.VUE_APP_BASE_API);
            });

            store.dispatch('GenerateRoutes').then((accessRoutes) => {
              // 根据roles权限生成可访问的路由表
              //srouter.addRoutes(accessRoutes); // 动态添加可访问路由表
              if (to.path == '/index') {
                getConfigKey('sys.indexType').then((res) => {
                  if (res.msg == 'noIndex') {
                    let path = accessRoutes[0].path;
                    if (
                      accessRoutes[0].children &&
                      accessRoutes[0].children.length > 0
                    ) {
                      path += '/' + accessRoutes[0].children[0].path;
                    }
                    next({path, replace: true});
                  } else {
                    next({...to, replace: true}); // hack方法 确保addRoutes已完成
                  }
                });
              } else {
                next({...to, replace: true}); // hack方法 确保addRoutes已完成
              }
            });
          })
          .catch((err) => {
            store.dispatch('LogOut').then(() => {
              Message.error(err);
              next({path: '/'});
            });
          });
      } else {
        next();
      }
    }
  } else {
    //     // 没有token
    if (whiteList.some((item) => to.path.includes(item))) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
