<template>
  <div class="table-container-wrapper">
    <!-- 表格容器 -->
    <div class="table-wrapper">
      <div class="table-container" ref="tableContainer" :style="tableContainerStyle">
        <div class="table-scroll-container">
          <table ref="editableTable" :style="tableStyle" @contextmenu="handleContextMenu">
          <!-- 动态表头 -->
          <tr v-for="(headerRow, headerRowIndex) in currentHeaderConfig.headers" :key="'header-' + headerRowIndex">
            <td
              v-for="(headerCell, headerCellIndex) in headerRow"
              :key="'header-' + headerRowIndex + '-' + headerCellIndex"
              v-show="!isHeaderCellHidden(headerRowIndex, headerCellIndex)"
              class="header-cell"
              :rowspan="getHeaderCellRowspan(headerRowIndex, headerCellIndex)"
              :colspan="getHeaderCellColspan(headerRowIndex, headerCellIndex)"
              :title="getHeaderCellTitle(headerRowIndex, headerCellIndex)"
              :style="getHeaderCellStyle(headerRowIndex, headerCellIndex)"
              :class="getHeaderCellClass(headerRowIndex, headerCellIndex)"
            >
              <span
                v-if="shouldUseVerticalText(headerCellIndex)"
                class="vertical-text-span"
              >
                {{ getHeaderCellContent(headerRowIndex, headerCellIndex) }}
              </span>
              <span v-else>
                {{ getHeaderCellContent(headerRowIndex, headerCellIndex) }}
              </span>
            </td>
          </tr>
          <!-- 数据行 - 可编辑 -->
          <tr v-for="(row, rowIndex) in dataRows" :key="rowIndex">
            <td
              v-for="(cell, cellIndex) in row"
              :key="cellIndex"
              v-show="!isCellHidden(rowIndex, cellIndex)"
              class="editable-cell"
              :class="{
                'has-math': cell.hasMath,
                'merged-cell': cell.merged,
                'has-nested-table': hasNestedTable(cell),
                'selected': isCellSelected(rowIndex, cellIndex),
                'editing': cell.isEditing
              }"
              :style="getMergedCellStyle(rowIndex, cellIndex)"
              :rowspan="getCellRowspan(cell)"
              :colspan="getCellColspan(cell)"
              @click="handleCellClick(rowIndex, cellIndex, $event)"
              @contextmenu="handleCellContextMenu(rowIndex, cellIndex, $event)"
            >
              <!-- 嵌套表格渲染 -->
              <div v-if="hasNestedTable(cell)" class="nested-table-container">
                <!-- 主内容区域 -->
                <div class="main-content-area">
                  <CellEditor
                    :ref="`cell-${rowIndex}-${cellIndex}`"
                    :content="cell.content"
                    :has-math="cell.hasMath"
                    :auto-focus="cell.isEditing"
                    :select-all="cell.selectAll"
                    :height="30"
                    :min-height="30"
                    @start-edit="handleCellStartEdit(rowIndex, cellIndex)"
                    @finish-edit="handleCellFinishEdit(rowIndex, cellIndex, $event)"
                    @cancel-edit="handleCellCancelEdit(rowIndex, cellIndex)"
                    @content-change="handleCellContentChange(rowIndex, cellIndex, $event)"
                    @input="handleCellInput(rowIndex, cellIndex, $event)"
                    @move-next="handleCellMoveNext(rowIndex, cellIndex, $event)"
                  />
                </div>

                <!-- 嵌套表格内容 - 简化版无表头 -->
                <div class="nested-table-content">
                  <table class="nested-table" :style="getNestedTableStyle(cell)">
                    <tr v-for="(nestedRow, nestedRowIndex) in getNestedTableDataRows(cell)" :key="nestedRowIndex">
                      <td
                        v-for="(nestedCell, nestedCellIndex) in nestedRow"
                        :key="nestedCellIndex"
                        class="nested-cell"
                        :style="getNestedCellStyle(cell, nestedCellIndex, nestedRowIndex)"
                      >
                        <CellEditor
                          :ref="`nested-cell-${rowIndex}-${cellIndex}-${nestedRowIndex}-${nestedCellIndex}`"
                          :content="nestedCell.content"
                          :has-math="nestedCell.hasMath"
                          :auto-focus="nestedCell.isEditing"
                          :select-all="nestedCell.selectAll"
                          :height="getNestedCellHeight(cell, nestedRowIndex)"
                          :min-height="getNestedCellHeight(cell, nestedRowIndex)"
                          @start-edit="handleNestedCellStartEdit(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex)"
                          @finish-edit="handleNestedCellFinishEdit(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex, $event)"
                          @cancel-edit="handleNestedCellCancelEdit(rowIndex, cellIndex, nestedRowIndex, nestedCellIndex)"
                        />
                      </td>
                    </tr>
                  </table>
                </div>
              </div>

              <!-- 普通单元格渲染 -->
              <CellEditor
                v-else
                :ref="`cell-${rowIndex}-${cellIndex}`"
                :content="cell.content"
                :has-math="cell.hasMath"
                :auto-focus="cell.isEditing"
                :select-all="cell.selectAll"
                :height="getCellEditorHeight(rowIndex, cellIndex)"
                :min-height="getCellEditorMinHeight(rowIndex, cellIndex)"
                @start-edit="handleCellStartEdit(rowIndex, cellIndex)"
                @finish-edit="handleCellFinishEdit(rowIndex, cellIndex, $event)"
                @cancel-edit="handleCellCancelEdit(rowIndex, cellIndex)"
                @content-change="handleCellContentChange(rowIndex, cellIndex, $event)"
                @input="handleCellInput(rowIndex, cellIndex, $event)"
                @move-next="handleCellMoveNext(rowIndex, cellIndex, $event)"
              />
            </td>
          </tr>
        </table>
        </div>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div
      v-if="contextMenuVisible"
      class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
    >
      <div class="context-menu-info">
        <span class="info-label">当前列宽:</span>
        <span class="info-value">{{ getCurrentColumnWidthForDisplay() }}px</span>
      </div>
      <div class="context-menu-divider"></div>
      <div
        class="context-menu-item"
        @click="handleRowHeightClick"
        @mousedown.stop
      >
        调整此行高度
      </div>
      <div
        class="context-menu-item"
        @click="handleColumnWidthClick"
        @mousedown.stop
      >
        调整此列宽度
      </div>
      <div class="context-menu-divider"></div>

      <!-- 嵌套表格相关菜单项 -->
      <div
        v-if="isDataRow && canAddNestedTable()"
        class="context-menu-item"
        @click="openNestedTableDesigner"
        @mousedown.stop
      >
        插入嵌套表格
      </div>
<!--      <div
        v-if="isDataRow && canAddNestedTable()"
        class="context-menu-item"
        @click="openAddNestedTableDialog"
        @mousedown.stop
      >
        快速添加嵌套表格
      </div>-->
      <div
        v-if="isDataRow && hasNestedTableInCurrentCell()"
        class="context-menu-item delete-item"
        @click="removeNestedTable"
        @mousedown.stop
      >
        移除嵌套表格
      </div>

      <div v-if="isDataRow" class="context-menu-divider"></div>
      <div
        v-if="isDataRow"
        class="context-menu-item delete-item"
        @click="handleDeleteRowClick"
        @mousedown.stop
      >
        删除此行
      </div>
    </div>

    <!-- 行高调整对话框 -->
    <div v-if="rowHeightDialogVisible" class="dialog-overlay" @click="closeRowHeightDialog">
      <div class="dialog" @click.stop>
        <h3>调整行高度</h3>
        <div class="dialog-content">
          <label>当前行高度: {{ currentRowHeight }}px</label>
          <input
            type="number"
            v-model="newRowHeight"
            :min="minCellHeight"
            placeholder="输入新的行高度(px)"
            @keydown.enter="applyRowHeight"
            @keydown.esc="closeRowHeightDialog"
            ref="rowHeightInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeRowHeightDialog" class="btn-cancel">取消</button>
          <button @click="applyRowHeight" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 列宽调整对话框 -->
    <div v-if="columnWidthDialogVisible" class="dialog-overlay" @click="closeColumnWidthDialog">
      <div class="dialog" @click.stop>
        <h3>调整列宽度</h3>
        <div class="dialog-content">
          <label>当前列宽度: {{ currentColumnWidth }}px</label>
          <input
            type="number"
            v-model="newColumnWidth"
            :min="minCellWidth"
            placeholder="输入新的列宽度(px)"
            @keydown.enter="applyColumnWidth"
            @keydown.esc="closeColumnWidthDialog"
            ref="columnWidthInput"
          >
        </div>
        <div class="dialog-buttons">
          <button @click="closeColumnWidthDialog" class="btn-cancel">取消</button>
          <button @click="applyColumnWidth" class="btn-confirm">确定</button>
        </div>
      </div>
    </div>

    <!-- 嵌套表格设计器弹窗 -->
    <div v-if="nestedTableDesignerVisible" class="designer-overlay" @click="closeNestedTableDesigner">
      <div class="designer-dialog" @click.stop>
        <div class="designer-header">
          <h3>嵌套表格设计器</h3>
          <button @click="closeNestedTableDesigner" class="close-btn">×</button>
        </div>
        <div class="designer-content">
          <NestedTableDesigner
            ref="nestedTableDesigner"
            @apply-to-demo="handleDesignerApply"
          />
        </div>
        <div class="designer-actions">
          <button @click="closeNestedTableDesigner" class="btn-cancel">取消</button>
          <button @click="insertNestedTableFromDesigner" class="btn-confirm">插入</button>
        </div>
      </div>
    </div>

    <!-- 添加嵌套表格对话框 -->
    <div v-if="addNestedTableDialogVisible" class="dialog-overlay" @click="closeAddNestedTableDialog">
      <div class="dialog" @click.stop>
        <h3>快速添加嵌套表格</h3>
        <div class="dialog-content">
          <div class="form-group">
            <label>表格标题:</label>
            <input
              v-model="nestedTableForm.title"
              type="text"
              placeholder="请输入表格标题（可选）"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>列数:</label>
            <input
              v-model.number="nestedTableForm.columns"
              type="number"
              min="1"
              max="10"
              class="form-input"
            />
          </div>
          <div class="form-group">
            <label>行数:</label>
            <input
              v-model.number="nestedTableForm.rows"
              type="number"
              min="1"
              max="20"
              class="form-input"
            />
          </div>
        </div>
        <div class="dialog-buttons">
          <button @click="closeAddNestedTableDialog" class="btn-cancel">取消</button>
          <button @click="createNestedTable" class="btn-confirm">创建</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CellEditor from './CellEditor.vue'
import NestedTableDesigner from '@/views/test/nested-table-designer.vue'

export default {
  name: 'TableContainer',
  components: {
    CellEditor,
    NestedTableDesigner
  },
  props: {
    // 表格尺寸
    tableWidth: {
      type: String,
      default: '1600px'
    },
    tableHeight: {
      type: String,
      default: '600px'
    },
    // 表格数据
    dataRows: {
      type: Array,
      default: () => [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ]
    },
    // 最小尺寸限制
    minCellWidth: {
      type: Number,
      default: 20
    },
    minCellHeight: {
      type: Number,
      default: 20
    },
    // 表头单元格默认配置
    headerCellWidth: {
      type: Number,
      default: 120
    },
    headerCellHeight: {
      type: Number,
      default: 50
    },
    // 每列的宽度配置（数组，默认8列，支持动态调整）
    columnWidths: {
      type: Array,
      default: () => [150, 200, 150, 50, 50, 80, 80, 80]
    },
    // 表头文字纵向显示配置（数组，默认8列，支持动态调整）
    verticalHeadersConfig: {
      type: Array,
      default: () => [false, false, false, false, false, true, true, true]
    },
    // 动态表头配置（JSON格式）
    headerConfig: {
      type: Object,
      default: null
    },
    // 表头宽度配置（JSON格式）
    headerWidthConfig: {
      type: Object,
      default: null
    },
    // 是否使用动态表头
    useDynamicHeader: {
      type: Boolean,
      default: false
    },

    // 新增：嵌套表格相关 props
    nestedLevel: {
      type: Number,
      default: 0
    },
    maxNestedLevel: {
      type: Number,
      default: 2
    },
    enableNestedTables: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 右键菜单状态
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      currentContextCell: null,
      isDataRow: false,
      currentRowIndex: -1,
      currentColumnIndex: -1,
      contextMenuColumnIndex: -1, // 右键菜单显示的列索引

      // 行高调整对话框
      rowHeightDialogVisible: false,
      currentRowHeight: 0,
      newRowHeight: 0,

      // 列宽调整对话框
      columnWidthDialogVisible: false,
      currentColumnWidth: 0,
      newColumnWidth: 0,

      // 内部列宽状态
      internalColumnWidths: [],

      // 内部行高状态（跟踪每行的实际高度）
      internalRowHeights: [],

      // 内部动态表头状态（避免直接修改props）
      internalUseDynamicHeader: false,
      internalHeaderConfig: null,
      internalHeaderWidthConfig: null,
      internalVerticalHeadersConfig: null,
      internalDataRows: null,

      // MathJax相关
      mathJaxReady: false,

      // 新增：嵌套表格相关状态
      addNestedTableDialogVisible: false,
      nestedTableDesignerVisible: false,
      nestedTableForm: {
        title: '',
        columns: 3,
        rows: 2
      },
      currentNestedTablePosition: null, // 当前操作的嵌套表格位置

      // 单元格选中状态
      selectedCell: {
        row: -1,
        col: -1
      },
      lastClickTime: 0,
      doubleClickDelay: 300 // 双击检测延迟（毫秒）
    }
  },
  computed: {
    tableContainerStyle() {
      const style = {
        width: this.tableWidth
      }

      if (this.tableHeight !== 'auto') {
        style.height = this.tableHeight
        style.maxHeight = this.tableHeight
      }

      return style
    },
    tableStyle() {
      // 计算表格总宽度
      const totalWidth = this.currentColumnWidths.reduce((sum, width) => sum + width, 0)
      return {
        width: `${totalWidth}px`,
        height: 'auto'
      }
    },
    // 获取当前使用的列宽数组
    currentColumnWidths() {
      console.log('currentColumnWidths计算 - 开始:', {
        internalColumnWidthsLength: this.internalColumnWidths.length,
        internalColumnWidths: this.internalColumnWidths,
        currentHeaderWidthConfig: this.currentHeaderWidthConfig,
        columnWidths: this.columnWidths
      })

      // 优先使用内部调整的列宽
      if (this.internalColumnWidths.length > 0) {
        console.log('currentColumnWidths使用内部调整配置:', this.internalColumnWidths)
        return this.internalColumnWidths
      }

      // 然后使用动态表头配置的列宽
      if (this.currentHeaderWidthConfig && this.currentHeaderWidthConfig.columnWidths) {
        console.log('currentColumnWidths使用动态配置:', this.currentHeaderWidthConfig.columnWidths)
        return this.currentHeaderWidthConfig.columnWidths
      }

      // 最后使用props的默认列宽
      console.log('currentColumnWidths使用默认配置:', this.columnWidths)
      return this.columnWidths
    },
    // 获取当前使用的表头配置
    currentHeaderConfig() {
      // 优先使用内部状态，然后是props，最后是默认值
      const useDynamic = this.internalUseDynamicHeader || this.useDynamicHeader
      const headerConfig = this.internalHeaderConfig || this.headerConfig

      if (useDynamic && headerConfig) {
        return headerConfig
      }
      // 返回默认表头配置
      return {
        headers: [
          ['检查项目', '技术要求', '检查结果', '完工', '', '检查员', '组长', '检验员'],
          ['', '', '', '月', '日', '', '', '']
        ],
        merges: [
          { startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '检查项目' },
          { startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '技术要求' },
          { startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '检查结果' },
          { startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '完工' },
          { startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: '检查员' },
          { startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: '组长' },
          { startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: '检验员' }
        ]
      }
    },
    // 获取当前使用的表头宽度配置
    currentHeaderWidthConfig() {
      // 优先使用内部状态，然后是props，最后是默认值
      const useDynamic = this.internalUseDynamicHeader || this.useDynamicHeader
      const widthConfig = this.internalHeaderWidthConfig || this.headerWidthConfig

      console.log('currentHeaderWidthConfig 计算:', {
        internalUseDynamicHeader: this.internalUseDynamicHeader,
        useDynamicHeader: this.useDynamicHeader,
        useDynamic,
        internalHeaderWidthConfig: this.internalHeaderWidthConfig,
        headerWidthConfig: this.headerWidthConfig,
        widthConfig
      })

      if (useDynamic && widthConfig) {
        console.log('使用动态宽度配置:', widthConfig)
        return widthConfig
      }
      // 返回默认宽度配置
      const defaultConfig = {
        columnWidths: [150, 200, 150, 50, 50, 80, 80, 80],
        headerHeights: [50, 50], // 两行表头的高度
        verticalHeaders: [false, false, false, false, false, true, true, true]
      }
      console.log('使用默认宽度配置:', defaultConfig)
      return defaultConfig
    },
    currentVerticalHeadersConfig() {
      return this.internalVerticalHeadersConfig || this.verticalHeadersConfig
    },
    // 获取当前表头的列数
    currentColumnCount() {
      // 从表头配置中获取列数
      if (this.currentHeaderConfig && this.currentHeaderConfig.headers && this.currentHeaderConfig.headers.length > 0) {
        // 取第一行表头的长度作为列数
        const firstHeaderRow = this.currentHeaderConfig.headers[0]
        return firstHeaderRow ? firstHeaderRow.length : 8
      }
      // 默认8列
      return 8
    },
    // 获取数据单元格的样式
    getDataCellStyle() {
      return (columnIndex) => {
        const width = this.currentColumnWidths[columnIndex] || this.headerCellWidth
        return {
          minWidth: `${width}px !important`,
          width: `${width}px !important`,
          maxWidth: `${width}px !important`,
          boxSizing: 'border-box'
        }
      }
    },
    // 获取表头文字的样式类
    getHeaderTextClass() {
      return (columnIndex) => {
        const isVertical = this.currentVerticalHeadersConfig[columnIndex] || false
        return {
          'vertical-text': isVertical,
          'horizontal-text': !isVertical
        }
      }
    },

    // === 合并单元格相关计算属性 ===

    // 获取合并单元格的样式
    getMergedCellStyle() {
      return (rowIndex, cellIndex) => {
        const baseStyle = this.getDataCellStyle(cellIndex)
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]

        if (cell && cell.merged) {
          const { rowspan, colspan } = cell.merged

          // 计算合并后的宽度
          if (colspan > 1) {
            let totalWidth = 0
            for (let i = cellIndex; i < cellIndex + colspan && i < this.currentColumnWidths.length; i++) {
              totalWidth += this.currentColumnWidths[i] || this.headerCellWidth
            }
            baseStyle.minWidth = `${totalWidth}px !important`
            baseStyle.width = `${totalWidth}px !important`
            baseStyle.maxWidth = `${totalWidth}px !important`
          }

          // 计算合并后的实际高度
          if (rowspan > 1) {
            const totalHeight = this.calculateMergedCellHeight(rowIndex, rowspan)

            baseStyle.minHeight = `${totalHeight}px !important`
            baseStyle.height = `${totalHeight}px !important`
            baseStyle.maxHeight = `${totalHeight}px !important`

            console.log(`合并单元格DOM样式: 行${rowIndex}, 跨度${rowspan}, 总高度${totalHeight}px`)
          }

          // 合并单元格的特殊样式
          baseStyle.backgroundColor = '#f8f9fa'
          baseStyle.border = '2px solid #007bff'
          baseStyle.verticalAlign = 'top' // 确保内容从顶部开始
          baseStyle.padding = '0' // 移除内边距，让编辑器占满整个空间
        }

        return baseStyle
      }
    },

    // 获取单元格的rowspan
    getCellRowspan() {
      return (cell) => {
        return cell && cell.merged && cell.merged.rowspan > 1 ? cell.merged.rowspan : null
      }
    },

    // 获取单元格的colspan
    getCellColspan() {
      return (cell) => {
        return cell && cell.merged && cell.merged.colspan > 1 ? cell.merged.colspan : null
      }
    },

    // 判断数据行单元格是否被隐藏（被其他合并单元格覆盖）
    isCellHidden() {
      return (dataRowIndex, cellIndex) => {
        // 检查是否被其他合并单元格覆盖
        for (let r = 0; r < this.dataRows.length; r++) {
          const row = this.dataRows[r]
          if (!row) continue

          for (let c = 0; c < row.length; c++) {
            const cell = row[c]
            if (cell && cell.merged && !(r === dataRowIndex && c === cellIndex)) {
              const { startRow, startCol, endRow, endCol } = cell.merged
              // 注意：这里的 startRow, endRow 是数据行索引，不需要偏移
              if (dataRowIndex >= startRow && dataRowIndex <= endRow &&
                  cellIndex >= startCol && cellIndex <= endCol) {
                console.log('单元格被合并覆盖:', {
                  dataRowIndex,
                  cellIndex,
                  合并范围: { startRow, startCol, endRow, endCol }
                })
                return true
              }
            }
          }
        }
        return false
      }
    },

    // 计算合并单元格的总高度（统一方法）
    calculateMergedCellHeight(rowIndex, rowspan) {
      let totalHeight = 0

      // 累加所有跨越行的实际高度
      for (let i = 0; i < rowspan; i++) {
        const targetRowIndex = rowIndex + i
        const rowHeight = this.internalRowHeights[targetRowIndex] || this.getActualRowHeight(targetRowIndex)
        totalHeight += Math.max(rowHeight, 50) // 至少50px
      }

      console.log(`合并单元格高度计算: 行${rowIndex}, 跨度${rowspan}, 总高度${totalHeight}px`)
      return totalHeight
    },

    // 获取单元格编辑器的高度
    getCellEditorHeight() {
      return (rowIndex, cellIndex) => {
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
        if (cell && cell.merged) {
          // 合并单元格：使用统一的高度计算方法
          return this.calculateMergedCellHeight(rowIndex, cell.merged.rowspan)
        }

        // 普通单元格：优先使用缓存的高度，避免频繁计算
        const cachedHeight = this.internalRowHeights[rowIndex]
        if (cachedHeight && cachedHeight > 50) {
          return cachedHeight
        }

        return 'auto'
      }
    },

    // 获取单元格编辑器的最小高度
    getCellEditorMinHeight() {
      return (rowIndex, cellIndex) => {
        const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
        if (cell && cell.merged) {
          // 合并单元格：使用统一的高度计算方法
          return this.calculateMergedCellHeight(rowIndex, cell.merged.rowspan)
        }

        // 普通单元格：优先使用缓存的高度作为最小高度
        const cachedHeight = this.internalRowHeights[rowIndex]
        if (cachedHeight && cachedHeight > 0) {
          return Math.max(cachedHeight, 50)
        }

        return 50 // 默认最小高度
      }
    }
  },
  watch: {
    // 监听 columnWidths prop 的变化
    columnWidths: {
      handler(newWidths) {
        // 只有在没有使用动态表头配置时才更新内部列宽
        if (!this.internalUseDynamicHeader) {
          console.log('columnWidths watch触发，更新internalColumnWidths:', newWidths)
          this.internalColumnWidths = [...newWidths]
        } else {
          console.log('columnWidths watch触发，但使用动态表头配置，忽略更新')
        }
      },
      immediate: true,
      deep: true
    },
    // 监听表头配置变化，自动调整数据行列数
    currentColumnCount: {
      handler(newColumnCount, oldColumnCount) {
        if (oldColumnCount !== undefined && newColumnCount !== oldColumnCount) {
          console.log('表头列数变化:', oldColumnCount, '->', newColumnCount)
          // 调整所有现有数据行的列数
          this.ensureAllRowsColumnCount()
        }
      },
      immediate: false
    }
  },
  mounted() {
    this.setupEventListeners()
    this.initializeMathJax()
    // 初始化内部列宽状态（只有在没有使用动态表头配置时）
    if (!this.internalUseDynamicHeader) {
      console.log('mounted初始化internalColumnWidths:', this.columnWidths)
      this.internalColumnWidths = [...this.columnWidths]
    } else {
      console.log('mounted时使用动态表头配置，不初始化internalColumnWidths')
    }
  },
  beforeDestroy() {
    this.removeEventListeners()
  },
  methods: {
    // ========== 嵌套表格相关方法 ==========

    // 检查单元格是否有嵌套表格
    hasNestedTable(cell) {
      const hasNested = cell && cell.nestedTable && cell.nestedTable.enabled === true
      if (hasNested) {
        console.log('发现嵌套表格:', cell.nestedTable)
      }
      return hasNested
    },

    // 获取嵌套表格样式
    getNestedTableStyle(cell) {
      if (!this.hasNestedTable(cell)) return {}

      const config = cell.nestedTable.config
      if (config && config.columnWidths) {
        const totalWidth = config.columnWidths.reduce((sum, width) => sum + width, 0)
        return {
          width: `${totalWidth}px`,
          borderCollapse: 'collapse',
          marginTop: '4px'
        }
      }

      return {
        width: '100%',
        borderCollapse: 'collapse',
        marginTop: '4px'
      }
    },

    // 获取嵌套单元格样式
    getNestedCellStyle(cell, cellIndex, rowIndex) {
      if (!this.hasNestedTable(cell)) return {}

      const config = cell.nestedTable.config
      const baseStyle = {
        border: '1px solid #e0e0e0',
        padding: '4px 6px',
        fontSize: '12px',
        lineHeight: '1.2'
      }

      if (config) {
        // 添加列宽配置
        if (config.columnWidths && config.columnWidths[cellIndex]) {
          baseStyle.width = `${config.columnWidths[cellIndex]}px`
          baseStyle.minWidth = `${config.columnWidths[cellIndex]}px`
        }

        // 添加行高配置
        if (config.rowHeights && config.rowHeights[rowIndex]) {
          baseStyle.height = `${config.rowHeights[rowIndex]}px`
          baseStyle.minHeight = `${config.rowHeights[rowIndex]}px`
        }
      }

      return baseStyle
    },

    // 获取嵌套单元格的高度
    getNestedCellHeight(cell, rowIndex) {
      if (!this.hasNestedTable(cell)) return 25 // 默认高度

      const config = cell.nestedTable.config
      if (config && config.rowHeights && config.rowHeights[rowIndex]) {
        return config.rowHeights[rowIndex]
      }

      return 25 // 默认高度
    },

    // 获取嵌套表格的数据行 - 简化版
    getNestedTableDataRows(cell) {
      if (!this.hasNestedTable(cell)) {
        console.log('单元格没有嵌套表格:', cell)
        return []
      }

      const config = cell.nestedTable.config
      console.log('嵌套表格配置:', config)

      if (config && config.cellRows) {
        console.log('嵌套表格原始数据行:', config.cellRows)
        // 简化的数据格式，直接返回可编辑的单元格数据
        const processedRows = config.cellRows.map(row =>
          row.map(cellData => ({
            content: cellData.content || '',
            originContent: cellData.originContent || cellData.content || '',
            isEditing: false,
            originalContent: cellData.content || '',
            hasMath: cellData.hasMath || false,
            selectAll: false
          }))
        )
        console.log('处理后的嵌套表格数据行:', processedRows)
        return processedRows
      }

      console.log('嵌套表格配置无效或缺少cellRows')
      return []
    },

    // 检查当前单元格是否可以添加嵌套表格
    canAddNestedTable() {
      console.log('是否可以添加嵌套表格:', this.enableNestedTables, this.nestedLevel, this.maxNestedLevel)
      if (!this.enableNestedTables) return false
      if (this.nestedLevel >= this.maxNestedLevel) return false

      const cell = this.getCurrentContextCell()
      console.log('当前单元格:', cell, !this.hasNestedTable(cell))
      return cell && !this.hasNestedTable(cell)
    },

    // 检查当前单元格是否有嵌套表格
    hasNestedTableInCurrentCell() {
      const cell = this.getCurrentContextCell()
      return this.hasNestedTable(cell)
    },

    // 获取当前右键菜单对应的单元格
    getCurrentContextCell() {
      if (!this.currentContextCell) return null

      const tableRowIndex = this.currentRowIndex
      const cellIndex = this.currentColumnIndex

      // 动态获取表头行数
      const headerRowCount = this.getHeaderRowCount()

      // 计算数据行的真实索引（减去表头行数）
      const dataRowIndex = tableRowIndex - headerRowCount

      console.log('当前右键菜单对应的单元格:', {
        tableRowIndex,
        headerRowCount,
        dataRowIndex,
        cellIndex,
        dataRowsLength: this.dataRows.length
      })

      if (dataRowIndex >= 0 && cellIndex >= 0 &&
          this.dataRows[dataRowIndex] && this.dataRows[dataRowIndex][cellIndex]) {
        return this.dataRows[dataRowIndex][cellIndex]
      }

      return null
    },

    // 动态获取表头行数
    getHeaderRowCount() {
      // 如果有表头配置，返回表头行数
      if (this.currentHeaderConfig.headers && this.currentHeaderConfig.headers.length > 0) {
        return this.currentHeaderConfig.headers.length
      }

      // 默认情况下两行表头
      return 2
    },

    // 打开嵌套表格设计器
    openNestedTableDesigner() {
      if (!this.canAddNestedTable()) return

      // 动态获取表头行数并计算数据行索引
      const headerRowCount = this.getHeaderRowCount()
      const dataRowIndex = this.currentRowIndex - headerRowCount

      this.currentNestedTablePosition = {
        rowIndex: dataRowIndex,
        cellIndex: this.currentColumnIndex
      }

      this.nestedTableDesignerVisible = true
      this.contextMenuVisible = false
    },

    // 关闭嵌套表格设计器
    closeNestedTableDesigner() {
      this.nestedTableDesignerVisible = false
      this.currentNestedTablePosition = null
    },

    // 从设计器插入嵌套表格
    insertNestedTableFromDesigner() {
      if (!this.currentNestedTablePosition) return

      const designer = this.$refs.nestedTableDesigner
      if (!designer) {
        console.error('嵌套表格设计器组件未找到')
        return
      }

      // 获取设计器生成的配置
      const designerConfig = this.getDesignerConfiguration(designer)
      if (!designerConfig) {
        console.error('无法获取设计器配置')
        return
      }

      // 插入嵌套表格到指定单元格
      this.insertNestedTableToCell(designerConfig)

      // 关闭设计器
      this.closeNestedTableDesigner()
    },

    // 获取设计器配置
    getDesignerConfiguration(designer) {
      try {
        // 调用设计器的生成JSON方法
        designer.generateJsonOutput()

        // 获取设计器的配置数据
        const tableConfig = designer.tableConfig
        const columnConfigs = designer.columnConfigs
        const rowConfigs = designer.rowConfigs
        const cellData = designer.cellData

        // 构建嵌套表格配置
        const nestedTableConfig = {
          enabled: true,
          config: {
            columnWidths: columnConfigs.map(col => col.width),
            rowHeights: rowConfigs.map(row => row.height),
            cellRows: this.generateCellRowsFromDesigner(tableConfig, cellData),
            metadata: {
              title: tableConfig.title,
              level: (this.nestedLevel || 0) + 1,
              parentCell: this.currentNestedTablePosition,
              columns: tableConfig.columns,
              rows: tableConfig.rows
            }
          }
        }

        console.log('生成的嵌套表格配置:', nestedTableConfig)
        return nestedTableConfig
      } catch (error) {
        console.error('获取设计器配置失败:', error)
        return null
      }
    },

    // 从设计器数据生成单元格行
    generateCellRowsFromDesigner(tableConfig, cellData) {
      const cellRows = []

      for (let row = 0; row < tableConfig.rows; row++) {
        const rowData = []
        for (let col = 0; col < tableConfig.columns; col++) {
          const cellKey = `${row}-${col}`
          const content = cellData[cellKey] || `R${row + 1}C${col + 1}`

          rowData.push({
            content: content,
            originContent: content,
            hasMath: this.containsMath(content)
          })
        }
        cellRows.push(rowData)
      }

      return cellRows
    },

    // 将嵌套表格插入到指定单元格
    insertNestedTableToCell(nestedTableConfig) {
      const { rowIndex, cellIndex } = this.currentNestedTablePosition

      // 确保行存在
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (!cell) {
        console.error('目标单元格不存在')
        return
      }

      // 使用 Vue.set 确保响应式更新
      this.$set(cell, 'nestedTable', nestedTableConfig)

      console.log('嵌套表格插入成功:', {
        rowIndex,
        cellIndex,
        config: nestedTableConfig
      })

      this.$emit('nested-table-created', { rowIndex, cellIndex, config: nestedTableConfig })
    },

    // 处理设计器的应用事件（如果需要）
    handleDesignerApply(config) {
      console.log('设计器应用事件:', config)
      // 这里可以处理设计器的应用事件，如果需要的话
    },


    // 打开添加嵌套表格对话框
    openAddNestedTableDialog() {
      if (!this.canAddNestedTable()) return

      // 动态获取表头行数并计算数据行索引
      const headerRowCount = this.getHeaderRowCount()
      const dataRowIndex = this.currentRowIndex - headerRowCount

      this.currentNestedTablePosition = {
        rowIndex: dataRowIndex,
        cellIndex: this.currentColumnIndex
      }

      // 重置表单
      this.nestedTableForm = {
        title: '',
        columns: 3,
        rows: 2
      }

      this.addNestedTableDialogVisible = true
      this.contextMenuVisible = false
    },

    // 关闭添加嵌套表格对话框
    closeAddNestedTableDialog() {
      this.addNestedTableDialogVisible = false
      this.currentNestedTablePosition = null
    },

    // 创建嵌套表格 - 简化版
    createNestedTable() {
      if (!this.currentNestedTablePosition) return

      const {rowIndex, cellIndex} = this.currentNestedTablePosition
      const {title, columns, rows} = this.nestedTableForm

      // 确保行存在
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (!cell) return

      // 创建简化的嵌套表格配置（无表头）
      const columnWidths = Array(columns).fill(100)
      const rowHeights = Array(rows).fill(30)

      console.log('创建嵌套表格 - 初始配置:', {
        columns,
        rows,
        columnWidths,
        rowHeights
      })

      const nestedTableConfig = {
        enabled: true,
        config: {
          columnWidths: columnWidths, // 简化的列宽配置
          rowHeights: rowHeights, // 添加行高配置，默认30px
          cellRows: Array(rows).fill(null).map(() =>
            Array(columns).fill(null).map(() => ({
              content: '',
              originContent: '',
              isEditing: false,
              hasMath: false
            }))
          ),
          metadata: {
            title: title,
            level: (this.nestedLevel || 0) + 1,
            parentCell: {row: rowIndex, col: cellIndex},
            columns: columns,
            rows: rows
          }
        }
      }

      // 使用 Vue.set 确保响应式更新
      this.$set(cell, 'nestedTable', nestedTableConfig)

      // 调试日志
      console.log('创建嵌套表格完成:', {
        rowIndex,
        cellIndex,
        config: nestedTableConfig,
        rowHeights: nestedTableConfig.config.rowHeights,
        rowHeightsLength: nestedTableConfig.config.rowHeights ? nestedTableConfig.config.rowHeights.length : 0
      })

      this.closeAddNestedTableDialog()
      this.$emit('nested-table-created', {rowIndex, cellIndex, config: nestedTableConfig})
    },

    // 移除嵌套表格 - 简化版
    removeNestedTable() {
      const cell = this.getCurrentContextCell()

      if (cell && this.hasNestedTable(cell)) {
        // 移除嵌套表格配置
        this.$delete(cell, 'nestedTable')

        this.contextMenuVisible = false
        this.$emit('nested-table-removed', {
          rowIndex: this.currentRowIndex,
          cellIndex: this.currentColumnIndex
        })
      }
    },

    // ========== 嵌套单元格编辑处理方法 ==========

    // 处理嵌套单元格开始编辑
    handleNestedCellStartEdit(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.isEditing = true
          this.$emit('nested-cell-start-edit', {
            parentRowIndex,
            parentCellIndex,
            nestedRowIndex,
            nestedCellIndex,
            nestedCell
          })
        }
      }
    },

    // 处理嵌套单元格完成编辑
    handleNestedCellFinishEdit(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex, newContent) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.content = newContent
          nestedCell.originContent = newContent
          nestedCell.isEditing = false
          this.$emit('nested-cell-finish-edit', {
            parentRowIndex,
            parentCellIndex,
            nestedRowIndex,
            nestedCellIndex,
            nestedCell,
            newContent
          })
        }
      }
    },

    // 处理嵌套单元格取消编辑
    handleNestedCellCancelEdit(parentRowIndex, parentCellIndex, nestedRowIndex, nestedCellIndex) {
      const parentCell = this.dataRows[parentRowIndex][parentCellIndex]
      if (parentCell && this.hasNestedTable(parentCell)) {
        const nestedCell = parentCell.nestedTable.config.cellRows[nestedRowIndex][nestedCellIndex]
        if (nestedCell) {
          nestedCell.content = nestedCell.originalContent
          nestedCell.isEditing = false
          this.$emit('nested-cell-cancel-edit', {
            parentRowIndex,
            parentCellIndex,
            nestedRowIndex,
            nestedCellIndex,
            nestedCell
          })
        }
      }
    },

    // ========== 动态表头相关方法 ==========

    /**
     * 设置动态表头配置（避免直接修改props）
     */
    setDynamicHeaderConfig(useDynamic, headerConfig, headerWidthConfig, verticalHeadersConfig) {
      console.log('设置动态表头配置 - 调用前状态:', {
        oldInternalUseDynamicHeader: this.internalUseDynamicHeader,
        oldInternalHeaderConfig: this.internalHeaderConfig,
        oldInternalHeaderWidthConfig: this.internalHeaderWidthConfig,
        oldInternalColumnWidths: this.internalColumnWidths
      })

      this.internalUseDynamicHeader = useDynamic
      this.internalHeaderConfig = headerConfig
      this.internalHeaderWidthConfig = headerWidthConfig
      this.internalVerticalHeadersConfig = verticalHeadersConfig

      // 关键修复：当启用动态表头配置时，清空内部列宽配置
      if (useDynamic) {
        console.log('启用动态表头配置，清空internalColumnWidths')
        this.internalColumnWidths = []
      }

      console.log('设置动态表头配置 - 调用后状态:', {
        useDynamic,
        headerConfig,
        headerWidthConfig,
        newInternalUseDynamicHeader: this.internalUseDynamicHeader,
        newInternalHeaderConfig: this.internalHeaderConfig,
        newInternalHeaderWidthConfig: this.internalHeaderWidthConfig,
        newInternalColumnWidths: this.internalColumnWidths
      })

      // 强制触发computed属性重新计算
      this.$nextTick(() => {
        console.log('nextTick后的currentHeaderWidthConfig:', this.currentHeaderWidthConfig)
        console.log('nextTick后的currentColumnWidths:', this.currentColumnWidths)
        console.log('nextTick后的currentColumnCount:', this.currentColumnCount)

        // 确保所有数据行的列数与新的表头一致
        this.ensureAllRowsColumnCount()

        // 移除$forceUpdate()，避免循环更新
        // this.$forceUpdate()
      })
    },

    /**
     * 检查表头单元格是否被隐藏（被合并覆盖）
     */
    isHeaderCellHidden(headerRowIndex, headerCellIndex) {
      if (!this.currentHeaderConfig.merges) return false

      for (const merge of this.currentHeaderConfig.merges) {
        // 检查当前单元格是否在合并范围内，但不是主单元格
        if (headerRowIndex >= merge.startRow && headerRowIndex <= merge.endRow &&
            headerCellIndex >= merge.startCol && headerCellIndex <= merge.endCol &&
            !(headerRowIndex === merge.startRow && headerCellIndex === merge.startCol)) {
          return true
        }
      }
      return false
    },

    /**
     * 获取表头单元格的行跨度
     */
    getHeaderCellRowspan(headerRowIndex, headerCellIndex) {
      if (!this.currentHeaderConfig.merges) return 1

      for (const merge of this.currentHeaderConfig.merges) {
        if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex) {
          return merge.endRow - merge.startRow + 1
        }
      }
      return 1
    },

    /**
     * 获取表头单元格的列跨度
     */
    getHeaderCellColspan(headerRowIndex, headerCellIndex) {
      if (!this.currentHeaderConfig.merges) return 1

      for (const merge of this.currentHeaderConfig.merges) {
        if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex) {
          return merge.endCol - merge.startCol + 1
        }
      }
      return 1
    },

    /**
     * 获取表头单元格的标题
     */
    getHeaderCellTitle(headerRowIndex, headerCellIndex) {
      return this.getHeaderCellContent(headerRowIndex, headerCellIndex)
    },

    /**
     * 获取表头单元格的内容
     */
    getHeaderCellContent(headerRowIndex, headerCellIndex) {
      // 首先检查是否有合并单元格的自定义内容
      if (this.currentHeaderConfig.merges) {
        for (const merge of this.currentHeaderConfig.merges) {
          if (merge.startRow === headerRowIndex && merge.startCol === headerCellIndex && merge.content) {
            return merge.content
          }
        }
      }

      // 返回原始表头内容
      const headerRow = this.currentHeaderConfig.headers[headerRowIndex]
      return headerRow ? (headerRow[headerCellIndex] || '') : ''
    },

    /**
     * 获取表头单元格的样式
     */
    getHeaderCellStyle(headerRowIndex, headerCellIndex) {
      const widthConfig = this.currentHeaderWidthConfig
      const width = widthConfig.columnWidths[headerCellIndex] || 150
      const height = widthConfig.headerHeights[headerRowIndex] || 50

      return {
        minWidth: `${width}px !important`,
        width: `${width}px !important`,
        height: `${height}px !important`,
        minHeight: `${height}px !important`
      }
    },

    /**
     * 获取表头单元格的CSS类
     */
    getHeaderCellClass(headerRowIndex, headerCellIndex) {
      return this.getHeaderTextClass(headerCellIndex)
    },

    /**
     * 判断是否应该使用纵向文字
     */
    shouldUseVerticalText(headerCellIndex) {
      return this.currentVerticalHeadersConfig[headerCellIndex] || false
    },

    // ========== 原有方法 ==========

    // 设置事件监听器
    setupEventListeners() {
      document.addEventListener('mousedown', this.handleDocumentClick)
      document.addEventListener('keydown', this.handleKeyDown)
    },

    // 移除事件监听器
    removeEventListeners() {
      document.removeEventListener('mousedown', this.handleDocumentClick)
      document.removeEventListener('keydown', this.handleKeyDown)
    },

    // 处理右键菜单
    handleContextMenu(e) {
      e.preventDefault()

      const cell = e.target.closest('td')
      if (!cell) return

      this.currentContextCell = cell
      this.contextMenuX = e.pageX
      this.contextMenuY = e.pageY
      this.contextMenuVisible = true

      // 判断是否为数据行（非表头）
      this.isDataRow = cell.classList.contains('editable-cell')

      console.log('数据行->', this.isDataRow)

      // 计算当前单元格的行列索引
      this.calculateCellPosition(cell)

      // 只在需要时获取当前行高和列宽（避免频繁计算）
      if (!this.currentRowHeight || this.currentRowHeight <= 0) {
        this.getCurrentRowHeight()
      }
      if (!this.currentColumnWidth || this.currentColumnWidth <= 0) {
        this.getCurrentColumnWidth()
      }

      // 调整菜单位置避免超出屏幕
      this.$nextTick(() => {
        this.adjustContextMenuPosition()
      })
    },

    // 计算单元格位置
    calculateCellPosition(cell) {
      const table = this.$refs.editableTable
      if (!table) {
        console.log('calculateCellPosition: table not found')
        return
      }

      const rows = Array.from(table.rows)
      const rowIndex = rows.findIndex(row => Array.from(row.cells).includes(cell))
      this.currentRowIndex = rowIndex

      console.log('calculateCellPosition开始:', {
        cell: cell,
        cellClasses: cell.className,
        rowIndex: rowIndex,
        totalRows: rows.length,
        isHeaderCell: cell.classList.contains('header-cell'),
        isEditableCell: cell.classList.contains('editable-cell')
      })

      if (rowIndex >= 0) {
        let columnIndex = 0
        const row = rows[rowIndex]
        const cells = Array.from(row.cells)
        const cellIndex = cells.indexOf(cell)

        console.log('计算列索引:', {
          cellIndex: cellIndex,
          totalCells: cells.length
        })

        for (let i = 0; i < cellIndex; i++) {
          const colspan = parseInt(cells[i].getAttribute('colspan') || '1')
          columnIndex += colspan
          console.log(`第${i}个单元格colspan=${colspan}, 累计columnIndex=${columnIndex}`)
        }

        this.currentColumnIndex = columnIndex
        this.contextMenuColumnIndex = columnIndex // 同时设置右键菜单的列索引

        console.log(`最终设置: currentColumnIndex=${columnIndex}, contextMenuColumnIndex=${columnIndex}`)
      } else {
        this.currentColumnIndex = 0
        this.contextMenuColumnIndex = 0
        console.log('行索引计算失败，使用默认值0')
      }
    },

    // 获取当前行高度
    getCurrentRowHeight() {
      if (this.currentRowIndex >= 0) {
        // 计算数据行索引
        const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0
        const dataRowIndex = this.currentRowIndex - headerRowCount

        // 如果是数据行，优先使用缓存的高度
        if (dataRowIndex >= 0 && this.internalRowHeights[dataRowIndex]) {
          this.currentRowHeight = this.internalRowHeights[dataRowIndex]
          return
        }

        // 否则计算实际高度
        const table = this.$refs.editableTable
        if (table && table.rows[this.currentRowIndex]) {
          const row = table.rows[this.currentRowIndex]
          const rect = row.getBoundingClientRect()
          const height = Math.round(rect.height)
          this.currentRowHeight = height

          // 如果是数据行，缓存高度
          if (dataRowIndex >= 0 && height > 0) {
            this.$set(this.internalRowHeights, dataRowIndex, height)
          }
        } else {
          this.currentRowHeight = 50
        }
      } else {
        this.currentRowHeight = 50
      }
    },

    // 获取当前列宽度
    getCurrentColumnWidth() {
      if (this.currentContextCell) {
        const rect = this.currentContextCell.getBoundingClientRect()
        this.currentColumnWidth = Math.round(rect.width)
      } else {
        this.currentColumnWidth = 120
      }
    },

    // 获取当前列宽度用于显示（右键菜单）
    getCurrentColumnWidthForDisplay() {
      console.log('getCurrentColumnWidthForDisplay调用:', {
        contextMenuColumnIndex: this.contextMenuColumnIndex,
        currentRowIndex: this.currentRowIndex,
        currentColumnWidths: this.currentColumnWidths,
        currentColumnWidthsLength: this.currentColumnWidths.length,
        headerCellWidth: this.headerCellWidth,
        currentHeaderConfig: this.currentHeaderConfig
      })

      if (this.contextMenuColumnIndex >= 0 && this.contextMenuColumnIndex < this.currentColumnWidths.length) {
        // 检查是否是表头行，如果是，需要考虑合并单元格
        if (this.currentRowIndex >= 0 && this.currentHeaderConfig && this.currentHeaderConfig.merges) {
          const mergedWidth = this.calculateMergedCellWidth(this.currentRowIndex, this.contextMenuColumnIndex)
          if (mergedWidth > 0) {
            console.log(`获取第${this.contextMenuColumnIndex}列合并宽度: ${mergedWidth}px (行${this.currentRowIndex})`)
            return mergedWidth
          } else if (mergedWidth === -1) {
            // 被合并覆盖的单元格，显示该列的原始宽度
            const width = this.currentColumnWidths[this.contextMenuColumnIndex]
            console.log(`获取第${this.contextMenuColumnIndex}列原始宽度: ${width}px (被合并覆盖)`)
            return width
          }
        }

        // 非合并单元格或数据行，返回单列宽度
        const width = this.currentColumnWidths[this.contextMenuColumnIndex]
        console.log(`获取第${this.contextMenuColumnIndex}列单独宽度: ${width}px`)
        return width
      }
      console.log('获取显示宽度失败，使用默认值:', this.headerCellWidth)
      return this.headerCellWidth
    },

    // 计算合并单元格的宽度
    calculateMergedCellWidth(rowIndex, columnIndex) {
      if (!this.currentHeaderConfig || !this.currentHeaderConfig.merges) {
        return 0
      }

      console.log('calculateMergedCellWidth:', {
        rowIndex,
        columnIndex,
        merges: this.currentHeaderConfig.merges
      })

      // 查找当前单元格是否是合并单元格的主单元格
      for (const merge of this.currentHeaderConfig.merges) {
        if (merge.startRow === rowIndex && merge.startCol === columnIndex) {
          // 这是合并单元格的主单元格，计算总宽度
          let totalWidth = 0
          for (let col = merge.startCol; col <= merge.endCol; col++) {
            if (col < this.currentColumnWidths.length) {
              totalWidth += this.currentColumnWidths[col]
            }
          }

          console.log(`找到合并单元格: 行${rowIndex} 列${merge.startCol}-${merge.endCol}, 总宽度: ${totalWidth}px`)
          return totalWidth
        }
      }

      // 检查当前单元格是否被其他合并单元格覆盖
      for (const merge of this.currentHeaderConfig.merges) {
        if (rowIndex >= merge.startRow && rowIndex <= merge.endRow &&
            columnIndex >= merge.startCol && columnIndex <= merge.endCol &&
            !(merge.startRow === rowIndex && merge.startCol === columnIndex)) {
          // 这个单元格被合并覆盖了，不应该显示
          console.log(`单元格被合并覆盖: 行${rowIndex} 列${columnIndex}`)
          return -1 // 返回-1表示被覆盖
        }
      }

      console.log(`非合并单元格: 行${rowIndex} 列${columnIndex}`)
      return 0 // 返回0表示非合并单元格
    },

    // 调整右键菜单位置
    adjustContextMenuPosition() {
      const menu = document.querySelector('.context-menu')
      if (!menu) return

      const menuRect = menu.getBoundingClientRect()
      const windowWidth = window.innerWidth
      const windowHeight = window.innerHeight

      if (this.contextMenuX + menuRect.width > windowWidth) {
        this.contextMenuX = windowWidth - menuRect.width - 10
      }

      if (this.contextMenuY + menuRect.height > windowHeight) {
        this.contextMenuY = windowHeight - menuRect.height - 10
      }
    },

    // 处理文档点击（关闭菜单和清除选中状态）
    handleDocumentClick(e) {
      // 处理右键菜单
      if (this.contextMenuVisible) {
        if (e.target.closest('.context-menu')) {
          return
        }
        this.contextMenuVisible = false
      }

      // 处理单元格选中状态
      // 如果点击的不是表格内部，清除选中状态
      const tableContainer = this.$refs.editableTable
      if (tableContainer && !tableContainer.contains(e.target)) {
        // 点击了表格外部，清除选中状态
        this.clearCellSelection()
      }
    },

    // 处理行高点击
    handleRowHeightClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showRowHeightDialog()
    },

    // 处理列宽点击
    handleColumnWidthClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.showColumnWidthDialog()
    },

    // 处理删除行点击
    handleDeleteRowClick(e) {
      e.preventDefault()
      e.stopPropagation()
      this.deleteCurrentRow()
    },

    // 显示行高调整对话框
    showRowHeightDialog() {
      this.contextMenuVisible = false
      this.newRowHeight = this.currentRowHeight || 50
      this.rowHeightDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.rowHeightInput) {
          this.$refs.rowHeightInput.focus()
          this.$refs.rowHeightInput.select()
        }
      })
    },

    // 显示列宽调整对话框
    showColumnWidthDialog() {
      this.contextMenuVisible = false
      this.newColumnWidth = this.currentColumnWidth || 120
      this.columnWidthDialogVisible = true

      this.$nextTick(() => {
        if (this.$refs.columnWidthInput) {
          this.$refs.columnWidthInput.focus()
          this.$refs.columnWidthInput.select()
        }
      })
    },

    // 关闭行高调整对话框
    closeRowHeightDialog() {
      this.rowHeightDialogVisible = false
    },

    // 关闭列宽调整对话框
    closeColumnWidthDialog() {
      this.columnWidthDialogVisible = false
    },

    // 删除当前行
    deleteCurrentRow() {
      if (this.isDataRow) {
        // 动态获取表头行数并计算数据行索引
        const headerRowCount = this.getHeaderRowCount()
        const dataRowIndex = this.currentRowIndex - headerRowCount

        if (dataRowIndex >= 0 && dataRowIndex < this.dataRows.length) {
          if (confirm('确定要删除这一行吗？此操作不可撤销。')) {
            this.$emit('delete-row', dataRowIndex)
            this.contextMenuVisible = false
          }
        }
      }
    },

    // 应用行高调整
    applyRowHeight() {
      const height = Math.max(this.minCellHeight, parseInt(this.newRowHeight) || this.minCellHeight)

      if (this.currentRowIndex >= 0) {
        const table = this.$refs.editableTable
        const targetRow = table.rows[this.currentRowIndex]

        const cells = Array.from(targetRow.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')

          if (rowspan > 1) {
            for (let i = 0; i < rowspan; i++) {
              const rowIndex = this.currentRowIndex + i
              if (rowIndex < table.rows.length) {
                this.setRowHeight(table.rows[rowIndex], height / rowspan)
              }
            }
          } else {
            cell.style.height = `${height}px`
            cell.style.minHeight = `${height}px`
          }
        })

        this.adjustCrossRowMergedCells(height)

        // 更新内部行高度状态
        const dataRowIndex = this.currentRowIndex - (this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0)
        if (dataRowIndex >= 0) {
          this.$set(this.internalRowHeights, dataRowIndex, height)
        }
      }

      this.closeRowHeightDialog()
      this.$emit('table-updated')
    },

    // 设置行高度
    setRowHeight(row, height) {
      const cells = Array.from(row.cells)
      cells.forEach(cell => {
        cell.style.height = `${height}px`
        cell.style.minHeight = `${height}px`
      })
    },

    // 调整跨行合并单元格
    adjustCrossRowMergedCells(newHeight) {
      const table = this.$refs.editableTable
      const rows = Array.from(table.rows)

      for (let i = 0; i < this.currentRowIndex; i++) {
        const row = rows[i]
        const cells = Array.from(row.cells)

        cells.forEach(cell => {
          const rowspan = parseInt(cell.getAttribute('rowspan') || '1')
          if (rowspan > 1 && i + rowspan > this.currentRowIndex) {
            const totalHeight = newHeight * (rowspan - (this.currentRowIndex - i))
            cell.style.height = `${totalHeight}px`
            cell.style.minHeight = `${totalHeight}px`
          }
        })
      }
    },

    // 应用列宽调整
    applyColumnWidth() {
      const newWidth = Math.max(this.minCellWidth, parseInt(this.newColumnWidth) || this.minCellWidth)

      if (this.currentColumnIndex >= 0) {
        // 更新 columnWidths 数组
        this.updateColumnWidth(this.currentColumnIndex, newWidth)
      }

      this.closeColumnWidthDialog()
      this.$emit('table-updated')
    },

    // 更新指定列的宽度
    updateColumnWidth(columnIndex, newWidth) {
      console.log(`更新列宽: 列${columnIndex} 新宽度${newWidth}px`)
      console.log('当前状态:', {
        internalUseDynamicHeader: this.internalUseDynamicHeader,
        internalColumnWidthsLength: this.internalColumnWidths.length,
        currentHeaderWidthConfig: this.currentHeaderWidthConfig
      })

      // 如果使用动态表头配置，需要更新动态配置
      if (this.internalUseDynamicHeader && this.internalHeaderWidthConfig && this.internalHeaderWidthConfig.columnWidths) {
        console.log('更新动态表头配置中的列宽')
        // 确保动态配置的列宽数组存在且长度足够
        if (columnIndex >= 0 && columnIndex < this.internalHeaderWidthConfig.columnWidths.length) {
          // 更新动态配置中的列宽
          this.$set(this.internalHeaderWidthConfig.columnWidths, columnIndex, newWidth)
          console.log('动态配置更新成功:', this.internalHeaderWidthConfig.columnWidths)
        }
      } else {
        // 使用传统的内部列宽更新方式
        console.log('更新内部列宽配置')
        // 确保内部列宽数组有足够的长度
        if (this.internalColumnWidths.length === 0) {
          this.internalColumnWidths = [...this.currentColumnWidths]
        }

        if (columnIndex >= 0 && columnIndex < this.internalColumnWidths.length) {
          this.$set(this.internalColumnWidths, columnIndex, newWidth)
          console.log('内部配置更新成功:', this.internalColumnWidths)
        }
      }

      // 发射事件通知父组件更新
      this.$emit('column-width-changed', {
        columnIndex,
        newWidth,
        columnWidths: [...this.currentColumnWidths],
        useDynamicHeader: this.internalUseDynamicHeader
      })
    },

    // 检测内容是否包含数学公式
    containsMath(content) {
      if (!content) return false

      const mathPatterns = [
        /\$.*?\$/,
        /\$\$.*?\$\$/,
        /\\\(.*?\\\)/,
        /\\\[.*?\\\]/,
        /\\begin\{.*?\}.*?\\end\{.*?\}/,
        /\\[a-zA-Z]+/
      ]

      return mathPatterns.some(pattern => pattern.test(content))
    },

    // ========== 单元格选中状态相关方法 ==========

    // 检查单元格是否被选中
    isCellSelected(rowIndex, cellIndex) {
      return this.selectedCell.row === rowIndex && this.selectedCell.col === cellIndex
    },

    // 选中单元格
    selectCell(rowIndex, cellIndex) {
      // 如果点击的是已选中的单元格，不改变选中状态
      if (this.isCellSelected(rowIndex, cellIndex)) {
        return false // 返回 false 表示没有改变选中状态
      }

      // 取消之前选中单元格的编辑状态
      this.cancelPreviousCellEdit()

      // 选中新单元格
      this.selectedCell.row = rowIndex
      this.selectedCell.col = cellIndex

      console.log('选中单元格:', rowIndex, cellIndex)
      return true // 返回 true 表示改变了选中状态
    },

    // 取消单元格选中状态
    clearCellSelection() {
      if (this.selectedCell.row !== -1 || this.selectedCell.col !== -1) {
        // 取消编辑状态
        this.cancelPreviousCellEdit()

        // 清除选中状态
        this.selectedCell.row = -1
        this.selectedCell.col = -1

        console.log('清除单元格选中状态')
      }
    },

    // 取消之前单元格的编辑状态
    cancelPreviousCellEdit() {
      if (this.selectedCell.row >= 0 && this.selectedCell.col >= 0) {
        const prevCell = this.dataRows[this.selectedCell.row] && this.dataRows[this.selectedCell.row][this.selectedCell.col]
        if (prevCell && prevCell.isEditing) {
          prevCell.isEditing = false
          console.log('取消之前单元格的编辑状态:', this.selectedCell.row, this.selectedCell.col)
        }
      }
    },

    // 处理单元格点击事件
    handleCellClick(rowIndex, cellIndex, event) {
      event.stopPropagation() // 阻止事件冒泡

      const currentTime = Date.now()
      const isDoubleClick = (currentTime - this.lastClickTime) < this.doubleClickDelay &&
                           this.isCellSelected(rowIndex, cellIndex)

      this.lastClickTime = currentTime

      console.log('单元格点击:', {
        rowIndex,
        cellIndex,
        isDoubleClick,
        isSelected: this.isCellSelected(rowIndex, cellIndex)
      })

      if (isDoubleClick) {
        // 双击或再次点击已选中的单元格 - 进入编辑模式
        this.enterEditMode(rowIndex, cellIndex)
      } else {
        // 首次点击 - 选中单元格
        this.selectCell(rowIndex, cellIndex)
      }
    },

    // 进入编辑模式
    enterEditMode(rowIndex, cellIndex) {
      const cell = this.dataRows[rowIndex] && this.dataRows[rowIndex][cellIndex]
      if (!cell) return

      // 确保单元格被选中
      this.selectCell(rowIndex, cellIndex)

      // 进入编辑状态
      cell.isEditing = true

      console.log('进入编辑模式:', rowIndex, cellIndex)

      // 触发编辑开始事件
      this.$emit('start-edit', { rowIndex, cellIndex, cell })

      // 下一帧聚焦到编辑器
      this.$nextTick(() => {
        const cellRef = this.$refs[`cell-${rowIndex}-${cellIndex}`]
        if (cellRef && cellRef[0] && cellRef[0].focus) {
          cellRef[0].focus()
        }
      })
    },

    // 处理键盘事件
    handleKeyDown(event) {
      // 只在有选中单元格且不在编辑状态时处理键盘导航
      if (this.selectedCell.row === -1 || this.selectedCell.col === -1) {
        return
      }

      const currentCell = this.dataRows[this.selectedCell.row] && this.dataRows[this.selectedCell.row][this.selectedCell.col]
      if (currentCell && currentCell.isEditing) {
        // 在编辑状态下，只处理 Escape 键
        if (event.key === 'Escape') {
          currentCell.isEditing = false
          event.preventDefault()
        }
        return
      }

      // 处理方向键导航
      let newRow = this.selectedCell.row
      let newCol = this.selectedCell.col

      switch (event.key) {
        case 'ArrowUp':
          newRow = Math.max(0, newRow - 1)
          event.preventDefault()
          break
        case 'ArrowDown':
          newRow = Math.min(this.dataRows.length - 1, newRow + 1)
          event.preventDefault()
          break
        case 'ArrowLeft':
          newCol = Math.max(0, newCol - 1)
          event.preventDefault()
          break
        case 'ArrowRight':
          newCol = Math.min((this.dataRows[newRow] || []).length - 1, newCol + 1)
          event.preventDefault()
          break
        case 'Enter':
        case 'F2':
          // 进入编辑模式
          this.enterEditMode(this.selectedCell.row, this.selectedCell.col)
          event.preventDefault()
          break
        case 'Delete':
        case 'Backspace':
          // 清空单元格内容
          if (currentCell) {
            currentCell.content = ''
            currentCell.hasMath = false
            this.$emit('content-change', {
              rowIndex: this.selectedCell.row,
              cellIndex: this.selectedCell.col,
              content: ''
            })
          }
          event.preventDefault()
          break
        case 'Escape':
          // 清除选中状态
          this.clearCellSelection()
          event.preventDefault()
          break
      }

      // 如果位置发生变化，选中新单元格
      if (newRow !== this.selectedCell.row || newCol !== this.selectedCell.col) {
        // 确保目标单元格存在
        if (this.dataRows[newRow] && this.dataRows[newRow][newCol]) {
          this.selectCell(newRow, newCol)
        }
      }
    },

    // 初始化MathJax
    async initializeMathJax() {
      try {
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$'], ['\\[', '\\]']]
          },
          chtml: {
            fontURL: '/fonts/mathjax'
          }
        }

        const mathJaxScript = document.createElement('script')
        mathJaxScript.id = 'MathJax-script'
        mathJaxScript.async = true
        mathJaxScript.src = '/js/mathjax/tex-mml-chtml-mathjax-newcm.js'

        mathJaxScript.onload = () => {
          this.mathJaxReady = true
        }

        mathJaxScript.onerror = () => {
          this.mathJaxReady = false
        }

        document.head.appendChild(mathJaxScript)

      } catch (error) {
        console.error('MathJax初始化失败:', error)
        this.mathJaxReady = false
      }
    },

    // 应用统一尺寸设置
    applyUniformSize(width, height) {
      const table = this.$refs.editableTable
      if (table) {
        const cells = table.querySelectorAll('td')

        cells.forEach((cell) => {
          cell.style.width = `${width}px`
          cell.style.minWidth = `${width}px`
          cell.style.height = `${height}px`
          cell.style.minHeight = `${height}px`
        })

        this.$emit('table-updated')
      }
    },

    // 获取表格引用（供父组件使用）
    getTableRef() {
      return this.$refs.editableTable
    },

    // 获取表格容器引用（供父组件使用）
    getTableContainerRef() {
      return this.$refs.tableContainer
    },

    // 获取当前列宽配置（供导出功能使用）
    getColumnWidths() {
      return [...this.currentColumnWidths]
    },

    // 获取实际的行高度
    getActualRowHeight(dataRowIndex) {
      // 优先使用缓存的高度，避免重复计算
      if (this.internalRowHeights[dataRowIndex] && this.internalRowHeights[dataRowIndex] > 0) {
        return this.internalRowHeights[dataRowIndex]
      }

      const table = this.$refs.editableTable
      if (!table) return 50

      // 计算实际的表格行索引（数据行索引 + 表头行数）
      const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0
      const actualRowIndex = dataRowIndex + headerRowCount

      if (actualRowIndex < table.rows.length) {
        const row = table.rows[actualRowIndex]
        const rect = row.getBoundingClientRect()
        const height = Math.round(rect.height)

        // 只有当高度发生实际变化时才更新缓存
        if (height > 0 && height !== this.internalRowHeights[dataRowIndex]) {
          this.$set(this.internalRowHeights, dataRowIndex, height)
        }

        return height
      }

      return this.internalRowHeights[dataRowIndex] || 50
    },

    // 获取所有行的实际高度
    getAllRowHeights() {
      const heights = []
      for (let i = 0; i < this.dataRows.length; i++) {
        heights.push(this.getActualRowHeight(i))
      }
      return heights
    },

    // 应用行高度到DOM元素
    applyRowHeightsToDOM(startRow, rowCount) {
      const table = this.$refs.editableTable
      if (!table) return

      const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0

      for (let i = 0; i < rowCount; i++) {
        const dataRowIndex = startRow + i
        const actualRowIndex = dataRowIndex + headerRowCount
        const height = this.internalRowHeights[dataRowIndex]

        if (height && actualRowIndex < table.rows.length) {
          const row = table.rows[actualRowIndex]
          const cells = Array.from(row.cells)

          cells.forEach(cell => {
            cell.style.height = `${height}px`
            cell.style.minHeight = `${height}px`
          })

          console.log(`应用行高度: 数据行${dataRowIndex} -> 表格行${actualRowIndex}, 高度${height}px`)
        }
      }
    },

    // 获取指定列的实际宽度
    getActualColumnWidth(columnIndex) {
      if (columnIndex < 0 || columnIndex >= this.currentColumnWidths.length) {
        return this.headerCellWidth
      }
      return this.currentColumnWidths[columnIndex]
    },

    // 获取所有列的实际宽度信息
    getColumnWidthsInfo() {
      return {
        columnWidths: [...this.currentColumnWidths],
        headerCellWidth: this.headerCellWidth,
        headerCellHeight: this.headerCellHeight,
        totalWidth: this.currentColumnWidths.reduce((sum, width) => sum + width, 0),
        verticalHeadersConfig: [...this.currentVerticalHeadersConfig]
      }
    },

    // 获取当前表格的列数
    getCurrentColumnCount() {
      return this.currentColumnCount
    },

    // === 新的 CellEditor 事件处理方法 ===

    // 处理单元格右键菜单
    handleCellContextMenu(rowIndex, cellIndex, event) {
      // 复用原来的右键菜单逻辑
      this.handleContextMenu(event)
    },

    // 处理单元格开始编辑
    handleCellStartEdit(rowIndex, cellIndex) {
      console.log('单元格开始编辑:', rowIndex, cellIndex)

      // 确保行存在
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        // 确保单元格被选中
        this.selectCell(rowIndex, cellIndex)

        // 进入编辑状态
        cell.isEditing = true
        this.$emit('start-edit', { rowIndex, cellIndex, cell })
      }
    },

    // 处理单元格完成编辑
    handleCellFinishEdit(rowIndex, cellIndex, newContent) {
      console.log('单元格完成编辑:', rowIndex, cellIndex, newContent)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = false
        cell.content = newContent || ''
        cell.hasMath = this.containsMath(cell.content)

        this.$emit('finish-edit', { rowIndex, cellIndex, cell, newContent })
      }
    },

    // 处理单元格取消编辑
    handleCellCancelEdit(rowIndex, cellIndex) {
      console.log('单元格取消编辑:', rowIndex, cellIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.isEditing = false
        this.$emit('cancel-edit', { rowIndex, cellIndex, cell })
      }
    },

    // 处理单元格内容变化
    handleCellContentChange(rowIndex, cellIndex, newContent) {
      console.log('单元格内容变化:', rowIndex, cellIndex, newContent)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = newContent || ''
        cell.hasMath = this.containsMath(cell.content)
      }
    },

    // 处理单元格输入
    handleCellInput(rowIndex, cellIndex, content) {
      // 实时更新内容
      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = content || ''
      }
    },

    // 处理单元格移动到下一个
    handleCellMoveNext(rowIndex, cellIndex, direction) {
      console.log('移动到下一个单元格:', direction)

      let nextRowIndex = rowIndex
      let nextCellIndex = cellIndex

      if (direction === 'next') {
        nextCellIndex++
        if (nextCellIndex >= this.currentColumnCount) {
          nextCellIndex = 0
          nextRowIndex++
        }
      } else if (direction === 'prev') {
        nextCellIndex--
        if (nextCellIndex < 0) {
          nextCellIndex = this.currentColumnCount - 1
          nextRowIndex--
        }
      }

      // 确保目标行存在
      if (nextRowIndex >= 0) {
        this.ensureRowExists(nextRowIndex)

        // 聚焦到下一个单元格
        this.$nextTick(() => {
          const nextCellRef = this.$refs[`cell-${nextRowIndex}-${nextCellIndex}`]
          if (nextCellRef && nextCellRef[0]) {
            nextCellRef[0].edit()
          }
        })
      }
    },

    // 确保行存在
    ensureRowExists(rowIndex) {
      while (this.dataRows.length <= rowIndex) {
        const newRow = Array(this.currentColumnCount).fill(null).map(() => ({
          content: '',
          originContent: '', // 添加原始内容字段
          isEditing: false,
          originalContent: '',
          hasMath: false,
          selectAll: false
        }))
        this.dataRows.push(newRow)
        this.$emit('ensure-row', this.dataRows.length - 1)
      }

      // 确保现有行的列数与当前表头一致
      this.ensureRowColumnCount(rowIndex)
    },

    // 确保指定行的列数与当前表头一致
    ensureRowColumnCount(rowIndex) {
      if (rowIndex >= 0 && rowIndex < this.dataRows.length) {
        const row = this.dataRows[rowIndex]
        const targetColumnCount = this.currentColumnCount

        // 如果当前行的列数少于目标列数，补充列
        while (row.length < targetColumnCount) {
          row.push({
            content: '',
            isEditing: false,
            originalContent: '',
            hasMath: false,
            selectAll: false
          })
        }

        // 如果当前行的列数多于目标列数，移除多余的列
        if (row.length > targetColumnCount) {
          row.splice(targetColumnCount)
        }
      }
    },

    // 确保所有现有行的列数与当前表头一致
    ensureAllRowsColumnCount() {
      for (let i = 0; i < this.dataRows.length; i++) {
        this.ensureRowColumnCount(i)
      }
    },

    // 外部调用：设置单元格内容
    setCellContent(rowIndex, cellIndex, content) {
      this.ensureRowExists(rowIndex)

      const cell = this.dataRows[rowIndex][cellIndex]
      if (cell) {
        cell.content = content || ''
        cell.hasMath = this.containsMath(content)

        const cellRef = this.$refs[`cell-${rowIndex}-${cellIndex}`]
        if (cellRef && cellRef[0]) {
          cellRef[0].setContent(content)
        }
      }
    },

    // === JSON数据插入功能 ===

    /**
     * 从JSON数据插入表格数据
     * @param {Object} jsonData - JSON数据对象
     * @param {Object} options - 插入选项
     */
    insertDataFromJSON(jsonData, options = {}) {
      try {
        const {
          clearExisting = false,
          startRow = 0,
          mergeCells = [],
          validateData = true
        } = options

        // 验证JSON数据格式
        if (validateData && !this.validateJSONData(jsonData)) {
          throw new Error('JSON数据格式不正确')
        }

        // 清空现有数据（如果需要）
        if (clearExisting) {
          this.clearAllData()
        }

        // 插入数据行（使用cellRows格式）
        if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
          this.insertCellRowsFromJSON(jsonData.cellRows, startRow)
        }

        // 应用单元格合并
        if (mergeCells && mergeCells.length > 0) {
          this.applyCellMerges(mergeCells)
        }

        // 强制更新表格以确保样式正确应用
        this.$nextTick(() => {
          this.$forceUpdate()
          // 再次确保合并单元格样式正确
          this.$nextTick(() => {
            this.forceUpdateAllMergedCells()
          })
        })

        // 更新表格信息
        this.$emit('table-updated')
        this.$emit('data-inserted', { jsonData, options })

        return {
          success: true,
          message: '数据插入成功',
          insertedRows: jsonData.cellRows ? jsonData.cellRows.length : 0
        }

      } catch (error) {
        console.error('JSON数据插入失败:', error)
        return {
          success: false,
          message: error.message || '数据插入失败',
          error: error
        }
      }
    },

    /**
     * 验证JSON数据格式
     * @param {Object} jsonData - 要验证的JSON数据
     * @returns {boolean} 验证结果
     */
    validateJSONData(jsonData) {
      if (!jsonData || typeof jsonData !== 'object') {
        return false
      }

      // 检查是否有cellRows数组
      if (jsonData.cellRows && Array.isArray(jsonData.cellRows)) {
        // 验证cellRows格式
        return this.validateCellRowsData(jsonData.cellRows)
      }

      return false
    },



    /**
     * 验证复杂cellRows数据格式
     */
    validateCellRowsData(cellRows) {
      for (const row of cellRows) {
        if (!Array.isArray(row) || row.length > this.currentColumnCount) {
          return false
        }

        // 检查每个单元格数据对象
        for (const cellData of row) {
          if (!cellData || typeof cellData !== 'object') {
            return false
          }

          // content字段是必需的
          if (!cellData.hasOwnProperty('content')) {
            return false
          }

          // 验证可选字段的类型
          if (cellData.hasMath !== undefined && typeof cellData.hasMath !== 'boolean') {
            return false
          }
          if (cellData.width !== undefined && typeof cellData.width !== 'number') {
            return false
          }
          if (cellData.height !== undefined && typeof cellData.height !== 'number') {
            return false
          }
        }
      }
      return true
    },



    /**
     * 从JSON数据插入复杂格式的行（支持公式和宽度配置）
     * @param {Array} cellRows - 单元格数据数组
     * @param {number} startRow - 开始插入的行索引
     */
    insertCellRowsFromJSON(cellRows, startRow = 0) {
      cellRows.forEach((rowData, index) => {
        const targetRowIndex = startRow + index
        this.ensureRowExists(targetRowIndex)

        // 填充行数据，确保每行的单元格数与当前表头一致
        for (let cellIndex = 0; cellIndex < this.currentColumnCount; cellIndex++) {
          const cellData = cellIndex < rowData.length ? rowData[cellIndex] : {}
          const content = cellData.content || ''
          const originContent = cellData.originContent || content

          const cell = this.dataRows[targetRowIndex][cellIndex]
          if (cell) {
            // 使用Vue.set确保响应式更新
            this.$set(cell, 'content', content)
            this.$set(cell, 'originContent', originContent) // 保存原始内容
            this.$set(cell, 'hasMath', cellData.hasMath || this.containsMath(content))
            this.$set(cell, 'isEditing', false)
            this.$set(cell, 'originalContent', content) // 保持向后兼容

            // 设置公式相关属性
            if (cellData.mathML) {
              this.$set(cell, 'mathML', cellData.mathML)
            }
            if (cellData.hasMultipleContent) {
              this.$set(cell, 'hasMultipleContent', cellData.hasMultipleContent)
            }
            if (cellData.mathMLMap) {
              this.$set(cell, 'mathMLMap', cellData.mathMLMap)
            }

            // 设置尺寸属性（如果有的话）
            if (cellData.width) {
              this.$set(cell, 'width', cellData.width)
            }
            if (cellData.height) {
              this.$set(cell, 'height', cellData.height)
              // 同时更新内部行高度状态
              this.$set(this.internalRowHeights, targetRowIndex, cellData.height)
            }

            // 处理嵌套表格数据
            if (cellData.nestedTable && this.enableNestedTables) {
              console.log(`处理嵌套表格数据 - 行${targetRowIndex}列${cellIndex}:`, cellData.nestedTable)
              console.log('嵌套表格配置详情:', {
                enabled: cellData.nestedTable.enabled,
                config: cellData.nestedTable.config,
                hasRowHeights: !!(cellData.nestedTable.config && cellData.nestedTable.config.rowHeights),
                rowHeights: cellData.nestedTable.config ? cellData.nestedTable.config.rowHeights : undefined
              })

              if (this.validateNestedTableConfig(cellData.nestedTable)) {
                this.$set(cell, 'nestedTable', cellData.nestedTable)
                console.log(`成功设置嵌套表格 - 行${targetRowIndex}列${cellIndex}`)
                console.log('设置后的嵌套表格:', cell.nestedTable)
              } else {
                console.warn(`行${targetRowIndex}列${cellIndex}的嵌套表格配置无效，已跳过`)
              }
            } else if (cellData.nestedTable) {
              console.log(`嵌套表格功能未启用，跳过 - 行${targetRowIndex}列${cellIndex}`)
            }

            // 清除可能存在的合并标记
            if (cell.merged) {
              this.$delete(cell, 'merged')
            }
          }
        }
      })

      // 应用行高度到DOM元素
      this.$nextTick(() => {
        this.applyRowHeightsToDOM(startRow, cellRows.length)
      })

      console.log('复杂格式数据插入完成，行数:', cellRows.length)
    },

    /**
     * 应用单元格合并
     * @param {Array} mergeCells - 合并单元格配置数组
     */
    applyCellMerges(mergeCells) {
      this.$nextTick(() => {
        const table = this.$refs.editableTable
        if (!table) return

        // 计算表头行数偏移
        const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0
        console.log('应用数据行合并，表头行数:', headerRowCount)

        mergeCells.forEach(merge => {
          try {
            const {
              startRow,
              startCol,
              endRow,
              endCol,
              content = ''
            } = merge

            // 将绝对行索引转换为数据行索引（减去表头行数）
            const dataStartRow = startRow - headerRowCount
            const dataEndRow = endRow - headerRowCount

            console.log('合并转换:', {
              原始: { startRow, endRow },
              转换后: { dataStartRow, dataEndRow },
              表头行数: headerRowCount
            })

            // 验证转换后的数据行合并范围
            if (!this.validateDataRowMergeRange(dataStartRow, startCol, dataEndRow, endCol)) {
              console.warn('无效的数据行合并范围:', {
                原始: merge,
                转换后: { dataStartRow, startCol, dataEndRow, endCol }
              })
              return
            }

            // 应用数据行合并
            this.mergeCellRange(dataStartRow, startCol, dataEndRow, endCol, content)

          } catch (error) {
            console.error('应用单元格合并失败:', error, merge)
          }
        })

        // 在所有合并应用完成后，强制更新整个表格
        this.$nextTick(() => {
          this.forceUpdateAllMergedCells()
        })
      })
    },

    /**
     * 验证合并范围
     * @param {number} startRow - 开始行
     * @param {number} startCol - 开始列
     * @param {number} endRow - 结束行
     * @param {number} endCol - 结束列
     * @returns {boolean} 验证结果
     */
    validateMergeRange(startRow, startCol, endRow, endCol) {
      return (
        typeof startRow === 'number' && startRow >= 0 &&
        typeof startCol === 'number' && startCol >= 0 && startCol < this.currentColumnCount &&
        typeof endRow === 'number' && endRow >= startRow &&
        typeof endCol === 'number' && endCol >= startCol && endCol < this.currentColumnCount
      )
    },

    /**
     * 验证数据行合并范围
     * @param {number} dataStartRow - 数据行开始行索引
     * @param {number} startCol - 开始列
     * @param {number} dataEndRow - 数据行结束行索引
     * @param {number} endCol - 结束列
     * @returns {boolean} 验证结果
     */
    validateDataRowMergeRange(dataStartRow, startCol, dataEndRow, endCol) {
      return (
        typeof dataStartRow === 'number' && dataStartRow >= 0 &&
        typeof startCol === 'number' && startCol >= 0 && startCol < this.currentColumnCount &&
        typeof dataEndRow === 'number' && dataEndRow >= dataStartRow &&
        typeof endCol === 'number' && endCol >= startCol && endCol < this.currentColumnCount
      )
    },

    /**
     * 合并单元格范围（数据行索引）
     * @param {number} dataStartRow - 数据行开始行索引
     * @param {number} startCol - 开始列
     * @param {number} dataEndRow - 数据行结束行索引
     * @param {number} endCol - 结束列
     * @param {string} content - 合并后的内容
     */
    mergeCellRange(dataStartRow, startCol, dataEndRow, endCol, content = '') {
      const table = this.$refs.editableTable
      if (!table) return

      console.log('执行数据行合并:', {
        dataStartRow,
        startCol,
        dataEndRow,
        endCol,
        content
      })

      // 确保所有相关数据行都存在
      for (let row = dataStartRow; row <= dataEndRow; row++) {
        this.ensureRowExists(row)
      }

      // 计算合并的行数和列数
      const rowspan = dataEndRow - dataStartRow + 1
      const colspan = endCol - startCol + 1

      // 计算表头行数偏移，获取实际的表格行索引
      const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0
      const actualStartRow = dataStartRow + headerRowCount
      const actualEndRow = dataEndRow + headerRowCount

      console.log('实际表格行索引:', {
        actualStartRow,
        actualEndRow,
        headerRowCount
      })

      // 获取目标单元格（实际表格中的位置）
      const targetRow = table.rows[actualStartRow]
      if (!targetRow) {
        console.error('目标行不存在:', actualStartRow)
        return
      }

      const targetCell = targetRow.cells[startCol]
      if (!targetCell) {
        console.error('目标单元格不存在:', actualStartRow, startCol)
        return
      }

      // 设置合并属性
      if (rowspan > 1) {
        targetCell.setAttribute('rowspan', rowspan)
      }
      if (colspan > 1) {
        targetCell.setAttribute('colspan', colspan)
      }

      // 设置内容
      if (content) {
        this.setCellContent(dataStartRow, startCol, content)
      }

      // 隐藏被合并的单元格（使用实际表格行索引）
      for (let actualRow = actualStartRow; actualRow <= actualEndRow; actualRow++) {
        for (let col = startCol; col <= endCol; col++) {
          if (actualRow === actualStartRow && col === startCol) {
            continue // 跳过主单元格
          }

          const cellRow = table.rows[actualRow]
          if (cellRow && cellRow.cells[col]) {
            cellRow.cells[col].style.display = 'none'
          }
        }
      }

      // 标记数据行单元格已合并（使用数据行索引）
      const mainCell = this.dataRows[dataStartRow][startCol]
      if (mainCell) {
        // 使用Vue.set确保响应式更新
        this.$set(mainCell, 'merged', {
          rowspan,
          colspan,
          startRow: dataStartRow,  // 数据行索引
          startCol,
          endRow: dataEndRow,      // 数据行索引
          endCol
        })

        // 强制更新合并单元格的高度缓存
        this.$nextTick(() => {
          // 确保合并单元格的高度被正确计算和缓存
          for (let i = 0; i < rowspan; i++) {
            const targetRowIndex = dataStartRow + i
            if (!this.internalRowHeights[targetRowIndex]) {
              this.getActualRowHeight(targetRowIndex)
            }
          }
          console.log(`合并单元格高度缓存更新完成: 行${dataStartRow}-${dataEndRow}`)
        })
      }

      // 强制重新渲染以应用样式（使用数据行索引）
      this.$nextTick(() => {
        this.forceUpdateMergedCellStyles(dataStartRow, startCol, dataEndRow, endCol)
      })
    },

    /**
     * 强制更新合并单元格样式（数据行索引）
     * @param {number} dataStartRow - 数据行开始行索引
     * @param {number} startCol - 开始列
     * @param {number} dataEndRow - 数据行结束行索引
     * @param {number} endCol - 结束列
     */
    forceUpdateMergedCellStyles(dataStartRow, startCol, dataEndRow, endCol) {
      const table = this.$refs.editableTable
      if (!table) return

      // 计算实际表格行索引
      const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0
      const actualStartRow = dataStartRow + headerRowCount

      // 获取主合并单元格（实际表格中的位置）
      const targetRow = table.rows[actualStartRow]
      if (!targetRow) return

      const targetCell = targetRow.cells[startCol]
      if (!targetCell) return

      // 强制添加合并单元格的CSS类
      if (!targetCell.classList.contains('merged-cell')) {
        targetCell.classList.add('merged-cell')
      }

      // 确保rowspan和colspan属性正确设置
      const rowspan = dataEndRow - dataStartRow + 1
      const colspan = endCol - startCol + 1

      if (rowspan > 1) {
        targetCell.setAttribute('rowspan', rowspan)
      }
      if (colspan > 1) {
        targetCell.setAttribute('colspan', colspan)
      }

      // 强制重新计算样式
      targetCell.style.display = 'none'
      targetCell.offsetHeight // 触发重排
      targetCell.style.display = ''

      console.log('强制更新合并单元格样式:', {
        dataStartRow, startCol, dataEndRow, endCol,
        actualStartRow,
        rowspan, colspan,
        hasClass: targetCell.classList.contains('merged-cell')
      })
    },

    /**
     * 强制更新所有合并单元格
     */
    forceUpdateAllMergedCells() {
      // 遍历所有数据行，找到合并单元格并强制更新
      this.dataRows.forEach((row, rowIndex) => {
        row.forEach((cell, cellIndex) => {
          if (cell && cell.merged) {
            const { startRow, startCol, endRow, endCol } = cell.merged
            this.forceUpdateMergedCellStyles(startRow, startCol, endRow, endCol)
          }
        })
      })

      // 强制Vue重新渲染
      this.$forceUpdate()

      console.log('强制更新所有合并单元格完成')
    },

    /**
     * 清空所有数据
     */
    clearAllData() {
      this.dataRows.splice(0, this.dataRows.length)

      // 添加一个空行
      const emptyRow = Array(this.currentColumnCount).fill(null).map(() => ({
        content: '',
        originContent: '', // 添加原始内容字段
        isEditing: false,
        originalContent: '',
        hasMath: false,
        selectAll: false
      }))
      this.dataRows.push(emptyRow)

      // 清除所有合并
      this.clearAllMerges()
    },

    /**
     * 清除所有单元格合并
     */
    clearAllMerges() {
      this.$nextTick(() => {
        const table = this.$refs.editableTable
        if (!table) return

        // 重置所有数据单元格的合并属性
        const dataCells = table.querySelectorAll('.editable-cell')
        dataCells.forEach(cell => {
          cell.removeAttribute('rowspan')
          cell.removeAttribute('colspan')
          cell.style.display = ''
        })

        // 清除数据中的合并标记
        this.dataRows.forEach(row => {
          row.forEach(cell => {
            if (cell.merged) {
              delete cell.merged
            }
          })
        })
      })
    },

    /**
     * 获取表格数据为JSON格式
     * @param {Object} options - 导出选项
     * @returns {Object} JSON格式的表格数据
     */
    getDataAsJSON(options = {}) {
      const {
        includeEmpty = false,
        includeMergeInfo = true,
        includeNestedTables = true
      } = options

      const result = {
        headerConfig: {
          headers: this.currentHeaderConfig.headers,
          merges: []
        },
        headerWidthConfig: {
          columnWidths: this.currentHeaderWidthConfig?.columnWidths || this.currentColumnWidths,
          headerHeights: this.currentHeaderWidthConfig?.headerHeights || [],
        },
        verticalHeadersConfig: this.currentVerticalHeadersConfig || this.verticalHeadersConfig,
        cellRows: [],
        merges: [],
        metadata: {
          title: this.internalHeaderConfig?.title || '检验记录表',
          useDynamicHeader: this.internalUseDynamicHeader || this.useDynamicHeader,
          hasCustomWidth: true,
          totalRows: this.dataRows.length,
          totalColumns: this.currentColumnCount,
          headerRows: this.currentHeaderConfig.headers.length,
          exportTime: new Date().toISOString(),
          hasNestedTables: includeNestedTables,
          nestedLevel: this.nestedLevel || 0,
          maxNestedLevel: this.maxNestedLevel
        }
      }

      // 处理表头合并信息
      if (includeMergeInfo && this.currentHeaderConfig.merges) {
        this.currentHeaderConfig.merges.forEach(merge => {
          const { startRow, startCol, endRow, endCol } = merge

          result.headerConfig.merges.push({
            startRow,
            startCol,
            endRow,
            endCol,
            content: merge.content || ''
          })
        })
      } else if (this.currentHeaderConfig.merges) {
        // 如果不包含合并信息，直接复制原始数据
        result.headerConfig.merges = [...this.currentHeaderConfig.merges]
      }

      // 导出行数据为cellRows格式（包含嵌套表格）
      this.dataRows.forEach((row, rowIndex) => {
        // 如果不包含空行，跳过完全空的行
        if (!includeEmpty && row.every(cell => !(cell.content || '').trim() && !this.hasNestedTable(cell))) {
          return
        }

        const cellRowData = row.map((cell, cellIndex) => {
          const cellData = {
            content: cell.content || '',
            originContent: cell.originContent || cell.content || '', // 导出原始内容
            width: this.currentColumnWidths[cellIndex] || this.headerCellWidth || 100,
            height: this.getActualRowHeight(rowIndex) // 使用实际的行高度
          }

          // 如果有数学公式相关信息，添加到cellData中
          if (cell.hasMath) {
            cellData.hasMath = cell.hasMath
          }
          if (cell.mathML) {
            cellData.mathML = cell.mathML
          }
          if (cell.mathMLMap) {
            cellData.mathMLMap = cell.mathMLMap
          }
          if (cell.hasMultipleContent) {
            cellData.hasMultipleContent = cell.hasMultipleContent
          }

          // 如果有嵌套表格，添加到cellData中
          if (includeNestedTables && this.hasNestedTable(cell)) {
            cellData.nestedTable = {
              enabled: true,
              config: this.exportNestedTableConfig(cell.nestedTable.config)
            }
          }

          return cellData
        })

        result.cellRows.push(cellRowData)

        // 收集合并信息
        if (includeMergeInfo) {
          row.forEach((cell, cellIndex) => {
            if (cell.merged) {
              const { startRow, startCol, endRow, endCol } = cell.merged

              // 数据行合并使用相对于表头的索引
              const headerRowCount = this.currentHeaderConfig.headers ? this.currentHeaderConfig.headers.length : 0

              result.merges.push({
                startRow: startRow + headerRowCount,  // 转换为绝对行索引
                endRow: endRow + headerRowCount,      // 转换为绝对行索引
                startCol,
                endCol,
                content: cell.content || ''
              })
            }
          })
        }
      })

      return result
    },

    /**
     * 导出嵌套表格配置
     */
    exportNestedTableConfig(config) {
      if (!config) return null

      const exported = {
        columnWidths: config.columnWidths || [],
        rowHeights: config.rowHeights || [], // 添加行高配置导出
        cellRows: config.cellRows || [],
        metadata: config.metadata || {}
      }

      // 调试日志：只在有rowHeights时输出
      if (config.rowHeights && config.rowHeights.length > 0) {
        console.log('导出嵌套表格配置 - 包含行高:', {
          rowHeights: config.rowHeights,
          exportedRowHeights: exported.rowHeights
        })
      }

      return exported
    },

    /**
     * 验证嵌套表格配置
     */
    validateNestedTableConfig(nestedTable) {
      try {
        console.log('开始验证嵌套表格配置:', nestedTable)

        if (!nestedTable || typeof nestedTable !== 'object') {
          console.warn('嵌套表格配置不是对象类型')
          return false
        }

        if (!nestedTable.enabled) {
          console.log('嵌套表格未启用，验证通过')
          return true // 如果未启用，则认为是有效的
        }

        const config = nestedTable.config
        if (!config || typeof config !== 'object') {
          console.warn('嵌套表格config不存在或不是对象类型')
          return false
        }

        // 验证嵌套表格的基本结构
        if (config.cellRows && Array.isArray(config.cellRows)) {
          // 递归验证嵌套表格的数据（但限制嵌套层级）
          if (this.nestedLevel < this.maxNestedLevel) {
            // 对于嵌套表格，使用简化的验证逻辑
            for (const row of config.cellRows) {
              if (!Array.isArray(row)) {
                console.warn('嵌套表格行数据不是数组类型')
                return false
              }
              for (const cell of row) {
                if (!cell || typeof cell !== 'object' || !cell.hasOwnProperty('content')) {
                  console.warn('嵌套表格单元格数据格式不正确')
                  return false
                }
              }
            }
          }
        }

        // 验证列宽配置
        if (config.columnWidths && !Array.isArray(config.columnWidths)) {
          console.warn('嵌套表格columnWidths不是数组类型')
          return false
        }

        // 验证行高配置
        if (config.rowHeights && !Array.isArray(config.rowHeights)) {
          console.warn('嵌套表格rowHeights不是数组类型')
          return false
        }

        console.log('嵌套表格配置验证通过')
        return true
      } catch (error) {
        console.error('嵌套表格配置验证过程中出错:', error)
        return false
      }
    }
  }
}
</script>

<style scoped>
/* 表格容器样式 */
.table-container-wrapper {
  position: relative;
}

.table-wrapper {
  width: 100%;
  overflow: auto;
}

.table-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: auto;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-scroll-container {
  overflow: auto;
  max-height: 100%;
}

table {
  border-collapse: collapse;
  font-family: Arial, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  table-layout: fixed;
  width: auto !important;
}

/* 表头样式 */
.header-cell {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid #ddd;
  padding: 12px 8px;
  text-align: center;
  font-weight: bold;
  color: #333;
  white-space: nowrap;
  user-select: none;
  cursor: default;
  box-sizing: border-box;
  /* 宽高通过内联样式控制，这里不设置固定值 */
}

/* 纵向文字样式 - 只影响文字方向，不影响单元格布局 */
.header-cell.vertical-text {
  padding: 12px 4px;
}

.header-cell.vertical-text .vertical-text-span {
  writing-mode: vertical-rl;
  text-orientation: upright;
  display: inline-block;
  white-space: nowrap;
  height: 100%;
  line-height: 1;
}

/* 横向文字样式 */
.header-cell.horizontal-text {
  padding: 12px 8px;
}

/* 可编辑单元格样式 */
.editable-cell {
  border: 1px solid #ddd;
  padding: 0;
  text-align: center;
  cursor: text;
  transition: all 0.2s ease;
  min-height: 50px;
  white-space: pre-wrap;
  word-wrap: break-word;
  vertical-align: top;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  /* 宽度通过内联样式控制，这里不设置固定值 */
}

.editable-cell:hover {
  background-color: #f8f9fa;
  border-color: #007bff;
}

/* 单元格选中状态样式 */
.editable-cell.selected {
  background-color: #e3f2fd !important;
  border: 2px solid #2196f3 !important;
  box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.3);
  position: relative;
  z-index: 1;
}

.editable-cell.selected:hover {
  background-color: #bbdefb !important;
  border-color: #1976d2 !important;
}

/* 编辑状态样式 */
.editable-cell.editing {
  background-color: #fff3e0 !important;
  border: 2px solid #ff9800 !important;
  box-shadow: 0 0 0 2px rgba(255, 152, 0, 0.2);
  z-index: 2;
}

.editable-cell.editing:hover {
  background-color: #fff3e0 !important;
  border-color: #f57c00 !important;
}

.editable-cell.has-math {
  background-color: #e8f5e8;
}

.editable-cell.merged-cell {
  background-color: #f8f9fa !important;
  border: 2px solid #007bff !important;
  font-weight: 500;
  vertical-align: top !important;
  padding: 0 !important;
  position: relative;
}

.editable-cell.merged-cell:hover {
  background-color: #e3f2fd !important;
  border-color: #0056b3 !important;
}

/* 合并单元格的选中状态 */
.editable-cell.merged-cell.selected {
  background-color: #e1f5fe !important;
  border: 3px solid #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.3) !important;
}

.editable-cell.merged-cell.selected:hover {
  background-color: #b2ebf2 !important;
  border-color: #0097a7 !important;
}

/* 合并单元格的编辑状态 */
.editable-cell.merged-cell.editing {
  background-color: #fff8e1 !important;
  border: 3px solid #ffc107 !important;
  box-shadow: 0 0 0 2px rgba(255, 193, 7, 0.3) !important;
}

.editable-cell.merged-cell.editing:hover {
  background-color: #fff8e1 !important;
  border-color: #ff8f00 !important;
}

/* 合并单元格中的编辑器样式 */
.editable-cell.merged-cell .cell-editor-wrapper {
  height: 100% !important;
  min-height: 100% !important;
}

.editable-cell.merged-cell .cell-editor {
  height: 100% !important;
  min-height: 100% !important;
  border: none !important;
  border-radius: 0 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  box-shadow: none !important;
  padding: 8px !important;
}

.editable-cell.merged-cell .cell-editor:focus {
  background-color: #ffffff !important;
  box-shadow: inset 0 0 0 1px rgba(0, 123, 255, 0.3) !important;
}

.editable-cell.merged-cell .cell-display {
  height: 100% !important;
  min-height: 100% !important;
  padding: 8px !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 右键菜单样式 */
.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 150px;
}

.context-menu-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: background-color 0.2s;
}

.context-menu-item:hover {
  background-color: #f5f5f5;
}

.context-menu-item.delete-item {
  color: #dc3545;
}

.context-menu-item.delete-item:hover {
  background-color: #f8d7da;
}

.context-menu-divider {
  height: 1px;
  background-color: #eee;
  margin: 4px 0;
}

.context-menu-info {
  padding: 8px 16px;
  font-size: 12px;
  color: #666;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.context-menu-info .info-label {
  font-weight: 500;
}

.context-menu-info .info-value {
  font-weight: bold;
  color: #007bff;
}

/* 嵌套表格设计器弹窗样式 */
.designer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
  padding: 20px;
  box-sizing: border-box;
}

.designer-dialog {
  background: white;
  border-radius: 12px;
  width: 95%;
  max-width: 1400px;
  height: 90%;
  max-height: 900px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.designer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.designer-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.designer-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
}

.designer-content .nested-table-designer {
  height: 100%;
  padding: 0;
  background: transparent;
}

.designer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.designer-actions .btn-cancel,
.designer-actions .btn-confirm {
  padding: 10px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.designer-actions .btn-cancel {
  background: #6c757d;
  color: white;
}

.designer-actions .btn-confirm {
  background: #007bff;
  color: white;
}

.designer-actions .btn-cancel:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.designer-actions .btn-confirm:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

/* 对话框样式 */
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog {
  background: white;
  border-radius: 8px;
  padding: 20px;
  min-width: 300px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dialog h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.dialog-content {
  margin-bottom: 20px;
}

.dialog-content label {
  display: block;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.dialog-content input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.dialog-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-cancel, .btn-confirm {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-confirm {
  background: #007bff;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
}

.btn-confirm:hover {
  background: #0056b3;
}

/* 嵌套表格样式 */
.has-nested-table {
  padding: 0 !important;
  vertical-align: top;
}

.nested-table-container {
  width: 100%;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.main-content-area {
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
  background: rgba(248, 249, 250, 0.5);
}

.nested-table-content {
  padding: 4px;
  background: white;
  animation: nestedTableSlideIn 0.3s ease-out;
  transform-origin: top;
}

.nested-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  line-height: 1.2;
}

.nested-cell {
  border: 1px solid #e0e0e0;
  padding: 4px 6px;
  vertical-align: top;
  background: white;
  position: relative;
}

.nested-cell:hover {
  background: rgba(23, 210, 239, 0.05);
}

/* 嵌套表格的展开/折叠动画 */
@keyframes nestedTableSlideIn {
  from {
    opacity: 0;
    transform: scaleY(0);
  }
  to {
    opacity: 1;
    transform: scaleY(1);
  }
}

/* 嵌套表格输入框样式 */
.nested-table-content input,
.nested-table-content textarea,
.nested-table-content select {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  width: 100% !important;
  height: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  font-size: 12px !important;
  line-height: 1.2 !important;
  color: #333 !important;
  resize: none !important;
}

.nested-table-content input:focus,
.nested-table-content textarea:focus,
.nested-table-content select:focus {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: rgba(23, 210, 239, 0.5) !important;
  box-shadow: 0 0 0 2px rgba(23, 210, 239, 0.2) !important;
}

/* 表单样式 */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* MathJax 样式覆盖 */
/* 覆盖 MathJax 容器的字体大小 */

/* 覆盖具有内联样式的 MathJax 容器 */
::v-deep .editable-cell mjx-container[style*="font-size"] {
  font-size: 100% !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .designer-dialog {
    width: 98%;
    height: 95%;
  }
}

@media (max-width: 768px) {
  .designer-overlay {
    padding: 10px;
  }

  .designer-dialog {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  .designer-header {
    padding: 15px 20px;
  }

  .designer-header h3 {
    font-size: 16px;
  }

  .designer-actions {
    padding: 15px 20px;
    flex-direction: column;
    gap: 8px;
  }

  .designer-actions .btn-cancel,
  .designer-actions .btn-confirm {
    width: 100%;
    padding: 12px 24px;
  }
}

</style>
