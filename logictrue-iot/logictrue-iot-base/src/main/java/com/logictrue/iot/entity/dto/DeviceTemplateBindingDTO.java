package com.logictrue.iot.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备模板绑定DTO
 *
 */
@Data
public class DeviceTemplateBindingDTO {

    /** 主键ID */
    private Long id;

    /** 设备编码 */
    @NotBlank(message = "设备编码不能为空")
    private String deviceCode;

    /** 模板ID */
    @NotNull(message = "模板ID不能为空")
    private Long templateId;

    /** 模板名称 */
    private String templateName;

    /** 模板编码 */
    private String templateCode;

    /** 绑定状态：1-启用，0-禁用 */
    private Integer bindStatus;

    /** 备注 */
    private String remark;

    /** 设备名称（查询时使用） */
    private String deviceName;

    /** 设备类型（查询时使用） */
    private String deviceType;
}
