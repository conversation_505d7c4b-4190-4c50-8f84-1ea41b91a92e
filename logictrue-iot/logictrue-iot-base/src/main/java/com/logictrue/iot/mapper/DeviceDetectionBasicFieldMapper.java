package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.DeviceDetectionBasicField;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 设备检测数据基础字段Mapper接口
 *
 */
@Mapper
public interface DeviceDetectionBasicFieldMapper extends BaseMapper<DeviceDetectionBasicField> {

    int batchInsert(List<DeviceDetectionBasicField> basicFields);
}
