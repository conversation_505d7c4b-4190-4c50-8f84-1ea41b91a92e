package com.logictrue.iot.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 设备数据导入请求DTO
 *
 */
@Data
@ApiModel(description = "设备数据导入请求")
public class DeviceImportRequest {

    @ApiModelProperty(value = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @ApiModelProperty(value = "导入模式：REPLACE-完全替换，MERGE-合并导入", required = true)
    @NotNull(message = "导入模式不能为空")
    private ImportMode importMode = ImportMode.REPLACE;

    @ApiModelProperty(value = "是否覆盖现有设备配置", example = "true")
    private Boolean overrideDeviceConfig = true;

    @ApiModelProperty(value = "是否覆盖现有模板绑定", example = "true")
    private Boolean overrideTemplateBinding = true;

    @ApiModelProperty(value = "是否覆盖现有检测数据", example = "true")
    private Boolean overrideDetectionData = true;

    @ApiModelProperty(value = "冲突处理策略：SKIP-跳过，OVERRIDE-覆盖，ERROR-报错")
    private ConflictStrategy conflictStrategy = ConflictStrategy.OVERRIDE;

    @ApiModelProperty(value = "导入备注")
    private String remark;

    /**
     * 导入模式枚举
     */
    public enum ImportMode {
        REPLACE("完全替换"),
        MERGE("合并导入");

        private final String description;

        ImportMode(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 冲突处理策略枚举
     */
    public enum ConflictStrategy {
        SKIP("跳过冲突项"),
        OVERRIDE("覆盖冲突项"),
        ERROR("遇到冲突报错");

        private final String description;

        ConflictStrategy(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
