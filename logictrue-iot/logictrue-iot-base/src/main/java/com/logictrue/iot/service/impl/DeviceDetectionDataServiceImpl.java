package com.logictrue.iot.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.entity.dto.DeviceDetectionDataDTO;
import com.logictrue.iot.entity.vo.DeviceDetectionDataVO;
import com.logictrue.iot.mapper.*;
import com.logictrue.iot.service.IDeviceDetectionDataService;
import com.logictrue.iot.service.IDeviceDetectionParseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备检测数据服务实现类
 *
 */
@Slf4j
@Service
public class DeviceDetectionDataServiceImpl extends ServiceImpl<DeviceDetectionDataMapper, DeviceDetectionData>
        implements IDeviceDetectionDataService {

    @Autowired
    private DeviceDetectionDataMapper detectionDataMapper;

    @Autowired
    private DeviceDetectionBasicFieldMapper basicFieldMapper;

    @Autowired
    private DeviceDetectionTableHeaderMapper tableHeaderMapper;

    @Autowired
    private DeviceDetectionTableDataMapper tableDataMapper;

    @Autowired
    private DeviceDetectionParseLogMapper parseLogMapper;

    @Autowired
    private IDeviceDetectionParseService parseService;

    @Override
    public IPage<DeviceDetectionDataVO> selectDetectionDataPage(Page<DeviceDetectionDataVO> page, DeviceDetectionDataDTO query) {
        return detectionDataMapper.selectDetectionDataPage(page,
                query.getDeviceCode(),
                query.getDeviceName(),
                query.getTemplateName(),
                query.getParseStatus(),
                query.getStartTime(),
                query.getEndTime());
    }

    @Override
    public DeviceDetectionDataVO getDetectionDataDetail(Long id) {
        DeviceDetectionDataVO detailVO = detectionDataMapper.selectDetectionDataDetail(id);
        if (detailVO == null) {
            return null;
        }

        // 获取基础字段数据
        LambdaQueryWrapper<DeviceDetectionBasicField> basicWrapper = new LambdaQueryWrapper<>();
        basicWrapper.eq(DeviceDetectionBasicField::getDetectionDataId, id);
        basicWrapper.orderBy(true, true, DeviceDetectionBasicField::getSheetIndex, DeviceDetectionBasicField::getSortOrder);
        List<DeviceDetectionBasicField> basicFields = basicFieldMapper.selectList(basicWrapper);

        // 获取表头数据
        LambdaQueryWrapper<DeviceDetectionTableHeader> headerWrapper = new LambdaQueryWrapper<>();
        headerWrapper.eq(DeviceDetectionTableHeader::getDetectionDataId, id);
        headerWrapper.orderBy(true, true, DeviceDetectionTableHeader::getSheetIndex, DeviceDetectionTableHeader::getColumnOrder);
        List<DeviceDetectionTableHeader> tableHeaders = tableHeaderMapper.selectList(headerWrapper);

        // 获取表格数据
        LambdaQueryWrapper<DeviceDetectionTableData> dataWrapper = new LambdaQueryWrapper<>();
        dataWrapper.eq(DeviceDetectionTableData::getDetectionDataId, id);
        dataWrapper.orderBy(true, true, DeviceDetectionTableData::getSheetIndex, DeviceDetectionTableData::getRowOrder);
        List<DeviceDetectionTableData> tableDataList = tableDataMapper.selectList(dataWrapper);

        // 按Sheet分组组织数据
        Map<String, DeviceDetectionDataVO.SheetDataVO> sheetDataMap = new HashMap<>();

        // 处理基础字段
        for (DeviceDetectionBasicField basicField : basicFields) {
            String sheetKey = getSheetKey(basicField.getSheetId(), basicField.getSheetIndex());
            DeviceDetectionDataVO.SheetDataVO sheetData = sheetDataMap.computeIfAbsent(sheetKey, k -> {
                DeviceDetectionDataVO.SheetDataVO sheet = new DeviceDetectionDataVO.SheetDataVO();
                sheet.setSheetId(basicField.getSheetId());
                sheet.setSheetName(basicField.getSheetName());
                sheet.setSheetIndex(basicField.getSheetIndex());
                sheet.setBasicFields(new ArrayList<>());
                sheet.setTableHeaders(new ArrayList<>());
                sheet.setTableDataList(new ArrayList<>());
                return sheet;
            });

            DeviceDetectionDataVO.BasicFieldVO basicFieldVO = new DeviceDetectionDataVO.BasicFieldVO();
            BeanUtils.copyProperties(basicField, basicFieldVO);
            sheetData.getBasicFields().add(basicFieldVO);
        }

        // 处理表头
        for (DeviceDetectionTableHeader tableHeader : tableHeaders) {
            String sheetKey = getSheetKey(tableHeader.getSheetId(), tableHeader.getSheetIndex());
            DeviceDetectionDataVO.SheetDataVO sheetData = sheetDataMap.computeIfAbsent(sheetKey, k -> {
                DeviceDetectionDataVO.SheetDataVO sheet = new DeviceDetectionDataVO.SheetDataVO();
                sheet.setSheetId(tableHeader.getSheetId());
                sheet.setSheetName(tableHeader.getSheetName());
                sheet.setSheetIndex(tableHeader.getSheetIndex());
                sheet.setBasicFields(new ArrayList<>());
                sheet.setTableHeaders(new ArrayList<>());
                sheet.setTableDataList(new ArrayList<>());
                return sheet;
            });

            DeviceDetectionDataVO.TableHeaderVO headerVO = new DeviceDetectionDataVO.TableHeaderVO();
            BeanUtils.copyProperties(tableHeader, headerVO);
            sheetData.getTableHeaders().add(headerVO);
        }

        // 处理表格数据
        Map<String, List<DeviceDetectionTableData>> dataBySheet = tableDataList.stream()
                .collect(Collectors.groupingBy(data -> getSheetKey(data.getSheetId(), data.getSheetIndex())));

        for (Map.Entry<String, List<DeviceDetectionTableData>> entry : dataBySheet.entrySet()) {
            String sheetKey = entry.getKey();
            List<DeviceDetectionTableData> sheetTableData = entry.getValue();

            DeviceDetectionDataVO.SheetDataVO sheetData = sheetDataMap.get(sheetKey);
            if (sheetData != null) {
                List<Map<String, Object>> tableDataVOList = sheetTableData.stream()
                        .map(data -> {
                            try {
                                @SuppressWarnings("unchecked")
                                Map<String, Object> rowMap = JSON.parseObject(data.getRowData(), Map.class);
                                return rowMap;
                            } catch (Exception e) {
                                log.warn("解析表格数据失败: {}", data.getRowData(), e);
                                return new HashMap<String, Object>();
                            }
                        })
                        .collect(Collectors.toList());
                sheetData.setTableDataList(tableDataVOList);
            }
        }

        // 设置Sheet数据列表
        List<DeviceDetectionDataVO.SheetDataVO> sheetDataList = new ArrayList<>(sheetDataMap.values());
        sheetDataList.sort(Comparator.comparing(DeviceDetectionDataVO.SheetDataVO::getSheetIndex));
        detailVO.setSheetDataList(sheetDataList);

        return detailVO;
    }

    /**
     * 获取Sheet键值
     */
    private String getSheetKey(String sheetId, Integer sheetIndex) {
        return StringUtils.hasText(sheetId) ? sheetId : "sheet_" + (sheetIndex != null ? sheetIndex : 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceDetectionData uploadAndParseExcel(String deviceCode, MultipartFile file, String remark) {
        return parseService.parseExcelFile(deviceCode, file, remark);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean reparseExcel(Long id) {
        try {
            parseService.reparseExcelFile(id);
            return true;
        } catch (Exception e) {
            log.error("重新解析Excel失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteDetectionData(Long id) {
        try {
            // 删除主记录（级联删除子记录）
            return removeById(id);
        } catch (Exception e) {
            log.error("删除检测数据失败，ID: {}", id, e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchDetectionData(List<Long> ids) {
        try {
            return removeByIds(ids);
        } catch (Exception e) {
            log.error("批量删除检测数据失败，IDs: {}", ids, e);
            return false;
        }
    }

    @Override
    public DeviceDetectionDataVO.ParseStatusStatistics getParseStatusStatistics() {
        LambdaQueryWrapper<DeviceDetectionData> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DeviceDetectionData::getParseStatus);
        List<DeviceDetectionData> dataList = list(wrapper);

        long total = dataList.size();
        long pending = dataList.stream().mapToLong(data -> data.getParseStatus() == 0 ? 1 : 0).sum();
        long success = dataList.stream().mapToLong(data -> data.getParseStatus() == 1 ? 1 : 0).sum();
        long failed = dataList.stream().mapToLong(data -> data.getParseStatus() == 2 ? 1 : 0).sum();

        DeviceDetectionDataVO.ParseStatusStatistics statistics = new DeviceDetectionDataVO.ParseStatusStatistics();
        statistics.setTotal(total);
        statistics.setPending(pending);
        statistics.setSuccess(success);
        statistics.setFailed(failed);
        statistics.setSuccessRate(total > 0 ? (double) success / total * 100 : 0.0);

        return statistics;
    }

    @Override
    public DeviceDetectionDataVO getLatestDetectionDataByDevice(String deviceCode) {
        LambdaQueryWrapper<DeviceDetectionData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceDetectionData::getDeviceCode, deviceCode);
        wrapper.orderByDesc(DeviceDetectionData::getCreateTime);
        wrapper.last("LIMIT 1");

        DeviceDetectionData latestData = getOne(wrapper);
        if (latestData == null) {
            return null;
        }

        return getDetectionDataDetail(latestData.getId());
    }

    @Override
    public void exportDetectionData(Long id, String format) {
        // 导出功能的实现
        log.info("导出检测数据，ID: {}, 格式: {}", id, format);
        // TODO: 实现导出逻辑
    }
}
