package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.iot.entity.DeviceTemplateBinding;
import com.logictrue.iot.entity.dto.DeviceTemplateBindingDTO;
import com.logictrue.iot.entity.vo.DeviceTemplateBindingVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 设备模板绑定Mapper接口
 *
 */
@Mapper
public interface DeviceTemplateBindingMapper extends BaseMapper<DeviceTemplateBinding> {

    /**
     * 分页查询设备模板绑定列表（关联设备信息）
     */
    IPage<DeviceTemplateBindingVO> selectBindingPageWithDevice(Page<DeviceTemplateBindingVO> page, @Param("query") DeviceTemplateBindingDTO query);

    /**
     * 根据设备编码查询绑定信息
     */
    @Select("SELECT * FROM device_template_binding WHERE device_code = #{deviceCode} AND bind_status = 1")
    DeviceTemplateBinding selectByDeviceCode(@Param("deviceCode") String deviceCode);

    /**
     * 根据模板ID查询绑定的设备列表
     */
    @Select("SELECT device_code FROM device_template_binding WHERE template_id = #{templateId} AND bind_status = 1")
    List<String> selectDeviceCodesByTemplateId(@Param("templateId") Long templateId);

    /**
     * 检查设备编码是否已绑定
     */
    @Select("SELECT COUNT(1) FROM device_template_binding WHERE device_code = #{deviceCode} AND bind_status = 1")
    int checkDeviceCodeExists(@Param("deviceCode") String deviceCode);

    /**
     * 获取所有可用的设备编码列表（未绑定的设备）
     */
    List<String> selectAvailableDeviceCodes();
}
