package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.entity.ExcelTemplateCell;
import com.logictrue.iot.entity.ExcelTemplateField;
import com.logictrue.iot.entity.ExcelTemplateSheet;
import com.logictrue.iot.entity.dto.ExcelTemplateDTO;
import com.logictrue.iot.entity.dto.ExcelTemplateSheetDTO;
import com.logictrue.iot.mapper.ExcelTemplateCellMapper;
import com.logictrue.iot.mapper.ExcelTemplateFieldMapper;
import com.logictrue.iot.mapper.ExcelTemplateMapper;
import com.logictrue.iot.service.IExcelTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel模板服务实现类
 *
 */
@Slf4j
@Service
public class ExcelTemplateServiceImpl extends ServiceImpl<ExcelTemplateMapper, ExcelTemplate>
        implements IExcelTemplateService {

    @Autowired
    private ExcelTemplateCellMapper cellMapper;

    @Autowired
    private ExcelTemplateFieldMapper fieldMapper;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ExcelTemplate saveTemplateDesign(ExcelTemplateDTO templateDTO) {
        try {
            ExcelTemplate template = new ExcelTemplate();
            BeanUtils.copyProperties(templateDTO, template);

            // 转换列宽度为JSON字符串
            if (!CollectionUtils.isEmpty(templateDTO.getColumnWidths())) {
                template.setColumnWidths(objectMapper.writeValueAsString(templateDTO.getColumnWidths()));
            }

            // 转换多Sheet配置为JSON字符串
            if (!CollectionUtils.isEmpty(templateDTO.getSheets())) {
                template.setSheetsConfig(objectMapper.writeValueAsString(templateDTO.getSheets()));
            }

            // 设置活动Sheet ID
            if (templateDTO.getActiveSheetId() != null) {
                template.setActiveSheetId(templateDTO.getActiveSheetId());
            }

            // 保存或更新模板主表
            boolean result;
            if (template.getId() != null) {
                result = updateById(template);
                // 删除原有的单元格和字段配置
                cellMapper.deleteByTemplateId(template.getId());
                fieldMapper.deleteByTemplateId(template.getId());
            } else {
                template.setStatus(1); // 默认启用
                result = save(template);
            }

            if (!result) {
                return null;
            }

            // 保存单元格配置
            if (!CollectionUtils.isEmpty(templateDTO.getCells())) {
                List<ExcelTemplateCell> cells = new ArrayList<>();
                for (ExcelTemplateDTO.ExcelTemplateCellDTO cellDTO : templateDTO.getCells()) {
                    ExcelTemplateCell cell = new ExcelTemplateCell();
                    BeanUtils.copyProperties(cellDTO, cell);
                    cell.setTemplateId(template.getId());
                    cells.add(cell);
                }
                cellMapper.batchInsert(cells);
            }

            // 保存字段配置
            if (!CollectionUtils.isEmpty(templateDTO.getFields())) {
                List<ExcelTemplateField> fields = new ArrayList<>();
                for (ExcelTemplateDTO.ExcelTemplateFieldDTO fieldDTO : templateDTO.getFields()) {
                    ExcelTemplateField field = new ExcelTemplateField();
                    BeanUtils.copyProperties(fieldDTO, field);
                    field.setTemplateId(template.getId());
                    fields.add(field);
                }
                fieldMapper.batchInsert(fields);
            }

            return template;
        } catch (Exception e) {
            log.error("保存Excel模板设计失败", e);
            throw new RuntimeException("保存Excel模板设计失败: " + e.getMessage());
        }
    }

    @Override
    public ExcelTemplate getTemplateWithDetails(Long templateId) {
        ExcelTemplate template = getById(templateId);
        if (template != null) {
            // 查询单元格配置
            List<ExcelTemplateCell> cells = cellMapper.selectByTemplateId(templateId);
            template.setCells(cells);

            // 查询字段配置
            List<ExcelTemplateField> fields = fieldMapper.selectByTemplateId(templateId);
            template.setFields(fields);

            // 解析多Sheet配置
            if (template.getSheetsConfig() != null && !template.getSheetsConfig().trim().isEmpty()) {
                try {
                    List<ExcelTemplateSheetDTO> sheetDTOs = objectMapper.readValue(
                        template.getSheetsConfig(),
                        objectMapper.getTypeFactory().constructCollectionType(List.class, ExcelTemplateSheetDTO.class)
                    );

                    // 转换为实体对象并按Sheet分组单元格和字段
                    List<ExcelTemplateSheet> sheets = new ArrayList<>();
                    for (ExcelTemplateSheetDTO sheetDTO : sheetDTOs) {
                        ExcelTemplateSheet sheet = new ExcelTemplateSheet();
                        sheet.setSheetId(sheetDTO.getSheetId());
                        sheet.setSheetName(sheetDTO.getSheetName());
                        sheet.setMaxColumns(sheetDTO.getMaxColumns());
                        sheet.setMaxRows(sheetDTO.getMaxRows());

                        // 设置列宽度
                        if (sheetDTO.getColumnWidths() != null) {
                            try {
                                sheet.setColumnWidths(objectMapper.writeValueAsString(sheetDTO.getColumnWidths()));
                            } catch (Exception e) {
                                log.warn("解析Sheet列宽度失败: {}", e.getMessage());
                            }
                        }

                        // 分组该Sheet的单元格
                        log.info("为Sheet {} 分组单元格，总单元格数: {}", sheetDTO.getSheetId(), cells.size());
                        List<ExcelTemplateCell> sheetCells = cells.stream()
                            .filter(cell -> {
                                boolean matches = sheetDTO.getSheetId().equals(cell.getSheetId());
                                if (matches) {
                                    log.info("匹配到单元格: {} [{}:{}] = {}", cell.getSheetId(), cell.getRowIndex(), cell.getColIndex(), cell.getContent());
                                }
                                return matches;
                            })
                            .collect(Collectors.toList());
                        log.info("Sheet {} 分组到 {} 个单元格", sheetDTO.getSheetId(), sheetCells.size());
                        sheet.setCells(sheetCells);

                        // 分组该Sheet的字段
                        List<ExcelTemplateField> sheetFields = fields.stream()
                            .filter(field -> sheetDTO.getSheetId().equals(field.getSheetId()))
                            .collect(Collectors.toList());
                        sheet.setFields(sheetFields);

                        sheets.add(sheet);
                    }

                    template.setSheets(sheets);
                } catch (Exception e) {
                    log.error("解析多Sheet配置失败: {}", e.getMessage(), e);
                }
            }
        }
        return template;
    }

    @Override
    public ExcelTemplate getByTemplateCode(String templateCode) {
        return baseMapper.selectByTemplateCode(templateCode);
    }

    @Override
    public List<ExcelTemplate> getByDeviceType(String deviceType) {
        return baseMapper.selectByDeviceType(deviceType);
    }

    @Override
    public IPage<ExcelTemplate> pageList(IPage<ExcelTemplate> page, ExcelTemplateDTO templateDTO) {
        LambdaQueryWrapper<ExcelTemplate> wrapper = new LambdaQueryWrapper<>();

        if (templateDTO != null) {
            wrapper.like(StringUtils.hasText(templateDTO.getTemplateName()),
                        ExcelTemplate::getTemplateName, templateDTO.getTemplateName())
                   .like(StringUtils.hasText(templateDTO.getTemplateCode()),
                        ExcelTemplate::getTemplateCode, templateDTO.getTemplateCode())
                   .eq(StringUtils.hasText(templateDTO.getDeviceType()),
                       ExcelTemplate::getDeviceType, templateDTO.getDeviceType())
                   .eq(templateDTO.getStatus() != null,
                       ExcelTemplate::getStatus, templateDTO.getStatus());
        }

        wrapper.orderByDesc(ExcelTemplate::getCreateTime);

        return page(page, wrapper);
    }

    @Override
    public void exportExcelTemplate(Long templateId, List<Map<String, Object>> data, HttpServletResponse response) {
        try {
            ExcelTemplate template = getTemplateWithDetails(templateId);
            if (template == null) {
                throw new RuntimeException("模板不存在");
            }

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();

            // 检查是否有多Sheet配置
            if (hasMultiSheetConfig(template)) {
                // 处理多Sheet导出
                exportMultiSheetTemplate(workbook, template, data);
            } else {
                // 兼容单Sheet导出
                exportSingleSheetTemplate(workbook, template, data);
            }

            // 设置响应头
            String fileName = URLEncoder.encode(template.getTemplateName() + ".xlsx", StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 输出文件
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出Excel模板失败", e);
            throw new RuntimeException("导出Excel模板失败: " + e.getMessage());
        }
    }

    @Override
    public void exportBlankTemplate(Long templateId, HttpServletResponse response) {
        try {
            ExcelTemplate template = getTemplateWithDetails(templateId);
            if (template == null) {
                throw new RuntimeException("模板不存在");
            }

            // 创建工作簿
            Workbook workbook = new XSSFWorkbook();

            // 检查是否有多Sheet配置
            if (hasMultiSheetConfig(template)) {
                // 处理多Sheet空白模板导出
                exportBlankMultiSheetTemplate(workbook, template);
            } else {
                // 兼容单Sheet空白模板导出
                exportBlankSingleSheetTemplate(workbook, template);
            }

            // 设置响应头
            String fileName = URLEncoder.encode(template.getTemplateName() + "_空白模板.xlsx", StandardCharsets.UTF_8.toString());
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

            // 输出文件
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            log.error("导出空白Excel模板失败", e);
            throw new RuntimeException("导出空白Excel模板失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteTemplate(Long templateId) {
        try {
            // 删除单元格配置
            cellMapper.deleteByTemplateId(templateId);
            // 删除字段配置
            fieldMapper.deleteByTemplateId(templateId);
            // 删除模板主表
            return removeById(templateId);
        } catch (Exception e) {
            log.error("删除模板失败", e);
            throw new RuntimeException("删除模板失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyTemplate(Long templateId, String newTemplateName, String newTemplateCode) {
        try {
            ExcelTemplate sourceTemplate = getTemplateWithDetails(templateId);
            if (sourceTemplate == null) {
                throw new RuntimeException("源模板不存在");
            }

            // 创建新模板
            ExcelTemplate newTemplate = new ExcelTemplate();
            BeanUtils.copyProperties(sourceTemplate, newTemplate);
            newTemplate.setId(null);
            newTemplate.setTemplateName(newTemplateName);
            newTemplate.setTemplateCode(newTemplateCode);

            if (!save(newTemplate)) {
                return false;
            }

            // 复制单元格配置
            if (!CollectionUtils.isEmpty(sourceTemplate.getCells())) {
                List<ExcelTemplateCell> newCells = new ArrayList<>();
                for (ExcelTemplateCell cell : sourceTemplate.getCells()) {
                    ExcelTemplateCell newCell = new ExcelTemplateCell();
                    BeanUtils.copyProperties(cell, newCell);
                    newCell.setId(null);
                    newCell.setTemplateId(newTemplate.getId());
                    newCells.add(newCell);
                }
                cellMapper.batchInsert(newCells);
            }

            // 复制字段配置
            if (!CollectionUtils.isEmpty(sourceTemplate.getFields())) {
                List<ExcelTemplateField> newFields = new ArrayList<>();
                for (ExcelTemplateField field : sourceTemplate.getFields()) {
                    ExcelTemplateField newField = new ExcelTemplateField();
                    BeanUtils.copyProperties(field, newField);
                    newField.setId(null);
                    newField.setTemplateId(newTemplate.getId());
                    newFields.add(newField);
                }
                fieldMapper.batchInsert(newFields);
            }

            return true;
        } catch (Exception e) {
            log.error("复制模板失败", e);
            throw new RuntimeException("复制模板失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isTemplateCodeUnique(String templateCode, Long excludeId) {
        LambdaQueryWrapper<ExcelTemplate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExcelTemplate::getTemplateCode, templateCode);
        if (excludeId != null) {
            wrapper.ne(ExcelTemplate::getId, excludeId);
        }
        return count(wrapper) == 0;
    }

    /**
     * 解析列宽度JSON字符串
     */
    private List<Integer> parseColumnWidths(String columnWidthsJson) {
        try {
            if (StringUtils.hasText(columnWidthsJson)) {
                return objectMapper.readValue(columnWidthsJson, new TypeReference<List<Integer>>() {});
            }
        } catch (Exception e) {
            log.warn("解析列宽度失败: {}", e.getMessage());
        }
        // 返回默认列宽（磅）
        List<Integer> defaultWidths = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            defaultWidths.add(72); // 72磅，约等于1英寸
        }
        return defaultWidths;
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建标签样式
     */
    private CellStyle createLabelStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建值样式
     */
    private CellStyle createValueStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.LAVENDER.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);

        Font font = workbook.createFont();
        font.setColor(IndexedColors.BLACK.getIndex());
        style.setFont(font);

        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 填充单元格数据
     */
    private void fillCellData(Sheet sheet, List<ExcelTemplateCell> cells, List<Map<String, Object>> data,
                             CellStyle headerStyle, CellStyle labelStyle, CellStyle valueStyle) {
        if (CollectionUtils.isEmpty(cells)) {
            return;
        }

        // 先处理合并单元格
        processMergedCells(sheet, cells);

        for (ExcelTemplateCell cellConfig : cells) {
            // 跳过被隐藏的单元格（被合并的单元格）
            if (Boolean.TRUE.equals(cellConfig.getHidden())) {
                continue;
            }
            Row row = sheet.getRow(cellConfig.getRowIndex());
            if (row == null) {
                row = sheet.createRow(cellConfig.getRowIndex());
            }

            Cell cell = row.getCell(cellConfig.getColIndex());
            if (cell == null) {
                cell = row.createCell(cellConfig.getColIndex());
            }

            // 设置单元格内容
            String content = cellConfig.getContent();
            if (!CollectionUtils.isEmpty(data) && "value".equals(cellConfig.getCellType())) {
                // 如果是值单元格且有数据，尝试从数据中获取值
                Object value = getValueFromData(data, cellConfig.getFieldCode());
                if (value != null) {
                    content = value.toString();
                }
            }

            cell.setCellValue(content);

            // 设置单元格样式
            switch (cellConfig.getCellType()) {
                case "header":
                    cell.setCellStyle(headerStyle);
                    break;
                case "label":
                    cell.setCellStyle(labelStyle);
                    break;
                case "value":
                    cell.setCellStyle(valueStyle);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 从数据中获取值
     */
    private Object getValueFromData(List<Map<String, Object>> data, String fieldCode) {
        if (CollectionUtils.isEmpty(data) || !StringUtils.hasText(fieldCode)) {
            return null;
        }

        // 取第一条数据（可以根据需要扩展为多行数据）
        Map<String, Object> firstRow = data.get(0);
        return firstRow.get(fieldCode);
    }

    /**
     * 填充空白单元格数据（只填充标签和表头，不填充数据值）
     */
    private void fillBlankCellData(Sheet sheet, List<ExcelTemplateCell> cells,
                                  CellStyle headerStyle, CellStyle labelStyle) {
        if (CollectionUtils.isEmpty(cells)) {
            return;
        }

        // 先处理合并单元格
        processMergedCells(sheet, cells);

        for (ExcelTemplateCell cellConfig : cells) {
            // 跳过被隐藏的单元格（被合并的单元格）
            if (Boolean.TRUE.equals(cellConfig.getHidden())) {
                continue;
            }

            // 只处理标签和表头类型的单元格
            if (!"label".equals(cellConfig.getCellType()) && !"header".equals(cellConfig.getCellType())) {
                continue;
            }

            Row row = sheet.getRow(cellConfig.getRowIndex());
            if (row == null) {
                row = sheet.createRow(cellConfig.getRowIndex());
            }

            Cell cell = row.getCell(cellConfig.getColIndex());
            if (cell == null) {
                cell = row.createCell(cellConfig.getColIndex());
            }

            // 设置单元格内容
            cell.setCellValue(cellConfig.getContent());

            // 设置单元格样式
            if ("header".equals(cellConfig.getCellType())) {
                cell.setCellStyle(headerStyle);
            } else if ("label".equals(cellConfig.getCellType())) {
                cell.setCellStyle(labelStyle);
            }
        }
    }

    /**
     * 检查是否有多Sheet配置
     */
    private boolean hasMultiSheetConfig(ExcelTemplate template) {
        // 检查是否有sheets配置或者单元格中有sheetId信息
        if (template.getSheetsConfig() != null && !template.getSheetsConfig().trim().isEmpty()) {
            return true;
        }

        // 检查单元格是否有sheetId信息
        if (!CollectionUtils.isEmpty(template.getCells())) {
            for (ExcelTemplateCell cell : template.getCells()) {
                if (cell.getSheetId() != null && !cell.getSheetId().trim().isEmpty()) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 导出多Sheet模板
     */
    private void exportMultiSheetTemplate(Workbook workbook, ExcelTemplate template, List<Map<String, Object>> data) {
        // 按Sheet分组单元格
        Map<String, List<ExcelTemplateCell>> cellsBySheet = new HashMap<>();
        for (ExcelTemplateCell cell : template.getCells()) {
            if (cell.getSheetId() != null) {
                cellsBySheet.computeIfAbsent(cell.getSheetId(), k -> new ArrayList<>()).add(cell);
            }
        }

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle labelStyle = createLabelStyle(workbook);
        CellStyle valueStyle = createValueStyle(workbook);

        // 从sheetsConfig获取正确的sheet顺序
        List<String> sheetOrder = getSheetOrderFromConfig(template);

        // 按配置的顺序创建Sheet
        for (String sheetId : sheetOrder) {
            List<ExcelTemplateCell> sheetCells = cellsBySheet.get(sheetId);
            if (sheetCells == null || sheetCells.isEmpty()) {
                continue; // 跳过没有单元格数据的sheet
            }

            // 获取Sheet名称
            String sheetName = sheetCells.isEmpty() ? sheetId :
                (sheetCells.get(0).getSheetName() != null ? sheetCells.get(0).getSheetName() : sheetId);

            // 创建Sheet
            Sheet sheet = workbook.createSheet(sheetName);

            // 设置列宽（从sheetsConfig中解析或使用默认值）
            List<Integer> columnWidths = parseSheetColumnWidths(template, sheetId);
            for (int i = 0; i < columnWidths.size(); i++) {
                int poiWidth = convertPointsToPoiWidth(columnWidths.get(i));
                sheet.setColumnWidth(i, poiWidth);
            }

            // 填充单元格数据
            fillCellData(sheet, sheetCells, data, headerStyle, labelStyle, valueStyle);
        }
    }

    /**
     * 导出单Sheet模板（兼容旧版）
     */
    private void exportSingleSheetTemplate(Workbook workbook, ExcelTemplate template, List<Map<String, Object>> data) {
        Sheet sheet = workbook.createSheet(template.getTemplateName());

        // 解析列宽度
        List<Integer> columnWidths = parseColumnWidths(template.getColumnWidths());

        // 设置列宽（将磅转换为POI的单位）
        for (int i = 0; i < columnWidths.size(); i++) {
            int poiWidth = convertPointsToPoiWidth(columnWidths.get(i));
            sheet.setColumnWidth(i, poiWidth);
        }

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle labelStyle = createLabelStyle(workbook);
        CellStyle valueStyle = createValueStyle(workbook);

        // 填充单元格数据
        fillCellData(sheet, template.getCells(), data, headerStyle, labelStyle, valueStyle);
    }

    /**
     * 解析指定Sheet的列宽配置
     */
    private List<Integer> parseSheetColumnWidths(ExcelTemplate template, String sheetId) {
        try {
            if (template.getSheetsConfig() != null && !template.getSheetsConfig().trim().isEmpty()) {
                // 从sheetsConfig中解析
                // 使用注入的全局ObjectMapper
                JsonNode sheetsNode = objectMapper.readTree(template.getSheetsConfig());

                if (sheetsNode.isArray()) {
                    for (JsonNode sheetNode : sheetsNode) {
                        if (sheetId.equals(sheetNode.path("id").asText())) {
                            JsonNode columnWidthsNode = sheetNode.path("columnWidths");
                            if (columnWidthsNode.isArray()) {
                                List<Integer> widths = new ArrayList<>();
                                for (JsonNode widthNode : columnWidthsNode) {
                                    widths.add(widthNode.asInt(72));
                                }
                                return widths;
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析Sheet列宽配置失败，使用默认配置: " + e.getMessage());
        }

        // 返回默认列宽（磅）
        List<Integer> defaultWidths = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            defaultWidths.add(72); // 72磅，约等于1英寸
        }
        return defaultWidths;
    }

    /**
     * 将磅（points）转换为POI列宽单位
     *
     * @param points 磅值
     * @return POI列宽单位
     */
    private int convertPointsToPoiWidth(Integer points) {
        if (points == null || points <= 0) {
            return 2560; // 默认列宽，约等于10个字符宽度
        }

        // Excel列宽转换公式：
        // 1. Excel列宽以字符宽度为单位（基于默认字体Calibri 11pt）
        // 2. POI中1个字符宽度 = 256个单位
        // 3. 1磅 ≈ 0.0138889个字符宽度（72磅 = 1英寸，1英寸 ≈ 7.2个字符）
        // 4. 因此：POI单位 = 磅 * 0.0138889 * 256 ≈ 磅 * 3.56

        // 使用更精确的转换系数，考虑到实际显示效果
        double conversionFactor = 35.6; // 经过测试的最佳转换系数
        int poiWidth = (int) Math.round(points * conversionFactor);

        // 确保最小列宽（约1个字符宽度）
        return Math.max(256, poiWidth);
    }

    /**
     * 从模板配置中获取Sheet的正确顺序
     */
    private List<String> getSheetOrderFromConfig(ExcelTemplate template) {
        List<String> sheetOrder = new ArrayList<>();

        if (template.getSheetsConfig() != null && !template.getSheetsConfig().trim().isEmpty()) {
            try {
                List<ExcelTemplateSheetDTO> sheetDTOs = objectMapper.readValue(
                    template.getSheetsConfig(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ExcelTemplateSheetDTO.class)
                );

                // 按配置的顺序添加sheetId
                for (ExcelTemplateSheetDTO sheetDTO : sheetDTOs) {
                    sheetOrder.add(sheetDTO.getSheetId());
                }
            } catch (Exception e) {
                log.error("解析Sheet配置失败: {}", e.getMessage(), e);
                // 如果解析失败，回退到从单元格数据中获取sheet顺序
                return getSheetOrderFromCells(template);
            }
        } else {
            // 如果没有sheetsConfig，从单元格数据中获取sheet顺序
            return getSheetOrderFromCells(template);
        }

        return sheetOrder;
    }

    /**
     * 从单元格数据中获取Sheet顺序（回退方案）
     */
    private List<String> getSheetOrderFromCells(ExcelTemplate template) {
        return template.getCells().stream()
            .filter(cell -> cell.getSheetId() != null)
            .sorted((cell1, cell2) -> {
                int index1 = cell1.getSheetIndex() != null ? cell1.getSheetIndex() : 0;
                int index2 = cell2.getSheetIndex() != null ? cell2.getSheetIndex() : 0;
                return Integer.compare(index1, index2);
            })
            .map(ExcelTemplateCell::getSheetId)
            .distinct()
            .collect(Collectors.toList());
    }

    /**
     * 导出多Sheet空白模板
     */
    private void exportBlankMultiSheetTemplate(Workbook workbook, ExcelTemplate template) {
        // 按Sheet分组单元格
        Map<String, List<ExcelTemplateCell>> cellsBySheet = new HashMap<>();
        for (ExcelTemplateCell cell : template.getCells()) {
            if (cell.getSheetId() != null) {
                cellsBySheet.computeIfAbsent(cell.getSheetId(), k -> new ArrayList<>()).add(cell);
            }
        }

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle labelStyle = createLabelStyle(workbook);

        // 从sheetsConfig获取正确的sheet顺序
        List<String> sheetOrder = getSheetOrderFromConfig(template);

        // 按配置的顺序创建Sheet
        for (String sheetId : sheetOrder) {
            List<ExcelTemplateCell> sheetCells = cellsBySheet.get(sheetId);
            if (sheetCells == null || sheetCells.isEmpty()) {
                continue; // 跳过没有单元格数据的sheet
            }

            // 获取Sheet名称
            String sheetName = sheetCells.isEmpty() ? sheetId :
                (sheetCells.get(0).getSheetName() != null ? sheetCells.get(0).getSheetName() : sheetId);

            // 创建Sheet
            Sheet sheet = workbook.createSheet(sheetName);

            // 设置列宽
            List<Integer> columnWidths = parseSheetColumnWidths(template, sheetId);
            for (int i = 0; i < columnWidths.size(); i++) {
                int poiWidth = convertPointsToPoiWidth(columnWidths.get(i));
                sheet.setColumnWidth(i, poiWidth);
            }

            // 只填充标签和表头，不填充数据
            fillBlankCellData(sheet, sheetCells, headerStyle, labelStyle);
        }
    }

    /**
     * 导出单Sheet空白模板（兼容旧版）
     */
    private void exportBlankSingleSheetTemplate(Workbook workbook, ExcelTemplate template) {
        Sheet sheet = workbook.createSheet(template.getTemplateName());

        // 解析列宽度
        List<Integer> columnWidths = parseColumnWidths(template.getColumnWidths());

        // 设置列宽
        for (int i = 0; i < columnWidths.size(); i++) {
            int poiWidth = convertPointsToPoiWidth(columnWidths.get(i));
            sheet.setColumnWidth(i, poiWidth);
        }

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle labelStyle = createLabelStyle(workbook);

        // 只填充标签和表头，不填充数据
        fillBlankCellData(sheet, template.getCells(), headerStyle, labelStyle);
    }

    /**
     * 处理合并单元格
     */
    private void processMergedCells(Sheet sheet, List<ExcelTemplateCell> cells) {
        if (CollectionUtils.isEmpty(cells)) {
            return;
        }

        // 找出所有需要合并的主单元格
        List<ExcelTemplateCell> mergedMainCells = cells.stream()
            .filter(cell -> Boolean.TRUE.equals(cell.getMerged()) && Boolean.TRUE.equals(cell.getMergeMain()))
            .collect(Collectors.toList());

        for (ExcelTemplateCell mainCell : mergedMainCells) {
            try {
                // 计算合并区域
                int firstRow = mainCell.getRowIndex();
                int lastRow = firstRow + (mainCell.getMergeRowSpan() != null ? mainCell.getMergeRowSpan() - 1 : 0);
                int firstCol = mainCell.getColIndex();
                int lastCol = firstCol + (mainCell.getMergeColSpan() != null ? mainCell.getMergeColSpan() - 1 : 0);

                // 验证合并区域的有效性
                if (lastRow >= firstRow && lastCol >= firstCol &&
                    firstRow >= 0 && lastRow < 65536 && firstCol >= 0 && lastCol < 256) {

                    // 创建合并区域
                    CellRangeAddress mergeRegion = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
                    sheet.addMergedRegion(mergeRegion);

                    log.debug("合并单元格区域: {}:{} 到 {}:{}",
                        getColumnLabel(firstCol), firstRow + 1,
                        getColumnLabel(lastCol), lastRow + 1);
                } else {
                    log.warn("无效的合并区域: 行[{}-{}], 列[{}-{}]", firstRow, lastRow, firstCol, lastCol);
                }
            } catch (Exception e) {
                log.error("处理合并单元格失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 将列索引转换为Excel列标签（A, B, C, ..., AA, AB, ...）
     */
    private String getColumnLabel(int colIndex) {
        StringBuilder result = new StringBuilder();
        while (colIndex >= 0) {
            result.insert(0, (char) ('A' + colIndex % 26));
            colIndex = colIndex / 26 - 1;
        }
        return result.toString();
    }
}
