package com.logictrue.iot.service.impl;

import com.logictrue.iot.service.ISysFileService;
import com.logictrue.iot.utils.FileUploadUtils;
import com.logictrue.iot.utils.ImageUploadUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

/**
 * 本地文件存储
 *
 * <AUTHOR>
 */
@Primary
@Service
public class LocalSysFileServiceImpl implements ISysFileService
{
    /**
     * 资源映射路径 前缀
     */
    @Value("${file.prefix:/static}")
    public String localFilePrefix;

    /**
     * 域名或本机访问地址
     */
    @Value("${file.domain:127.0.0.1}")
    public String domain;

    /**
     * 上传文件存储在本地的根路径
     */
    @Value("${file.path:/home/<USER>")
    private String localFilePath;



    /**
     * 本地文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Override
    public String uploadFile(MultipartFile file, Boolean hasAbb) throws Exception
    {
        String name = FileUploadUtils.upload(localFilePath, file, hasAbb);
        return domain + localFilePrefix + name;
    }

    @Override
    public String userDefinedPathUpload(MultipartFile file,String path) throws Exception {
        String name = ImageUploadUtils.pathUpload(localFilePath,file,path);
        return domain + localFilePrefix + name;
    }
}
