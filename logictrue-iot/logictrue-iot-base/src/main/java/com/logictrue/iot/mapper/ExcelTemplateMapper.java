package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.ExcelTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Excel模板Mapper接口
 *
 */
@Mapper
public interface ExcelTemplateMapper extends BaseMapper<ExcelTemplate> {

    /**
     * 根据模板编码查询模板
     *
     * @param templateCode 模板编码
     * @return Excel模板
     */
    @Select("SELECT * FROM excel_template WHERE template_code = #{templateCode} AND status = 1")
    ExcelTemplate selectByTemplateCode(@Param("templateCode") String templateCode);

    /**
     * 查询模板列表（包含单元格和字段信息）
     *
     * @param templateId 模板ID
     * @return Excel模板
     */
    ExcelTemplate selectTemplateWithDetails(@Param("templateId") Long templateId);

    /**
     * 根据设备类型查询模板列表
     *
     * @param deviceType 设备类型
     * @return 模板列表
     */
    @Select("SELECT * FROM excel_template WHERE device_type = #{deviceType} AND status = 1 ORDER BY create_time DESC")
    List<ExcelTemplate> selectByDeviceType(@Param("deviceType") String deviceType);
}
