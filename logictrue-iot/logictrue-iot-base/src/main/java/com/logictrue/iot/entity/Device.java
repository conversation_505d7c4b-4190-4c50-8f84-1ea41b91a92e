package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 设备表
 */
@Data
@TableName(value = "drl_device")
public class Device {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备编码
     */
    @TableField(value = "device_code")
    private String deviceCode;

    /**
     * 设备名称
     */
    @TableField(value = "device_name")
    private String deviceName;

    /**
     * 设备状态
     */
    @TableField(value = "device_status")
    private Integer deviceStatus;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private Date createTime;

    /**
     * ip
     */
    @TableField(value = "ip")
    private String ip;

    /**
     * 路径
     */
    @TableField(value = "address")
    private String address;

    /**
     * pdf路径
     */
    @TableField(value = "pdf_address")
    private String pdfAddress;

    /**
     * 1excel 2pdfToExcel
     */
    @TableField(value = "collect_type")
    private Integer collectType;

    /**
     * 图片ID
     */
    @TableField(value = "file_id")
    private String fileId;

    /**
     * vnc跳转路径
     */
    @TableField(value = "vnc_ip")
    private String vncIp;

    /**
     * 最后上线时间
     */
    @TableField(value = "last_time")
    private Date lastTime;

    /**
     * 方法名
     */
    @TableField(value = "method_name")
    private String methodName;

    /**
     * 数采开启 0关闭1开启
     */
    @TableField(value = "save_status")
    private Integer saveStatus;

    /**
     * 备注
     */
    @TableField(value = "device_remark")
    private String deviceRemark;

    /**
     * 设备型号
     */
    @TableField(value = "device_model")
    private String deviceModel;

    /**
     * 责任人
     */
    @TableField(value = "device_duty")
    private String deviceDuty;

    /**
     * 设备类别
     */
    @TableField(value = "device_type")
    private String deviceType;

    /**
     * 设备有效期
     */
    @TableField(value = "device_date")
    private Date deviceDate;

    /**
     * 0无监控1有监控
     */
    @TableField(value = "is_camera")
    private Boolean isCamera;

    /**
     * 监控账号
     */
    @TableField(value = "camera_user")
    private String cameraUser;

    /**
     * 监控密码
     */
    @TableField(value = "camera_password")
    private String cameraPassword;

    /**
     * 监控IP
     */
    @TableField(value = "camera_ip")
    private String cameraIp;

    /**
     * 监控端口号
     */
    @TableField(value = "camera_port")
    private Integer cameraPort;

    /**
     * 解析方法
     */
    @TableField(value = "analysis_type")
    private String analysisType;


}
