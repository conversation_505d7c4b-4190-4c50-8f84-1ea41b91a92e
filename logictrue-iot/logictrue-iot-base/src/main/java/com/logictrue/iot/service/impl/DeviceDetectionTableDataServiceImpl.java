package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.DeviceDetectionTableData;
import com.logictrue.iot.mapper.DeviceDetectionTableDataMapper;
import com.logictrue.iot.service.IDeviceDetectionTableDataService;
import org.springframework.stereotype.Service;

/**
 * 设备检测数据表格数据Service实现类
 *
 */
@Service
public class DeviceDetectionTableDataServiceImpl extends ServiceImpl<DeviceDetectionTableDataMapper, DeviceDetectionTableData> 
        implements IDeviceDetectionTableDataService {

}
