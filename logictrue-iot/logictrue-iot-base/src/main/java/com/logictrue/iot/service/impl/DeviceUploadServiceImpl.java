package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.DeviceUpload;
import com.logictrue.iot.mapper.DeviceUploadMapper;
import com.logictrue.iot.service.IDeviceUploadService;
import org.springframework.stereotype.Service;

/**
 * 文件存储记录Service业务层处理
 *
 */
@Service
public class DeviceUploadServiceImpl extends ServiceImpl<DeviceUploadMapper, DeviceUpload> implements IDeviceUploadService {

}
