package com.logictrue.iot.utils;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

public class CellUtils {

    public static Object getCellValue(Cell cell){

        if (cell == null) {
            return "";
        }

        Object value = null;

        CellType cellType = cell.getCellType();

        switch (cellType) {
            case NUMERIC:
                value = cell.getNumericCellValue();
                break;
            case STRING:
                value = cell.getStringCellValue();
                break;
            case BLANK:
                value = "";
                break;
            default:
                value = "";
        }


        return value;
    }


    public static String getValue(Cell cell){
        String cellValue = "";

        if (cell != null) {
            switch (cell.getCellType()) {
                case _NONE:
                    break;
                case NUMERIC:
                    cellValue = cell.getNumericCellValue() + "";
                    if(cellValue.endsWith(".0")) {
                        cellValue = cellValue.replaceAll(".0", "");
                    }
                    break;
                case  STRING:
                    cellValue = cell.getStringCellValue();
                    break;
                case FORMULA:
                    break;
                case BLANK:
                    break;
                case BOOLEAN:
                    break;
                case ERROR:
                    break;
            }

        }
        return cellValue;
    }
}
