package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备导入日志实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_import_log")
public class DeviceImportLog {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 任务ID */
    @TableField("task_id")
    private String taskId;

    /** 导入文件名 */
    @TableField("file_name")
    private String fileName;

    /** 导入文件路径 */
    @TableField("file_path")
    private String filePath;

    /** 导入文件大小（字节） */
    @TableField("file_size")
    private Long fileSize;

    /** 导入状态：0-进行中，1-成功，2-失败，3-部分成功 */
    @TableField("import_status")
    private Integer importStatus;

    /** 导入进度百分比（0-100） */
    @TableField("progress")
    private Integer progress;

    /** 当前处理步骤 */
    @TableField("current_step")
    private String currentStep;

    /** 导入的设备数量 */
    @TableField("imported_device_count")
    private Integer importedDeviceCount;

    /** 跳过的设备数量 */
    @TableField("skipped_device_count")
    private Integer skippedDeviceCount;

    /** 失败的设备数量 */
    @TableField("failed_device_count")
    private Integer failedDeviceCount;

    /** 导入的检测数据记录数 */
    @TableField("imported_detection_data_count")
    private Long importedDetectionDataCount;

    /** 导入的原始文件数量 */
    @TableField("imported_raw_file_count")
    private Integer importedRawFileCount;

    /** 导入模式：REPLACE-完全替换，MERGE-合并导入 */
    @TableField("import_mode")
    private String importMode;

    /** 冲突处理策略：SKIP-跳过，OVERRIDE-覆盖，ERROR-报错 */
    @TableField("conflict_strategy")
    private String conflictStrategy;

    /** 是否覆盖现有设备配置 */
    @TableField("override_device_config")
    private Boolean overrideDeviceConfig;

    /** 是否覆盖现有模板绑定 */
    @TableField("override_template_binding")
    private Boolean overrideTemplateBinding;

    /** 是否覆盖现有检测数据 */
    @TableField("override_detection_data")
    private Boolean overrideDetectionData;

    /** 成功导入的设备编码列表（JSON格式） */
    @TableField("success_device_codes")
    private String successDeviceCodes;

    /** 失败的设备编码列表（JSON格式） */
    @TableField("failed_device_codes")
    private String failedDeviceCodes;

    /** 错误信息 */
    @TableField("error_message")
    private String errorMessage;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 完成时间 */
    @TableField("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /** 更新人 */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
