package com.logictrue.iot.entity.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备数据导出请求DTO
 *
 */
@Data
@ApiModel(description = "设备数据导出请求")
public class DeviceExportRequest {

    @ApiModelProperty(value = "设备编码列表，或者'ALL_DEVICES'表示全部设备", required = true)
    private Object deviceCodes; // 可以是List<String>或者String("ALL_DEVICES")

    @ApiModelProperty(value = "时间范围类型：FULL_YEAR-全年，HALF_YEAR-半年，QUARTER-季度，SINGLE_MONTH-单月，ALL_DATA-全部数据，CUSTOM-自定义", required = true)
    @NotNull(message = "时间范围类型不能为空")
    private TimeRangeType timeRangeType;

    @ApiModelProperty(value = "开始时间（自定义时间范围时必填）")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间（自定义时间范围时必填）")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "是否包含原始文件", example = "true")
    private Boolean includeRawFiles = true;

    @ApiModelProperty(value = "是否包含解析数据", example = "true")
    private Boolean includeParsedData = true;

    @ApiModelProperty(value = "是否包含设备模板信息", example = "true")
    private Boolean includeTemplateInfo = true;

    @ApiModelProperty(value = "导出备注")
    private String remark;

    /**
     * 获取实际的设备编码列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getActualDeviceCodes() {
        if (deviceCodes instanceof String && "ALL_DEVICES".equals(deviceCodes)) {
            return null; // 返回null表示全部设备
        } else if (deviceCodes instanceof List) {
            return (List<String>) deviceCodes;
        } else {
            return new ArrayList<>();
        }
    }

    /**
     * 是否选择全部设备
     */
    public boolean isAllDevices() {
        return deviceCodes instanceof String && "ALL_DEVICES".equals(deviceCodes);
    }

    /**
     * 时间范围类型枚举
     */
    public enum TimeRangeType {
        FULL_YEAR("全年", 12),
        HALF_YEAR("半年", 6),
        QUARTER("季度", 3),
        SINGLE_MONTH("单月", 1),
        ALL_DATA("全部数据", -1),
        CUSTOM("自定义", 0);

        private final String description;
        private final int months;

        TimeRangeType(String description, int months) {
            this.description = description;
            this.months = months;
        }

        public String getDescription() {
            return description;
        }

        public int getMonths() {
            return months;
        }
    }

    /**
     * 获取实际的开始时间
     */
    public LocalDateTime getActualStartTime() {
        if (timeRangeType == TimeRangeType.CUSTOM) {
            return startTime;
        } else if (timeRangeType == TimeRangeType.ALL_DATA) {
            return null; // 表示不限制开始时间
        } else {
            LocalDateTime now = LocalDateTime.now();
            return now.minusMonths(timeRangeType.getMonths());
        }
    }

    /**
     * 获取实际的结束时间
     */
    public LocalDateTime getActualEndTime() {
        if (timeRangeType == TimeRangeType.CUSTOM) {
            return endTime;
        } else if (timeRangeType == TimeRangeType.ALL_DATA) {
            return null; // 表示不限制结束时间
        } else {
            return LocalDateTime.now();
        }
    }
}
