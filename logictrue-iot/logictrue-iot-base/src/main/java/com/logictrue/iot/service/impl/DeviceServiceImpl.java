package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.Device;
import com.logictrue.iot.mapper.DeviceMapper;
import com.logictrue.iot.service.IDeviceService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数采设备Service业务层处理
 */
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements IDeviceService {

    @Override
    public Device getByDeviceCode(String deviceCode) {
        if (!StringUtils.hasText(deviceCode)) {
            return null;
        }

        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Device::getDeviceCode, deviceCode);
        return getOne(wrapper);
    }

    @Override
    public List<String> getAllDeviceCodes() {
        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(Device::getDeviceCode);
        wrapper.orderBy(true, true, Device::getDeviceCode);

        List<Device> devices = list(wrapper);
        return devices.stream()
                .map(Device::getDeviceCode)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByDeviceCode(String deviceCode) {
        if (!StringUtils.hasText(deviceCode)) {
            return false;
        }

        LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Device::getDeviceCode, deviceCode);
        return count(wrapper) > 0;
    }
}
