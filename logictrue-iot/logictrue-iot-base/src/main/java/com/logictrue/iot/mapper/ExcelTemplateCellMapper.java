package com.logictrue.iot.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.ExcelTemplateCell;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * Excel单元格配置Mapper接口
 *
 */
@Mapper
public interface ExcelTemplateCellMapper extends BaseMapper<ExcelTemplateCell> {

    /**
     * 根据模板ID查询单元格配置
     *
     * @param templateId 模板ID
     * @return 单元格配置列表
     */
    @Select("SELECT * FROM excel_template_cell WHERE template_id = #{templateId} ORDER BY sheet_index, row_index, col_index")
    List<ExcelTemplateCell> selectByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据模板ID删除单元格配置
     *
     * @param templateId 模板ID
     * @return 删除数量
     */
    @Delete("DELETE FROM excel_template_cell WHERE template_id = #{templateId}")
    int deleteByTemplateId(@Param("templateId") Long templateId);

    /**
     * 根据位置查询单元格
     *
     * @param templateId 模板ID
     * @param rowIndex 行索引
     * @param colIndex 列索引
     * @return 单元格配置
     */
    @Select("SELECT * FROM excel_template_cell WHERE template_id = #{templateId} AND row_index = #{rowIndex} AND col_index = #{colIndex}")
    ExcelTemplateCell selectByPosition(@Param("templateId") Long templateId,
                                     @Param("rowIndex") Integer rowIndex,
                                     @Param("colIndex") Integer colIndex);

    /**
     * 批量插入单元格配置
     *
     * @param cells 单元格配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("cells") List<ExcelTemplateCell> cells);
}
