package com.logictrue.iot.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.service.IExcelTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Excel模板JSON下载控制器
 * 专门为JavaFX客户端提供JSON格式的模板配置下载服务
 */
@Api(tags = "Excel模板JSON下载")
@Slf4j
@RestController
@RequestMapping({"/templateDownload", "/td"})
public class ExcelTemplateDownloadController {

    @Autowired
    private IExcelTemplateService excelTemplateService;

    /**
     * 根据模板ID下载JSON格式的模板配置文件
     */
    @ApiOperation("根据模板ID下载JSON格式的模板配置文件")
    @GetMapping("/templateId/{templateId}")
    public ResponseEntity<byte[]> downloadTemplateFileById(
            @ApiParam("模板ID") @PathVariable Long templateId) {
        try {
            log.info("开始下载模板配置文件，模板ID: {}", templateId);

            ExcelTemplate template = excelTemplateService.getTemplateWithDetails(templateId);
            if (template == null) {
                log.warn("模板不存在，模板ID: {}", templateId);
                return ResponseEntity.notFound().build();
            }

            // 构建JSON配置内容
            String jsonContent = buildTemplateJsonContent(template);

            // 设置文件名
            String fileName = template.getTemplateCode() + "_template.json";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            log.info("模板配置文件下载成功，模板ID: {}, 文件名: {}", templateId, fileName);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(jsonContent.getBytes("UTF-8"));

        } catch (Exception e) {
            log.error("下载模板配置文件失败，模板ID: {}", templateId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }


    /**
     * 根据模板编码下载JSON格式的模板配置文件
     */
    @ApiOperation("根据模板编码下载JSON格式的模板配置文件")
    @GetMapping("/templateCode/{templateCode}")
    public ResponseEntity<byte[]> downloadTemplateFileByCode(
            @ApiParam("模板编码") @PathVariable String templateCode) {
        try {
            log.info("开始下载模板配置文件，模板编码: {}", templateCode);

            ExcelTemplate template = excelTemplateService.getByTemplateCode(templateCode);
            if (template == null) {
                log.warn("模板不存在，模板编码: {}", templateCode);
                return ResponseEntity.notFound().build();
            }

            // 获取完整的模板详情
            template = excelTemplateService.getTemplateWithDetails(template.getId());

            // 构建JSON配置内容
            String jsonContent = buildTemplateJsonContent(template);

            // 设置文件名
            String fileName = template.getTemplateCode() + "_template.json";

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            log.info("模板配置文件下载成功，模板编码: {}, 文件名: {}", templateCode, fileName);

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(jsonContent.getBytes("UTF-8"));

        } catch (Exception e) {
            log.error("下载模板配置文件失败，模板编码: {}", templateCode, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 构建模板JSON文件内容
     */
    private String buildTemplateJsonContent(ExcelTemplate template) {
        Map<String, Object> jsonData = new HashMap<>();

        // 基本信息
        jsonData.put("templateId", template.getId());
        jsonData.put("templateName", template.getTemplateName());
        jsonData.put("templateCode", template.getTemplateCode());
        jsonData.put("deviceType", template.getDeviceType());
        jsonData.put("description", template.getDescription());
        jsonData.put("maxColumns", template.getMaxColumns());
        jsonData.put("maxRows", template.getMaxRows());
        jsonData.put("activeSheetId", template.getActiveSheetId());

        // 配置信息
        if (template.getColumnWidths() != null) {
            try {
                jsonData.put("columnWidths", JSON.parse(template.getColumnWidths()));
            } catch (Exception e) {
                log.warn("解析列宽配置失败: {}", e.getMessage());
                jsonData.put("columnWidths", null);
            }
        }

        // Sheet配置 - 核心配置信息，直接使用sheetsConfig字段
        if (template.getSheetsConfig() != null && !template.getSheetsConfig().trim().isEmpty()) {
            try {
                // 直接解析sheetsConfig字段为JSON对象
                Object sheetsConfigObj = JSON.parse(template.getSheetsConfig());
                jsonData.put("sheetsConfig", sheetsConfigObj);
            } catch (Exception e) {
                log.warn("解析Sheet配置失败: {}", e.getMessage());
                jsonData.put("sheetsConfig", null);
            }
        } else {
            jsonData.put("sheetsConfig", null);
        }

        // 时间戳信息
        jsonData.put("createTime", template.getCreateTime());
        jsonData.put("updateTime", template.getUpdateTime());

        // 转换为格式化的JSON字符串
        return JSON.toJSONString(jsonData, JSONWriter.Feature.PrettyFormat);
    }

}
