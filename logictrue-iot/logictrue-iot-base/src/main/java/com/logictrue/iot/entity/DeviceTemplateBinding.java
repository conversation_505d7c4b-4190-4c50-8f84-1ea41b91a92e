package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备模板绑定实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_template_binding")
public class DeviceTemplateBinding {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 设备编码 */
    @TableField("device_code")
    private String deviceCode;

    /** 模板ID */
    @TableField("template_id")
    private Long templateId;

    /** 模板名称（冗余字段，便于查询） */
    @TableField("template_name")
    private String templateName;

    /** 模板编码（冗余字段，便于查询） */
    @TableField("template_code")
    private String templateCode;

    /** 绑定状态：1-启用，0-禁用 */
    @TableField("bind_status")
    private Integer bindStatus;

    /** 创建人 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新人 */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 备注 */
    @TableField("remark")
    private String remark;
}
