package com.logictrue.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.entity.dto.DeviceDetectionDataDTO;
import com.logictrue.iot.entity.vo.DeviceDetectionDataVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 设备检测数据服务接口
 * 
 */
public interface IDeviceDetectionDataService extends IService<DeviceDetectionData> {

    /**
     * 分页查询设备检测数据列表
     */
    IPage<DeviceDetectionDataVO> selectDetectionDataPage(Page<DeviceDetectionDataVO> page, DeviceDetectionDataDTO query);

    /**
     * 根据ID查询检测数据详情
     */
    DeviceDetectionDataVO getDetectionDataDetail(Long id);

    /**
     * 上传并解析Excel文件
     */
    DeviceDetectionData uploadAndParseExcel(String deviceCode, MultipartFile file, String remark);

    /**
     * 重新解析Excel文件
     */
    boolean reparseExcel(Long id);

    /**
     * 删除检测数据
     */
    boolean deleteDetectionData(Long id);

    /**
     * 批量删除检测数据
     */
    boolean deleteBatchDetectionData(List<Long> ids);

    /**
     * 获取解析状态统计
     */
    DeviceDetectionDataVO.ParseStatusStatistics getParseStatusStatistics();

    /**
     * 根据设备编码获取最新的检测数据
     */
    DeviceDetectionDataVO getLatestDetectionDataByDevice(String deviceCode);

    /**
     * 导出检测数据
     */
    void exportDetectionData(Long id, String format);
}
