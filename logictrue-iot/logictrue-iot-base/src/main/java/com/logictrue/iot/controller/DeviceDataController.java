package com.logictrue.iot.controller;

import com.logictrue.common.core.domain.R;
import com.logictrue.iot.entity.DeviceDetectionData;
import com.logictrue.iot.service.IDeviceDetectionDataService;
import com.logictrue.iot.service.IDeviceDetectionParseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CompletableFuture;

/**
 * 设备数据推送Controller
 * 接收外部系统推送的设备数据和文件
 */
@Slf4j
@RestController
@RequestMapping("/data")
@Api(value = "设备数据推送管理", tags = "设备数据推送管理")
@Validated
public class DeviceDataController {

    @Autowired
    private IDeviceDetectionDataService detectionDataService;

    @Autowired
    private IDeviceDetectionParseService detectionParseService;


    /**
     * 文件上传根目录
     */
    @Value("${file.path:/home/<USER>/data}")
    private String uploadPath;



    /**
     * 接收设备数据推送
     *
     * @param deviceCode 设备编码
     * @param collectData 动态表单JSON数据
     * @param file 采集的原始文件
     * @return 处理结果
     */
    @PostMapping("/push")
    @ApiOperation(value = "接收设备数据推送")
    public R<String> pushDeviceData(
            @ApiParam(value = "设备编码", required = true) @RequestParam String deviceCode,
            @ApiParam(value = "模板ID", required = true) @RequestParam Long templateId,
            @ApiParam(value = "动态表单JSON数据", required = true) @RequestParam String collectData,
            @ApiParam(value = "采集的原始文件", required = true) @RequestParam("file") MultipartFile file) {

        try {
            log.info("接收到设备数据推送请求，设备编码: {}, 文件名: {}, 文件大小: {} bytes",
                    deviceCode, file.getOriginalFilename(), file.getSize());

            // 验证参数
            if (deviceCode == null || deviceCode.trim().isEmpty()) {
                return R.fail("设备编码不能为空");
            }

            if (templateId == null) {
                return R.fail("模板ID不能为空");
            }

            if (collectData == null || collectData.trim().isEmpty()) {
                return R.fail("动态表单数据不能为空");
            }

            if (file == null || file.isEmpty()) {
                return R.fail("文件不能为空");
            }


            // 保存文件
            String savedFilePath = saveUploadedFile(file, deviceCode);
            if (savedFilePath == null) {
                return R.fail("文件保存失败");
            }

            // 创建DeviceDetectionData记录
            DeviceDetectionData detectionData = new DeviceDetectionData();
            detectionData.setDeviceCode(deviceCode);
            detectionData.setTemplateId(templateId);
            detectionData.setFileName(file.getOriginalFilename());
            detectionData.setFilePath(savedFilePath);
            detectionData.setCollectData(collectData);
            detectionData.setCollectPath(savedFilePath);
            detectionData.setParseStatus(0); // 0-待解析
            detectionData.setCreateTime(LocalDateTime.now());
            detectionData.setUpdateTime(LocalDateTime.now());

            // 5. 保存到数据库
            boolean saveSuccess = detectionDataService.save(detectionData);
            if (!saveSuccess) {
                log.error("保存设备检测数据失败，设备编码: {}", deviceCode);
                return R.fail("保存数据失败");
            }

            log.info("成功保存设备检测数据，ID: {}, 设备编码: {}", detectionData.getId(), deviceCode);

            // 6. 异步解析文件
            CompletableFuture.runAsync(() -> {
                detectionParseService.parseExcelFileAsync(detectionData.getId());
            });

            return R.ok("数据接收成功，正在后台解析文件");

        } catch (Exception e) {
            log.error("处理设备数据推送失败，设备编码: {}", deviceCode, e);
            return R.fail("处理失败: " + e.getMessage());
        }
    }

    /**
     * 保存上传的文件
     *
     * @param file 上传的文件
     * @param deviceCode 设备编码
     * @return 保存的文件路径，失败返回null
     */
    private String saveUploadedFile(MultipartFile file, String deviceCode) {
        try {
            // 创建上传目录
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String deviceDir = uploadPath + File.separator + "data" + File.separator + deviceCode + File.separator + dateStr;
            Path uploadDir = Paths.get(deviceDir);

            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            // 生成文件名（避免重复）
            String originalFilename = file.getOriginalFilename();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
            String fileName = timestamp + "_" + originalFilename;

            // 保存文件
            Path filePath = uploadDir.resolve(fileName);
            Files.copy(file.getInputStream(), filePath);

            String savedPath = filePath.toString();
            log.info("文件保存成功: {}", savedPath);
            return savedPath;

        } catch (IOException e) {
            log.error("保存文件失败，设备编码: {}, 文件名: {}", deviceCode, file.getOriginalFilename(), e);
            return null;
        }
    }


}
