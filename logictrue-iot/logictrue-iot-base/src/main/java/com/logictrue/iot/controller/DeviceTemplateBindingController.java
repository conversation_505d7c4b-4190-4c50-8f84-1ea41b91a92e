package com.logictrue.iot.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.iot.entity.dto.DeviceTemplateBindingDTO;
import com.logictrue.iot.entity.vo.DeviceTemplateBindingVO;
import com.logictrue.iot.service.IDeviceTemplateBindingService;
import com.logictrue.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 设备模板绑定Controller
 *
 */
@Slf4j
@RestController
@RequestMapping("/deviceTemplateBinding")
@Api(value = "设备模板绑定管理", tags = "设备模板绑定管理")
@Validated
public class DeviceTemplateBindingController {

    @Autowired
    private IDeviceTemplateBindingService bindingService;

    /**
     * 分页查询设备模板绑定列表
     */
    @GetMapping("/pageList")
    @ApiOperation(value = "分页查询设备模板绑定列表")
    public R<IPage<DeviceTemplateBindingVO>> pageList(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Long pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long pageSize,
            DeviceTemplateBindingDTO query) {
        try {
            Page<DeviceTemplateBindingVO> page = new Page<>(pageNum, pageSize);
            IPage<DeviceTemplateBindingVO> result = bindingService.selectBindingPage(page, query);
            return R.ok(result);
        } catch (Exception e) {
            log.error("分页查询设备模板绑定列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据设备编码查询绑定的模板ID
     */
    @GetMapping("/template-id/{deviceCode}")
    @ApiOperation(value = "根据设备编码查询绑定的模板ID")
    public R<Long> getTemplateIdByDeviceCode(@PathVariable String deviceCode) {
        try {
            Long templateId = bindingService.getTemplateIdByDeviceCode(deviceCode);
            return R.ok(templateId);
        } catch (Exception e) {
            log.error("查询设备绑定模板失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据模板ID查询绑定的设备编码列表
     */
    @GetMapping("/device-codes/{templateId}")
    @ApiOperation(value = "根据模板ID查询绑定的设备编码列表")
    public R<List<String>> getDeviceCodesByTemplateId(@PathVariable Long templateId) {
        try {
            List<String> deviceCodes = bindingService.getDeviceCodesByTemplateId(templateId);
            return R.ok(deviceCodes);
        } catch (Exception e) {
            log.error("查询模板绑定设备失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 创建设备模板绑定
     */
    @PostMapping
    @ApiOperation(value = "创建设备模板绑定")
    public R<String> createBinding(@Valid @RequestBody DeviceTemplateBindingDTO bindingDTO) {
        try {
            boolean success = bindingService.createBinding(bindingDTO);
            return success ? R.ok("绑定成功") : R.fail("绑定失败");
        } catch (Exception e) {
            log.error("创建设备模板绑定失败", e);
            return R.fail("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 更新设备模板绑定
     */
    @PutMapping
    @ApiOperation(value = "更新设备模板绑定")
    public R<String> updateBinding(@Valid @RequestBody DeviceTemplateBindingDTO bindingDTO) {
        try {
            boolean success = bindingService.updateBinding(bindingDTO);
            return success ? R.ok("更新成功") : R.fail("更新失败");
        } catch (Exception e) {
            log.error("更新设备模板绑定失败", e);
            return R.fail("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除设备模板绑定
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除设备模板绑定")
    public R<String> deleteBinding(@PathVariable Long id) {
        try {
            boolean success = bindingService.deleteBinding(id);
            return success ? R.ok("删除成功") : R.fail("删除失败");
        } catch (Exception e) {
            log.error("删除设备模板绑定失败", e);
            return R.fail("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除设备模板绑定
     */
    @DeleteMapping("/batch")
    @ApiOperation(value = "批量删除设备模板绑定")
    public R<String> deleteBatchBindings(@RequestBody List<Long> ids) {
        try {
            boolean success = bindingService.deleteBatchBindings(ids);
            return success ? R.ok("批量删除成功") : R.fail("批量删除失败");
        } catch (Exception e) {
            log.error("批量删除设备模板绑定失败", e);
            return R.fail("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 启用/禁用绑定
     */
    @PutMapping("/status/{id}")
    @ApiOperation(value = "启用/禁用绑定")
    public R<String> updateBindingStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            boolean success = bindingService.updateBindingStatus(id, status);
            return success ? R.ok("状态更新成功") : R.fail("状态更新失败");
        } catch (Exception e) {
            log.error("更新绑定状态失败", e);
            return R.fail("状态更新失败：" + e.getMessage());
        }
    }

    /**
     * 检查设备编码是否已绑定
     */
    @GetMapping("/check-device-code")
    @ApiOperation(value = "检查设备编码是否已绑定")
    public R<Boolean> checkDeviceCodeExists(@RequestParam String deviceCode,
                                           @RequestParam(required = false) Long excludeId) {
        try {
            boolean exists = excludeId != null ?
                bindingService.checkDeviceCodeExists(deviceCode, excludeId) :
                bindingService.checkDeviceCodeExists(deviceCode);
            return R.ok(exists);
        } catch (Exception e) {
            log.error("检查设备编码失败", e);
            return R.fail("检查失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有可用的设备编码列表（未绑定的设备）
     */
    @GetMapping("/availableDeviceCodes")
    @ApiOperation(value = "获取所有可用的设备编码列表")
    public R<List<String>> getAvailableDeviceCodes() {
        try {
            List<String> deviceCodes = bindingService.getAvailableDeviceCodes();
            return R.ok(deviceCodes);
        } catch (Exception e) {
            log.error("获取可用设备编码失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 快速绑定模板
     */
    @PostMapping("/bind")
    @ApiOperation(value = "快速绑定模板")
    public R<String> bindTemplate(@RequestParam String deviceCode,
                                 @RequestParam Long templateId,
                                 @RequestParam(required = false) String remark) {
        try {
            boolean success = bindingService.bindTemplate(deviceCode, templateId, remark);
            return success ? R.ok("绑定成功") : R.fail("绑定失败");
        } catch (Exception e) {
            log.error("快速绑定模板失败", e);
            return R.fail("绑定失败：" + e.getMessage());
        }
    }

    /**
     * 解绑设备模板
     */
    @DeleteMapping("/unbind/{deviceCode}")
    @ApiOperation(value = "解绑设备模板")
    public R<String> unbindTemplate(@PathVariable String deviceCode) {
        try {
            boolean success = bindingService.unbindTemplate(deviceCode);
            return success ? R.ok("解绑成功") : R.fail("解绑失败");
        } catch (Exception e) {
            log.error("解绑设备模板失败", e);
            return R.fail("解绑失败：" + e.getMessage());
        }
    }
}
