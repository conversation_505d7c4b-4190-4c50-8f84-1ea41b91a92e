package com.logictrue.iot.controller;

import com.logictrue.iot.service.IFileAttachInfoService;
import com.logictrue.iot.service.ISysFileService;
import com.logictrue.iot.service.UploadService;
import com.logictrue.iot.service.IDeviceService;
import com.logictrue.iot.service.IDeviceTemplateBindingService;
import com.logictrue.iot.service.IExcelTemplateService;
import com.logictrue.iot.service.IDeviceDetectionParseService;
import com.logictrue.common.core.domain.R;
import com.logictrue.common.core.text.UUID;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.common.core.utils.file.FileUtils;
import com.logictrue.iot.entity.FileAttachInfo;
import com.logictrue.iot.entity.Device;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.entity.DeviceDetectionData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
public class UploadController {

    @Autowired
    private ISysFileService sysFileService;

    @Autowired
    private IFileAttachInfoService iFileAttachInfoService;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDeviceTemplateBindingService deviceTemplateBindingService;

    @Autowired
    private IExcelTemplateService excelTemplateService;

    @Autowired
    private IDeviceDetectionParseService deviceDetectionParseService;
    /**
     * 文件上传请求
     */
    @RequestMapping("/upload")
    @ResponseBody
    public R<FileAttachInfo> upload(@RequestParam("file") MultipartFile file, @RequestParam(value = "fileId", required = false) String fileId) {
        try {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file, Boolean.FALSE);


            Double fileSize = file.getSize() / 1024D / 1024D;

            // 添加附件数据表
            FileAttachInfo build = FileAttachInfo.builder().fileId(StringUtils.isNotEmpty(fileId) ? fileId : UUID.randomUUID().toString().replaceAll("-", "")).uid(UUID.randomUUID().toString().replaceAll("-", "")).fileName(file.getOriginalFilename()).pathName(FileUtils.getRelativeUrl(url)).fileType(FileUtils.getFileType(url)).fileSize(fileSize).build();
            iFileAttachInfoService.save(build);

            ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attr.getRequest();
            String remoteAddr = request.getRemoteAddr();

            //
            uploadService.saveInfo(build, remoteAddr);

            return R.ok(build);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 基于设备编码的文件上传请求
     *
     * @param file 上传的文件
     * @param deviceCode 设备编码（必填）
     * @param remark 备注信息（可选）
     * @return 上传结果
     */
    @RequestMapping("/uploadWithDeviceCode")
    @ResponseBody
    public R<DeviceDetectionData> uploadWithDeviceCode(
            @RequestParam("file") MultipartFile file,
            @RequestParam("deviceCode") String deviceCode,
            @RequestParam(value = "remark", required = false) String remark) {

        try {
            // 1. 验证设备编码
            if (StringUtils.isEmpty(deviceCode)) {
                return R.fail("设备编码不能为空");
            }

            // 2. 验证设备是否存在
            Device device = deviceService.getByDeviceCode(deviceCode);
            if (device == null) {
                return R.fail("设备编码不存在: " + deviceCode);
            }

            // 3. 查询设备绑定的模板
            Long templateId = deviceTemplateBindingService.getTemplateIdByDeviceCode(deviceCode);
            if (templateId == null) {
                return R.fail("设备 " + deviceCode + " 未绑定Excel模板，请先绑定模板");
            }

            // 4. 获取模板信息
            ExcelTemplate template = excelTemplateService.getById(templateId);
            if (template == null) {
                return R.fail("模板不存在，ID: " + templateId);
            }

            // 5. 验证文件格式
            if (!deviceDetectionParseService.validateExcelFile(file)) {
                return R.fail("不支持的文件格式，仅支持 .xlsx 和 .xls 文件");
            }

            // 6. 解析文件并保存记录（包含文件存储到日期目录结构）
            DeviceDetectionData detectionData = deviceDetectionParseService.parseExcelFile(deviceCode, file, remark);

            log.info("设备 {} 文件上传成功: {}", deviceCode, file.getOriginalFilename());
            return R.ok(detectionData);

        } catch (Exception e) {
            log.error("设备编码文件上传失败，设备编码: {}, 文件: {}", deviceCode, file.getOriginalFilename(), e);
            return R.fail("文件上传失败: " + e.getMessage());
        }
    }


}
