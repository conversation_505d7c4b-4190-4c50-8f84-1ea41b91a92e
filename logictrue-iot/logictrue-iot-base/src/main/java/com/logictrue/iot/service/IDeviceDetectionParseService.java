package com.logictrue.iot.service;

import com.logictrue.iot.entity.DeviceDetectionData;
import org.springframework.web.multipart.MultipartFile;

/**
 * 设备检测数据解析服务接口
 * 
 */
public interface IDeviceDetectionParseService {

    /**
     * 解析Excel文件
     * 
     * @param deviceCode 设备编码
     * @param file Excel文件
     * @param remark 备注
     * @return 解析结果
     */
    DeviceDetectionData parseExcelFile(String deviceCode, MultipartFile file, String remark);

    /**
     * 重新解析Excel文件
     * 
     * @param detectionDataId 检测数据ID
     * @return 解析结果
     */
    DeviceDetectionData reparseExcelFile(Long detectionDataId);

    /**
     * 验证Excel文件格式
     * 
     * @param file Excel文件
     * @return 是否有效
     */
    boolean validateExcelFile(MultipartFile file);

    /**
     * 获取支持的文件类型
     *
     * @return 支持的文件扩展名数组
     */
    String[] getSupportedFileTypes();

    /**
     * 异步解析Excel文件
     *
     * @param detectionDataId 检测数据ID
     */
    void parseExcelFileAsync(Long detectionDataId);
}
