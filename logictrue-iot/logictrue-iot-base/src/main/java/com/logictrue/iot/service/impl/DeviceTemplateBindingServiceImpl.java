package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.DeviceTemplateBinding;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.entity.dto.DeviceTemplateBindingDTO;
import com.logictrue.iot.entity.vo.DeviceTemplateBindingVO;
import com.logictrue.iot.mapper.DeviceTemplateBindingMapper;
import com.logictrue.iot.service.IDeviceTemplateBindingService;
import com.logictrue.iot.service.IExcelTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备模板绑定Service实现类
 *
 */
@Slf4j
@Service
public class DeviceTemplateBindingServiceImpl extends ServiceImpl<DeviceTemplateBindingMapper, DeviceTemplateBinding>
        implements IDeviceTemplateBindingService {

    @Autowired
    private DeviceTemplateBindingMapper bindingMapper;

    @Autowired
    private IExcelTemplateService excelTemplateService;

    @Override
    public IPage<DeviceTemplateBindingVO> selectBindingPage(Page<DeviceTemplateBindingVO> page, DeviceTemplateBindingDTO query) {
        return bindingMapper.selectBindingPageWithDevice(page, query);
    }

    @Override
    public Long getTemplateIdByDeviceCode(String deviceCode) {
        DeviceTemplateBinding binding = getBindingByDeviceCode(deviceCode);
        return binding != null ? binding.getTemplateId() : null;
    }

    @Override
    public DeviceTemplateBinding getBindingByDeviceCode(String deviceCode) {
        return bindingMapper.selectByDeviceCode(deviceCode);
    }

    @Override
    public List<String> getDeviceCodesByTemplateId(Long templateId) {
        return bindingMapper.selectDeviceCodesByTemplateId(templateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createBinding(DeviceTemplateBindingDTO bindingDTO) {
        // 检查设备编码是否已绑定
        if (checkDeviceCodeExists(bindingDTO.getDeviceCode())) {
            throw new RuntimeException("设备编码已绑定模板，一个设备只能绑定一个模板");
        }

        // 获取模板信息
        ExcelTemplate template = excelTemplateService.getById(bindingDTO.getTemplateId());
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        DeviceTemplateBinding binding = new DeviceTemplateBinding();
        BeanUtils.copyProperties(bindingDTO, binding);

        // 设置模板信息
        binding.setTemplateName(template.getTemplateName());
        binding.setTemplateCode(template.getTemplateCode());
        binding.setBindStatus(1); // 默认启用

        return save(binding);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBinding(DeviceTemplateBindingDTO bindingDTO) {
        // 检查设备编码是否已被其他记录绑定
        if (checkDeviceCodeExists(bindingDTO.getDeviceCode(), bindingDTO.getId())) {
            throw new RuntimeException("设备编码已绑定模板，一个设备只能绑定一个模板");
        }

        // 获取模板信息
        ExcelTemplate template = excelTemplateService.getById(bindingDTO.getTemplateId());
        if (template == null) {
            throw new RuntimeException("模板不存在");
        }

        DeviceTemplateBinding binding = new DeviceTemplateBinding();
        BeanUtils.copyProperties(bindingDTO, binding);

        // 设置模板信息
        binding.setTemplateName(template.getTemplateName());
        binding.setTemplateCode(template.getTemplateCode());

        return updateById(binding);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBinding(Long id) {
        return removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchBindings(List<Long> ids) {
        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBindingStatus(Long id, Integer status) {
        DeviceTemplateBinding binding = new DeviceTemplateBinding();
        binding.setId(id);
        binding.setBindStatus(status);
        return updateById(binding);
    }

    @Override
    public boolean checkDeviceCodeExists(String deviceCode) {
        return bindingMapper.checkDeviceCodeExists(deviceCode) > 0;
    }

    @Override
    public boolean checkDeviceCodeExists(String deviceCode, Long excludeId) {
        LambdaQueryWrapper<DeviceTemplateBinding> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceTemplateBinding::getDeviceCode, deviceCode)
               .eq(DeviceTemplateBinding::getBindStatus, 1);
        if (excludeId != null) {
            wrapper.ne(DeviceTemplateBinding::getId, excludeId);
        }
        return count(wrapper) > 0;
    }

    @Override
    public List<String> getAvailableDeviceCodes() {
        return bindingMapper.selectAvailableDeviceCodes();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean bindTemplate(String deviceCode, Long templateId, String remark) {
        DeviceTemplateBindingDTO bindingDTO = new DeviceTemplateBindingDTO();
        bindingDTO.setDeviceCode(deviceCode);
        bindingDTO.setTemplateId(templateId);
        bindingDTO.setRemark(remark);
        return createBinding(bindingDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unbindTemplate(String deviceCode) {
        LambdaQueryWrapper<DeviceTemplateBinding> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceTemplateBinding::getDeviceCode, deviceCode);
        return remove(wrapper);
    }

    @Override
    public List<String> getBoundDeviceCodes() {
        LambdaQueryWrapper<DeviceTemplateBinding> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(DeviceTemplateBinding::getDeviceCode);
        wrapper.eq(DeviceTemplateBinding::getBindStatus, 1);

        List<DeviceTemplateBinding> bindings = list(wrapper);
        return bindings.stream()
                .map(DeviceTemplateBinding::getDeviceCode)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }
}
