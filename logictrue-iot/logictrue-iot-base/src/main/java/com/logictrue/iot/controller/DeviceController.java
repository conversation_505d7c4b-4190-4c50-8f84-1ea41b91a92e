package com.logictrue.iot.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.iot.entity.Device;
import com.logictrue.iot.service.IDeviceService;
import com.logictrue.iot.service.IDeviceTemplateBindingService;
import com.logictrue.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 设备Controller
 *
 */
@Slf4j
@RestController
@RequestMapping("/device")
@Api(value = "设备管理", tags = "设备管理")
public class DeviceController {

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IDeviceTemplateBindingService bindingService;

    /**
     * 分页查询设备列表
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页查询设备列表")
    public R<IPage<Device>> list(
            @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") Long pageNum,
            @ApiParam(value = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long pageSize,
            @ApiParam(value = "设备编码") @RequestParam(required = false) String deviceCode,
            @ApiParam(value = "设备名称") @RequestParam(required = false) String deviceName,
            @ApiParam(value = "设备类型") @RequestParam(required = false) String deviceType) {
        try {
            Page<Device> page = new Page<>(pageNum, pageSize);
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();

            if (StringUtils.hasText(deviceCode)) {
                wrapper.like(Device::getDeviceCode, deviceCode);
            }
            if (StringUtils.hasText(deviceName)) {
                wrapper.like(Device::getDeviceName, deviceName);
            }
            if (StringUtils.hasText(deviceType)) {
                wrapper.like(Device::getDeviceType, deviceType);
            }

            wrapper.orderByDesc(Device::getCreateTime);

            IPage<Device> result = deviceService.page(page, wrapper);
            return R.ok(result);
        } catch (Exception e) {
            log.error("查询设备列表失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据设备编码获取设备信息
     */
    @GetMapping("/code/{deviceCode}")
    @ApiOperation(value = "根据设备编码获取设备信息")
    public R<Device> getByCode(@PathVariable String deviceCode) {
        try {
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(Device::getDeviceCode, deviceCode);
            Device device = deviceService.getOne(wrapper);
            return R.ok(device);
        } catch (Exception e) {
            log.error("查询设备信息失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取设备选项列表（设备名称-设备编码格式）
     */
    @GetMapping("/options")
    @ApiOperation(value = "获取设备选项列表")
    public R<List<Map<String, String>>> getDeviceOptions() {
        try {
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(Device::getDeviceCode, Device::getDeviceName);
            wrapper.orderBy(true, true, Device::getDeviceCode);

            List<Device> devices = deviceService.list(wrapper);
            List<Map<String, String>> deviceOptions = devices.stream()
                    .filter(device -> StringUtils.hasText(device.getDeviceCode()))
                    .map(device -> {
                        Map<String, String> option = new HashMap<>();
                        String label = StringUtils.hasText(device.getDeviceName()) ?
                            device.getDeviceName() + "-" + device.getDeviceCode() :
                            device.getDeviceCode();
                        option.put("label", label);
                        option.put("value", device.getDeviceCode());
                        return option;
                    })
                    .collect(Collectors.toList());

            return R.ok(deviceOptions);
        } catch (Exception e) {
            log.error("获取设备选项列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取可用设备选项列表（未绑定的设备，设备名称-设备编码格式）
     */
    @GetMapping("/availableOptions")
    @ApiOperation(value = "获取可用设备选项列表")
    public R<List<Map<String, String>>> getAvailableDeviceOptions() {
        try {
            // 获取所有设备
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(Device::getDeviceCode, Device::getDeviceName);
            wrapper.orderBy(true, true, Device::getDeviceCode);
            List<Device> allDevices = deviceService.list(wrapper);

            // 获取已绑定的设备编码
            List<String> boundDeviceCodes = bindingService.getBoundDeviceCodes();

            // 过滤出未绑定的设备
            List<Map<String, String>> availableOptions = allDevices.stream()
                    .filter(device -> StringUtils.hasText(device.getDeviceCode()))
                    .filter(device -> !boundDeviceCodes.contains(device.getDeviceCode()))
                    .map(device -> {
                        Map<String, String> option = new HashMap<>();
                        String label = StringUtils.hasText(device.getDeviceName()) ?
                            device.getDeviceName() + "-" + device.getDeviceCode() :
                            device.getDeviceCode();
                        option.put("label", label);
                        option.put("value", device.getDeviceCode());
                        return option;
                    })
                    .collect(Collectors.toList());

            return R.ok(availableOptions);
        } catch (Exception e) {
            log.error("获取可用设备选项列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有设备编码列表
     */
    @GetMapping("/getAll")
    @ApiOperation(value = "获取全部设备列表")
    public R<List<Map<String, String>>> getAllDevice() {
        try {
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(Device::getDeviceCode, Device::getDeviceName);
            List<Device> allDevices = deviceService.list(wrapper);

            List<Map<String, String>> availableOptions = allDevices.stream()
                    .map(device -> {
                        Map<String, String> option = new HashMap<>();
                        String label = StringUtils.hasText(device.getDeviceName()) ?
                                device.getDeviceName() + "-" + device.getDeviceCode() :
                                device.getDeviceCode();
                        option.put("label", label);
                        option.put("value", device.getDeviceCode());
                        return option;
                    })
                    .collect(Collectors.toList());

            return R.ok(availableOptions);
        } catch (Exception e) {
            log.error("获取全部设备列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    @GetMapping("/bindOptions")
    @ApiOperation(value = "获取已绑定设备选项列表")
    public R<List<Map<String, String>>> getBindOptions() {
        try {
            // 获取已绑定的设备编码
            List<String> boundDeviceCodes = bindingService.getBoundDeviceCodes();
            // 获取已绑定设备
            LambdaQueryWrapper<Device> wrapper = new LambdaQueryWrapper<>();
            wrapper.select(Device::getDeviceCode, Device::getDeviceName);
            wrapper.in(Device::getDeviceCode, boundDeviceCodes);
            List<Device> allDevices = deviceService.list(wrapper);

            List<Map<String, String>> availableOptions = allDevices.stream()
                    .map(device -> {
                        Map<String, String> option = new HashMap<>();
                        String label = StringUtils.hasText(device.getDeviceName()) ?
                                device.getDeviceName() + "-" + device.getDeviceCode() :
                                device.getDeviceCode();
                        option.put("label", label);
                        option.put("value", device.getDeviceCode());
                        return option;
                    })
                    .collect(Collectors.toList());

            return R.ok(availableOptions);
        } catch (Exception e) {
            log.error("获取已绑定设备选项列表失败", e);
            return R.fail("获取失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID获取设备信息
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取设备信息")
    public R<Device> getById(@PathVariable Long id) {
        try {
            Device device = deviceService.getById(id);
            return R.ok(device);
        } catch (Exception e) {
            log.error("查询设备信息失败", e);
            return R.fail("查询失败：" + e.getMessage());
        }
    }
}
