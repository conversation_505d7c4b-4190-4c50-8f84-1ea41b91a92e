package com.logictrue.iot.entity.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备检测数据VO
 * 
 */
@Data
public class DeviceDetectionDataVO {

    /** 主键ID */
    private Long id;

    /** 设备编码 */
    private String deviceCode;

    /** 设备名称 */
    private String deviceName;

    /** 设备类型 */
    private String deviceType;

    /** 模板ID */
    private Long templateId;

    /** 模板名称 */
    private String templateName;

    /** 模板编码 */
    private String templateCode;

    /** 原始文件名 */
    private String fileName;

    /** 文件存储路径 */
    private String filePath;

    /** 文件大小（字节） */
    private Long fileSize;

    /** 文件大小（格式化） */
    private String fileSizeFormatted;

    /** 解析状态：0-待解析，1-解析成功，2-解析失败 */
    private Integer parseStatus;

    /** 解析状态文本 */
    private String parseStatusText;

    /** 解析结果消息 */
    private String parseMessage;

    /** 解析时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime parseTime;

    /** 总Sheet数量 */
    private Integer totalSheets;

    /** 已解析Sheet数量 */
    private Integer parsedSheets;

    /** 基础字段数量 */
    private Integer basicFieldsCount;

    /** 表格数据行数 */
    private Integer tableRowsCount;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新人 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 备注 */
    private String remark;

    // 详情页面使用的字段
    /** Sheet数据列表 */
    private List<SheetDataVO> sheetDataList;

    /**
     * Sheet数据VO
     */
    @Data
    public static class SheetDataVO {
        /** Sheet ID */
        private String sheetId;

        /** Sheet名称 */
        private String sheetName;

        /** Sheet索引 */
        private Integer sheetIndex;

        /** 基础字段列表 */
        private List<BasicFieldVO> basicFields;

        /** 表格表头列表 */
        private List<TableHeaderVO> tableHeaders;

        /** 表格数据列表 */
        private List<Map<String, Object>> tableDataList;
    }

    /**
     * 基础字段VO
     */
    @Data
    public static class BasicFieldVO {
        /** 字段编码 */
        private String fieldCode;

        /** 字段名称 */
        private String fieldName;

        /** 字段类型 */
        private String fieldType;

        /** 字段值 */
        private String fieldValue;

        /** 标签位置 */
        private String labelPosition;

        /** 值位置 */
        private String valuePosition;

        /** 排序序号 */
        private Integer sortOrder;
    }

    /**
     * 表格表头VO
     */
    @Data
    public static class TableHeaderVO {
        /** 表头编码 */
        private String headerCode;

        /** 表头名称 */
        private String headerName;

        /** 表头位置 */
        private String headerPosition;

        /** 数据类型 */
        private String dataType;

        /** 列顺序 */
        private Integer columnOrder;
    }

    /**
     * 解析状态统计VO
     */
    @Data
    public static class ParseStatusStatistics {
        /** 总数 */
        private Long total;

        /** 待解析数量 */
        private Long pending;

        /** 解析成功数量 */
        private Long success;

        /** 解析失败数量 */
        private Long failed;

        /** 成功率 */
        private Double successRate;
    }
}
