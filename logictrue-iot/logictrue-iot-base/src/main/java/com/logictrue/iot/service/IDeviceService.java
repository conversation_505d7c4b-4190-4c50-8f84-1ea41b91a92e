package com.logictrue.iot.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.logictrue.iot.entity.Device;

import java.util.List;

/**
 * 数采设备Service接口
 */
public interface IDeviceService extends IService<Device> {

    /**
     * 根据设备编码获取设备信息
     */
    Device getByDeviceCode(String deviceCode);

    /**
     * 获取所有设备编码列表
     */
    List<String> getAllDeviceCodes();

    /**
     * 检查设备编码是否存在
     */
    boolean existsByDeviceCode(String deviceCode);
}
