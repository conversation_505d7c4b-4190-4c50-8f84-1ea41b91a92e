package com.logictrue.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.logictrue.iot.entity.ExcelTemplate;
import com.logictrue.iot.entity.dto.ExcelTemplateDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Excel模板服务接口
 *
 */
public interface IExcelTemplateService extends IService<ExcelTemplate> {

    /**
     * 保存Excel模板设计
     *
     * @param templateDTO 模板数据
     * @return 保存的模板对象
     */
    ExcelTemplate saveTemplateDesign(ExcelTemplateDTO templateDTO);

    /**
     * 根据ID查询模板详情（包含单元格和字段信息）
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    ExcelTemplate getTemplateWithDetails(Long templateId);

    /**
     * 根据模板编码查询模板
     *
     * @param templateCode 模板编码
     * @return 模板信息
     */
    ExcelTemplate getByTemplateCode(String templateCode);

    /**
     * 根据设备类型查询模板列表
     *
     * @param deviceType 设备类型
     * @return 模板列表
     */
    List<ExcelTemplate> getByDeviceType(String deviceType);

    /**
     * 分页查询模板列表
     *
     * @param page 分页对象
     * @param templateDTO 查询条件
     * @return 分页结果
     */
    IPage<ExcelTemplate> pageList(IPage<ExcelTemplate> page, ExcelTemplateDTO templateDTO);

    /**
     * 导出Excel模板文件
     *
     * @param templateId 模板ID
     * @param data 填充数据
     * @param response HTTP响应
     */
    void exportExcelTemplate(Long templateId, List<Map<String, Object>> data, HttpServletResponse response);

    /**
     * 导出空白Excel模板
     *
     * @param templateId 模板ID
     * @param response HTTP响应
     */
    void exportBlankTemplate(Long templateId, HttpServletResponse response);

    /**
     * 删除模板（级联删除相关数据）
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    boolean deleteTemplate(Long templateId);

    /**
     * 复制模板
     *
     * @param templateId 源模板ID
     * @param newTemplateName 新模板名称
     * @param newTemplateCode 新模板编码
     * @return 复制结果
     */
    boolean copyTemplate(Long templateId, String newTemplateName, String newTemplateCode);

    /**
     * 验证模板编码是否唯一
     *
     * @param templateCode 模板编码
     * @param excludeId 排除的ID（用于更新时验证）
     * @return 是否唯一
     */
    boolean isTemplateCodeUnique(String templateCode, Long excludeId);
}
