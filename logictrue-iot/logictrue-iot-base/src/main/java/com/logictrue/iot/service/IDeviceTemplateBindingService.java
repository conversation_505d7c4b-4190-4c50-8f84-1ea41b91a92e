package com.logictrue.iot.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.logictrue.iot.entity.DeviceTemplateBinding;
import com.logictrue.iot.entity.dto.DeviceTemplateBindingDTO;
import com.logictrue.iot.entity.vo.DeviceTemplateBindingVO;

import java.util.List;

/**
 * 设备模板绑定Service接口
 *
 */
public interface IDeviceTemplateBindingService extends IService<DeviceTemplateBinding> {

    /**
     * 分页查询设备模板绑定列表
     */
    IPage<DeviceTemplateBindingVO> selectBindingPage(Page<DeviceTemplateBindingVO> page, DeviceTemplateBindingDTO query);

    /**
     * 根据设备编码查询绑定的模板ID
     */
    Long getTemplateIdByDeviceCode(String deviceCode);

    /**
     * 根据设备编码查询绑定信息
     */
    DeviceTemplateBinding getBindingByDeviceCode(String deviceCode);

    /**
     * 根据模板ID查询绑定的设备编码列表
     */
    List<String> getDeviceCodesByTemplateId(Long templateId);

    /**
     * 创建设备模板绑定
     */
    boolean createBinding(DeviceTemplateBindingDTO bindingDTO);

    /**
     * 更新设备模板绑定
     */
    boolean updateBinding(DeviceTemplateBindingDTO bindingDTO);

    /**
     * 删除设备模板绑定
     */
    boolean deleteBinding(Long id);

    /**
     * 批量删除设备模板绑定
     */
    boolean deleteBatchBindings(List<Long> ids);

    /**
     * 启用/禁用绑定
     */
    boolean updateBindingStatus(Long id, Integer status);

    /**
     * 检查设备编码是否已绑定
     */
    boolean checkDeviceCodeExists(String deviceCode);

    /**
     * 检查设备编码是否已绑定（排除指定ID）
     */
    boolean checkDeviceCodeExists(String deviceCode, Long excludeId);

    /**
     * 获取所有可用的设备编码列表（未绑定的设备）
     */
    List<String> getAvailableDeviceCodes();

    /**
     * 根据设备编码绑定模板
     */
    boolean bindTemplate(String deviceCode, Long templateId, String remark);

    /**
     * 解绑设备模板
     */
    boolean unbindTemplate(String deviceCode);

    /**
     * 获取所有已绑定的设备编码列表
     */
    List<String> getBoundDeviceCodes();
}
