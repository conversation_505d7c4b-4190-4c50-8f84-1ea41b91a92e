package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 设备导出日志实体类
 *
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("device_export_log")
public class DeviceExportLog {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 任务ID */
    @TableField("task_id")
    private String taskId;

    /** 设备编码列表（JSON格式） */
    @TableField("device_codes")
    private String deviceCodes;

    /** 时间范围开始 */
    @TableField("time_range_start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timeRangeStart;

    /** 时间范围结束 */
    @TableField("time_range_end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime timeRangeEnd;

    /** 导出状态：0-进行中，1-成功，2-失败 */
    @TableField("export_status")
    private Integer exportStatus;

    /** 导出进度百分比（0-100） */
    @TableField("progress")
    private Integer progress;

    /** 当前处理步骤 */
    @TableField("current_step")
    private String currentStep;

    /** 导出文件路径 */
    @TableField("file_path")
    private String filePath;

    /** 导出文件名 */
    @TableField("file_name")
    private String fileName;

    /** 导出文件大小（字节） */
    @TableField("file_size")
    private Long fileSize;

    /** 导出的设备数量 */
    @TableField("device_count")
    private Integer deviceCount;

    /** 导出的检测数据记录数 */
    @TableField("detection_data_count")
    private Long detectionDataCount;

    /** 导出的原始文件数量 */
    @TableField("raw_file_count")
    private Integer rawFileCount;

    /** 是否包含原始文件 */
    @TableField("include_raw_files")
    private Boolean includeRawFiles;

    /** 是否包含解析数据 */
    @TableField("include_parsed_data")
    private Boolean includeParsedData;

    /** 是否包含设备模板信息 */
    @TableField("include_template_info")
    private Boolean includeTemplateInfo;

    /** 错误信息 */
    @TableField("error_message")
    private String errorMessage;

    /** 备注 */
    @TableField("remark")
    private String remark;

    /** 创建人 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 完成时间 */
    @TableField("complete_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completeTime;

    /** 更新人 */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
