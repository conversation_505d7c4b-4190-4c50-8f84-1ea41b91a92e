package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.mapper.DeviceDataBackupRecordMapper;
import com.logictrue.iot.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 设备数据备份服务实现类
 *
 */
@Slf4j
@Service
public class DeviceDataBackupServiceImpl extends ServiceImpl<DeviceDataBackupRecordMapper, DeviceDataBackupRecord>
        implements IDeviceDataBackupService {

    @Value("${file.path:/home/<USER>")
    private String uploadPath;

    @Autowired
    private IDeviceDetectionDataService detectionDataService;

    @Autowired
    private IDeviceDetectionBasicFieldService basicFieldService;

    @Autowired
    private IDeviceDetectionTableHeaderService tableHeaderService;

    @Autowired
    private IDeviceDetectionTableDataService tableDataService;

    @Autowired
    private IDeviceDetectionParseLogService parseLogService;

    @Autowired
    private IDeviceService deviceService;

    @Autowired
    private IExcelTemplateService templateService;

    @Autowired
    private IDeviceTemplateBindingService bindingService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceDataBackupRecord backupDetectionData(DeviceDetectionData detectionData,
                                                     String backupBatchId,
                                                     String backupReason) {
        log.info("开始备份检测数据 - 设备: {}, 文件: {}, 批次ID: {}",
                 detectionData.getDeviceCode(), detectionData.getFileName(), backupBatchId);

        DeviceDataBackupRecord backupRecord = new DeviceDataBackupRecord();
        backupRecord.setBackupBatchId(backupBatchId);
        backupRecord.setDataType(DeviceDataBackupRecord.DataType.DETECTION_DATA.getCode());
        backupRecord.setOriginalDataId(detectionData.getId());
        backupRecord.setDeviceCode(detectionData.getDeviceCode());
        backupRecord.setIdentifier(detectionData.getFileName());
        backupRecord.setBackupReason(backupReason);
        backupRecord.setOriginalFilePath(detectionData.getFilePath());
        backupRecord.setBackupTime(LocalDateTime.now());

        try {
            // 1. 备份主表数据
            backupMainData(detectionData, backupBatchId, backupReason);

            // 2. 备份基础字段数据
            backupBasicFields(detectionData.getId(), backupBatchId);

            // 3. 备份表格表头数据
            backupTableHeaders(detectionData.getId(), backupBatchId);

            // 4. 备份表格数据
            backupTableData(detectionData.getId(), backupBatchId);

            // 5. 备份解析日志数据
            backupParseLogs(detectionData.getId(), backupBatchId);

            // 6. 备份原始文件
            String backupFilePath = null;
            if (StringUtils.hasText(detectionData.getFilePath())) {
                try {
                    backupFilePath = backupOriginalFile(detectionData.getFilePath(),
                                                      detectionData.getDeviceCode(),
                                                      detectionData.getFileName());
                    backupRecord.setBackupFilePath(backupFilePath);
                } catch (Exception e) {
                    log.warn("备份原始文件失败 - 设备: {}, 文件路径: {}",
                             detectionData.getDeviceCode(), detectionData.getFilePath(), e);
                    // 文件备份失败不影响数据备份
                }
            }

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.SUCCESS.getCode());
            backupRecord.setBackupMessage("备份成功");

            log.info("检测数据备份成功 - 设备: {}, 文件: {}, 备份文件: {}",
                     detectionData.getDeviceCode(), detectionData.getFileName(), backupFilePath);

        } catch (Exception e) {
            log.error("备份检测数据失败 - 设备: {}, 文件: {}",
                      detectionData.getDeviceCode(), detectionData.getFileName(), e);

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.FAILED.getCode());
            backupRecord.setBackupMessage("备份失败: " + e.getMessage());

            throw new RuntimeException("备份检测数据失败: " + e.getMessage(), e);
        }

        // 保存备份记录
        save(backupRecord);
        return backupRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<DeviceDataBackupRecord> batchBackupDetectionData(List<DeviceDetectionData> detectionDataList,
                                                                String backupBatchId,
                                                                String backupReason) {
        log.info("开始批量备份检测数据 - 数量: {}, 批次ID: {}", detectionDataList.size(), backupBatchId);

        List<DeviceDataBackupRecord> backupRecords = new ArrayList<>();

        for (DeviceDetectionData detectionData : detectionDataList) {
            try {
                DeviceDataBackupRecord record = backupDetectionData(detectionData, backupBatchId, backupReason);
                backupRecords.add(record);
            } catch (Exception e) {
                log.error("批量备份中单个数据备份失败 - 设备: {}, 文件: {}",
                          detectionData.getDeviceCode(), detectionData.getFileName(), e);
                // 继续备份其他数据，但记录失败信息
                DeviceDataBackupRecord failedRecord = new DeviceDataBackupRecord();
                failedRecord.setBackupBatchId(backupBatchId);
                failedRecord.setDataType(DeviceDataBackupRecord.DataType.DETECTION_DATA.getCode());
                failedRecord.setOriginalDataId(detectionData.getId());
                failedRecord.setDeviceCode(detectionData.getDeviceCode());
                failedRecord.setIdentifier(detectionData.getFileName());
                failedRecord.setBackupReason(backupReason);
                failedRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.FAILED.getCode());
                failedRecord.setBackupMessage("备份失败: " + e.getMessage());
                failedRecord.setBackupTime(LocalDateTime.now());
                save(failedRecord);
                backupRecords.add(failedRecord);
            }
        }

        log.info("批量备份检测数据完成 - 总数: {}, 成功: {}, 失败: {}",
                 detectionDataList.size(),
                 backupRecords.stream().mapToLong(r -> "SUCCESS".equals(r.getBackupStatus()) ? 1 : 0).sum(),
                 backupRecords.stream().mapToLong(r -> "FAILED".equals(r.getBackupStatus()) ? 1 : 0).sum());

        return backupRecords;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceDataBackupRecord backupDevice(Device device, String backupBatchId, String backupReason) {
        log.info("开始备份设备基础信息 - 设备编码: {}, 设备名称: {}, 批次ID: {}",
                 device.getDeviceCode(), device.getDeviceName(), backupBatchId);

        DeviceDataBackupRecord backupRecord = new DeviceDataBackupRecord();
        backupRecord.setBackupBatchId(backupBatchId);
        backupRecord.setDataType(DeviceDataBackupRecord.DataType.DEVICE.getCode());
        backupRecord.setOriginalDataId(device.getId());
        backupRecord.setDeviceCode(device.getDeviceCode());
        backupRecord.setIdentifier(device.getDeviceCode());
        backupRecord.setBackupReason(backupReason);
        backupRecord.setBackupTime(LocalDateTime.now());

        try {
            // 备份设备基础信息
            int count = baseMapper.backupDevice(backupReason, device.getId());

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.SUCCESS.getCode());
            backupRecord.setBackupMessage("设备信息备份成功，备份条数: " + count);

            log.info("设备基础信息备份成功 - 设备编码: {}, 备份条数: {}", device.getDeviceCode(), count);

        } catch (Exception e) {
            log.error("备份设备基础信息失败 - 设备编码: {}", device.getDeviceCode(), e);

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.FAILED.getCode());
            backupRecord.setBackupMessage("备份失败: " + e.getMessage());

            throw new RuntimeException("备份设备基础信息失败: " + e.getMessage(), e);
        }

        // 保存备份记录
        save(backupRecord);
        return backupRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceDataBackupRecord backupTemplate(ExcelTemplate template, String backupBatchId, String backupReason) {
        log.info("开始备份Excel模板信息 - 模板编码: {}, 模板名称: {}, 批次ID: {}",
                 template.getTemplateCode(), template.getTemplateName(), backupBatchId);

        DeviceDataBackupRecord backupRecord = new DeviceDataBackupRecord();
        backupRecord.setBackupBatchId(backupBatchId);
        backupRecord.setDataType(DeviceDataBackupRecord.DataType.TEMPLATE.getCode());
        backupRecord.setOriginalDataId(template.getId());
        backupRecord.setIdentifier(template.getTemplateCode());
        backupRecord.setBackupReason(backupReason);
        backupRecord.setBackupTime(LocalDateTime.now());

        try {
            // 备份模板基础信息
            int templateCount = baseMapper.backupTemplate(backupReason, template.getId());

            // 备份模板单元格配置
            int cellCount = baseMapper.backupTemplateCells(template.getId());

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.SUCCESS.getCode());
            backupRecord.setBackupMessage(String.format("模板信息备份成功，模板: %d条，单元格配置: %d条",
                                                       templateCount, cellCount));

            log.info("Excel模板信息备份成功 - 模板编码: {}, 模板备份: {}条, 单元格备份: {}条",
                     template.getTemplateCode(), templateCount, cellCount);

        } catch (Exception e) {
            log.error("备份Excel模板信息失败 - 模板编码: {}", template.getTemplateCode(), e);

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.FAILED.getCode());
            backupRecord.setBackupMessage("备份失败: " + e.getMessage());

            throw new RuntimeException("备份Excel模板信息失败: " + e.getMessage(), e);
        }

        // 保存备份记录
        save(backupRecord);
        return backupRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DeviceDataBackupRecord backupTemplateBinding(DeviceTemplateBinding binding, String backupBatchId, String backupReason) {
        log.info("开始备份设备模板绑定 - 设备编码: {}, 模板ID: {}, 批次ID: {}",
                 binding.getDeviceCode(), binding.getTemplateId(), backupBatchId);

        DeviceDataBackupRecord backupRecord = new DeviceDataBackupRecord();
        backupRecord.setBackupBatchId(backupBatchId);
        backupRecord.setDataType(DeviceDataBackupRecord.DataType.BINDING.getCode());
        backupRecord.setOriginalDataId(binding.getId());
        backupRecord.setDeviceCode(binding.getDeviceCode());
        backupRecord.setIdentifier(binding.getDeviceCode() + "-" + binding.getTemplateId());
        backupRecord.setBackupReason(backupReason);
        backupRecord.setBackupTime(LocalDateTime.now());

        try {
            // 备份设备模板绑定
            int count = baseMapper.backupTemplateBinding(backupReason, binding.getId());

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.SUCCESS.getCode());
            backupRecord.setBackupMessage("设备模板绑定备份成功，备份条数: " + count);

            log.info("设备模板绑定备份成功 - 设备编码: {}, 模板ID: {}, 备份条数: {}",
                     binding.getDeviceCode(), binding.getTemplateId(), count);

        } catch (Exception e) {
            log.error("备份设备模板绑定失败 - 设备编码: {}, 模板ID: {}",
                      binding.getDeviceCode(), binding.getTemplateId(), e);

            backupRecord.setBackupStatus(DeviceDataBackupRecord.BackupStatus.FAILED.getCode());
            backupRecord.setBackupMessage("备份失败: " + e.getMessage());

            throw new RuntimeException("备份设备模板绑定失败: " + e.getMessage(), e);
        }

        // 保存备份记录
        save(backupRecord);
        return backupRecord;
    }

    @Override
    public String backupOriginalFile(String originalFilePath, String deviceCode, String fileName) {
        if (!StringUtils.hasText(originalFilePath)) {
            return null;
        }

        try {
            Path originalPath = Paths.get(originalFilePath);
            if (!Files.exists(originalPath)) {
                log.warn("原始文件不存在，跳过备份 - 路径: {}", originalFilePath);
                return null;
            }

            // 构建备份文件路径
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileExtension = getFileExtension(fileName);
            String baseFileName = getBaseFileName(fileName);
            String backupFileName = baseFileName + "_backup_" + timestamp + "." + fileExtension;

            // 提取日期目录
            String dateDir = extractDateFromPath(originalFilePath);
            if (dateDir == null) {
                dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            }

            Path backupDir = Paths.get(uploadPath, "bak", "device", deviceCode, dateDir);

            // 确保备份目录存在
            if (!Files.exists(backupDir)) {
                Files.createDirectories(backupDir);
                log.info("创建备份目录: {}", backupDir);
            }

            Path backupPath = backupDir.resolve(backupFileName);

            // 复制文件到备份目录
            Files.copy(originalPath, backupPath, StandardCopyOption.REPLACE_EXISTING);

            log.info("原始文件备份成功 - 原路径: {}, 备份路径: {}", originalFilePath, backupPath);

            return backupPath.toString();

        } catch (IOException e) {
            log.error("备份原始文件失败 - 原路径: {}", originalFilePath, e);
            throw new RuntimeException("备份原始文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 备份主表数据
     */
    private void backupMainData(DeviceDetectionData detectionData, String backupBatchId, String backupReason) {
        int count = baseMapper.backupMainData(backupReason, detectionData.getId());
        log.debug("备份主表数据完成 - ID: {}, 备份条数: {}", detectionData.getId(), count);
    }

    /**
     * 备份基础字段数据
     */
    private void backupBasicFields(Long detectionDataId, String backupBatchId) {
        int count = baseMapper.backupBasicFields(detectionDataId);
        log.debug("备份基础字段数据完成 - 检测数据ID: {}, 备份条数: {}", detectionDataId, count);
    }

    /**
     * 备份表格表头数据
     */
    private void backupTableHeaders(Long detectionDataId, String backupBatchId) {
        int count = baseMapper.backupTableHeaders(detectionDataId);
        log.debug("备份表格表头数据完成 - 检测数据ID: {}, 备份条数: {}", detectionDataId, count);
    }

    /**
     * 备份表格数据
     */
    private void backupTableData(Long detectionDataId, String backupBatchId) {
        int count = baseMapper.backupTableData(detectionDataId);
        log.debug("备份表格数据完成 - 检测数据ID: {}, 备份条数: {}", detectionDataId, count);
    }

    /**
     * 备份解析日志数据
     */
    private void backupParseLogs(Long detectionDataId, String backupBatchId) {
        int count = baseMapper.backupParseLogs(detectionDataId);
        log.debug("备份解析日志数据完成 - 检测数据ID: {}, 备份条数: {}", detectionDataId, count);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 获取文件基础名（不含扩展名）
     */
    private String getBaseFileName(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return fileName;
        }
        return fileName.substring(0, fileName.lastIndexOf("."));
    }

    /**
     * 从文件路径中提取日期目录
     */
    private String extractDateFromPath(String filePath) {
        try {
            Path path = Paths.get(filePath);
            Path parent = path.getParent();
            if (parent != null) {
                String parentName = parent.getFileName().toString();
                // 检查是否是日期格式 (yyyy-MM-dd)
                if (parentName.matches("\\d{4}-\\d{2}-\\d{2}")) {
                    return parentName;
                }
            }
        } catch (Exception e) {
            log.debug("提取日期目录失败: {}", filePath, e);
        }
        return null;
    }

    @Override
    public boolean restoreBackupData(String backupBatchId) {
        // TODO: 实现数据恢复功能
        log.warn("数据恢复功能暂未实现 - 批次ID: {}", backupBatchId);
        return false;
    }

    @Override
    public boolean deleteBackupData(String backupBatchId, boolean deleteFiles) {
        // TODO: 实现备份数据删除功能
        log.warn("备份数据删除功能暂未实现 - 批次ID: {}", backupBatchId);
        return false;
    }

    @Override
    public List<DeviceDataBackupRecord> getBackupRecords(String backupBatchId) {
        LambdaQueryWrapper<DeviceDataBackupRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DeviceDataBackupRecord::getBackupBatchId, backupBatchId);
        wrapper.orderByDesc(DeviceDataBackupRecord::getBackupTime);
        return list(wrapper);
    }

    @Override
    public int cleanupExpiredBackups(int expireDays) {
        // TODO: 实现过期备份清理功能
        log.warn("过期备份清理功能暂未实现 - 过期天数: {}", expireDays);
        return 0;
    }
}
