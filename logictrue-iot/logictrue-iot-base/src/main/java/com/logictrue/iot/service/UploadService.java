package com.logictrue.iot.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.logictrue.iot.entity.Device;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.iot.entity.FileAttachInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UploadService {

    @Value("${lt.saveRecord:'http://7451.com/interfaces/collect/save/saveRecord'}")
    private String saveRecord;

    @Autowired
    private IDeviceService deviceService;

    public void saveInfo(FileAttachInfo build, String remoteAddr) {
        //保存上传的信息

        System.out.println("ip:" + remoteAddr);

        //根据ip查询设备解析方法
        LambdaQueryWrapper<Device> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Device::getIp, remoteAddr);
        Device device = deviceService.getOne(queryWrapper);

        if (device != null && StringUtils.isNotEmpty(device.getAnalysisType())) {
            String analysisType = device.getAnalysisType();

        } else {
            log.warn("设备ip:{},未录入设备信息", remoteAddr);
        }


    }
}
