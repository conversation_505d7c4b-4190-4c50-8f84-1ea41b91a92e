package com.logictrue.iot.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.iot.entity.DeviceDetectionParseLog;
import com.logictrue.iot.mapper.DeviceDetectionParseLogMapper;
import com.logictrue.iot.service.IDeviceDetectionParseLogService;
import org.springframework.stereotype.Service;

/**
 * 设备检测数据解析日志Service实现类
 *
 */
@Service
public class DeviceDetectionParseLogServiceImpl extends ServiceImpl<DeviceDetectionParseLogMapper, DeviceDetectionParseLog> 
        implements IDeviceDetectionParseLogService {

}
