package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 文件存储记录对象 file_attach_info
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Data
@Builder
@ApiModel(description="文件存储记录")
@TableName("file_attach_info")
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class FileAttachInfo
{


    /** 主键 */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 文件表单id */
    @ApiModelProperty(value = "文件表单id")
    @TableField("file_id")
    private String fileId;

    /** 文件名称 */
    @ApiModelProperty(value = "文件名称")
    @TableField("file_name")
    private String fileName;

    /** 文件存放路径 */
    @ApiModelProperty(value = "文件存放路径")
    @TableField("path_name")
    private String pathName;

    /** 文件类型 */
    @ApiModelProperty(value = "文件类型")
    @TableField("file_type")
    private String fileType;

    /** 缩略图文件路径 */
    @ApiModelProperty(value = "缩略图文件路径")
    @TableField("thumb_path_name")
    private String thumbPathName;

    /** 文件id */
    @ApiModelProperty(value = "文件id")
    @TableField("uid")
    private String uid;

    @ApiModelProperty(value = "文件大小")
    @TableField("file_size")
    private Double fileSize;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @TableField("create_time")
    private Date createTime;

}
