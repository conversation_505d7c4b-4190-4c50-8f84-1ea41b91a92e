package com.logictrue.iot.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("drl_device_upload")
public class DeviceUpload {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("file_id")
    private String fileId;

    @TableField("file_path")
    private String filePath;

    @TableField("ip")
    private String ip;

    @TableField("upload_time")
    private Date uploadTime;
}
