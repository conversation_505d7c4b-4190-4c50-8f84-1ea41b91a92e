<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.ExcelTemplateFieldMapper">

    <resultMap type="com.logictrue.iot.entity.ExcelTemplateField" id="ExcelTemplateFieldResult">
        <result property="id" column="id"/>
        <result property="templateId" column="template_id"/>
        <result property="sheetId" column="sheet_id"/>
        <result property="sheetName" column="sheet_name"/>
        <result property="fieldName" column="field_name"/>
        <result property="fieldCode" column="field_code"/>
        <result property="fieldType" column="field_type"/>
        <result property="fieldCategory" column="field_category"/>
        <result property="isRequired" column="is_required"/>
        <result property="defaultValue" column="default_value"/>
        <result property="validationRule" column="validation_rule"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectExcelTemplateFieldVo">
        select id, template_id, sheet_id, sheet_name, field_name, field_code, field_type, field_category,
               is_required, default_value, validation_rule, sort_order, create_time, update_time
        from excel_template_field
    </sql>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into excel_template_field(template_id, sheet_id, sheet_name, field_name, field_code, field_type, field_category,
                                        is_required, default_value, validation_rule, sort_order, create_time, update_time)
        values
        <foreach collection="fields" item="field" separator=",">
            (#{field.templateId}, #{field.sheetId}, #{field.sheetName}, #{field.fieldName}, #{field.fieldCode}, #{field.fieldType}, #{field.fieldCategory},
             #{field.isRequired}, #{field.defaultValue}, #{field.validationRule}, #{field.sortOrder}, now(), now())
        </foreach>
    </insert>

</mapper>
