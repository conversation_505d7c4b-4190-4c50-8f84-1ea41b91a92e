<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.ExcelTemplateMapper">

    <resultMap type="com.logictrue.iot.entity.ExcelTemplate" id="ExcelTemplateResult">
        <result property="id" column="id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateCode" column="template_code"/>
        <result property="deviceType" column="device_type"/>
        <result property="description" column="description"/>
        <result property="maxColumns" column="max_columns"/>
        <result property="maxRows" column="max_rows"/>
        <result property="columnWidths" column="column_widths"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <resultMap type="com.logictrue.iot.entity.ExcelTemplate" id="ExcelTemplateWithDetailsResult" extends="ExcelTemplateResult">
        <collection property="cells" ofType="com.logictrue.iot.entity.ExcelTemplateCell">
            <result property="id" column="cell_id"/>
            <result property="templateId" column="cell_template_id"/>
            <result property="sheetId" column="cell_sheet_id"/>
            <result property="sheetName" column="cell_sheet_name"/>
            <result property="sheetIndex" column="cell_sheet_index"/>
            <result property="rowIndex" column="row_index"/>
            <result property="colIndex" column="col_index"/>
            <result property="cellPosition" column="cell_position"/>
            <result property="content" column="content"/>
            <result property="cellType" column="cell_type"/>
            <result property="fieldType" column="field_type"/>
            <result property="fieldCode" column="field_code"/>
            <result property="sortOrder" column="cell_sort_order"/>
            <result property="merged" column="cell_merged"/>
            <result property="mergeMain" column="cell_merge_main"/>
            <result property="mergeRowSpan" column="cell_merge_row_span"/>
            <result property="mergeColSpan" column="cell_merge_col_span"/>
            <result property="hidden" column="cell_hidden"/>
            <result property="createTime" column="cell_create_time"/>
            <result property="updateTime" column="cell_update_time"/>
        </collection>
        <collection property="fields" ofType="com.logictrue.iot.entity.ExcelTemplateField">
            <result property="id" column="field_id"/>
            <result property="templateId" column="field_template_id"/>
            <result property="fieldName" column="field_name"/>
            <result property="fieldCode" column="field_field_code"/>
            <result property="fieldType" column="field_field_type"/>
            <result property="fieldCategory" column="field_category"/>
            <result property="isRequired" column="is_required"/>
            <result property="defaultValue" column="default_value"/>
            <result property="validationRule" column="validation_rule"/>
            <result property="sortOrder" column="field_sort_order"/>
            <result property="createTime" column="field_create_time"/>
            <result property="updateTime" column="field_update_time"/>
        </collection>
    </resultMap>

    <sql id="selectExcelTemplateVo">
        select id, template_name, template_code, device_type, description,
               max_columns, max_rows, column_widths, status,
               create_by, create_time, update_by, update_time
        from excel_template
    </sql>

    <select id="selectTemplateWithDetails" parameterType="Long" resultMap="ExcelTemplateWithDetailsResult">
        select t.id, t.template_name, t.template_code, t.device_type, t.description,
               t.max_columns, t.max_rows, t.column_widths, t.sheets_config, t.active_sheet_id, t.status,
               t.create_by, t.create_time, t.update_by, t.update_time,
               c.id as cell_id, c.template_id as cell_template_id, c.sheet_id as cell_sheet_id, c.sheet_name as cell_sheet_name, c.sheet_index as cell_sheet_index,
               c.row_index, c.col_index, c.cell_position, c.content, c.cell_type, c.field_type, c.field_code,
               c.sort_order as cell_sort_order, c.merged as cell_merged, c.merge_main as cell_merge_main,
               c.merge_row_span as cell_merge_row_span, c.merge_col_span as cell_merge_col_span, c.hidden as cell_hidden,
               c.create_time as cell_create_time, c.update_time as cell_update_time,
               f.id as field_id, f.template_id as field_template_id, f.field_name,
               f.field_code as field_field_code, f.field_type as field_field_type, f.field_category,
               f.is_required, f.default_value, f.validation_rule,
               f.sort_order as field_sort_order, f.create_time as field_create_time, f.update_time as field_update_time
        from excel_template t
        left join excel_template_cell c on t.id = c.template_id
        left join excel_template_field f on t.id = f.template_id
        where t.id = #{templateId}
        order by c.row_index, c.col_index, f.sort_order
    </select>

</mapper>
