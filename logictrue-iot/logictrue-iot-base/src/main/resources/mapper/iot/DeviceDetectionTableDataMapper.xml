<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceDetectionTableDataMapper">
    <resultMap id="BaseResultMap" type="com.logictrue.iot.entity.DeviceDetectionTableData">
        <!--@Table device_detection_table_data-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="detection_data_id" jdbcType="BIGINT" property="detectionDataId" />
        <result column="sheet_id" jdbcType="VARCHAR" property="sheetId" />
        <result column="sheet_name" jdbcType="VARCHAR" property="sheetName" />
        <result column="sheet_index" jdbcType="INTEGER" property="sheetIndex" />
        <result column="row_index" jdbcType="INTEGER" property="rowIndex" />
        <result column="row_data" jdbcType="LONGVARCHAR" property="rowData" />
        <result column="row_order" jdbcType="INTEGER" property="rowOrder" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, detection_data_id, sheet_id, sheet_name, sheet_index, row_index, row_data, row_order,
        create_time
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into device_detection_table_data
        (detection_data_id, sheet_id, sheet_name, sheet_index, row_index, row_data, row_order,
        create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.detectionDataId,jdbcType=BIGINT}, #{item.sheetId,jdbcType=VARCHAR}, #{item.sheetName,jdbcType=VARCHAR},
            #{item.sheetIndex,jdbcType=INTEGER}, #{item.rowIndex,jdbcType=INTEGER}, #{item.rowData,jdbcType=LONGVARCHAR},
            #{item.rowOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>
