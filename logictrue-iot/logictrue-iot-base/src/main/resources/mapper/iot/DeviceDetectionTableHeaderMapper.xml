<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceDetectionTableHeaderMapper">
    <resultMap id="BaseResultMap" type="com.logictrue.iot.entity.DeviceDetectionTableHeader">
        <!--@Table device_detection_table_header-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="detection_data_id" jdbcType="BIGINT" property="detectionDataId" />
        <result column="sheet_id" jdbcType="VARCHAR" property="sheetId" />
        <result column="sheet_name" jdbcType="VARCHAR" property="sheetName" />
        <result column="sheet_index" jdbcType="INTEGER" property="sheetIndex" />
        <result column="header_name" jdbcType="VARCHAR" property="headerName" />
        <result column="header_code" jdbcType="VARCHAR" property="headerCode" />
        <result column="header_position" jdbcType="VARCHAR" property="headerPosition" />
        <result column="header_row_index" jdbcType="INTEGER" property="headerRowIndex" />
        <result column="header_col_index" jdbcType="INTEGER" property="headerColIndex" />
        <result column="data_type" jdbcType="VARCHAR" property="dataType" />
        <result column="column_order" jdbcType="INTEGER" property="columnOrder" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, detection_data_id, sheet_id, sheet_name, sheet_index, header_name, header_code,
        header_position, header_row_index, header_col_index, data_type, column_order, create_time
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into device_detection_table_header
        (detection_data_id, sheet_id, sheet_name, sheet_index, header_name, header_code,
        header_position, header_row_index, header_col_index, data_type, column_order, create_time
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.detectionDataId,jdbcType=BIGINT}, #{item.sheetId,jdbcType=VARCHAR}, #{item.sheetName,jdbcType=VARCHAR},
            #{item.sheetIndex,jdbcType=INTEGER}, #{item.headerName,jdbcType=VARCHAR}, #{item.headerCode,jdbcType=VARCHAR},
            #{item.headerPosition,jdbcType=VARCHAR}, #{item.headerRowIndex,jdbcType=INTEGER},
            #{item.headerColIndex,jdbcType=INTEGER}, #{item.dataType,jdbcType=VARCHAR}, #{item.columnOrder,jdbcType=INTEGER},
            #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
</mapper>
