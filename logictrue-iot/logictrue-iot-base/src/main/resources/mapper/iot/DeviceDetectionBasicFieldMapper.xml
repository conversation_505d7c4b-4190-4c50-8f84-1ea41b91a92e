<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceDetectionBasicFieldMapper">
    <resultMap id="BaseResultMap" type="com.logictrue.iot.entity.DeviceDetectionBasicField">
        <!--@Table device_detection_basic_field-->
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="detection_data_id" jdbcType="BIGINT" property="detectionDataId" />
        <result column="sheet_id" jdbcType="VARCHAR" property="sheetId" />
        <result column="sheet_name" jdbcType="VARCHAR" property="sheetName" />
        <result column="sheet_index" jdbcType="INTEGER" property="sheetIndex" />
        <result column="field_code" jdbcType="VARCHAR" property="fieldCode" />
        <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
        <result column="field_type" jdbcType="VARCHAR" property="fieldType" />
        <result column="field_value" jdbcType="LONGVARCHAR" property="fieldValue" />
        <result column="label_position" jdbcType="VARCHAR" property="labelPosition" />
        <result column="value_position" jdbcType="VARCHAR" property="valuePosition" />
        <result column="label_row_index" jdbcType="INTEGER" property="labelRowIndex" />
        <result column="label_col_index" jdbcType="INTEGER" property="labelColIndex" />
        <result column="value_row_index" jdbcType="INTEGER" property="valueRowIndex" />
        <result column="value_col_index" jdbcType="INTEGER" property="valueColIndex" />
        <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    </resultMap>
    <sql id="Base_Column_List">
        id, detection_data_id, sheet_id, sheet_name, sheet_index, field_code, field_name,
        field_type, field_value, label_position, value_position, label_row_index, label_col_index,
        value_row_index, value_col_index, sort_order, create_time
    </sql>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into device_detection_basic_field
        (detection_data_id, sheet_id, sheet_name, sheet_index, field_code, field_name, field_type,
        field_value, label_position, value_position, label_row_index, label_col_index,
        value_row_index, value_col_index, sort_order, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.detectionDataId,jdbcType=BIGINT}, #{item.sheetId,jdbcType=VARCHAR}, #{item.sheetName,jdbcType=VARCHAR},
            #{item.sheetIndex,jdbcType=INTEGER}, #{item.fieldCode,jdbcType=VARCHAR}, #{item.fieldName,jdbcType=VARCHAR},
            #{item.fieldType,jdbcType=VARCHAR}, #{item.fieldValue,jdbcType=LONGVARCHAR}, #{item.labelPosition,jdbcType=VARCHAR},
            #{item.valuePosition,jdbcType=VARCHAR}, #{item.labelRowIndex,jdbcType=INTEGER},
            #{item.labelColIndex,jdbcType=INTEGER}, #{item.valueRowIndex,jdbcType=INTEGER},
            #{item.valueColIndex,jdbcType=INTEGER}, #{item.sortOrder,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>
</mapper>
