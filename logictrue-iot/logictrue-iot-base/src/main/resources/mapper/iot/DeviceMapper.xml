<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.iot.mapper.DeviceMapper">
  <resultMap id="BaseResultMap" type="com.logictrue.iot.entity.Device">
    <!--@mbg.generated-->
    <!--@Table drl_device-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="device_code" jdbcType="VARCHAR" property="deviceCode" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="device_status" jdbcType="TINYINT" property="deviceStatus" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="ip" jdbcType="VARCHAR" property="ip" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="pdf_address" jdbcType="VARCHAR" property="pdfAddress" />
    <result column="collect_type" jdbcType="INTEGER" property="collectType" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="vnc_ip" jdbcType="VARCHAR" property="vncIp" />
    <result column="last_time" jdbcType="TIMESTAMP" property="lastTime" />
    <result column="method_name" jdbcType="VARCHAR" property="methodName" />
    <result column="save_status" jdbcType="INTEGER" property="saveStatus" />
    <result column="device_remark" jdbcType="VARCHAR" property="deviceRemark" />
    <result column="device_model" jdbcType="VARCHAR" property="deviceModel" />
    <result column="device_duty" jdbcType="VARCHAR" property="deviceDuty" />
    <result column="device_type" jdbcType="VARCHAR" property="deviceType" />
    <result column="device_date" jdbcType="TIMESTAMP" property="deviceDate" />
    <result column="is_camera" jdbcType="BOOLEAN" property="isCamera" />
    <result column="camera_user" jdbcType="VARCHAR" property="cameraUser" />
    <result column="camera_password" jdbcType="VARCHAR" property="cameraPassword" />
    <result column="camera_ip" jdbcType="VARCHAR" property="cameraIp" />
    <result column="camera_port" jdbcType="INTEGER" property="cameraPort" />
    <result column="analysis_type" jdbcType="VARCHAR" property="analysisType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, device_code, device_name, device_status, create_by, create_time, ip, address,
    pdf_address, collect_type, file_id, vnc_ip, last_time, method_name, save_status,
    device_remark, device_model, device_duty, device_type, device_date, is_camera, camera_user,
    camera_password, camera_ip, camera_port, analysis_type
  </sql>
</mapper>
