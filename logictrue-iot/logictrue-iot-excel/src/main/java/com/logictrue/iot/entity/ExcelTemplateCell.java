package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Excel单元格配置实体类
 */
@TableName("excel_template_cell")
public class ExcelTemplateCell {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 模板ID */
    @TableField("template_id")
    private Long templateId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引（从0开始） */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 行索引（从0开始） */
    @TableField("row_index")
    private Integer rowIndex;

    /** 列索引（从0开始） */
    @TableField("col_index")
    private Integer colIndex;

    /** 单元格位置（如A1, B2） */
    @TableField("cell_position")
    private String cellPosition;

    /** 单元格内容 */
    @TableField("content")
    private String content;

    /** 单元格类型：label-标签，value-值，header-表头 */
    @TableField("cell_type")
    private String cellType;

    /** 字段类型：text-文本，number-数字，date-日期，header-表头 */
    @TableField("field_type")
    private String fieldType;

    /** 字段编码 */
    @TableField("field_code")
    private String fieldCode;

    /** 排序序号 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 是否为合并单元格：1-是，0-否 */
    @TableField("merged")
    private Boolean merged;

    /** 是否为合并单元格的主单元格：1-是，0-否 */
    @TableField("merge_main")
    private Boolean mergeMain;

    /** 合并的行数 */
    @TableField("merge_row_span")
    private Integer mergeRowSpan;

    /** 合并的列数 */
    @TableField("merge_col_span")
    private Integer mergeColSpan;

    /** 是否隐藏（被合并的单元格）：1-是，0-否 */
    @TableField("hidden")
    private Boolean hidden;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // 默认构造函数
    public ExcelTemplateCell() {
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public Integer getRowIndex() {
        return rowIndex;
    }

    public void setRowIndex(Integer rowIndex) {
        this.rowIndex = rowIndex;
    }

    public Integer getColIndex() {
        return colIndex;
    }

    public void setColIndex(Integer colIndex) {
        this.colIndex = colIndex;
    }

    public String getCellPosition() {
        return cellPosition;
    }

    public void setCellPosition(String cellPosition) {
        this.cellPosition = cellPosition;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCellType() {
        return cellType;
    }

    public void setCellType(String cellType) {
        this.cellType = cellType;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Boolean getMerged() {
        return merged;
    }

    public void setMerged(Boolean merged) {
        this.merged = merged;
    }

    public Boolean getMergeMain() {
        return mergeMain;
    }

    public void setMergeMain(Boolean mergeMain) {
        this.mergeMain = mergeMain;
    }

    public Integer getMergeRowSpan() {
        return mergeRowSpan;
    }

    public void setMergeRowSpan(Integer mergeRowSpan) {
        this.mergeRowSpan = mergeRowSpan;
    }

    public Integer getMergeColSpan() {
        return mergeColSpan;
    }

    public void setMergeColSpan(Integer mergeColSpan) {
        this.mergeColSpan = mergeColSpan;
    }

    public Boolean getHidden() {
        return hidden;
    }

    public void setHidden(Boolean hidden) {
        this.hidden = hidden;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    // equals 和 hashCode 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExcelTemplateCell that = (ExcelTemplateCell) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(templateId, that.templateId) &&
                Objects.equals(sheetId, that.sheetId) &&
                Objects.equals(sheetName, that.sheetName) &&
                Objects.equals(sheetIndex, that.sheetIndex) &&
                Objects.equals(rowIndex, that.rowIndex) &&
                Objects.equals(colIndex, that.colIndex) &&
                Objects.equals(cellPosition, that.cellPosition) &&
                Objects.equals(content, that.content) &&
                Objects.equals(cellType, that.cellType) &&
                Objects.equals(fieldType, that.fieldType) &&
                Objects.equals(fieldCode, that.fieldCode) &&
                Objects.equals(sortOrder, that.sortOrder) &&
                Objects.equals(merged, that.merged) &&
                Objects.equals(mergeMain, that.mergeMain) &&
                Objects.equals(mergeRowSpan, that.mergeRowSpan) &&
                Objects.equals(mergeColSpan, that.mergeColSpan) &&
                Objects.equals(hidden, that.hidden) &&
                Objects.equals(createTime, that.createTime) &&
                Objects.equals(updateTime, that.updateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, templateId, sheetId, sheetName, sheetIndex, rowIndex, colIndex,
                cellPosition, content, cellType, fieldType, fieldCode, sortOrder, merged,
                mergeMain, mergeRowSpan, mergeColSpan, hidden, createTime, updateTime);
    }

    @Override
    public String toString() {
        return "ExcelTemplateCell{" +
                "id=" + id +
                ", templateId=" + templateId +
                ", sheetId='" + sheetId + '\'' +
                ", sheetName='" + sheetName + '\'' +
                ", sheetIndex=" + sheetIndex +
                ", rowIndex=" + rowIndex +
                ", colIndex=" + colIndex +
                ", cellPosition='" + cellPosition + '\'' +
                ", content='" + content + '\'' +
                ", cellType='" + cellType + '\'' +
                ", fieldType='" + fieldType + '\'' +
                ", fieldCode='" + fieldCode + '\'' +
                ", sortOrder=" + sortOrder +
                ", merged=" + merged +
                ", mergeMain=" + mergeMain +
                ", mergeRowSpan=" + mergeRowSpan +
                ", mergeColSpan=" + mergeColSpan +
                ", hidden=" + hidden +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
