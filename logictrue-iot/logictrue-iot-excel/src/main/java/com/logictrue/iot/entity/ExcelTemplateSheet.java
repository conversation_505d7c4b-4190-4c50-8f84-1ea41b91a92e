package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Excel模板Sheet配置实体类
 *
 */
@TableName("excel_template_sheet")
public class ExcelTemplateSheet {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 模板ID */
    @TableField("template_id")
    private Long templateId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引（从0开始） */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 最大列数 */
    @TableField("max_columns")
    private Integer maxColumns;

    /** 最大行数 */
    @TableField("max_rows")
    private Integer maxRows;

    /** 列宽度配置JSON */
    @TableField("column_widths")
    private String columnWidths;

    /** 排序序号 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 单元格配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateCell> cells;

    /** 字段配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateField> fields;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public Integer getMaxColumns() {
        return maxColumns;
    }

    public void setMaxColumns(Integer maxColumns) {
        this.maxColumns = maxColumns;
    }

    public Integer getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(Integer maxRows) {
        this.maxRows = maxRows;
    }

    public String getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(String columnWidths) {
        this.columnWidths = columnWidths;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public List<ExcelTemplateCell> getCells() {
        return cells;
    }

    public void setCells(List<ExcelTemplateCell> cells) {
        this.cells = cells;
    }

    public List<ExcelTemplateField> getFields() {
        return fields;
    }

    public void setFields(List<ExcelTemplateField> fields) {
        this.fields = fields;
    }
}
