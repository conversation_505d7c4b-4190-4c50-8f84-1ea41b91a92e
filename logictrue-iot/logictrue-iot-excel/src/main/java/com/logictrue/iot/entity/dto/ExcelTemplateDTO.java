package com.logictrue.iot.entity.dto;

import com.logictrue.iot.entity.ExcelTemplateCell;
import com.logictrue.iot.entity.ExcelTemplateField;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * Excel模板数据传输对象
 */
public class ExcelTemplateDTO {

    /** 主键ID */
    private Long id;

    /** 模板名称 */
    @NotBlank(message = "模板名称不能为空")
    private String templateName;

    /** 模板编码 */
    @NotBlank(message = "模板编码不能为空")
    private String templateCode;

    /** 设备类型 */
    private String deviceType;

    /** 模板描述 */
    private String description;

    /** 最大列数 */
    @NotNull(message = "最大列数不能为空")
    private Integer maxColumns;

    /** 最大行数 */
    @NotNull(message = "最大行数不能为空")
    private Integer maxRows;

    /** 列宽度配置 */
    private List<Integer> columnWidths;

    /** 状态：1-启用，0-禁用 */
    private Integer status;

    /** 单元格配置列表 */
    private List<ExcelTemplateCellDTO> cells;

    /** 字段配置列表 */
    private List<ExcelTemplateFieldDTO> fields;

    /** Sheet配置列表 */
    private List<ExcelTemplateSheetDTO> sheets;

    /** 当前活动Sheet ID */
    private String activeSheetId;

    // 默认构造函数
    public ExcelTemplateDTO() {
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getMaxColumns() {
        return maxColumns;
    }

    public void setMaxColumns(Integer maxColumns) {
        this.maxColumns = maxColumns;
    }

    public Integer getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(Integer maxRows) {
        this.maxRows = maxRows;
    }

    public List<Integer> getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(List<Integer> columnWidths) {
        this.columnWidths = columnWidths;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<ExcelTemplateCellDTO> getCells() {
        return cells;
    }

    public void setCells(List<ExcelTemplateCellDTO> cells) {
        this.cells = cells;
    }

    public List<ExcelTemplateFieldDTO> getFields() {
        return fields;
    }

    public void setFields(List<ExcelTemplateFieldDTO> fields) {
        this.fields = fields;
    }

    public List<ExcelTemplateSheetDTO> getSheets() {
        return sheets;
    }

    public void setSheets(List<ExcelTemplateSheetDTO> sheets) {
        this.sheets = sheets;
    }

    public String getActiveSheetId() {
        return activeSheetId;
    }

    public void setActiveSheetId(String activeSheetId) {
        this.activeSheetId = activeSheetId;
    }

    /**
     * 单元格配置DTO
     */
    public static class ExcelTemplateCellDTO {
        /** Sheet ID */
        private String sheetId;

        /** Sheet名称 */
        private String sheetName;

        /** Sheet索引 */
        private Integer sheetIndex;

        /** 行索引 */
        private Integer rowIndex;

        /** 列索引 */
        private Integer colIndex;

        /** 单元格位置 */
        private String cellPosition;

        /** 单元格内容 */
        private String content;

        /** 单元格类型 */
        private String cellType;

        /** 字段类型 */
        private String fieldType;

        /** 字段编码 */
        private String fieldCode;

        /** 排序序号 */
        private Integer sortOrder;

        /** 是否为合并单元格 */
        private Boolean merged;

        /** 是否为合并单元格的主单元格 */
        private Boolean mergeMain;

        /** 合并的行数 */
        private Integer mergeRowSpan;

        /** 合并的列数 */
        private Integer mergeColSpan;

        /** 是否隐藏（被合并的单元格） */
        private Boolean hidden;

        // 默认构造函数
        public ExcelTemplateCellDTO() {
        }

        // Getter 和 Setter 方法
        public String getSheetId() {
            return sheetId;
        }

        public void setSheetId(String sheetId) {
            this.sheetId = sheetId;
        }

        public String getSheetName() {
            return sheetName;
        }

        public void setSheetName(String sheetName) {
            this.sheetName = sheetName;
        }

        public Integer getSheetIndex() {
            return sheetIndex;
        }

        public void setSheetIndex(Integer sheetIndex) {
            this.sheetIndex = sheetIndex;
        }

        public Integer getRowIndex() {
            return rowIndex;
        }

        public void setRowIndex(Integer rowIndex) {
            this.rowIndex = rowIndex;
        }

        public Integer getColIndex() {
            return colIndex;
        }

        public void setColIndex(Integer colIndex) {
            this.colIndex = colIndex;
        }

        public String getCellPosition() {
            return cellPosition;
        }

        public void setCellPosition(String cellPosition) {
            this.cellPosition = cellPosition;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public String getCellType() {
            return cellType;
        }

        public void setCellType(String cellType) {
            this.cellType = cellType;
        }

        public String getFieldType() {
            return fieldType;
        }

        public void setFieldType(String fieldType) {
            this.fieldType = fieldType;
        }

        public String getFieldCode() {
            return fieldCode;
        }

        public void setFieldCode(String fieldCode) {
            this.fieldCode = fieldCode;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }

        public Boolean getMerged() {
            return merged;
        }

        public void setMerged(Boolean merged) {
            this.merged = merged;
        }

        public Boolean getMergeMain() {
            return mergeMain;
        }

        public void setMergeMain(Boolean mergeMain) {
            this.mergeMain = mergeMain;
        }

        public Integer getMergeRowSpan() {
            return mergeRowSpan;
        }

        public void setMergeRowSpan(Integer mergeRowSpan) {
            this.mergeRowSpan = mergeRowSpan;
        }

        public Integer getMergeColSpan() {
            return mergeColSpan;
        }

        public void setMergeColSpan(Integer mergeColSpan) {
            this.mergeColSpan = mergeColSpan;
        }

        public Boolean getHidden() {
            return hidden;
        }

        public void setHidden(Boolean hidden) {
            this.hidden = hidden;
        }
    }

    /**
     * 字段配置DTO
     */
    public static class ExcelTemplateFieldDTO {
        /** Sheet ID */
        private String sheetId;

        /** Sheet名称 */
        private String sheetName;

        /** 字段名称 */
        private String fieldName;

        /** 字段编码 */
        private String fieldCode;

        /** 字段类型 */
        private String fieldType;

        /** 字段分类 */
        private String fieldCategory;

        /** 是否必填 */
        private Integer isRequired;

        /** 默认值 */
        private String defaultValue;

        /** 验证规则 */
        private String validationRule;

        /** 排序序号 */
        private Integer sortOrder;

        // 默认构造函数
        public ExcelTemplateFieldDTO() {
        }

        // Getter 和 Setter 方法
        public String getSheetId() {
            return sheetId;
        }

        public void setSheetId(String sheetId) {
            this.sheetId = sheetId;
        }

        public String getSheetName() {
            return sheetName;
        }

        public void setSheetName(String sheetName) {
            this.sheetName = sheetName;
        }

        public String getFieldName() {
            return fieldName;
        }

        public void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        public String getFieldCode() {
            return fieldCode;
        }

        public void setFieldCode(String fieldCode) {
            this.fieldCode = fieldCode;
        }

        public String getFieldType() {
            return fieldType;
        }

        public void setFieldType(String fieldType) {
            this.fieldType = fieldType;
        }

        public String getFieldCategory() {
            return fieldCategory;
        }

        public void setFieldCategory(String fieldCategory) {
            this.fieldCategory = fieldCategory;
        }

        public Integer getIsRequired() {
            return isRequired;
        }

        public void setIsRequired(Integer isRequired) {
            this.isRequired = isRequired;
        }

        public String getDefaultValue() {
            return defaultValue;
        }

        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }

        public String getValidationRule() {
            return validationRule;
        }

        public void setValidationRule(String validationRule) {
            this.validationRule = validationRule;
        }

        public Integer getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
        }
    }
}
