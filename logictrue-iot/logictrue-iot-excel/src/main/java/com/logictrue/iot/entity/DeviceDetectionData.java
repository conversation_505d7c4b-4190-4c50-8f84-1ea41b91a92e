package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测数据主表实体类
 *
 */
@TableName("device_detection_data")
public class DeviceDetectionData {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 设备编码 */
    @TableField("device_code")
    private String deviceCode;

    /** 模板ID */
    @TableField("template_id")
    private Long templateId;

    @TableField("template_code")
    private String templateCode;

    /** 模板名称 */
    @TableField("template_name")
    private String templateName;

    /** 原始文件名 */
    @TableField("file_name")
    private String fileName;

    /** 文件存储路径 */
    @TableField("file_path")
    private String filePath;

    @TableField("collect_path")
    private String collectPath;

    /** 文件大小（字节） */
    @TableField("file_size")
    private Long fileSize;

    /** 解析状态：0-待解析，1-解析成功，2-解析失败 */
    @TableField("parse_status")
    private Integer parseStatus;

    /** 解析结果消息 */
    @TableField("parse_message")
    private String parseMessage;

    /** 解析时间 */
    @TableField("parse_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime parseTime;

    /** 总Sheet数量 */
    @TableField("total_sheets")
    private Integer totalSheets;

    /** 已解析Sheet数量 */
    @TableField("parsed_sheets")
    private Integer parsedSheets;

    /** 基础字段数量 */
    @TableField("basic_fields_count")
    private Integer basicFieldsCount;

    /** 表格数据行数 */
    @TableField("table_rows_count")
    private Integer tableRowsCount;

    /** 创建人 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新人 */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 备注 */
    @TableField("remark")
    private String remark;

    @TableField("collect_data")
    private String collectData;

    @TableField(value = "push_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pushTime;

    @TableField(value = "push_url")
    private String pushUrl;

    @TableField("push_retry_count")
    private Integer pushRetryCount;

    @TableField("push_status")
    private int pushStatus;

    @TableField("push_error_message")
    private String pushErrorMessage;

    // 非数据库字段
    /** 基础字段列表 */
    @TableField(exist = false)
    private List<DeviceDetectionBasicField> basicFields;

    /** 表格表头列表 */
    @TableField(exist = false)
    private List<DeviceDetectionTableHeader> tableHeaders;

    /** 表格数据列表 */
    @TableField(exist = false)
    private List<DeviceDetectionTableData> tableDataList;

    /** 解析日志列表 */
    @TableField(exist = false)
    private List<DeviceDetectionParseLog> parseLogs;

    /** 设备名称（关联查询） */
    @TableField(exist = false)
    private String deviceName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getCollectPath() {
        return collectPath;
    }

    public void setCollectPath(String collectPath) {
        this.collectPath = collectPath;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public Integer getParseStatus() {
        return parseStatus;
    }

    public void setParseStatus(Integer parseStatus) {
        this.parseStatus = parseStatus;
    }

    public String getParseMessage() {
        return parseMessage;
    }

    public void setParseMessage(String parseMessage) {
        this.parseMessage = parseMessage;
    }

    public LocalDateTime getParseTime() {
        return parseTime;
    }

    public void setParseTime(LocalDateTime parseTime) {
        this.parseTime = parseTime;
    }

    public Integer getTotalSheets() {
        return totalSheets;
    }

    public void setTotalSheets(Integer totalSheets) {
        this.totalSheets = totalSheets;
    }

    public Integer getParsedSheets() {
        return parsedSheets;
    }

    public void setParsedSheets(Integer parsedSheets) {
        this.parsedSheets = parsedSheets;
    }

    public Integer getBasicFieldsCount() {
        return basicFieldsCount;
    }

    public void setBasicFieldsCount(Integer basicFieldsCount) {
        this.basicFieldsCount = basicFieldsCount;
    }

    public Integer getTableRowsCount() {
        return tableRowsCount;
    }

    public void setTableRowsCount(Integer tableRowsCount) {
        this.tableRowsCount = tableRowsCount;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public List<DeviceDetectionBasicField> getBasicFields() {
        return basicFields;
    }

    public void setBasicFields(List<DeviceDetectionBasicField> basicFields) {
        this.basicFields = basicFields;
    }

    public List<DeviceDetectionTableHeader> getTableHeaders() {
        return tableHeaders;
    }

    public void setTableHeaders(List<DeviceDetectionTableHeader> tableHeaders) {
        this.tableHeaders = tableHeaders;
    }

    public List<DeviceDetectionTableData> getTableDataList() {
        return tableDataList;
    }

    public void setTableDataList(List<DeviceDetectionTableData> tableDataList) {
        this.tableDataList = tableDataList;
    }

    public List<DeviceDetectionParseLog> getParseLogs() {
        return parseLogs;
    }

    public void setParseLogs(List<DeviceDetectionParseLog> parseLogs) {
        this.parseLogs = parseLogs;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getCollectData() {
        return collectData;
    }

    public void setCollectData(String collectData) {
        this.collectData = collectData;
    }

    public LocalDateTime getPushTime() {
        return pushTime;
    }

    public void setPushTime(LocalDateTime pushTime) {
        this.pushTime = pushTime;
    }

    public String getPushUrl() {
        return pushUrl;
    }

    public void setPushUrl(String pushUrl) {
        this.pushUrl = pushUrl;
    }

    public Integer getPushRetryCount() {
        return pushRetryCount;
    }

    public void setPushRetryCount(Integer pushRetryCount) {
        this.pushRetryCount = pushRetryCount;
    }

    public int getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(int pushStatus) {
        this.pushStatus = pushStatus;
    }

    public String getPushErrorMessage() {
        return pushErrorMessage;
    }

    public void setPushErrorMessage(String pushErrorMessage) {
        this.pushErrorMessage = pushErrorMessage;
    }
}
