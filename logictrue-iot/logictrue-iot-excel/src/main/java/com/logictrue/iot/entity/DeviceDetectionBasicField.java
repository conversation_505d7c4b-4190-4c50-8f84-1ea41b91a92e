package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 设备检测数据基础字段实体类
 */
@TableName("device_detection_basic_field")
public class DeviceDetectionBasicField {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 检测数据主表ID */
    @TableField("detection_data_id")
    private Long detectionDataId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引 */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 字段编码 */
    @TableField("field_code")
    private String fieldCode;

    /** 字段名称 */
    @TableField("field_name")
    private String fieldName;

    /** 字段类型：text-文本，number-数字，date-日期 */
    @TableField("field_type")
    private String fieldType;

    /** 字段值 */
    @TableField("field_value")
    private String fieldValue;

    /** 标签位置（如A1） */
    @TableField("label_position")
    private String labelPosition;

    /** 值位置（如B1） */
    @TableField("value_position")
    private String valuePosition;

    /** 标签行索引 */
    @TableField("label_row_index")
    private Integer labelRowIndex;

    /** 标签列索引 */
    @TableField("label_col_index")
    private Integer labelColIndex;

    /** 值行索引 */
    @TableField("value_row_index")
    private Integer valueRowIndex;

    /** 值列索引 */
    @TableField("value_col_index")
    private Integer valueColIndex;

    /** 排序序号 */
    @TableField("sort_order")
    private Integer sortOrder;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    // 默认构造函数
    public DeviceDetectionBasicField() {
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDetectionDataId() {
        return detectionDataId;
    }

    public void setDetectionDataId(Long detectionDataId) {
        this.detectionDataId = detectionDataId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public String getLabelPosition() {
        return labelPosition;
    }

    public void setLabelPosition(String labelPosition) {
        this.labelPosition = labelPosition;
    }

    public String getValuePosition() {
        return valuePosition;
    }

    public void setValuePosition(String valuePosition) {
        this.valuePosition = valuePosition;
    }

    public Integer getLabelRowIndex() {
        return labelRowIndex;
    }

    public void setLabelRowIndex(Integer labelRowIndex) {
        this.labelRowIndex = labelRowIndex;
    }

    public Integer getLabelColIndex() {
        return labelColIndex;
    }

    public void setLabelColIndex(Integer labelColIndex) {
        this.labelColIndex = labelColIndex;
    }

    public Integer getValueRowIndex() {
        return valueRowIndex;
    }

    public void setValueRowIndex(Integer valueRowIndex) {
        this.valueRowIndex = valueRowIndex;
    }

    public Integer getValueColIndex() {
        return valueColIndex;
    }

    public void setValueColIndex(Integer valueColIndex) {
        this.valueColIndex = valueColIndex;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    // equals 和 hashCode 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DeviceDetectionBasicField that = (DeviceDetectionBasicField) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(detectionDataId, that.detectionDataId) &&
                Objects.equals(sheetId, that.sheetId) &&
                Objects.equals(sheetName, that.sheetName) &&
                Objects.equals(sheetIndex, that.sheetIndex) &&
                Objects.equals(fieldCode, that.fieldCode) &&
                Objects.equals(fieldName, that.fieldName) &&
                Objects.equals(fieldType, that.fieldType) &&
                Objects.equals(fieldValue, that.fieldValue) &&
                Objects.equals(labelPosition, that.labelPosition) &&
                Objects.equals(valuePosition, that.valuePosition) &&
                Objects.equals(labelRowIndex, that.labelRowIndex) &&
                Objects.equals(labelColIndex, that.labelColIndex) &&
                Objects.equals(valueRowIndex, that.valueRowIndex) &&
                Objects.equals(valueColIndex, that.valueColIndex) &&
                Objects.equals(sortOrder, that.sortOrder) &&
                Objects.equals(createTime, that.createTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, detectionDataId, sheetId, sheetName, sheetIndex, fieldCode,
                fieldName, fieldType, fieldValue, labelPosition, valuePosition, labelRowIndex,
                labelColIndex, valueRowIndex, valueColIndex, sortOrder, createTime);
    }

    @Override
    public String toString() {
        return "DeviceDetectionBasicField{" +
                "id=" + id +
                ", detectionDataId=" + detectionDataId +
                ", sheetId='" + sheetId + '\'' +
                ", sheetName='" + sheetName + '\'' +
                ", sheetIndex=" + sheetIndex +
                ", fieldCode='" + fieldCode + '\'' +
                ", fieldName='" + fieldName + '\'' +
                ", fieldType='" + fieldType + '\'' +
                ", fieldValue='" + fieldValue + '\'' +
                ", labelPosition='" + labelPosition + '\'' +
                ", valuePosition='" + valuePosition + '\'' +
                ", labelRowIndex=" + labelRowIndex +
                ", labelColIndex=" + labelColIndex +
                ", valueRowIndex=" + valueRowIndex +
                ", valueColIndex=" + valueColIndex +
                ", sortOrder=" + sortOrder +
                ", createTime=" + createTime +
                '}';
    }
}
