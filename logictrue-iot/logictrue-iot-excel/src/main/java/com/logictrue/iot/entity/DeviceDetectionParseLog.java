package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 设备检测数据解析日志实体类
 *
 */
@TableName("device_detection_parse_log")
public class DeviceDetectionParseLog {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 检测数据主表ID */
    @TableField("detection_data_id")
    private Long detectionDataId;

    /** 日志级别：INFO-信息，WARN-警告，ERROR-错误 */
    @TableField("log_level")
    private String logLevel;

    /** 日志消息 */
    @TableField("log_message")
    private String logMessage;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** 位置信息 */
    @TableField("position")
    private String position;

    /** 字段编码 */
    @TableField("field_code")
    private String fieldCode;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDetectionDataId() {
        return detectionDataId;
    }

    public void setDetectionDataId(Long detectionDataId) {
        this.detectionDataId = detectionDataId;
    }

    public String getLogLevel() {
        return logLevel;
    }

    public void setLogLevel(String logLevel) {
        this.logLevel = logLevel;
    }

    public String getLogMessage() {
        return logMessage;
    }

    public void setLogMessage(String logMessage) {
        this.logMessage = logMessage;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getFieldCode() {
        return fieldCode;
    }

    public void setFieldCode(String fieldCode) {
        this.fieldCode = fieldCode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
