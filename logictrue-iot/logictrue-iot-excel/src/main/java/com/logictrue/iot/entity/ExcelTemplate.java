package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Excel模板实体类
 *
 */
@TableName("excel_template")
public class ExcelTemplate {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 模板名称 */
    @TableField("template_name")
    private String templateName;

    /** 模板编码 */
    @TableField("template_code")
    private String templateCode;

    /** 设备类型 */
    @TableField("device_type")
    private String deviceType;

    /** 模板描述 */
    @TableField("description")
    private String description;

    /** 最大列数 */
    @TableField("max_columns")
    private Integer maxColumns;

    /** 最大行数 */
    @TableField("max_rows")
    private Integer maxRows;

    /** 列宽度配置JSON */
    @TableField("column_widths")
    private String columnWidths;

    /** 多Sheet配置JSON */
    @TableField("sheets_config")
    private String sheetsConfig;

    /** 当前活动Sheet ID */
    @TableField("active_sheet_id")
    private String activeSheetId;

    /** 状态：1-启用，0-禁用 */
    @TableField("status")
    private Integer status;

    /** 创建人 */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /** 更新人 */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /** 单元格配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateCell> cells;

    /** 字段配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateField> fields;

    /** Sheet配置列表（非数据库字段） */
    @TableField(exist = false)
    private List<ExcelTemplateSheet> sheets;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getMaxColumns() {
        return maxColumns;
    }

    public void setMaxColumns(Integer maxColumns) {
        this.maxColumns = maxColumns;
    }

    public Integer getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(Integer maxRows) {
        this.maxRows = maxRows;
    }

    public String getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(String columnWidths) {
        this.columnWidths = columnWidths;
    }

    public String getSheetsConfig() {
        return sheetsConfig;
    }

    public void setSheetsConfig(String sheetsConfig) {
        this.sheetsConfig = sheetsConfig;
    }

    public String getActiveSheetId() {
        return activeSheetId;
    }

    public void setActiveSheetId(String activeSheetId) {
        this.activeSheetId = activeSheetId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public List<ExcelTemplateCell> getCells() {
        return cells;
    }

    public void setCells(List<ExcelTemplateCell> cells) {
        this.cells = cells;
    }

    public List<ExcelTemplateField> getFields() {
        return fields;
    }

    public void setFields(List<ExcelTemplateField> fields) {
        this.fields = fields;
    }

    public List<ExcelTemplateSheet> getSheets() {
        return sheets;
    }

    public void setSheets(List<ExcelTemplateSheet> sheets) {
        this.sheets = sheets;
    }
}
