package com.logictrue.iot.entity.dto;

import com.logictrue.iot.entity.ExcelTemplateCell;
import com.logictrue.iot.entity.ExcelTemplateField;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * Excel模板Sheet数据传输对象
 */
public class ExcelTemplateSheetDTO {

    /** 主键ID */
    private Long id;

    /** 模板ID */
    private Long templateId;

    /** Sheet ID */
    @NotBlank(message = "Sheet ID不能为空")
    private String sheetId;

    /** Sheet名称 */
    @NotBlank(message = "Sheet名称不能为空")
    private String sheetName;

    /** Sheet索引（从0开始） */
    private Integer sheetIndex;

    /** 最大列数 */
    @NotNull(message = "最大列数不能为空")
    private Integer maxColumns;

    /** 最大行数 */
    @NotNull(message = "最大行数不能为空")
    private Integer maxRows;

    /** 列宽度配置 */
    private List<Integer> columnWidths;

    /** 排序序号 */
    private Integer sortOrder;

    /** 单元格配置列表 */
    private List<ExcelTemplateCell> cells;

    /** 字段配置列表 */
    private List<ExcelTemplateField> fields;

    // 默认构造函数
    public ExcelTemplateSheetDTO() {
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public Integer getMaxColumns() {
        return maxColumns;
    }

    public void setMaxColumns(Integer maxColumns) {
        this.maxColumns = maxColumns;
    }

    public Integer getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(Integer maxRows) {
        this.maxRows = maxRows;
    }

    public List<Integer> getColumnWidths() {
        return columnWidths;
    }

    public void setColumnWidths(List<Integer> columnWidths) {
        this.columnWidths = columnWidths;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public List<ExcelTemplateCell> getCells() {
        return cells;
    }

    public void setCells(List<ExcelTemplateCell> cells) {
        this.cells = cells;
    }

    public List<ExcelTemplateField> getFields() {
        return fields;
    }

    public void setFields(List<ExcelTemplateField> fields) {
        this.fields = fields;
    }

    // equals 和 hashCode 方法
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ExcelTemplateSheetDTO that = (ExcelTemplateSheetDTO) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(templateId, that.templateId) &&
                Objects.equals(sheetId, that.sheetId) &&
                Objects.equals(sheetName, that.sheetName) &&
                Objects.equals(sheetIndex, that.sheetIndex) &&
                Objects.equals(maxColumns, that.maxColumns) &&
                Objects.equals(maxRows, that.maxRows) &&
                Objects.equals(columnWidths, that.columnWidths) &&
                Objects.equals(sortOrder, that.sortOrder) &&
                Objects.equals(cells, that.cells) &&
                Objects.equals(fields, that.fields);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, templateId, sheetId, sheetName, sheetIndex, maxColumns,
                maxRows, columnWidths, sortOrder, cells, fields);
    }

    @Override
    public String toString() {
        return "ExcelTemplateSheetDTO{" +
                "id=" + id +
                ", templateId=" + templateId +
                ", sheetId='" + sheetId + '\'' +
                ", sheetName='" + sheetName + '\'' +
                ", sheetIndex=" + sheetIndex +
                ", maxColumns=" + maxColumns +
                ", maxRows=" + maxRows +
                ", columnWidths=" + columnWidths +
                ", sortOrder=" + sortOrder +
                ", cells=" + cells +
                ", fields=" + fields +
                '}';
    }
}
