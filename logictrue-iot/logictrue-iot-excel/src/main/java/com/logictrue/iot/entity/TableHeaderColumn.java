package com.logictrue.iot.entity;


/**
 * 表头列定义类
 */
public class TableHeaderColumn {
    private String headerName;
    private String headerCode;
    private String headerPosition;
    private Integer headerRowIndex;
    private Integer headerColIndex;
    private ExcelTemplateCell originalHeaderCell;
    private Integer mergedColumnIndex; // 在合并单元格中的列索引（从0开始）

    public String getHeaderName() {
        return headerName;
    }

    public void setHeaderName(String headerName) {
        this.headerName = headerName;
    }

    public String getHeaderCode() {
        return headerCode;
    }

    public void setHeaderCode(String headerCode) {
        this.headerCode = headerCode;
    }

    public String getHeaderPosition() {
        return headerPosition;
    }

    public void setHeaderPosition(String headerPosition) {
        this.headerPosition = headerPosition;
    }

    public Integer getHeaderRowIndex() {
        return headerRowIndex;
    }

    public void setHeaderRowIndex(Integer headerRowIndex) {
        this.headerRowIndex = headerRowIndex;
    }

    public Integer getHeaderColIndex() {
        return headerColIndex;
    }

    public void setHeaderColIndex(Integer headerColIndex) {
        this.headerColIndex = headerColIndex;
    }

    public ExcelTemplateCell getOriginalHeaderCell() {
        return originalHeaderCell;
    }

    public void setOriginalHeaderCell(ExcelTemplateCell originalHeaderCell) {
        this.originalHeaderCell = originalHeaderCell;
    }

    public Integer getMergedColumnIndex() {
        return mergedColumnIndex;
    }

    public void setMergedColumnIndex(Integer mergedColumnIndex) {
        this.mergedColumnIndex = mergedColumnIndex;
    }
}
