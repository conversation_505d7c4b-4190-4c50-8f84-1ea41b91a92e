package com.logictrue.iot.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.logictrue.iot.entity.*;
import com.logictrue.iot.entity.dto.ExcelTemplateSheetDTO;
import com.logictrue.iot.uitl.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Excel模板解析服务实现类
 */
public class ExcelTemplateParserService {

    private static final Logger log = LoggerFactory.getLogger(ExcelTemplateParserService.class);

    /**
     * 从Excel Sheet解析基础字段
     */
    public static JSONObject parseBasicFieldsFromSheet(Long detectionDataId, Sheet sheet, int sheetIndex,
                                                String sheetId, String sheetName,
                                                List<ExcelTemplateCell> sheetCells, List<ExcelTemplateField> sheetFields) {
        JSONObject result = new JSONObject();

        log.info("开始解析基础字段，Sheet: {}, 单元格数量: {}, 字段数量: {}", sheetName, sheetCells.size(), sheetFields.size());

        // 获取基础字段的单元格配置 - 包含label和value类型的单元格
        List<ExcelTemplateCell> basicCells = sheetCells.stream()
                .filter(cell -> "label".equals(cell.getCellType()) || "value".equals(cell.getCellType()))
                .collect(Collectors.toList());

        log.info("找到基础字段单元格数量: {}", basicCells.size());

        // 按基础字段编码分组，处理label和value的关联
        Map<String, List<ExcelTemplateCell>> cellsByBaseFieldCode = new HashMap<>();

        for (ExcelTemplateCell cell : basicCells) {
            if (!StringUtils.hasText(cell.getFieldCode())) {
                continue;
            }

            String baseFieldCode;
            if ("value".equals(cell.getCellType()) && cell.getFieldCode().endsWith("_value")) {
                // value单元格，去掉_value后缀得到基础字段编码
                baseFieldCode = cell.getFieldCode().substring(0, cell.getFieldCode().length() - 6);
            } else if ("label".equals(cell.getCellType())) {
                // label单元格，直接使用字段编码
                baseFieldCode = cell.getFieldCode();
            } else {
                log.warn("未识别的单元格类型或字段编码格式: 类型={}, 字段编码={}", cell.getCellType(), cell.getFieldCode());
                continue;
            }

            cellsByBaseFieldCode.computeIfAbsent(baseFieldCode, k -> new ArrayList<>()).add(cell);
        }

        log.info("按基础字段编码分组后的字段数量: {}", cellsByBaseFieldCode.size());

        int count = 0;
        List<DeviceDetectionBasicField> basicFields = new ArrayList<>();
        // 处理每个基础字段的label和value关联
        for (Map.Entry<String, List<ExcelTemplateCell>> entry : cellsByBaseFieldCode.entrySet()) {
            String baseFieldCode = entry.getKey();
            List<ExcelTemplateCell> fieldCells = entry.getValue();

            log.info("处理基础字段: {}, 相关单元格数量: {}", baseFieldCode, fieldCells.size());

            // 查找对应的字段配置（使用基础字段编码）
            ExcelTemplateField fieldConfig = sheetFields.stream()
                    .filter(field -> baseFieldCode.equals(field.getFieldCode()) && "basic".equals(field.getFieldCategory()))
                    .findFirst()
                    .orElse(null);

            if (fieldConfig != null) {
                // 查找label和value单元格
                ExcelTemplateCell labelCell = fieldCells.stream()
                        .filter(cell -> "label".equals(cell.getCellType()))
                        .findFirst().orElse(null);

                ExcelTemplateCell valueCell = fieldCells.stream()
                        .filter(cell -> "value".equals(cell.getCellType()))
                        .findFirst().orElse(null);

                // 创建基础字段记录
                DeviceDetectionBasicField basicField = new DeviceDetectionBasicField();
                basicField.setDetectionDataId(detectionDataId);
                basicField.setSheetId(sheetId);
                basicField.setSheetName(sheetName);
                basicField.setSheetIndex(sheetIndex);
                basicField.setFieldCode(baseFieldCode);
                basicField.setFieldType(fieldConfig.getFieldType());
                basicField.setSortOrder(fieldConfig.getSortOrder());

                // 设置label信息并读取label的实际值作为fieldName
                String fieldName = null;
                if (labelCell != null) {
                    fieldName = getCellValueAsString(sheet, labelCell.getRowIndex(), labelCell.getColIndex());
                    basicField.setLabelPosition(labelCell.getCellPosition());
                    basicField.setLabelRowIndex(labelCell.getRowIndex());
                    basicField.setLabelColIndex(labelCell.getColIndex());
                    log.debug("从label位置 {} 读取到字段名称: '{}'", labelCell.getCellPosition(), fieldName);
                } else {
                    // 如果没有label单元格，使用字段配置中的fieldName作为备用
                    fieldName = fieldConfig.getFieldName();
                    log.warn("基础字段 {} 没有找到label单元格，使用配置中的字段名称: '{}'", baseFieldCode, fieldName);
                }
                basicField.setFieldName(fieldName);

                // 设置value信息并读取实际值
                String fieldValue = null;
                if (valueCell != null) {
                    fieldValue = getCellValueAsString(sheet, valueCell.getRowIndex(), valueCell.getColIndex());
                    basicField.setValuePosition(valueCell.getCellPosition());
                    basicField.setValueRowIndex(valueCell.getRowIndex());
                    basicField.setValueColIndex(valueCell.getColIndex());
                    log.info("从value位置 {} 读取到值: '{}'", valueCell.getCellPosition(), fieldValue);
                } else {
                    log.warn("基础字段 {} 没有找到value单元格", baseFieldCode);
                }

                basicField.setFieldValue(fieldValue);

                basicFields.add(basicField);
                count++;

                log.info("成功创建基础字段记录: 字段编码={}, 字段名='{}' (从label读取), 字段值='{}'",
                        baseFieldCode, fieldName, fieldValue);

            } else {
                log.warn("未找到字段配置: 基础字段编码={}", baseFieldCode);
            }
        }

        log.info("Sheet {} 基础字段解析完成，成功解析 {} 个字段", sheetName, count);
        result.put("basicFields", basicFields);
        result.put("basicFieldCount", count);
        return result;
    }

    /**
     * 从Excel Sheet解析表格数据
     */
    public static JSONObject parseTableDataFromSheet(Long detectionDataId, Sheet sheet, int sheetIndex,
                                        String sheetId, String sheetName,
                                        List<ExcelTemplateCell> sheetCells, List<ExcelTemplateField> sheetFields) {
        JSONObject result = new JSONObject();
        // 获取表头单元格
        List<ExcelTemplateCell> headerCells = sheetCells.stream()
                .filter(cell -> "header".equals(cell.getCellType()) || "header".equals(cell.getFieldType()))
                .sorted(Comparator.comparing(ExcelTemplateCell::getColIndex))
                .collect(Collectors.toList());

        if (headerCells.isEmpty()) {
            result.put("tableDataCount", 0);
            return result;
        }

        // 扩展表头单元格列表，处理合并单元格
        List<TableHeaderColumn> expandedHeaders = expandMergedHeaders(headerCells, sheetCells);
        log.info("原始表头数量: {}, 扩展后表头数量: {}", headerCells.size(), expandedHeaders.size());

        List<DeviceDetectionTableHeader> tableHeaders = new ArrayList<>();
        // 创建表头记录（使用扩展后的表头列表）
        for (int i = 0; i < expandedHeaders.size(); i++) {
            TableHeaderColumn headerColumn = expandedHeaders.get(i);

            DeviceDetectionTableHeader tableHeader = new DeviceDetectionTableHeader();
            tableHeader.setDetectionDataId(detectionDataId);
            tableHeader.setSheetId(sheetId);
            tableHeader.setSheetName(sheetName);
            tableHeader.setSheetIndex(sheetIndex);
            tableHeader.setHeaderName(headerColumn.getHeaderName());
            tableHeader.setHeaderCode(headerColumn.getHeaderCode());
            tableHeader.setHeaderPosition(headerColumn.getHeaderPosition());
            tableHeader.setHeaderRowIndex(headerColumn.getHeaderRowIndex());
            tableHeader.setHeaderColIndex(headerColumn.getHeaderColIndex());
            tableHeader.setDataType("text");
            tableHeader.setColumnOrder(i);

            tableHeaders.add(tableHeader);
        }

        // 从Excel中读取表格数据行
        int dataRowCount = 0;
        List<DeviceDetectionTableData> tableDataList = new ArrayList<>();
        if (!expandedHeaders.isEmpty()) {
            // 获取表头行索引，数据从下一行开始
            int headerRowIndex = expandedHeaders.get(0).getHeaderRowIndex();
            int dataStartRow = headerRowIndex + 1;
            int lastRowNum = sheet.getLastRowNum();


            // 读取数据行直到遇到空行或Sheet结束
            for (int rowIndex = dataStartRow; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null) {
                    break; // 遇到空行，停止读取
                }

                // 检查是否为空行
                boolean isEmptyRow = true;
                Map<String, Object> rowData = new HashMap<>();

                // 使用扩展后的表头列表读取数据
                for (int colIndex = 0; colIndex < expandedHeaders.size(); colIndex++) {
                    TableHeaderColumn headerColumn = expandedHeaders.get(colIndex);
                    String headerCode = headerColumn.getHeaderCode();

                    String cellValue = getCellValueAsString(sheet, rowIndex, headerColumn.getHeaderColIndex());
                    rowData.put(headerCode, cellValue);

                    if (StringUtils.hasText(cellValue)) {
                        isEmptyRow = false;
                    }
                }

                // 如果是空行，停止读取
                if (isEmptyRow) {
                    break;
                }

                // 保存数据行
                DeviceDetectionTableData tableData = new DeviceDetectionTableData();
                tableData.setDetectionDataId(detectionDataId);
                tableData.setSheetId(sheetId);
                tableData.setSheetName(sheetName);
                tableData.setSheetIndex(sheetIndex);
                tableData.setRowIndex(rowIndex);
                tableData.setRowData(JSON.toJSONString(rowData));
                tableData.setRowOrder(dataRowCount);

                tableDataList.add(tableData);
                dataRowCount++;
            }
        }
        result.put("tableHeaders", tableHeaders);
        result.put("tableDataList", tableDataList);
        result.put("tableDataCount", dataRowCount);

        return result;
    }

    /**
     * 获取单元格值作为字符串（支持合并单元格）
     */
    private static String getCellValueAsString(Sheet sheet, int rowIndex, int colIndex) {
        try {
            Row row = sheet.getRow(rowIndex);
            if (row == null) {
                return "";
            }

            Cell cell = row.getCell(colIndex);
            if (cell == null) {
                return "";
            }

            // 检查是否为合并单元格
            String mergedValue = getMergedCellValue(sheet, rowIndex, colIndex);
            if (mergedValue != null) {
                return mergedValue;
            }

            // 不是合并单元格，直接读取单元格值
            return getCellValueAsStringDirect(cell);

        } catch (Exception e) {
            log.warn("读取单元格值失败，行: {}, 列: {}", rowIndex, colIndex, e);
            return "";
        }
    }

    /**
     * 获取合并单元格的值
     * 如果指定位置是合并单元格的一部分，返回主单元格的值
     */
    private static String getMergedCellValue(Sheet sheet, int rowIndex, int colIndex) {
        try {
            // 获取所有合并区域
            int numMergedRegions = sheet.getNumMergedRegions();

            for (int i = 0; i < numMergedRegions; i++) {
                CellRangeAddress mergedRegion = sheet.getMergedRegion(i);

                // 检查指定位置是否在合并区域内
                if (mergedRegion.isInRange(rowIndex, colIndex)) {
                    // 获取合并区域的主单元格（左上角）
                    int firstRow = mergedRegion.getFirstRow();
                    int firstCol = mergedRegion.getFirstColumn();

                    Row mainRow = sheet.getRow(firstRow);
                    if (mainRow != null) {
                        Cell mainCell = mainRow.getCell(firstCol);
                        if (mainCell != null) {
                            return getCellValueAsStringDirect(mainCell);
                        }
                    }

                    // 如果主单元格为空，返回空字符串
                    return "";
                }
            }

            // 不是合并单元格，返回null让调用方继续处理
            return null;

        } catch (Exception e) {
            log.warn("获取合并单元格值失败，行: {}, 列: {}", rowIndex, colIndex, e);
            return null;
        }
    }

    /**
     * 直接获取单元格值作为字符串（不处理合并单元格）
     */
    private static String getCellValueAsStringDirect(Cell cell) {
        if (cell == null) {
            return "";
        }

        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue().trim();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        double numericValue = cell.getNumericCellValue();
                        // 如果是整数，不显示小数点
                        if (numericValue == Math.floor(numericValue)) {
                            return String.valueOf((long) numericValue);
                        } else {
                            return String.valueOf(numericValue);
                        }
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    try {
                        return cell.getStringCellValue().trim();
                    } catch (Exception e) {
                        return String.valueOf(cell.getNumericCellValue());
                    }
                case BLANK:
                case _NONE:
                default:
                    return "";
            }
        } catch (Exception e) {
            log.warn("读取单元格值失败: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 扩展合并表头，处理合并单元格的情况
     * 为每个被合并的列创建独立的表头列定义
     */
    private static List<TableHeaderColumn> expandMergedHeaders(List<ExcelTemplateCell> headerCells, List<ExcelTemplateCell> allCells) {
        List<TableHeaderColumn> expandedHeaders = new ArrayList<>();

        for (ExcelTemplateCell headerCell : headerCells) {
            // 检查是否为合并的主单元格
            if (Boolean.TRUE.equals(headerCell.getMerged()) && Boolean.TRUE.equals(headerCell.getMergeMain())) {
                // 这是合并单元格的主单元格，需要为合并区域的每一列创建表头
                int mergeColSpan = headerCell.getMergeColSpan() != null ? headerCell.getMergeColSpan() : 1;
                String baseHeaderName = StringUtils.hasText(headerCell.getContent()) ? headerCell.getContent() : "列";
                String baseHeaderCode = StringUtils.hasText(headerCell.getFieldCode()) ? headerCell.getFieldCode() : "col";

                log.info("处理合并表头单元格: {} ({}), 合并列数: {}",
                        baseHeaderName, headerCell.getCellPosition(), mergeColSpan);

                // 为合并区域的每一列创建表头列
                for (int i = 0; i < mergeColSpan; i++) {
                    int actualColIndex = headerCell.getColIndex() + i;
                    String columnName = baseHeaderName;
                    String columnCode = mergeColSpan > 1 ? baseHeaderCode + "_" + (i + 1) : baseHeaderCode;
                    String columnPosition = getColumnLabel(actualColIndex) + (headerCell.getRowIndex() + 1);

                    TableHeaderColumn headerColumn = new TableHeaderColumn();
                    headerColumn.setHeaderName(columnName);
                    headerColumn.setHeaderCode(columnCode);
                    headerColumn.setHeaderPosition(columnPosition);
                    headerColumn.setHeaderRowIndex(headerCell.getRowIndex());
                    headerColumn.setHeaderColIndex(actualColIndex);
                    headerColumn.setOriginalHeaderCell(headerCell);
                    headerColumn.setMergedColumnIndex(i);

                    expandedHeaders.add(headerColumn);

                    log.debug("创建扩展表头列: {} -> {} (列索引: {})", columnName, columnPosition, actualColIndex);
                }
            } else {
                // 普通单元格或被合并的单元格，直接创建表头列
                if (!Boolean.TRUE.equals(headerCell.getHidden())) {
                    String headerName = StringUtils.hasText(headerCell.getContent()) ? headerCell.getContent() : "列" + headerCell.getColIndex();
                    String headerCode = StringUtils.hasText(headerCell.getFieldCode()) ? headerCell.getFieldCode() : "col_" + headerCell.getColIndex();

                    TableHeaderColumn headerColumn = new TableHeaderColumn();
                    headerColumn.setHeaderName(headerName);
                    headerColumn.setHeaderCode(headerCode);
                    headerColumn.setHeaderPosition(headerCell.getCellPosition());
                    headerColumn.setHeaderRowIndex(headerCell.getRowIndex());
                    headerColumn.setHeaderColIndex(headerCell.getColIndex());
                    headerColumn.setOriginalHeaderCell(headerCell);
                    headerColumn.setMergedColumnIndex(0);

                    expandedHeaders.add(headerColumn);
                }
            }
        }

        // 按列索引排序
        expandedHeaders.sort(Comparator.comparing(TableHeaderColumn::getHeaderColIndex));

        log.info("表头扩展完成: 原始表头 {} 个，扩展后 {} 个", headerCells.size(), expandedHeaders.size());
        return expandedHeaders;
    }

    public static JSONObject parseTemplateJson(ExcelTemplate excelTemplate) {
        String sheetsConfig = excelTemplate.getSheetsConfig();
        return parseTemplateJson(sheetsConfig);
    }

    public static JSONObject parseTemplateJson(String sheetsConfig) {
        JSONObject result = new JSONObject();
        List<ExcelTemplateSheetDTO> excelTemplateSheetDTOS = JSON.parseObject(sheetsConfig, new TypeReference<List<ExcelTemplateSheetDTO>>() {
        });
        Map<String, List<ExcelTemplateCell>> cellsBySheet = new HashMap<>();
        Map<String, List<ExcelTemplateField>> fieldsBySheet = new HashMap<>();

        excelTemplateSheetDTOS.forEach(excelTemplateSheetDTO -> {
            cellsBySheet.put(excelTemplateSheetDTO.getSheetId(), excelTemplateSheetDTO.getCells());
            fieldsBySheet.put(excelTemplateSheetDTO.getSheetId(), excelTemplateSheetDTO.getFields());
        });
        result.put("cellsBySheet", cellsBySheet);
        result.put("fieldsBySheet", fieldsBySheet);

        return result;
    }

    /**
     * 根据Sheet索引和名称获取对应的SheetId
     */
    public static String getSheetIdByIndex(Map<String, List<ExcelTemplateCell>> cellsBySheet, int sheetIndex, String sheetName) {
        // 优先根据Sheet名称匹配
        for (Map.Entry<String, List<ExcelTemplateCell>> entry : cellsBySheet.entrySet()) {
            List<ExcelTemplateCell> cells = entry.getValue();
            if (!cells.isEmpty()) {
                ExcelTemplateCell firstCell = cells.get(0);
                if (sheetName.equals(firstCell.getSheetName()) ||
                        (firstCell.getSheetIndex() != null && firstCell.getSheetIndex().equals(sheetIndex))) {
                    return entry.getKey();
                }
            }
        }

        // 如果没有匹配的，返回默认值
        return cellsBySheet.containsKey("default") ? "default" :
                (cellsBySheet.isEmpty() ? "default" : cellsBySheet.keySet().iterator().next());
    }

    /**
     * 将列索引转换为Excel列标签（A, B, C, ..., AA, AB, ...）
     */
    private static String getColumnLabel(int colIndex) {
        StringBuilder result = new StringBuilder();
        while (colIndex >= 0) {
            result.insert(0, (char) ('A' + colIndex % 26));
            colIndex = colIndex / 26 - 1;
        }
        return result.toString();
    }
}
