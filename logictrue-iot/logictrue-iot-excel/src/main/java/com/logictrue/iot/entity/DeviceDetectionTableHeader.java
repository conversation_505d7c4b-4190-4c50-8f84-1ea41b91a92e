package com.logictrue.iot.entity;

import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;

/**
 * 设备检测数据表格表头实体类
 *
 */
@TableName("device_detection_table_header")
public class DeviceDetectionTableHeader {

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 检测数据主表ID */
    @TableField("detection_data_id")
    private Long detectionDataId;

    /** Sheet ID */
    @TableField("sheet_id")
    private String sheetId;

    /** Sheet名称 */
    @TableField("sheet_name")
    private String sheetName;

    /** Sheet索引 */
    @TableField("sheet_index")
    private Integer sheetIndex;

    /** 表头名称 */
    @TableField("header_name")
    private String headerName;

    /** 表头编码 */
    @TableField("header_code")
    private String headerCode;

    /** 表头位置（如A5） */
    @TableField("header_position")
    private String headerPosition;

    /** 表头行索引 */
    @TableField("header_row_index")
    private Integer headerRowIndex;

    /** 表头列索引 */
    @TableField("header_col_index")
    private Integer headerColIndex;

    /** 数据类型：text-文本，number-数字，date-日期 */
    @TableField("data_type")
    private String dataType;

    /** 列顺序 */
    @TableField("column_order")
    private Integer columnOrder;

    /** 创建时间 */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDetectionDataId() {
        return detectionDataId;
    }

    public void setDetectionDataId(Long detectionDataId) {
        this.detectionDataId = detectionDataId;
    }

    public String getSheetId() {
        return sheetId;
    }

    public void setSheetId(String sheetId) {
        this.sheetId = sheetId;
    }

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public Integer getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(Integer sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public String getHeaderName() {
        return headerName;
    }

    public void setHeaderName(String headerName) {
        this.headerName = headerName;
    }

    public String getHeaderCode() {
        return headerCode;
    }

    public void setHeaderCode(String headerCode) {
        this.headerCode = headerCode;
    }

    public String getHeaderPosition() {
        return headerPosition;
    }

    public void setHeaderPosition(String headerPosition) {
        this.headerPosition = headerPosition;
    }

    public Integer getHeaderRowIndex() {
        return headerRowIndex;
    }

    public void setHeaderRowIndex(Integer headerRowIndex) {
        this.headerRowIndex = headerRowIndex;
    }

    public Integer getHeaderColIndex() {
        return headerColIndex;
    }

    public void setHeaderColIndex(Integer headerColIndex) {
        this.headerColIndex = headerColIndex;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public Integer getColumnOrder() {
        return columnOrder;
    }

    public void setColumnOrder(Integer columnOrder) {
        this.columnOrder = columnOrder;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
}
