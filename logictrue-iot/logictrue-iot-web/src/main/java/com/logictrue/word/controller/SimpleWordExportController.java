package com.logictrue.word.controller;

import com.logictrue.common.core.web.domain.AjaxResult;
import com.logictrue.word.dto.SimpleWordExportRequest;
import com.logictrue.word.service.SimpleWordExportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 简单Word导出控制器
 */
@Slf4j
@RestController
@RequestMapping("/simpleWordExport")
@RequiredArgsConstructor
@Api(value = "简单Word导出", tags = "简单Word导出")
public class SimpleWordExportController {

    private final SimpleWordExportService simpleWordExportService;

    /**
     * 导出简单Word文档
     */
    @PostMapping("/export")
    @ApiOperation(value = "导出简单Word文档")
    public ResponseEntity<byte[]> exportSimpleWord(@RequestBody SimpleWordExportRequest request) {
        try {
            log.info("接收到简单Word导出请求，标题: {}", request.getTitle());

            // 记录请求数据统计
            if (request.getJsonContent() != null) {
                ValidationResult stats = validateJsonContent(request.getJsonContent());
                if (stats.isValid()) {
                    log.info("JSON内容统计 - 总节点: {}, 图片数量: {}, 文本节点: {}",
                            stats.getTotalNodes(), stats.getImageNodes(), stats.getTextNodes());
                } else {
                    log.warn("JSON内容验证失败: {}", stats.getErrorMessage());
                    return ResponseEntity.badRequest()
                            .body(("JSON内容验证失败: " + stats.getErrorMessage()).getBytes(StandardCharsets.UTF_8));
                }
            } else {
                log.warn("请求中没有JSON内容数据");
                return ResponseEntity.badRequest()
                        .body("请求中没有JSON内容数据".getBytes(StandardCharsets.UTF_8));
            }

            // 导出Word文档
            byte[] wordBytes = simpleWordExportService.exportSimpleWord(request);

            // 生成文件名
            String fileName = generateFileName(request.getTitle());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", fileName);
            headers.setContentLength(wordBytes.length);

            log.info("简单Word文档导出成功，文件名: {}, 大小: {} bytes", fileName, wordBytes.length);

            return new ResponseEntity<>(wordBytes, headers, HttpStatus.OK);

        } catch (IOException e) {
            log.error("导出Word文档失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("导出失败: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("处理导出请求时发生未知错误", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(("系统错误: " + e.getMessage()).getBytes(StandardCharsets.UTF_8));
        }
    }

    /**
     * 预览简单Word文档（返回HTML预览）
     */
    @PostMapping("/preview")
    @ApiOperation(value = "预览简单Word文档")
    public ResponseEntity<String> previewSimpleWord(@RequestBody SimpleWordExportRequest request) {
        try {
            log.info("接收到简单Word预览请求，标题: {}", request.getTitle());

            // 生成HTML预览
            String htmlPreview = generateHtmlPreview(request);

            log.info("简单Word预览生成成功");

            return ResponseEntity.ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlPreview);

        } catch (Exception e) {
            log.error("生成简单Word预览失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("<html><body><h1>预览生成失败</h1><p>" + e.getMessage() + "</p></body></html>");
        }
    }

    /**
     * 验证简单Word导出请求
     */
    @PostMapping("/validate")
    @ApiOperation(value = "验证简单Word导出请求")
    public AjaxResult validateExportRequest(@RequestBody SimpleWordExportRequest request) {
        try {
            log.info("验证简单Word导出请求，标题: {}", request.getTitle());

            // 验证基本参数
            if (request.getTitle() == null || request.getTitle().trim().isEmpty()) {
                return AjaxResult.error("文档标题不能为空");
            }

            // 验证JSON内容
            if (request.getJsonContent() == null || request.getJsonContent().isEmpty()) {
                return AjaxResult.error("文档内容不能为空");
            }

            // 验证JSON内容结构并统计信息
            ValidationResult validationResult = validateJsonContent(request.getJsonContent());
            if (!validationResult.isValid()) {
                return AjaxResult.error("JSON内容格式错误: " + validationResult.getErrorMessage());
            }

            log.info("JSON内容验证通过 - 节点总数: {}, 图片数量: {}, 文本节点: {}",
                    validationResult.getTotalNodes(),
                    validationResult.getImageNodes(),
                    validationResult.getTextNodes());

            // 验证页面设置
            if (request.getPageSettings() != null) {
                SimpleWordExportRequest.PageSettings settings = request.getPageSettings();

                if (settings.getMarginTop() != null && (settings.getMarginTop() < 0 || settings.getMarginTop() > 100)) {
                    return AjaxResult.error("上边距必须在0-100毫米之间");
                }

                if (settings.getMarginBottom() != null && (settings.getMarginBottom() < 0 || settings.getMarginBottom() > 100)) {
                    return AjaxResult.error("下边距必须在0-100毫米之间");
                }

                if (settings.getMarginLeft() != null && (settings.getMarginLeft() < 0 || settings.getMarginLeft() > 100)) {
                    return AjaxResult.error("左边距必须在0-100毫米之间");
                }

                if (settings.getMarginRight() != null && (settings.getMarginRight() < 0 || settings.getMarginRight() > 100)) {
                    return AjaxResult.error("右边距必须在0-100毫米之间");
                }

                if (settings.getHeaderFooterFontSize() != null && (settings.getHeaderFooterFontSize() < 8 || settings.getHeaderFooterFontSize() > 72)) {
                    return AjaxResult.error("页眉页脚字体大小必须在8-72之间");
                }
            }

            log.info("简单Word导出请求验证通过");
            return AjaxResult.success("请求验证通过");

        } catch (Exception e) {
            log.error("验证简单Word导出请求时发生错误", e);
            return AjaxResult.error("验证失败：" + e.getMessage());
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String title) {
        try {
            String baseFileName = (title != null && !title.trim().isEmpty())
                    ? title.trim()
                    : "简单Word文档";

            // 添加时间戳
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = baseFileName + "_" + timestamp + ".docx";

            // URL编码文件名以支持中文
            return URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString())
                    .replaceAll("\\+", "%20");

        } catch (Exception e) {
            log.warn("生成文件名失败，使用默认文件名", e);
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            return "simple_word_export_" + timestamp + ".docx";
        }
    }

    /**
     * 生成HTML预览
     */
    private String generateHtmlPreview(SimpleWordExportRequest request) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>");
        html.append("<html>");
        html.append("<head>");
        html.append("<meta charset='UTF-8'>");
        html.append("<title>").append(request.getTitle() != null ? request.getTitle() : "文档预览").append("</title>");
        html.append("<style>");
        html.append("body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; line-height: 1.6; }");
        html.append(".page { background: white; padding: 40px; box-shadow: 0 0 10px rgba(0,0,0,0.1); margin: 20px auto; max-width: 800px; }");
        html.append(".header { text-align: center; border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 20px; }");
        html.append(".footer { text-align: center; border-top: 1px solid #eee; padding-top: 10px; margin-top: 20px; }");
        html.append(".content { min-height: 400px; }");
        html.append("@media print { body { margin: 0; } .page { box-shadow: none; margin: 0; } }");
        html.append("</style>");
        html.append("</head>");
        html.append("<body>");

        html.append("<div class='page'>");

        // 页眉
        if (request.getPageSettings() != null &&
            request.getPageSettings().getShowHeader() &&
            request.getPageSettings().getHeaderText() != null &&
            !request.getPageSettings().getHeaderText().trim().isEmpty()) {

            html.append("<div class='header' style='");
            if (request.getPageSettings().getHeaderFooterFont() != null) {
                html.append("font-family: ").append(request.getPageSettings().getHeaderFooterFont()).append(";");
            }
            if (request.getPageSettings().getHeaderFooterFontSize() != null) {
                html.append("font-size: ").append(request.getPageSettings().getHeaderFooterFontSize()).append("px;");
            }
            if (request.getPageSettings().getHeaderFooterColor() != null) {
                html.append("color: ").append(request.getPageSettings().getHeaderFooterColor()).append(";");
            }
            html.append("'>");
            html.append(request.getPageSettings().getHeaderText());
            html.append("</div>");
        }

        // 主要内容
        html.append("<div class='content'>");
        html.append("</div>");

        // 页脚
        if (request.getPageSettings() != null &&
            (request.getPageSettings().getShowFooter() || request.getPageSettings().getShowPageNumber())) {

            html.append("<div class='footer' style='");
            if (request.getPageSettings().getHeaderFooterFont() != null) {
                html.append("font-family: ").append(request.getPageSettings().getHeaderFooterFont()).append(";");
            }
            if (request.getPageSettings().getHeaderFooterFontSize() != null) {
                html.append("font-size: ").append(request.getPageSettings().getHeaderFooterFontSize()).append("px;");
            }
            if (request.getPageSettings().getHeaderFooterColor() != null) {
                html.append("color: ").append(request.getPageSettings().getHeaderFooterColor()).append(";");
            }
            html.append("'>");

            if (request.getPageSettings().getShowFooter() &&
                request.getPageSettings().getFooterText() != null &&
                !request.getPageSettings().getFooterText().trim().isEmpty()) {
                html.append(request.getPageSettings().getFooterText());
                if (request.getPageSettings().getShowPageNumber()) {
                    html.append(" ");
                }
            }

            if (request.getPageSettings().getShowPageNumber()) {
                html.append("第 1 页");
            }

            html.append("</div>");
        }

        html.append("</div>");
        html.append("</body>");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 验证JSON内容结构
     */
    private ValidationResult validateJsonContent(java.util.List<SimpleWordExportRequest.ContentNode> jsonContent) {
        try {
            int totalNodes = 0;
            int imageNodes = 0;
            int textNodes = 0;

            for (SimpleWordExportRequest.ContentNode node : jsonContent) {
                ValidationResult result = validateNode(node, "root");
                if (!result.isValid()) {
                    return result;
                }
                totalNodes += result.getTotalNodes();
                imageNodes += result.getImageNodes();
                textNodes += result.getTextNodes();
            }

            return ValidationResult.success(totalNodes, imageNodes, textNodes);

        } catch (Exception e) {
            log.error("验证JSON内容时发生错误", e);
            return ValidationResult.error("JSON内容验证失败: " + e.getMessage());
        }
    }

    /**
     * 递归验证单个节点
     */
    private ValidationResult validateNode(SimpleWordExportRequest.ContentNode node, String path) {
        if (node == null) {
            return ValidationResult.error("节点不能为空: " + path);
        }

        if (node.getType() == null || node.getType().trim().isEmpty()) {
            return ValidationResult.error("节点类型不能为空: " + path);
        }

        int totalNodes = 1;
        int imageNodes = 0;
        int textNodes = 0;

        // 验证图片节点
        if ("img".equals(node.getType())) {
            imageNodes = 1;

            if (node.getAttributes() == null || node.getAttributes().get("src") == null) {
                return ValidationResult.error("图片节点缺少src属性: " + path);
            }

            String src = node.getAttributes().get("src");
            if (!src.startsWith("data:image/")) {
                return ValidationResult.error("图片src格式不正确，必须是Base64格式: " + path);
            }

            // 验证Base64数据完整性
            if (!src.contains(",") || src.split(",").length != 2) {
                return ValidationResult.error("Base64图片数据格式不正确: " + path);
            }

            // 检查图片大小限制（Base64数据大小）
            String base64Data = src.split(",")[1];
            long estimatedSize = (base64Data.length() * 3L) / 4L; // Base64解码后的大小估算
            long maxSize = 10 * 1024 * 1024; // 10MB限制

            if (estimatedSize > maxSize) {
                return ValidationResult.error("图片文件过大，超过10MB限制: " + path);
            }

            log.debug("验证图片节点通过 - 路径: {}, 估算大小: {}KB", path, estimatedSize / 1024);

        } else if (node.getContent() != null && !node.getContent().trim().isEmpty()) {
            textNodes = 1;
        }

        // 递归验证子节点
        if (node.getChildren() != null) {
            for (int i = 0; i < node.getChildren().size(); i++) {
                ValidationResult childResult = validateNode(node.getChildren().get(i), path + ".children[" + i + "]");
                if (!childResult.isValid()) {
                    return childResult;
                }
                totalNodes += childResult.getTotalNodes();
                imageNodes += childResult.getImageNodes();
                textNodes += childResult.getTextNodes();
            }
        }

        return ValidationResult.success(totalNodes, imageNodes, textNodes);
    }

    /**
     * 验证结果类
     */
    private static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final int totalNodes;
        private final int imageNodes;
        private final int textNodes;

        private ValidationResult(boolean valid, String errorMessage, int totalNodes, int imageNodes, int textNodes) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.totalNodes = totalNodes;
            this.imageNodes = imageNodes;
            this.textNodes = textNodes;
        }

        public static ValidationResult success(int totalNodes, int imageNodes, int textNodes) {
            return new ValidationResult(true, null, totalNodes, imageNodes, textNodes);
        }

        public static ValidationResult error(String errorMessage) {
            return new ValidationResult(false, errorMessage, 0, 0, 0);
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public int getTotalNodes() {
            return totalNodes;
        }

        public int getImageNodes() {
            return imageNodes;
        }

        public int getTextNodes() {
            return textNodes;
        }
    }
}
