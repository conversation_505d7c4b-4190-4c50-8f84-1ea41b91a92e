package com.logictrue.word.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.word.entity.DesignWord;

import java.util.List;

/**
 * 检验记录表设计Service接口
 */
public interface IDesignWordService {

    /**
     * 查询检验记录表设计
     *
     * @param id 检验记录表设计主键
     * @return 检验记录表设计
     */
    DesignWord selectDesignWordById(Long id);

    /**
     * 查询检验记录表设计列表
     *
     * @param designWord 检验记录表设计
     * @return 检验记录表设计集合
     */
    List<DesignWord> selectDesignWordList(DesignWord designWord);

    /**
     * 分页查询检验记录表设计列表
     *
     * @param page 分页对象
     * @param designWord 检验记录表设计
     * @return 检验记录表设计集合
     */
    IPage<DesignWord> selectDesignWordPage(Page<DesignWord> page, DesignWord designWord);

    /**
     * 根据车辆ID查询检验记录表设计
     *
     * @param carId 车辆ID
     * @return 检验记录表设计
     */
    DesignWord selectDesignWordByCarId(String carId);

    /**
     * 新增检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    int insertDesignWord(DesignWord designWord);

    /**
     * 修改检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    int updateDesignWord(DesignWord designWord);

    /**
     * 批量删除检验记录表设计
     *
     * @param ids 需要删除的检验记录表设计主键集合
     * @return 结果
     */
    int deleteDesignWordByIds(Long[] ids);

    /**
     * 删除检验记录表设计信息
     *
     * @param id 检验记录表设计主键
     * @return 结果
     */
    int deleteDesignWordById(Long id);

    /**
     * 保存或更新检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    int saveOrUpdateDesignWord(DesignWord designWord);

    /**
     * 根据车辆ID查询所有页面
     *
     * @param carId 车辆ID
     * @return 检验记录表设计列表
     */
    List<DesignWord> selectDesignWordPagesByCarId(String carId);

    /**
     * 根据车辆ID和页面顺序查询特定页面
     *
     * @param carId 车辆ID
     * @param pageOrder 页面顺序
     * @return 检验记录表设计
     */
    DesignWord selectDesignWordByCarIdAndPage(String carId, Integer pageOrder);

    /**
     * 根据车辆ID查询当前活动页面
     *
     * @param carId 车辆ID
     * @return 检验记录表设计
     */
    DesignWord selectActiveDesignWordByCarId(String carId);

    /**
     * 更新车辆的总页数
     *
     * @param carId 车辆ID
     * @param totalPages 总页数
     * @return 结果
     */
    int updateTotalPagesByCarId(String carId, Integer totalPages);

    /**
     * 设置活动页面
     *
     * @param carId 车辆ID
     * @param pageOrder 页面顺序
     * @return 结果
     */
    int setActivePage(String carId, Integer pageOrder);
}
