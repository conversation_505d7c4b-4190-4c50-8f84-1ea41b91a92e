package com.logictrue.word.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.logictrue.word.entity.DesignWord;
import com.logictrue.word.mapper.DesignWordMapper;
import com.logictrue.word.service.IDesignWordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 检验记录表设计Service业务层处理
 */
@Service
public class DesignWordServiceImpl implements IDesignWordService {

    @Autowired
    private DesignWordMapper designWordMapper;

    /**
     * 查询检验记录表设计
     *
     * @param id 检验记录表设计主键
     * @return 检验记录表设计
     */
    @Override
    public DesignWord selectDesignWordById(Long id) {
        return designWordMapper.selectById(id);
    }

    /**
     * 查询检验记录表设计列表
     *
     * @param designWord 检验记录表设计
     * @return 检验记录表设计
     */
    @Override
    public List<DesignWord> selectDesignWordList(DesignWord designWord) {
        return designWordMapper.selectDesignWordList(designWord);
    }

    /**
     * 分页查询检验记录表设计列表
     *
     * @param page 分页对象
     * @param designWord 检验记录表设计
     * @return 检验记录表设计集合
     */
    @Override
    public IPage<DesignWord> selectDesignWordPage(Page<DesignWord> page, DesignWord designWord) {
        return designWordMapper.selectDesignWordPage(page, designWord);
    }

    /**
     * 根据车辆ID查询检验记录表设计
     *
     * @param carId 车辆ID
     * @return 检验记录表设计
     */
    @Override
    public DesignWord selectDesignWordByCarId(String carId) {
        return designWordMapper.selectDesignWordByCarId(carId);
    }

    /**
     * 新增检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    @Override
    public int insertDesignWord(DesignWord designWord) {
        return designWordMapper.insertDesignWord(designWord);
    }

    /**
     * 修改检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    @Override
    public int updateDesignWord(DesignWord designWord) {
        return designWordMapper.updateDesignWord(designWord);
    }

    /**
     * 批量删除检验记录表设计
     *
     * @param ids 需要删除的检验记录表设计主键
     * @return 结果
     */
    @Override
    public int deleteDesignWordByIds(Long[] ids) {
        return designWordMapper.deleteDesignWordByIds(ids);
    }

    /**
     * 删除检验记录表设计信息
     *
     * @param id 检验记录表设计主键
     * @return 结果
     */
    @Override
    public int deleteDesignWordById(Long id) {
        return designWordMapper.deleteDesignWordById(id);
    }

    /**
     * 保存或更新检验记录表设计
     *
     * @param designWord 检验记录表设计
     * @return 结果
     */
    @Override
    public int saveOrUpdateDesignWord(DesignWord designWord) {
        if (designWord.getId() != null) {
            return updateDesignWord(designWord);
        } else {
            return insertDesignWord(designWord);
        }
    }

    /**
     * 根据车辆ID查询所有页面
     *
     * @param carId 车辆ID
     * @return 检验记录表设计列表
     */
    @Override
    public List<DesignWord> selectDesignWordPagesByCarId(String carId) {
        return designWordMapper.selectDesignWordPagesByCarId(carId);
    }

    /**
     * 根据车辆ID和页面顺序查询特定页面
     *
     * @param carId 车辆ID
     * @param pageOrder 页面顺序
     * @return 检验记录表设计
     */
    @Override
    public DesignWord selectDesignWordByCarIdAndPage(String carId, Integer pageOrder) {
        return designWordMapper.selectDesignWordByCarIdAndPage(carId, pageOrder);
    }

    /**
     * 根据车辆ID查询当前活动页面
     *
     * @param carId 车辆ID
     * @return 检验记录表设计
     */
    @Override
    public DesignWord selectActiveDesignWordByCarId(String carId) {
        return designWordMapper.selectActiveDesignWordByCarId(carId);
    }

    /**
     * 更新车辆的总页数
     *
     * @param carId 车辆ID
     * @param totalPages 总页数
     * @return 结果
     */
    @Override
    public int updateTotalPagesByCarId(String carId, Integer totalPages) {
        return designWordMapper.updateTotalPagesByCarId(carId, totalPages);
    }

    /**
     * 设置活动页面
     *
     * @param carId 车辆ID
     * @param pageOrder 页面顺序
     * @return 结果
     */
    @Override
    public int setActivePage(String carId, Integer pageOrder) {
        return designWordMapper.setActivePage(carId, pageOrder);
    }
}
