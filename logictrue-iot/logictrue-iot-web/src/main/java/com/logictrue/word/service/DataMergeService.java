package com.logictrue.word.service;

import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据合并服务
 * 支持根据指定字段对数据进行分组合并
 */
@Service
public class DataMergeService {

    /**
     * 合并配置类
     */
    @Data
    public static class MergeConfig {
        /**
         * 分组字段名（用于分组的字段）
         */
        private String groupByField;
        
        /**
         * 需要合并的字段名（相同分组内该字段的值会被合并）
         */
        private String mergeField;
        
        /**
         * 合并分隔符（默认为换行符）
         */
        private String mergeSeparator = "\n";
        
        /**
         * 字段输出顺序（指定输出字段的顺序）
         */
        private List<String> fieldOrder;
        
        /**
         * 是否去除数据库字段key（true: 只返回值的List，false: 返回Map）
         */
        private boolean removeKeys = true;
    }

    /**
     * 根据配置合并数据
     * 
     * @param dbResults 数据库查询结果
     * @param config 合并配置
     * @return 合并后的数据
     */
    public List<List<Object>> mergeData(List<Map<String, Object>> dbResults, MergeConfig config) {
        if (dbResults == null || dbResults.isEmpty()) {
            return new ArrayList<>();
        }
        
        if (config == null || config.getGroupByField() == null || config.getMergeField() == null) {
            throw new IllegalArgumentException("合并配置不能为空，必须指定分组字段和合并字段");
        }
        
        // 按分组字段分组
        Map<String, List<Map<String, Object>>> groupedData = dbResults.stream()
                .collect(Collectors.groupingBy(record -> 
                    String.valueOf(record.get(config.getGroupByField()))));
        
        List<List<Object>> mergedResults = new ArrayList<>();
        
        // 处理每个分组
        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
            String groupKey = entry.getKey();
            List<Map<String, Object>> records = entry.getValue();
            
            if (records.isEmpty()) {
                continue;
            }
            
            // 获取第一条记录作为基础数据
            Map<String, Object> firstRecord = records.get(0);
            
            // 合并指定字段的值
            String mergedValue = records.stream()
                    .map(record -> String.valueOf(record.get(config.getMergeField())))
                    .filter(Objects::nonNull)
                    .filter(value -> !"null".equals(value))
                    .collect(Collectors.joining(config.getMergeSeparator()));
            
            // 创建合并后的记录
            Map<String, Object> mergedRecord = new HashMap<>(firstRecord);
            mergedRecord.put(config.getMergeField(), mergedValue);
            
            // 根据配置决定输出格式
            if (config.isRemoveKeys()) {
                List<Object> valueList = convertToValueList(mergedRecord, config.getFieldOrder());
                mergedResults.add(valueList);
            } else {
                // 如果不移除key，则返回Map格式（这里转换为List<Object>以保持接口一致性）
                List<Object> mapAsList = new ArrayList<>();
                mapAsList.add(mergedRecord);
                mergedResults.add(mapAsList);
            }
        }
        
        return mergedResults;
    }

    /**
     * 根据字段顺序将Map转换为值列表
     * 
     * @param record 记录Map
     * @param fieldOrder 字段顺序
     * @return 按顺序排列的值列表
     */
    private List<Object> convertToValueList(Map<String, Object> record, List<String> fieldOrder) {
        List<Object> valueList = new ArrayList<>();
        
        if (fieldOrder != null && !fieldOrder.isEmpty()) {
            // 按指定顺序添加字段值
            for (String fieldName : fieldOrder) {
                Object value = record.get(fieldName);
                valueList.add(value != null ? value : "");
            }
        } else {
            // 如果没有指定顺序，按Map的自然顺序添加
            valueList.addAll(record.values());
        }
        
        return valueList;
    }

    /**
     * 创建默认的合并配置
     * 
     * @param groupByField 分组字段
     * @param mergeField 合并字段
     * @param fieldOrder 字段顺序
     * @return 合并配置
     */
    public static MergeConfig createDefaultConfig(String groupByField, String mergeField, List<String> fieldOrder) {
        MergeConfig config = new MergeConfig();
        config.setGroupByField(groupByField);
        config.setMergeField(mergeField);
        config.setFieldOrder(fieldOrder);
        config.setMergeSeparator("\n");
        config.setRemoveKeys(true);
        return config;
    }

    /**
     * 为检验记录表创建专用的合并配置
     * 
     * @return 检验记录表的合并配置
     */
    public static MergeConfig createInspectionRecordConfig() {
        MergeConfig config = new MergeConfig();
        config.setGroupByField("check_name");
        config.setMergeField("check_content");
        config.setMergeSeparator("\n");
        config.setRemoveKeys(true);
        
        // 设置字段输出顺序：[检查工序名称, 检查项目及技术条件, 实际检查结果, 月, 日, 操作员, 班组长, 检验员]
        List<String> fieldOrder = Arrays.asList(
            "check_name",           // 检查工序名称
            "check_content",        // 检查项目及技术条件（合并后）
            "result",              // 实际检查结果
            "month_str",           // 月
            "day_str",             // 日
            "check_user_name",     // 操作员
            "bzz",                 // 班组长
            "jyy"                  // 检验员
        );
        config.setFieldOrder(fieldOrder);
        
        return config;
    }

    /**
     * 批量合并多个数据集
     * 
     * @param dataSetList 多个数据集
     * @param config 合并配置
     * @return 合并后的数据集列表
     */
    public List<List<List<Object>>> batchMergeData(List<List<Map<String, Object>>> dataSetList, MergeConfig config) {
        List<List<List<Object>>> results = new ArrayList<>();
        
        for (List<Map<String, Object>> dataSet : dataSetList) {
            List<List<Object>> mergedData = mergeData(dataSet, config);
            results.add(mergedData);
        }
        
        return results;
    }

    /**
     * 验证合并配置的有效性
     * 
     * @param config 合并配置
     * @param sampleData 样本数据（用于验证字段是否存在）
     * @return 验证结果
     */
    public boolean validateConfig(MergeConfig config, List<Map<String, Object>> sampleData) {
        if (config == null) {
            return false;
        }
        
        if (config.getGroupByField() == null || config.getMergeField() == null) {
            return false;
        }
        
        if (sampleData == null || sampleData.isEmpty()) {
            return true; // 没有样本数据时认为配置有效
        }
        
        // 检查样本数据中是否包含指定的字段
        Map<String, Object> sampleRecord = sampleData.get(0);
        boolean hasGroupField = sampleRecord.containsKey(config.getGroupByField());
        boolean hasMergeField = sampleRecord.containsKey(config.getMergeField());
        
        if (!hasGroupField || !hasMergeField) {
            return false;
        }
        
        // 检查字段顺序中的字段是否都存在
        if (config.getFieldOrder() != null) {
            for (String fieldName : config.getFieldOrder()) {
                if (!sampleRecord.containsKey(fieldName)) {
                    return false;
                }
            }
        }
        
        return true;
    }
}
