package com.logictrue.iot.config;

import com.logictrue.iot.interceptor.MagicWebInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 通用映射配置
 *
 * <AUTHOR>
 */
@Configuration
public class MagicWebConfig implements WebMvcConfigurer
{
    @Autowired
    private MagicWebInterceptor magicWebInterceptor;

    /**
     * 添加拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 添加Magic Web拦截器，拦截/magic/web/index.html路径
        registry.addInterceptor(magicWebInterceptor)
                .addPathPatterns("/magic/web/index.html");
    }

}
