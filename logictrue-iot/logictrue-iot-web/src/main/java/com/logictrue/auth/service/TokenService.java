/*
 * Copyright (c) 2021 伦图科技（长沙）有限公司. All rights reserved.
 */

package com.logictrue.auth.service;

import com.alibaba.fastjson2.JSON;
import com.logictrue.auth.entity.LoginUser;
import com.logictrue.auth.mapper.TokenMapper;
import com.logictrue.common.core.constant.CacheConstants;
import com.logictrue.common.core.constant.Constants;
import com.logictrue.common.core.utils.IdUtils;
import com.logictrue.common.core.utils.SecurityUtils;
import com.logictrue.common.core.utils.ServletUtils;
import com.logictrue.common.core.utils.StringUtils;
import com.logictrue.common.core.utils.ip.IpUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {
    protected static final long MILLIS_SECOND = 1000;
    private final static long EXPIRE_TIME = Constants.TOKEN_EXPIRE * 60;
    private final static String ACCESS_TOKEN = CacheConstants.LOGIN_TOKEN_KEY;

    private final static String FORCE_OFFLINE_MESSAGE_FOR_LOGIN = "您的账号被在其他终端被登录，如果不是本人登录请及时修改密码!";
    private final static String FORCE_OFFLINE_MESSAGE_FOR_ADMIN = "您的账号被管理员强制下线！";

    //设置时间的格式
    private final static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 线程临时变量
    private static final ThreadLocal<LoginUser> temporaryUserInfo = new ThreadLocal<>();
    @Autowired
    private RedisService redisService;

    @Autowired
    private TokenMapper tokenMapper;

    @Autowired
    private HttpServletRequest request;


    @Value("${lt.allowRepeatLogin:false}")
    private boolean allowRepeatLogin;


    public HttpServletRequest getRequest() {
        return request;
    }

    public void setTemporaryUserInfo(LoginUser temporaryUserInfo) {
        TokenService.temporaryUserInfo.set(temporaryUserInfo);
    }

    public void removeTemporaryUserInfo() {
        TokenService.temporaryUserInfo.remove();
    }


    public boolean checkUserIsLogin(LoginUser user) {
        // 如果允许重复登录则直接返回 false 不进行判断
        if (Boolean.TRUE.equals(allowRepeatLogin)) {
            return false;
        }
        String loginTerminalType = null;
        Enumeration<String> terminalType = request.getHeaders("terminalType");
        if (terminalType.hasMoreElements()) {
            loginTerminalType = terminalType.nextElement();
        }

        if (StringUtils.isNotEmpty(loginTerminalType)) {
            Long userId = user.getSysUser().getUserId();
            String loginTypeToken = String.format("%s%s:%s", CacheConstants.USER_TOKEN_KEY, loginTerminalType, userId);
            // 登录终端
            if (redisService.hasKey(loginTypeToken)) {
                return redisService.hasKey(ACCESS_TOKEN + redisService.<String>getCacheObject(loginTypeToken));
            }
        }

        return false;
    }

    /**
     * 创建令牌
     */
    public Map<String, Object> createToken(LoginUser loginUser) {
        String token = null;
        if (Objects.nonNull(loginUser.getLoginClient())
                && loginUser.getLoginClient().equals(2)) {
            Object cacheObject = null;

            //判断loginUser的token是否存在
            // 生成token
            token = IdUtils.fastUUID();
            //存入token
            tokenMapper.updateTokenById(token, loginUser.getSysUser().getUserId());
            loginUser.setToken(token);

        } else {
            token = IdUtils.fastUUID();
            loginUser.setToken(token);
        }
        String loginTerminalType = null;
        Enumeration<String> terminalType = request.getHeaders("terminalType");
        if (terminalType.hasMoreElements()) {
            loginTerminalType = terminalType.nextElement();
        }

        //存入最后登录时间
        Date date = new Date();
        loginUser.setUserid(loginUser.getSysUser().getUserId());
        loginUser.setUsername(loginUser.getSysUser().getUserName());
        loginUser.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));

        tokenMapper.updateLoginToIpMacDateById(date, loginUser.getUserid(), loginUser.getIpaddr(), loginUser.getMacaddr());

        refreshToken(loginUser);

        // 保存或更新用户token
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("access_token", token);
        map.put("expires_in", EXPIRE_TIME);
        redisService.setCacheObject(ACCESS_TOKEN + token, loginUser, EXPIRE_TIME, TimeUnit.SECONDS);
        if (StringUtils.isNotEmpty(loginTerminalType) && !allowRepeatLogin) {
            String loginTypeToken = CacheConstants.USER_TOKEN_KEY + loginTerminalType + ":" + loginUser.getUserid();
            // 登录终端
            if (redisService.hasKey(loginTypeToken)) {
                redisService.deleteObject(ACCESS_TOKEN + redisService.<String>getCacheObject(loginTypeToken));
            }
            redisService.deleteObject(loginTypeToken);
            redisService.setCacheObject(loginTypeToken, token, EXPIRE_TIME, TimeUnit.SECONDS);
        }
        return map;
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser() {
        LoginUser loginUser = getLoginUser(ServletUtils.getRequest());
        if (Objects.nonNull(loginUser)) {
            return loginUser;
        }
        if (TokenService.temporaryUserInfo.get() != null) {
            return TokenService.temporaryUserInfo.get();
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser(LoginUser loginUser) {
        if (StringUtils.isNotNull(loginUser) && StringUtils.isNotEmpty(loginUser.getToken())) {
            refreshToken(loginUser);
        }
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(HttpServletRequest request) {
        if (Objects.isNull(request)) {
            return null;
        }
        // 获取请求携带的令牌
        String token = SecurityUtils.getToken(request);
        return getLoginUser(token);
    }

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            Object cacheObject = redisService.getCacheObject(userKey);
            LoginUser user = JSON.parseObject(JSON.toJSONString(cacheObject), LoginUser.class);
            return user;
        }
        return null;
    }

    public void delLoginUser(String token) {
        if (StringUtils.isNotEmpty(token)) {
            String userKey = getTokenKey(token);
            Object cacheObject = redisService.getCacheObject(userKey);
            LoginUser user = JSON.parseObject(JSON.toJSONString(cacheObject), LoginUser.class);
            redisService.deleteObject(userKey);
            String loginTerminalType = null;
            Enumeration<String> terminalType = request.getHeaders("terminalType");
            if (terminalType.hasMoreElements()) {
                loginTerminalType = terminalType.nextElement();
            }
            if (StringUtils.isNotEmpty(loginTerminalType)) {
                String loginTypeToken = CacheConstants.USER_TOKEN_KEY + loginTerminalType + ":" + user.getUserid();
                redisService.deleteObject(loginTypeToken);
            }
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken(LoginUser loginUser) {
        loginUser.setLoginTime(System.currentTimeMillis());
        loginUser.setExpireTime(System.currentTimeMillis() + EXPIRE_TIME * MILLIS_SECOND);
        // 根据uuid将loginUser缓存
        String userKey = getTokenKey(loginUser.getToken());
        redisService.setCacheObject(userKey, loginUser, EXPIRE_TIME, TimeUnit.SECONDS);
    }

    private String getTokenKey(String token) {
        return ACCESS_TOKEN + token;
    }
}
