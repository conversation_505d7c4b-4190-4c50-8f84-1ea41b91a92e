package com.logictrue.auth.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

@Mapper
public interface TokenMapper extends BaseMapper {


      void updateTokenById( @Param("token") String token, @Param("userId") Long userId);

      void updateLoginDateById(@Param("loginDate") Date loginDate, @Param("userId") Long userId);


      void updateLoginToIpMacDateById(@Param("loginDate") Date loginDate, @Param("userId") Long userId,  @Param("login_ip") String login_ip,@Param("login_mac") String login_mac);

}
