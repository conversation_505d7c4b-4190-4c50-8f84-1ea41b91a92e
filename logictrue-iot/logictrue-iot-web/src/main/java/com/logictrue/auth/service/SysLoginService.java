package com.logictrue.auth.service;

//import com.alibaba.fastjson.JSONObject;
//import com.logictrue.auth.config.WxConnectConfig;
import com.logictrue.auth.entity.LoginUser;
import com.logictrue.auth.entity.SysUser;
import com.logictrue.auth.form.LoginBody;
import com.logictrue.common.core.constant.Constants;
import com.logictrue.common.core.constant.SecurityConstants;
import com.logictrue.common.core.constant.UserConstants;
import com.logictrue.common.core.domain.R;
import com.logictrue.common.core.enums.UserStatus;
import com.logictrue.common.core.exception.BaseException;
import com.logictrue.common.core.utils.SecurityUtils;
import com.logictrue.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {

    @Autowired
    private UserService userService;


    /*@Autowired
    private WxConnectConfig wxConnectConfig;*/

    @Autowired
    private HttpServletRequest request;




    public R<LoginUser> getUserInfoByName(String username, String type) {
        return userService.getUserInfo(username, type);
    }

    /**
     * 登录
     */
    public LoginUser login(String username, String password,String type) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new BaseException("用户/密码必须填写");
        }
        // 密码如果不在指定范围内 错误
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new BaseException("用户密码不在指定范围");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new BaseException("用户名不在指定范围");
        }
        // 查询用户信息
        R<LoginUser> userResult = this.getUserInfoByName(username,type);

        if (R.FAIL == userResult.getCode()) {
            throw new BaseException(userResult.getMsg());
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            throw new BaseException("登录用户：" + username + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            throw new BaseException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new BaseException("对不起，您的账号：" + username + " 已停用");
        }
        if (!SecurityUtils.matchesPassword(password, user.getPassword())) {
            throw new BaseException("用户不存在/密码错误");
        }
        // 刷新用户登录 ip mac 等信息
        return userInfo;
    }


    /**
     * 刷卡登录
     */
    public LoginUser cardLogin(String username, String userNumber, String userId,String type) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, userNumber, userId)) {
            throw new BaseException("登录信息不完整");
        }
        // 用户名不在指定范围内 错误
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new BaseException("用户名不在指定范围");
        }
        // 查询用户信息
        R<LoginUser> userResult = userService.getUserInfo(username, type);

        if (R.FAIL == userResult.getCode()) {
            throw new BaseException(userResult.getMsg());
        }

        if (StringUtils.isNull(userResult) || StringUtils.isNull(userResult.getData())) {
            throw new BaseException("登录用户：" + username + " 不存在");
        }
        LoginUser userInfo = userResult.getData();
        SysUser user = userResult.getData().getSysUser();
        if (UserStatus.DELETED.getCode().equals(user.getDelFlag())) {
            throw new BaseException("对不起，您的账号：" + username + " 已被删除");
        }
        if (UserStatus.DISABLE.getCode().equals(user.getStatus())) {
            throw new BaseException("对不起，您的账号：" + username + " 已停用");
        }
        if (!(userNumber.equals(user.getUserNumber()) && userId.equals(user.getUserId().toString()))) {
            throw new BaseException("刷卡登录信息有误");
        }
        return userInfo;
    }

    public void logout(String loginName) {
//        recordLogininfor(loginName, Constants.LOGOUT, "退出成功");
    }

    /**
     * 注册
     */
    public void register(String username, String password) {
        // 用户名或密码为空 错误
        if (StringUtils.isAnyBlank(username, password)) {
            throw new BaseException("用户/密码必须填写");
        }
        if (username.length() < UserConstants.USERNAME_MIN_LENGTH
                || username.length() > UserConstants.USERNAME_MAX_LENGTH) {
            throw new BaseException("账户长度必须在2到20个字符之间");
        }
        if (password.length() < UserConstants.PASSWORD_MIN_LENGTH
                || password.length() > UserConstants.PASSWORD_MAX_LENGTH) {
            throw new BaseException("密码长度必须在5到20个字符之间");
        }

        // 注册用户信息
        SysUser sysUser = new SysUser();
        sysUser.setUserName(username);
        sysUser.setNickName(username);
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        R<?> registerResult = userService.registerUser(sysUser);

        if (R.FAIL == registerResult.getCode()) {
            throw new BaseException(registerResult.getMsg());
        }
    }

}
