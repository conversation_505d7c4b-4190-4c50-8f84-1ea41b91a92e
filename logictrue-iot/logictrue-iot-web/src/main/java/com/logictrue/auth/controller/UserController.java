package com.logictrue.auth.controller;

import com.logictrue.auth.entity.LoginUser;
import com.logictrue.auth.entity.SysUser;
import com.logictrue.auth.service.TokenService;
import com.logictrue.auth.service.UserService;
import com.logictrue.common.core.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/user")
@Api(value = "用户信息", tags = "用户信息")
public class UserController {

    @Autowired
    private UserService userService;
    @Autowired
    private TokenService tokenService;

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/getInfo")
    @ApiOperation(value = "获取用户信息")
    public R<Map<String, Object>> getInfo() {
        try {
            LoginUser loginUser = tokenService.getLoginUser();
            if (loginUser == null) {
                return R.fail("登陆状态已过期，请重新登录");
            }
            SysUser sysUser = loginUser.getSysUser();

            // 简化的角色和权限集合
            Set<String> roles = new HashSet<>();
            Set<String> permissions = new HashSet<>();

            // 管理员用户
            if (sysUser.isAdmin()) {
                roles.add("admin");
                permissions.add("*:*:*");
            } else {
                // 普通用户
                roles.add("user");
                permissions.add("user:*:*");
            }

            Map<String, Object> result = new HashMap<>();
            result.put("user", sysUser);
            result.put("roles", roles);
            result.put("permissions", permissions);

            return R.ok(result);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return R.fail("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据用户编号获取详细信息
     */
    @GetMapping(value = "/{userId}")
    @ApiOperation(value = "根据用户编号获取详细信息")
    public R<Map<String, Object>> getInfo(@ApiParam("用户ID") @PathVariable(value = "userId", required = false) Long userId) {
        try {
            SysUser sysUser = userService.selectUserById(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("user", sysUser);
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取用户信息失败", e);
            return R.fail("获取用户信息失败：" + e.getMessage());
        }
    }
}
