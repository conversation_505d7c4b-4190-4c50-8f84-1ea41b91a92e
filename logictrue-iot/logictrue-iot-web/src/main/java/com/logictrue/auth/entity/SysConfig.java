package com.logictrue.auth.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.logictrue.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 参数配置对象 sys_config
 *
 * <AUTHOR>
 * @date 2021-09-26
 */
@ApiModel(description = "参数配置")
@TableName("sys_config")
public class SysConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 参数主键
     */
    @ApiModelProperty(value = "参数主键")
    @TableId(value = "config_id", type = IdType.AUTO)
    private Long configId;

    /**
     * 参数名称
     */
    @ApiModelProperty(value = "参数名称")
    @TableField("config_name")
    private String configName;

    /**
     * 参数键名
     */
    @ApiModelProperty(value = "参数键名")
    @TableField("config_key")
    private String configKey;

    /**
     * 键值类型
     */
    @ApiModelProperty(value = "键值类型")
    @TableField("config_value_type")
    private String configValueType;

    /**
     * 参数键值
     */
    @ApiModelProperty(value = "参数键值")
    @TableField("config_value")
    private String configValue;

    /**
     * 参数键值
     */
    @ApiModelProperty(value = "键值Option")
    @TableField("config_value_options")
    private String configValueOptions;

    /**
     * 系统内置（Y是 N否）
     */
    @ApiModelProperty(value = "系统内置")
    @TableField(value = "config_type")
    private String configType;

    @Version
    @ApiModelProperty(value = "版本号")
    @TableField(value = "version")
    private Integer version;

    @Version
    @ApiModelProperty(value = "用于保存复选框选中的值")
    @TableField(exist = false)
    private String[] configValueCheck;

    public String[] getConfigValueCheck() {
        if (StringUtils.equals(configValueType, "checkbox") && StringUtils.isNotEmpty(configValue)) {
            return configValue.split(",");
        }
        return configValueCheck;
    }

    public void setConfigValueCheck(String[] configValueCheck) {
        this.configValueCheck = configValueCheck;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigType(String configType) {
        this.configType = configType;
    }

    public String getConfigType() {
        return configType;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getConfigValueType() {
        return configValueType;
    }

    public void setConfigValueType(String configValueType) {
        this.configValueType = configValueType;
    }

    public String getConfigValueOptions() {
        return configValueOptions;
    }

    public void setConfigValueOptions(String configValueOptions) {
        this.configValueOptions = configValueOptions;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("configId", getConfigId())
                .append("configName", getConfigName())
                .append("configKey", getConfigKey())
                .append("configValue", getConfigValue())
                .append("configType", getConfigType())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .append("version", getVersion())
                .toString();
    }
}
