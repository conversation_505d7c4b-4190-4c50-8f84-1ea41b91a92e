package com.logictrue.auth.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.logictrue.auth.entity.LoginUser;
import com.logictrue.auth.entity.SysUser;
import com.logictrue.auth.mapper.UserMapper;
import com.logictrue.common.core.domain.R;
import com.logictrue.common.core.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 用户 业务层实现 (简化版用于iot-base模块)
 *
 * <AUTHOR>
 */
@Service
public class UserService extends ServiceImpl<UserMapper, SysUser> {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private UserMapper userMapper;

    public SysUser selectUserByUserName(String userName) {
        return userMapper.selectUserByUserName(userName);
    }



    public SysUser selectUserById(Long userId) {
        return userMapper.selectUserById(userId);
    }

    public R<LoginUser> getUserInfo(String username, String type) {
        SysUser sysUser = selectUserByUserName(username);
        if (StringUtils.isNull(sysUser)) {
            return R.fail("用户名或密码错误");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(sysUser.getUserId());

        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRoles(roles);
        return R.ok(sysUserVo);
    }


    public R<Boolean> registerUser(SysUser user) {
        return R.ok(userMapper.insert(user) > 0);
    }

}
