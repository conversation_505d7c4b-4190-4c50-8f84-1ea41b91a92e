<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.word.mapper.DesignWordMapper">

    <resultMap type="com.logictrue.word.entity.DesignWord" id="DesignWordResult">
        <result property="id" column="id"/>
        <result property="carId" column="car_id"/>
        <result property="title" column="title"/>
        <result property="tableConfig" column="table_config"/>
        <result property="tableData" column="table_data"/>
        <result property="status" column="status"/>
        <result property="pageName" column="page_name"/>
        <result property="pageOrder" column="page_order"/>
        <result property="totalPages" column="total_pages"/>
        <result property="isActive" column="is_active"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectDesignWordVo">
        select id, car_id, title, table_config, table_data, status, page_name, page_order, total_pages, is_active, create_by, create_time, update_by, update_time, remark
        from design_word
    </sql>

    <select id="selectDesignWordList" parameterType="com.logictrue.word.entity.DesignWord" resultMap="DesignWordResult">
        <include refid="selectDesignWordVo"/>
        <where>
            <if test="carId != null and carId != ''">and car_id like concat('%', #{carId}, '%')</if>
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="status != null">and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectDesignWordPage" resultMap="DesignWordResult">
        <include refid="selectDesignWordVo"/>
        <where>
            <if test="designWord.carId != null and designWord.carId != ''">and car_id like concat('%', #{designWord.carId}, '%')</if>
            <if test="designWord.title != null and designWord.title != ''">and title like concat('%', #{designWord.title}, '%')</if>
            <if test="designWord.status != null">and status = #{designWord.status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectDesignWordByCarId" parameterType="String" resultMap="DesignWordResult">
        <include refid="selectDesignWordVo"/>
        where car_id = #{carId} and status = 1
        order by create_time desc
        limit 1
    </select>

    <select id="selectDesignWordPagesByCarId" parameterType="String" resultMap="DesignWordResult">
        <include refid="selectDesignWordVo"/>
        where car_id = #{carId} and status = 1
        order by page_order asc
    </select>

    <select id="selectDesignWordByCarIdAndPage" resultMap="DesignWordResult">
        <include refid="selectDesignWordVo"/>
        where car_id = #{carId} and page_order = #{pageOrder} and status = 1
        limit 1
    </select>

    <select id="selectActiveDesignWordByCarId" parameterType="String" resultMap="DesignWordResult">
        <include refid="selectDesignWordVo"/>
        where car_id = #{carId} and is_active = 1 and status = 1
        order by page_order asc
        limit 1
    </select>

    <insert id="insertDesignWord" parameterType="com.logictrue.word.entity.DesignWord" useGeneratedKeys="true" keyProperty="id">
        insert into design_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="carId != null and carId != ''">car_id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="tableConfig != null">table_config,</if>
            <if test="tableData != null">table_data,</if>
            <if test="status != null">status,</if>
            <if test="pageName != null and pageName != ''">page_name,</if>
            <if test="pageOrder != null">page_order,</if>
            <if test="totalPages != null">total_pages,</if>
            <if test="isActive != null">is_active,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="carId != null and carId != ''">#{carId},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="tableConfig != null">#{tableConfig},</if>
            <if test="tableData != null">#{tableData},</if>
            <if test="status != null">#{status},</if>
            <if test="pageName != null and pageName != ''">#{pageName},</if>
            <if test="pageOrder != null">#{pageOrder},</if>
            <if test="totalPages != null">#{totalPages},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updateDesignWord" parameterType="com.logictrue.word.entity.DesignWord">
        update design_word
        <trim prefix="SET" suffixOverrides=",">
            <if test="carId != null and carId != ''">car_id = #{carId},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="tableConfig != null">table_config = #{tableConfig},</if>
            <if test="tableData != null">table_data = #{tableData},</if>
            <if test="status != null">status = #{status},</if>
            <if test="pageName != null and pageName != ''">page_name = #{pageName},</if>
            <if test="pageOrder != null">page_order = #{pageOrder},</if>
            <if test="totalPages != null">total_pages = #{totalPages},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <update id="updateTotalPagesByCarId">
        update design_word set total_pages = #{totalPages}, update_time = sysdate()
        where car_id = #{carId} and status = 1
    </update>

    <update id="setActivePage">
        update design_word set is_active =
        case when page_order = #{pageOrder} then 1 else 0 end,
        update_time = sysdate()
        where car_id = #{carId} and status = 1
    </update>

    <delete id="deleteDesignWordById" parameterType="Long">
        delete from design_word where id = #{id}
    </delete>

    <delete id="deleteDesignWordByIds" parameterType="String">
        delete from design_word where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>
