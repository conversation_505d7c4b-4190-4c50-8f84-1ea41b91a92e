<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.logictrue.auth.mapper.TokenMapper">

    <update id="updateTokenById">
        update sys_user set mobile_token = #{token} where user_id = #{userId}
    </update>

    <update id="updateLoginDateById">
        update sys_user set login_date = #{loginDate} where user_id = #{userId}
    </update>

    <update id="updateLoginToIpMacDateById">
        update sys_user set login_date = #{loginDate}, login_ip = #{login_ip}, login_mac= #{login_mac} where user_id = #{userId}
    </update>

</mapper>
