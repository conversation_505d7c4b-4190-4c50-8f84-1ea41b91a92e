# Tomcat
server:
  port: 9335

# Spring
spring:
  application:
    # 应用名称
    name: logictrue-iot-web

  datasource:
    url: **************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
  redis:
    host: *************
    port: 6379

file:
  path: /home/<USER>

magic-api:
  web: /magic/web
  resource:
    type: database  # 配置接口存储方式，这里选择存在数据库中
    table-name: magic_api_file  # 数据库中的表名
    prefix: /magic-api  # 前缀
  show-url: false
#    location: classpath:magic-api
# 其它配置请参考 https://ssssssss.org/magic-api/config/
