10:48:31.504 [main] INFO  c.l.LogicTrueIotApplication - [logStarting,55] - Starting LogicTrueIotApplication using Java 1.8.0_181 on DESKTOP-C9OS88D with PID 158268 (/home/<USER>/nl-mes/iot-all/logictrue-iot/logictrue-iot-web/target/classes started by tao in /home/<USER>/nl-mes/iot-all)
10:48:31.507 [main] INFO  c.l.LogicTrueIotApplication - [logStartupProfileInfo,663] - The following profiles are active: nl-mes
10:48:32.726 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9335"]
10:48:32.727 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:48:32.727 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.50]
10:48:32.774 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:48:34.152 [main] INFO  c.z.h.HikariDataSource - [getConnection,110] - HikariPool-1 - Starting...
10:48:34.282 [main] INFO  c.z.h.HikariDataSource - [getConnection,123] - HikariPool-1 - Start completed.
10:48:34.648 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：default
10:48:34.649 [main] INFO  o.s.m.d.m.MagicDynamicDataSource - [put,67] - 注册数据源：slave
10:48:34.997 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicNotifyService,221] - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
10:48:35.026 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [pageProvider,111] - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=size,默认首页=1,默认页大小=10,最大页大小=-1)
10:48:35.027 [main] INFO  o.s.m.s.b.s.MagicModuleConfiguration - [sqlCache,122] - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
10:48:35.104 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [magicConfiguration,302] - magic-api工作目录:db://magic_api_file//magic-api
10:48:35.110 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [setupMagicModules,267] - 注册模块:log -> interface org.slf4j.Logger
10:48:35.112 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
10:48:35.113 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$7,272] - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
10:48:35.114 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$8,280] - 自动导入模块：db
10:48:35.116 [main] INFO  o.s.m.s.b.s.MagicAPIAutoConfiguration - [lambda$setupMagicModules$10,288] - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
10:48:35.172 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9335"]
10:48:35.197 [main] INFO  c.l.LogicTrueIotApplication - [logStarted,61] - Started LogicTrueIotApplication in 4.153 seconds (JVM running for 4.776)
10:49:09.699 [http-nio-9335-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:50:28.253 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
10:50:28.254 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
10:50:28.259 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
10:50:28.260 [http-nio-9335-exec-4] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:30:00.151 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:30:00.158 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
11:30:00.162 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:30:00.163 [http-nio-9335-exec-1] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
11:34:07.464 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
11:34:07.466 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
11:34:07.469 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
11:34:07.470 [http-nio-9335-exec-6] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:23:25.499 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:23:25.500 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:23:25.504 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:23:25.505 [http-nio-9335-exec-5] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:30:08.186 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:30:08.187 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:30:08.190 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:30:08.192 [http-nio-9335-exec-3] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:31:33.719 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:31:33.720 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:31:33.723 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:31:33.724 [http-nio-9335-exec-10] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
12:59:36.558 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select count(1) from ( 
select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?
 ) count_
12:59:36.560 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String)
12:59:36.564 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,31] - 执行SQL：select
car_id,
check_name,
check_content,
result,
MONTH(check_time) as month_str,
DAY(check_time) as day_str,
check_user_name,
check_user_name as bzz,
check_user_name as jyy
from drl_work_check_records WHERE car_id = ?  order by sort
 limit ?,?
12:59:36.565 [http-nio-9335-exec-8] INFO  /检验卡片/查询列表(/check/get) - [handleLog,36] - SQL参数：0822(String), 0(Long), 10(Long)
